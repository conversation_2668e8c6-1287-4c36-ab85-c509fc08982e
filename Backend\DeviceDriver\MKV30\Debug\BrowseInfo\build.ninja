##IAR Ninja build file


#Rules
rule COMPILER_XCL
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\XclFileGenerator.exe" $xclcommand -f "$rspfile_name"
  description = IAR_NEW_TOOL+++COMPILER_XCL+++$out
  rspfile = $rspfile_name
  rspfile_content = $flags

rule INDEXER
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\SourceIndexer.exe" $flags
  depfile = $out.dep
  deps = gcc
  description = IAR_NEW_TOOL+++INDEXER+++$out

rule MAKEBROWSE
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\makeBrowseData.exe" $flags
  description = IAR_NEW_TOOL+++MAKEBROWSE+++$out

rule PDBLINK
  command = "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\common\bin\PbdLink.exe" $flags
  description = IAR_NEW_TOOL+++PDBLINK+++$out



#Build steps
build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\board\board.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\board_15934270115374689736.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\board\board.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\board\clock_config.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\board_15934270115374689736.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\board\clock_config.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\board\peripherals.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\board_15934270115374689736.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\board\peripherals.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\board\pin_mux.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\board_15934270115374689736.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\board\pin_mux.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\component\lists\fsl_component_generic_list.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\lists_3027992814978638235.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\component\lists\fsl_component_generic_list.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\component\uart\fsl_adapter_uart.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\uart_2732247187628865378.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\component\uart\fsl_adapter_uart.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\device\system_MKV30F12810.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\device_14273152114726608622.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\device\system_MKV30F12810.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_adc16.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_adc16.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_clock.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_clock.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_common.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_common.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_common_arm.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_common_arm.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_dac.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_dac.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_ftm.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_ftm.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_gpio.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_gpio.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_lpuart.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_lpuart.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_pit.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_pit.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_smc.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_smc.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_uart.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_uart.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\drivers\fsl_vref.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\drivers_4306295370633009451.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\drivers\fsl_vref.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\lab\Lab_pwm.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\lab_5703968128717247585.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\lab\Lab_pwm.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\source\ServoControl.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\source_5634686896477721948.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\source\ServoControl.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\utilities\fsl_assert.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\utilities_9832829787270487890.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\utilities\fsl_assert.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\utilities\fsl_debug_console.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\utilities_9832829787270487890.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\utilities\fsl_debug_console.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.xcl : COMPILER_XCL 
    flags = D$:\IAR.work\MKV30_t2\utilities\fsl_str.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D$:\IAR.work\MKV30_t2\debug\obj\utilities_9832829787270487890.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\inc\c\DLib_Config_Normal.h" -I D$:\IAR.work\MKV30_t2/board\ -I D$:\IAR.work\MKV30_t2/CMSIS\ -I D$:\IAR.work\MKV30_t2/component/lists\ -I D$:\IAR.work\MKV30_t2/component/uart\ -I D$:\IAR.work\MKV30_t2/device\ -I D$:\IAR.work\MKV30_t2/drivers\ -I D$:\IAR.work\MKV30_t2/utilities\ -I D$:\IAR.work\MKV30_t2\lab\ -On --predef_macros D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.tmp
    rspfile_name = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.xcl.rsp
    xclcommand = -source_file D$:\IAR.work\MKV30_t2\utilities\fsl_str.c -xcl_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.xcl -macro_file D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.tmp -icc_path "C$:\Program$ Files\IAR$ Systems\Embedded$ Workbench$ 9.2\arm\bin\iccarm.exe"

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.xcl | D$:\IAR.work\MKV30_t2\board\board.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.xcl | D$:\IAR.work\MKV30_t2\board\clock_config.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.xcl | D$:\IAR.work\MKV30_t2\board\peripherals.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.xcl | D$:\IAR.work\MKV30_t2\board\pin_mux.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.xcl | D$:\IAR.work\MKV30_t2\component\lists\fsl_component_generic_list.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.xcl | D$:\IAR.work\MKV30_t2\component\uart\fsl_adapter_uart.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.xcl | D$:\IAR.work\MKV30_t2\device\system_MKV30F12810.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_adc16.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_clock.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_common.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_common_arm.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_dac.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_ftm.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_gpio.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_lpuart.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_pit.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_smc.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_uart.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.xcl | D$:\IAR.work\MKV30_t2\drivers\fsl_vref.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.xcl | D$:\IAR.work\MKV30_t2\lab\Lab_pwm.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.xcl | D$:\IAR.work\MKV30_t2\source\ServoControl.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.xcl | D$:\IAR.work\MKV30_t2\utilities\fsl_assert.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.xcl | D$:\IAR.work\MKV30_t2\utilities\fsl_debug_console.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.pbi : INDEXER D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.xcl | D$:\IAR.work\MKV30_t2\utilities\fsl_str.c
    flags = -out=D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.pbi -f D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.xcl

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test.pbw : MAKEBROWSE D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test.pbd
    flags = D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test.pbd -output D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test.pbw

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part0.pbi : PDBLINK D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.pbi | D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.pbi
    flags = -M D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part0.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\board.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\clock_config.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\peripherals.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\board_15934270115374689736.dir\pin_mux.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lists_3027992814978638235.dir\fsl_component_generic_list.pbi

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part1.pbi : PDBLINK D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.pbi | D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.pbi
    flags = -M D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part1.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\uart_2732247187628865378.dir\fsl_adapter_uart.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\device_14273152114726608622.dir\system_MKV30F12810.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_adc16.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_clock.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common.pbi

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part2.pbi : PDBLINK D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.pbi | D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.pbi
    flags = -M D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part2.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_common_arm.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_dac.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_ftm.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_gpio.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_lpuart.pbi

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part3.pbi : PDBLINK D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.pbi | D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.pbi
    flags = -M D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part3.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_pit.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_smc.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_uart.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\drivers_4306295370633009451.dir\fsl_vref.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\lab_5703968128717247585.dir\Lab_pwm.pbi

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part4.pbi : PDBLINK D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.pbi | D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.pbi
    flags = -M D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part4.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\source_5634686896477721948.dir\ServoControl.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_assert.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_debug_console.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\utilities_9832829787270487890.dir\fsl_str.pbi

build D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test.pbd : PDBLINK D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part0.pbi | D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part1.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part2.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part3.pbi $
 D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part4.pbi
    flags = -M D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test.pbd D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part0.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part1.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part2.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part3.pbi D$:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test_part4.pbi


###############################################################################
#
# IAR ELF Linker V9.50.1.380/W64 for ARM                  16/Feb/2025  15:48:57
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =  D:\IAR.work\MKV30_t2\debug\MKV30_test.out
#    Map file     =  D:\IAR.work\MKV30_t2\debug\list\MKV30_test.map
#    Command line =
#        -f D:\IAR.work\MKV30_t2\Debug\MKV30_test.out.rsp
#        (D:\IAR.work\MKV30_t2\Debug\obj\board_15934270115374689736.dir\board.o
#        D:\IAR.work\MKV30_t2\Debug\obj\board_15934270115374689736.dir\clock_config.o
#        D:\IAR.work\MKV30_t2\Debug\obj\uart_2732247187628865378.dir\fsl_adapter_uart.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_adc16.o
#        D:\IAR.work\MKV30_t2\Debug\obj\utilities_9832829787270487890.dir\fsl_assert.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_clock.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_common.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_common_arm.o
#        D:\IAR.work\MKV30_t2\Debug\obj\lists_3027992814978638235.dir\fsl_component_generic_list.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_dac.o
#        D:\IAR.work\MKV30_t2\Debug\obj\utilities_9832829787270487890.dir\fsl_debug_console.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_ftm.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_gpio.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_lpuart.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_pit.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_smc.o
#        D:\IAR.work\MKV30_t2\Debug\obj\utilities_9832829787270487890.dir\fsl_str.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_uart.o
#        D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir\fsl_vref.o
#        D:\IAR.work\MKV30_t2\Debug\obj\lab_5703968128717247585.dir\Lab_pwm.o
#        D:\IAR.work\MKV30_t2\Debug\obj\board_15934270115374689736.dir\peripherals.o
#        D:\IAR.work\MKV30_t2\Debug\obj\board_15934270115374689736.dir\pin_mux.o
#        D:\IAR.work\MKV30_t2\Debug\obj\source_5634686896477721948.dir\ServoControl.o
#        D:\IAR.work\MKV30_t2\Debug\obj\startup_5253055491327143397.dir\startup_MKV30F12810.o
#        D:\IAR.work\MKV30_t2\Debug\obj\device_14273152114726608622.dir\system_MKV30F12810.o
#        --redirect _Printf=_PrintfSmallNoMb --redirect _Scanf=_ScanfSmallNoMb
#        --no_out_extension -o D:\IAR.work\MKV30_t2\debug\MKV30_test.out --map
#        D:\IAR.work\MKV30_t2\debug\list\MKV30_test.map --config "C:\Program
#        Files\IAR Systems\Embedded Workbench
#        9.2\arm/config/linker/NXP/MKV30F64xxx10.icf" --semihosting --entry
#        Reset_Handler --vfe --text_out locale --cpu=Cortex-M4 --fpu=VFPv4_sp)
#        --dependencies=n D:\IAR.work\MKV30_t2\Debug\MKV30_test.out.iar_deps
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor       = *
__CPP_Runtime   = 1
__Heap_Handler  = Basic
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x0 { ro section .intvec };
"P1":  place in [from 0x400 to 0x40f] { section FlashConfig };
"P2":  place in [from 0x0 to 0x3ff] |
                [from 0x410 to 0xffff] { ro };
define block CSTACK with size = 1K, alignment = 8 { };
define block HEAP with size = 2K, alignment = 8 { };
"P3":  place in [from 0x1fff'e000 to 0x1fff'ffff repeat 2] {
          rw, block CSTACK, block HEAP };
initialize by copy { rw };

  Section            Kind         Address  Aligment    Size  Object
  -------            ----         -------  --------    ----  ------
"A0":                                                 0x400
  .intvec            ro code          0x0         4   0x400  startup_MKV30F12810.o [7]
                                  - 0x400             0x400

"P1":                                                  0x10
  FlashConfig        ro code        0x400         4    0x10  startup_MKV30F12810.o [7]
                                  - 0x410              0x10

"P2":                                                0x3254
  .text              ro code        0x410         4   0x808  fsl_ftm.o [3]
  .text              ro code        0xc18         4    0x1c  fsl_assert.o [9]
  .text              ro code        0xc34         4   0x3fc  fsl_debug_console.o [9]
  .text              ro code       0x1030         4    0xa6  ABImemcpy.o [12]
  .text              ro code       0x10d6         2    0x2a  copy_init3.o [12]
  .text              ro code       0x1100         4    0x36  strlen.o [12]
  .rodata            const         0x1136         2     0x2  peripherals.o [1]
  .text              ro code       0x1138         4   0x67c  fsl_clock.o [3]
  .text              ro code       0x17b4         4   0x490  ServoControl.o [6]
  .text              ro code       0x1c44         4   0x454  pin_mux.o [1]
  .text              ro code       0x2098         4    0xa8  clock_config.o [1]
  .text              ro code       0x2140         4   0x2d8  peripherals.o [1]
  .text              ro code       0x2418         4    0xf4  Lab_pwm.o [4]
  .text              ro code       0x250c         4   0x310  fsl_uart.o [3]
  .text              ro code       0x281c         4    0x64  fsl_gpio.o [3]
  .text              ro code       0x2880         4    0x12  fsl_smc.o [3]
  .rodata            const         0x2892         2     0x2  pin_mux.o [1]
  .text              ro code       0x2894         4   0x110  fsl_dac.o [3]
  .text              ro code       0x29a4         4    0xb0  fsl_vref.o [3]
  .text              ro code       0x2a54         4   0x1a0  fsl_adc16.o [3]
  .text              ro code       0x2bf4         4    0xcc  fsl_pit.o [3]
  .rodata            const         0x2cc0         4    0xd4  fsl_ftm.o [3]
  .rodata            const         0x2d94         4    0xc4  fsl_uart.o [3]
  .rodata            const         0x2e58         4    0xc4  fsl_uart.o [3]
  .rodata            const         0x2f1c         4    0x40  fsl_adc16.o [3]
  .rodata            const         0x2f5c         4    0x3c  fsl_dac.o [3]
  .rodata            const         0x2f98         4    0x3c  fsl_pit.o [3]
  .rodata            const         0x2fd4         4    0x3c  fsl_vref.o [3]
  .text              ro code       0x3010         2    0x38  zero_init3.o [12]
  .text              ro code       0x3048         4    0x34  system_MKV30F12810.o [2]
  .text              ro code       0x307c         2     0x2  system_MKV30F12810.o [2]
  .rodata            const         0x307e         2     0x2  pin_mux.o [1]
  .rodata            const         0x3080         4    0x2c  fsl_adc16.o [3]
  .rodata            const         0x30ac         4    0x2c  fsl_assert.o [9]
  .rodata            const         0x30d8         4    0x2c  fsl_clock.o [3]
  .text              ro code       0x3104         4    0x2a  data_init.o [12]
  .rodata            const         0x312e         2     0x2  pin_mux.o [1]
  .rodata            const         0x3130         4    0x28  fsl_dac.o [3]
  .rodata            const         0x3158         4    0x28  fsl_ftm.o [3]
  .rodata            const         0x3180         4    0x28  fsl_gpio.o [3]
  .rodata            const         0x31a8         4    0x28  fsl_pit.o [3]
  .rodata            const         0x31d0         4    0x28  fsl_uart.o [3]
  .rodata            const         0x31f8         4    0x28  fsl_vref.o [3]
  .rodata            const         0x3220         4    0x28  peripherals.o [1]
  .text              ro code       0x3248         4    0x28  startup_MKV30F12810.o [7]
  .text              ro code       0x3270         4    0x22  fpinit_M.o [11]
  .iar.init_table    const         0x3294         4    0x24  - Linker created -
  .rodata            const         0x32b8         2     0x2  pin_mux.o [1]
  .rodata            const         0x32bc         4    0x20  peripherals.o [1]
  .rodata            const         0x32dc         4    0x20  peripherals.o [1]
  .rodata            const         0x32fc         4    0x20  peripherals.o [1]
  .text              ro code       0x331c         4    0x1e  cmain.o [12]
  .text              ro code       0x333a         2     0x4  low_level_init.o [10]
  .text              ro code       0x333e         2     0x4  exit.o [10]
  .rodata            const         0x3342         2     0x2  pin_mux.o [1]
  .text              ro code       0x3344         4     0xa  cexit.o [12]
  .rodata            const         0x334e         2     0x2  pin_mux.o [1]
  .text              ro code       0x3350         4    0x14  exit.o [13]
  .rodata            const         0x3364         4    0x1c  fsl_ftm.o [3]
  .rodata            const         0x3380         4    0x1c  fsl_uart.o [3]
  .rodata            const         0x339c         4    0x1c  fsl_uart.o [3]
  .text              ro code       0x33b8         4    0x1c  cstartup_M.o [12]
  .rodata            const         0x33d4         4    0x14  fsl_adc16.o [3]
  .rodata            const         0x33e8         4    0x14  fsl_clock.o [3]
  .rodata            const         0x33fc         4    0x14  fsl_clock.o [3]
  .rodata            const         0x3410         4    0x14  fsl_ftm.o [3]
  .rodata            const         0x3424         4    0x14  fsl_ftm.o [3]
  .rodata            const         0x3438         4    0x14  fsl_ftm.o [3]
  .rodata            const         0x344c         4    0x10  fsl_clock.o [3]
  .rodata            const         0x345c         4    0x10  fsl_clock.o [3]
  .rodata            const         0x346c         4    0x10  fsl_dac.o [3]
  .rodata            const         0x347c         4    0x10  fsl_ftm.o [3]
  .rodata            const         0x348c         4    0x10  fsl_ftm.o [3]
  .rodata            const         0x349c         4    0x10  fsl_ftm.o [3]
  .rodata            const         0x34ac         4    0x10  fsl_ftm.o [3]
  .rodata            const         0x34bc         4     0xc  clock_config.o [1]
  .rodata            const         0x34c8         4     0xc  fsl_adc16.o [3]
  .rodata            const         0x34d4         4     0xc  fsl_clock.o [3]
  .rodata            const         0x34e0         4     0xc  fsl_clock.o [3]
  .rodata            const         0x34ec         4     0xc  fsl_dac.o [3]
  .rodata            const         0x34f8         4     0xc  fsl_gpio.o [3]
  .rodata            const         0x3504         4     0xc  fsl_pit.o [3]
  .rodata            const         0x3510         4     0xc  fsl_uart.o [3]
  .rodata            const         0x351c         4     0xc  fsl_uart.o [3]
  .rodata            const         0x3528         4     0xc  fsl_uart.o [3]
  .rodata            const         0x3534         4     0xc  fsl_vref.o [3]
  .rodata            const         0x3540         4     0xc  peripherals.o [1]
  .rodata            const         0x354c         4     0xc  peripherals.o [1]
  .rodata            const         0x3558         4     0xc  peripherals.o [1]
  .rodata            const         0x3564         4     0xc  peripherals.o [1]
  .rodata            const         0x3570         4     0xc  peripherals.o [1]
  .rodata            const         0x357c         4     0x8  clock_config.o [1]
  .rodata            const         0x3584         4     0x8  clock_config.o [1]
  .rodata            const         0x358c         4     0x8  fsl_adc16.o [3]
  .rodata            const         0x3594         4     0x8  fsl_adc16.o [3]
  .rodata            const         0x359c         4     0x8  fsl_dac.o [3]
  .rodata            const         0x35a4         4     0x8  fsl_dac.o [3]
  .rodata            const         0x35ac         4     0x8  fsl_ftm.o [3]
  .text              ro code       0x35b4         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35bc         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35c4         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35cc         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35d4         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35dc         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35e4         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35ec         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35f4         4     0x8  startup_MKV30F12810.o [7]
  .text              ro code       0x35fc         4     0x8  startup_MKV30F12810.o [7]
  .rodata            const         0x3604         4     0x4  fsl_pit.o [3]
  .rodata            const         0x3608         4     0x4  fsl_pit.o [3]
  .rodata            const         0x360c         4     0x4  fsl_vref.o [3]
  .rodata            const         0x3610         4     0x4  fsl_vref.o [3]
  .text              ro code       0x3614         2     0x4  startup_MKV30F12810.o [7]
  .rodata            const         0x3618         2     0x2  pin_mux.o [1]
  .rodata            const         0x361a         2     0x2  pin_mux.o [1]
  .rodata            const         0x361c         2     0x2  pin_mux.o [1]
  .rodata            const         0x361e         2     0x2  pin_mux.o [1]
  .rodata            const         0x3620         2     0x2  pin_mux.o [1]
  .rodata            const         0x3622         2     0x2  pin_mux.o [1]
  .rodata            const         0x3624         2     0x2  pin_mux.o [1]
  .rodata            const         0x3626         2     0x2  pin_mux.o [1]
  .rodata            const         0x3628         2     0x2  pin_mux.o [1]
  .rodata            const         0x362a         2     0x2  pin_mux.o [1]
  .rodata            const         0x362c         2     0x2  pin_mux.o [1]
  .rodata            const         0x362e         2     0x2  pin_mux.o [1]
  .rodata            const         0x3630         2     0x2  pin_mux.o [1]
  .rodata            const         0x3632         2     0x2  pin_mux.o [1]
  .rodata            const         0x3634         2     0x2  pin_mux.o [1]
  .text              ro code       0x3636         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x3638         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x363a         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x363c         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x363e         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x3640         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x3642         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x3644         2     0x2  startup_MKV30F12810.o [7]
  .text              ro code       0x3646         2     0x2  startup_MKV30F12810.o [7]
  .rodata            const         0x3648               0x1  peripherals.o [1]
  .rodata            const         0x3649               0x1  peripherals.o [1]
  .rodata            const         0x364a               0x1  peripherals.o [1]
  .rodata            const         0x364b               0x0  zero_init3.o [12]
  .rodata            const         0x364b               0x0  copy_init3.o [12]
  Initializer bytes  const         0x364c         4    0x18  <for P3 s0>
                                 - 0x3664            0x3254

"P3", part 1 of 3:                                     0x18
  P3 s0                       0x1fff'e000              0x18  <Init block>
    .data            inited   0x1fff'e000         4     0x8  peripherals.o [1]
    .data            inited   0x1fff'e008         4     0x4  fsl_clock.o [3]
    .data            inited   0x1fff'e00c         4     0x4  fsl_clock.o [3]
    .data            inited   0x1fff'e010         4     0x4  ServoControl.o [6]
    .data            inited   0x1fff'e014         4     0x4  system_MKV30F12810.o [2]
                            - 0x1fff'e018              0x18

"P3", part 2 of 3:                                     0x70
  .bss               zero     0x1fff'e018         4    0x14  fsl_debug_console.o [9]
  .bss               zero     0x1fff'e02c         4     0xc  fsl_uart.o [3]
  .bss               zero     0x1fff'e038         4     0xc  ServoControl.o [6]
  .bss               zero     0x1fff'e044         4     0x8  ServoControl.o [6]
  .bss               zero     0x1fff'e04c         4     0x4  fsl_clock.o [3]
  .bss               zero     0x1fff'e050         4     0x4  fsl_clock.o [3]
  .bss               zero     0x1fff'e054         4     0x4  fsl_uart.o [3]
  .bss               zero     0x1fff'e058         4     0x4  ServoControl.o [6]
  .bss               zero     0x1fff'e05c         4     0x4  ServoControl.o [6]
  .bss               zero     0x1fff'e060         4     0x4  ServoControl.o [6]
  .bss               zero     0x1fff'e064         4     0x4  ServoControl.o [6]
  .bss               zero     0x1fff'e068         4     0x4  ServoControl.o [6]
  .bss               zero     0x1fff'e06c         4     0x4  ServoControl.o [6]
  .bss               zero     0x1fff'e070         4     0x4  ServoControl.o [6]
  .bss               zero     0x1fff'e074         2     0x2  ServoControl.o [6]
  .bss               zero     0x1fff'e076         2     0x2  ServoControl.o [6]
  .bss               zero     0x1fff'e078         2     0x2  ServoControl.o [6]
  .bss               zero     0x1fff'e07a         2     0x2  ServoControl.o [6]
  .bss               zero     0x1fff'e07c         2     0x2  ServoControl.o [6]
  .bss               zero     0x1fff'e07e               0x1  ServoControl.o [6]
  .bss               zero     0x1fff'e07f               0x1  ServoControl.o [6]
  .bss               zero     0x1fff'e080               0x1  ServoControl.o [6]
  .bss               zero     0x1fff'e081               0x1  ServoControl.o [6]
  .bss               zero     0x1fff'e082               0x1  ServoControl.o [6]
  .bss               zero     0x1fff'e083               0x1  ServoControl.o [6]
  .bss               zero     0x1fff'e084               0x1  ServoControl.o [6]
                            - 0x1fff'e085              0x6d

"P3", part 3 of 3:                                    0x400
  CSTACK                      0x1fff'e088         8   0x400  <Block>
    CSTACK           uninit   0x1fff'e088             0x400  <Block tail>
                            - 0x1fff'e488             0x400

Unused ranges:

         From           To    Size
         ----           --    ----
       0x3664       0xffff  0xc99c
  0x1fff'e488  0x1fff'ffff  0x1b78
  0x2000'0000  0x2000'1fff  0x2000


*******************************************************************************
*** INIT TABLE
***

          Address      Size
          -------      ----
Zero (__iar_zero_init3)
    1 destination range, total size 0x6d:
          0x1fff'e018  0x6d

Copy (__iar_copy_init3)
    1 source range, total size 0x18:
               0x364c  0x18
    1 destination range, total size 0x18:
          0x1fff'e000  0x18



*******************************************************************************
*** MODULE SUMMARY
***

    Module                 ro code  ro data  rw data
    ------                 -------  -------  -------
command line/config:
    ------------------------------------------------
    Total:

D:\IAR.work\MKV30_t2\Debug\obj\board_15934270115374689736.dir: [1]
    clock_config.o             168       28
    peripherals.o              728      209        8
    pin_mux.o                1'108       42
    ------------------------------------------------
    Total:                   2'004      279        8

D:\IAR.work\MKV30_t2\Debug\obj\device_14273152114726608622.dir: [2]
    system_MKV30F12810.o        54        4        4
    ------------------------------------------------
    Total:                      54        4        4

D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir: [3]
    fsl_adc16.o                416      156
    fsl_clock.o              1'660      148       16
    fsl_dac.o                  272      144
    fsl_ftm.o                2'056      412
    fsl_gpio.o                 100       52
    fsl_pit.o                  204      120
    fsl_smc.o                   18
    fsl_uart.o                 784      524       16
    fsl_vref.o                 176      120
    ------------------------------------------------
    Total:                   5'686    1'676       32

D:\IAR.work\MKV30_t2\Debug\obj\lab_5703968128717247585.dir: [4]
    Lab_pwm.o                  244
    ------------------------------------------------
    Total:                     244

D:\IAR.work\MKV30_t2\Debug\obj\lists_3027992814978638235.dir: [5]
    ------------------------------------------------
    Total:

D:\IAR.work\MKV30_t2\Debug\obj\source_5634686896477721948.dir: [6]
    ServoControl.o           1'168        4       69
    ------------------------------------------------
    Total:                   1'168        4       69

D:\IAR.work\MKV30_t2\Debug\obj\startup_5253055491327143397.dir: [7]
    startup_MKV30F12810.o    1'182
    ------------------------------------------------
    Total:                   1'182

D:\IAR.work\MKV30_t2\Debug\obj\uart_2732247187628865378.dir: [8]
    ------------------------------------------------
    Total:

D:\IAR.work\MKV30_t2\Debug\obj\utilities_9832829787270487890.dir: [9]
    fsl_assert.o                28       44
    fsl_debug_console.o      1'020                20
    ------------------------------------------------
    Total:                   1'048       44       20

dl7M_tln.a: [10]
    exit.o                       4
    low_level_init.o             4
    ------------------------------------------------
    Total:                       8

m7M_tls.a: [11]
    fpinit_M.o                  34
    ------------------------------------------------
    Total:                      34

rt7M_tl.a: [12]
    ABImemcpy.o                166
    cexit.o                     10
    cmain.o                     30
    copy_init3.o                42
    cstartup_M.o                28
    data_init.o                 42
    strlen.o                    54
    zero_init3.o                56
    ------------------------------------------------
    Total:                     428

shb_l.a: [13]
    exit.o                      20
    ------------------------------------------------
    Total:                      20

    Gaps                                  4
    Linker created                       37    1'024
----------------------------------------------------
    Grand Total:            11'876    2'048    1'157


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address   Size  Type      Object
-----                       -------   ----  ----      ------
.iar.init_table$$Base        0x3294          --   Gb  - Linker created -
.iar.init_table$$Limit       0x32b8          --   Gb  - Linker created -
?main                        0x331d         Code  ??  cmain.o [12]
ADC0_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
ADC0_channelsConfig     0x1fff'e000    0x8  Data  ??  peripherals.o [1]
ADC0_config                  0x3570    0xc  Data  ??  peripherals.o [1]
ADC0_init                    0x231d   0x2a  Code  Lc  peripherals.o [1]
ADC0_muxMode                 0x3649    0x1  Data  ??  peripherals.o [1]
ADC16_EnableHardwareTrigger
                             0x21cb   0x1c  Code  Lc  peripherals.o [1]
ADC16_GetInstance            0x2a69   0x2e  Code  Lc  fsl_adc16.o [3]
ADC16_Init                   0x2a97   0xb4  Code  ??  fsl_adc16.o [3]
ADC16_SetChannelConfig       0x2b87   0x50  Code  ??  fsl_adc16.o [3]
ADC16_SetChannelMuxMode
                             0x2b4b   0x1c  Code  ??  fsl_adc16.o [3]
ADC16_SetHardwareAverage
                             0x2b67   0x20  Code  ??  fsl_adc16.o [3]
ADC1_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
BOARD_BootClockRUN           0x20c9   0x4e  Code  ??  clock_config.o [1]
BOARD_InitBootClocks         0x20c1    0x8  Code  ??  clock_config.o [1]
BOARD_InitBootPins           0x1c67    0x8  Code  ??  pin_mux.o [1]
BOARD_InitPeripherals        0x23f9   0x20  Code  ??  peripherals.o [1]
BOARD_InitPins               0x1c6f  0x376  Code  ??  pin_mux.o [1]
BusFault_Handler             0x363d         Code  Wk  startup_MKV30F12810.o [7]
CLOCK_BootToFeeMode          0x1765   0x2c  Code  ??  fsl_clock.o [3]
CLOCK_CONFIG_FllStableDelay
                             0x20af   0x12  Code  Lc  clock_config.o [1]
CLOCK_EnableClock            0x2a55   0x14  Code  Lc  fsl_adc16.o [3]
CLOCK_EnableClock            0x2895   0x12  Code  Lc  fsl_dac.o [3]
CLOCK_EnableClock             0x411   0x14  Code  Lc  fsl_ftm.o [3]
CLOCK_EnableClock            0x2bf5   0x12  Code  Lc  fsl_pit.o [3]
CLOCK_EnableClock            0x250d   0x14  Code  Lc  fsl_uart.o [3]
CLOCK_EnableClock            0x29a5   0x12  Code  Lc  fsl_vref.o [3]
CLOCK_EnableClock            0x1c45   0x14  Code  Lc  pin_mux.o [1]
CLOCK_GetEr32kClkFreq        0x132b   0x5c  Code  ??  fsl_clock.o [3]
CLOCK_GetFixedFreqClkFreq
                             0x1551   0x1e  Code  ??  fsl_clock.o [3]
CLOCK_GetFllExtRefClkFreq
                             0x11d9   0x6c  Code  Lc  fsl_clock.o [3]
CLOCK_GetFllFreq             0x14e7   0x54  Code  ??  fsl_clock.o [3]
CLOCK_GetFllFreq::fllFactorTable
                             0x345c   0x10  Data  Lc  fsl_clock.o [3]
CLOCK_GetFllRefClkFreq       0x126b   0x20  Code  Lc  fsl_clock.o [3]
CLOCK_GetFreq                0x13b9   0xdc  Code  ??  fsl_clock.o [3]
CLOCK_GetInternalRefClkFreq
                             0x153b   0x16  Code  ??  fsl_clock.o [3]
CLOCK_GetInternalRefClkSelectFreq
                             0x1245   0x26  Code  Lc  fsl_clock.o [3]
CLOCK_GetMcgExtClkFreq       0x1175   0x64  Code  Lc  fsl_clock.o [3]
CLOCK_GetOsc0ErClkDivFreq
                             0x12e5   0x46  Code  ??  fsl_clock.o [3]
CLOCK_GetOsc0ErClkUndivFreq
                             0x12b3   0x32  Code  ??  fsl_clock.o [3]
CLOCK_GetOscRangeFromFreq
                             0x1293   0x20  Code  Lc  fsl_clock.o [3]
CLOCK_GetOutClkFreq          0x14af   0x38  Code  ??  fsl_clock.o [3]
CLOCK_GetPll0Freq            0x156f   0x5e  Code  ??  fsl_clock.o [3]
CLOCK_GetPll0RefFreq         0x128b    0x8  Code  Lc  fsl_clock.o [3]
CLOCK_GetPllFllSelClkFreq
                             0x1387   0x32  Code  ??  fsl_clock.o [3]
CLOCK_InitOsc0               0x1611   0x4c  Code  ??  fsl_clock.o [3]
CLOCK_SetEr32kClock          0x1139    0xe  Code  Lc  fsl_clock.o [3]
CLOCK_SetExternalRefClkConfig
                             0x15cd   0x44  Code  ??  fsl_clock.o [3]
CLOCK_SetFeeMode             0x1681   0xb4  Code  ??  fsl_clock.o [3]
CLOCK_SetPllFllSelClock
                             0x1147    0xe  Code  Lc  fsl_clock.o [3]
CLOCK_SetSimConfig           0x1495   0x1a  Code  ??  fsl_clock.o [3]
CLOCK_SetSimSafeDivs         0x2099    0x8  Code  Lc  clock_config.o [1]
CLOCK_SetXtal0Freq           0x20a1    0x6  Code  Lc  clock_config.o [1]
CMP0_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
CMP1_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
CSTACK$$Base            0x1fff'e088          --   Gb  - Linker created -
CSTACK$$Limit           0x1fff'e488          --   ??  - Linker created -
CmdDataBuffer           0x1fff'e044    0x8  Data  ??  ServoControl.o [6]
CmdDataLen              0x1fff'e082    0x1  Data  ??  ServoControl.o [6]
CmdDataReady            0x1fff'e083    0x1  Data  ??  ServoControl.o [6]
CmdReceiving            0x1fff'e084    0x1  Data  ??  ServoControl.o [6]
DAC0_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
DAC0_config                  0x1136    0x2  Data  ??  peripherals.o [1]
DAC0_init                    0x22c3   0x28  Code  Lc  peripherals.o [1]
DAC_Enable                   0x21a7   0x24  Code  Lc  peripherals.o [1]
DAC_GetInstance              0x28a7   0x2e  Code  Lc  fsl_dac.o [3]
DAC_Init                     0x28d5   0x4a  Code  ??  fsl_dac.o [3]
DAC_SetBufferReadPointer
                             0x295b   0x2e  Code  ??  fsl_dac.o [3]
DAC_SetBufferValue           0x291f   0x3c  Code  ??  fsl_dac.o [3]
DC1                     0x1fff'e07e    0x1  Data  ??  ServoControl.o [6]
DC1_t                   0x1fff'e07f    0x1  Data  ??  ServoControl.o [6]
DC2                     0x1fff'e080    0x1  Data  ??  ServoControl.o [6]
DC2_t                   0x1fff'e081    0x1  Data  ??  ServoControl.o [6]
DMA0_DriverIRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
DMA0_IRQHandler              0x35b5         Code  Wk  startup_MKV30F12810.o [7]
DMA1_DriverIRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
DMA1_IRQHandler              0x35bd         Code  Wk  startup_MKV30F12810.o [7]
DMA2_DriverIRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
DMA2_IRQHandler              0x35c5         Code  Wk  startup_MKV30F12810.o [7]
DMA3_DriverIRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
DMA3_IRQHandler              0x35cd         Code  Wk  startup_MKV30F12810.o [7]
DMA_Error_DriverIRQHandler
                             0x3615         Code  Wk  startup_MKV30F12810.o [7]
DMA_Error_IRQHandler         0x35d5         Code  Wk  startup_MKV30F12810.o [7]
DbgConsole_ConvertRadixNumToString
                              0xcc1   0x82  Code  Lc  fsl_debug_console.o [9]
DbgConsole_Printf             0xc35   0x18  Code  ??  fsl_debug_console.o [9]
DbgConsole_PrintfFormattedData
                              0xd43  0x2ee  Code  Lc  fsl_debug_console.o [9]
DbgConsole_PrintfPaddingCharacter
                              0xc95   0x2c  Code  Lc  fsl_debug_console.o [9]
DbgConsole_Putchar            0xc75   0x1c  Code  ??  fsl_debug_console.o [9]
DbgConsole_Vprintf            0xc4d   0x24  Code  ??  fsl_debug_console.o [9]
DebugMon_Handler             0x3643         Code  Wk  startup_MKV30F12810.o [7]
DefaultISR                   0x3615         Code  Wk  startup_MKV30F12810.o [7]
EnableIRQ                    0x215f   0x24  Code  Lc  peripherals.o [1]
FTF_IRQHandler               0x3615         Code  Wk  startup_MKV30F12810.o [7]
FTM0_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
FTM0_config                  0x32dc   0x20  Data  ??  peripherals.o [1]
FTM0_init                    0x227d   0x46  Code  Lc  peripherals.o [1]
FTM0_pwmSignalParams         0x32fc   0x20  Data  ??  peripherals.o [1]
FTM1_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
FTM1_config                  0x32bc   0x20  Data  ??  peripherals.o [1]
FTM1_init                    0x224f   0x2e  Code  Lc  peripherals.o [1]
FTM1_phaseAParams            0x354c    0xc  Data  ??  peripherals.o [1]
FTM1_phaseBParams            0x3558    0xc  Data  ??  peripherals.o [1]
FTM2_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
FTM_GetInstance               0x48d   0x36  Code  Lc  fsl_ftm.o [3]
FTM_Init                      0x6b9   0xaa  Code  ??  fsl_ftm.o [3]
FTM_SetComplementaryEnable
                              0x459   0x34  Code  Lc  fsl_ftm.o [3]
FTM_SetDeadTimeEnable         0x425   0x34  Code  Lc  fsl_ftm.o [3]
FTM_SetPwmSync                0x4c3  0x126  Code  Lc  fsl_ftm.o [3]
FTM_SetQuadDecoderModuloValue
                             0x21a1    0x6  Code  Lc  peripherals.o [1]
FTM_SetReloadPoints           0x5e9   0xd0  Code  Lc  fsl_ftm.o [3]
FTM_SetSoftwareTrigger       0x2419   0x1c  Code  Lc  Lab_pwm.o [4]
FTM_SetTimerPeriod           0x2183    0x8  Code  Lc  peripherals.o [1]
FTM_SetupPwmMode              0x8fd  0x226  Code  ??  fsl_ftm.o [3]
FTM_SetupQuadDecode           0xb29   0xaa  Code  ??  fsl_ftm.o [3]
FTM_StartTimer               0x218b   0x16  Code  Lc  peripherals.o [1]
FTM_UpdateChnlEdgeLevelSelect
                              0x8d3   0x2a  Code  ??  fsl_ftm.o [3]
FTM_UpdatePwmDutycycle        0x763  0x170  Code  ??  fsl_ftm.o [3]
GPIO_PinInit                 0x2835   0x42  Code  ??  fsl_gpio.o [3]
GPIO_PinRead                 0x17bd    0xa  Code  Lc  ServoControl.o [6]
GPIO_PinWrite                0x281d   0x18  Code  Lc  fsl_gpio.o [3]
GPIO_PortClear               0x17b9    0x4  Code  Lc  ServoControl.o [6]
GPIO_PortSet                 0x17b5    0x4  Code  Lc  ServoControl.o [6]
HardFault_Handler            0x3639         Code  Wk  startup_MKV30F12810.o [7]
I2C0_DriverIRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
I2C0_IRQHandler              0x35dd         Code  Wk  startup_MKV30F12810.o [7]
Kd                      0x1fff'e064    0x4  Data  ??  ServoControl.o [6]
Ki                      0x1fff'e060    0x4  Data  ??  ServoControl.o [6]
Kp                      0x1fff'e05c    0x4  Data  ??  ServoControl.o [6]
LLWU_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
LPTMR0_IRQHandler            0x3615         Code  Wk  startup_MKV30F12810.o [7]
LVD_LVW_IRQHandler           0x3615         Code  Wk  startup_MKV30F12810.o [7]
MCG_IRQHandler               0x3615         Code  Wk  startup_MKV30F12810.o [7]
MCM_IRQHandler               0x3615         Code  Wk  startup_MKV30F12810.o [7]
MemManage_Handler            0x363b         Code  Wk  startup_MKV30F12810.o [7]
NMI_Handler                  0x3637         Code  Wk  startup_MKV30F12810.o [7]
OSC_SetCapLoad               0x1169    0xc  Code  Lc  fsl_clock.o [3]
OSC_SetExtRefClkConfig       0x1155   0x14  Code  Lc  fsl_clock.o [3]
OutDataBuffer           0x1fff'e038    0xc  Data  ??  ServoControl.o [6]
PDB0_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
PID_init                     0x17e9   0x12  Code  ??  ServoControl.o [6]
PID_update                   0x17fd   0x68  Code  ??  ServoControl.o [6]
PIT0_IRQHandler              0x19e5   0x1e  Code  ??  ServoControl.o [6]
PIT1_IRQHandler              0x1a05   0x54  Code  ??  ServoControl.o [6]
PIT2_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
PIT3_IRQHandler              0x3615         Code  Wk  startup_MKV30F12810.o [7]
PIT_ClearStatusFlags         0x17db    0xe  Code  Lc  ServoControl.o [6]
PIT_EnableInterrupts         0x21e7   0x20  Code  Lc  peripherals.o [1]
PIT_GetInstance              0x2c07   0x2e  Code  Lc  fsl_pit.o [3]
PIT_GetStatusFlags           0x17cb   0x10  Code  Lc  ServoControl.o [6]
PIT_Init                     0x2c35   0x74  Code  ??  fsl_pit.o [3]
PIT_SetTimerPeriod           0x2207   0x2a  Code  Lc  peripherals.o [1]
PIT_StartTimer               0x2231   0x1e  Code  Lc  peripherals.o [1]
PIT_config                   0x364a    0x1  Data  ??  peripherals.o [1]
PIT_init                     0x2347   0x54  Code  Lc  peripherals.o [1]
PORTA_IRQHandler             0x3615         Code  Wk  startup_MKV30F12810.o [7]
PORTB_IRQHandler             0x3615         Code  Wk  startup_MKV30F12810.o [7]
PORTC_IRQHandler             0x3615         Code  Wk  startup_MKV30F12810.o [7]
PORTD_IRQHandler             0x3615         Code  Wk  startup_MKV30F12810.o [7]
PORTE_IRQHandler             0x3615         Code  Wk  startup_MKV30F12810.o [7]
PORT_SetPinMux               0x1c59    0xe  Code  Lc  pin_mux.o [1]
PcCmdProcess                 0x1a59   0xfa  Code  ??  ServoControl.o [6]
PendSV_Handler               0x3645         Code  Wk  startup_MKV30F12810.o [7]
QES_value               0x1fff'e07a    0x2  Data  ??  ServoControl.o [6]
QES_value_t             0x1fff'e07c    0x2  Data  ??  ServoControl.o [6]
Read_Collision_IRQHandler
                             0x3615         Code  Wk  startup_MKV30F12810.o [7]
Region$$Table$$Base          0x3294          --   ??  - Linker created -
Region$$Table$$Limit         0x32b8          --   ??  - Linker created -
Reserved20_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved21_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved22_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved23_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved24_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved25_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved26_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved27_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved28_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved29_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved30_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved31_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved39_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved41_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved43_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved44_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved45_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved46_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved51_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved52_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved53_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved54_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved61_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved62_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved63_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved69_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved70_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved71_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved81_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved82_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved83_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved84_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved85_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved86_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved87_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reserved88_IRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
Reset_Handler                0x3249         Code  Wk  startup_MKV30F12810.o [7]
SMC_GetPowerModeState        0x20ab    0x4  Code  Lc  clock_config.o [1]
SMC_SetPowerModeHsrun        0x2881   0x12  Code  ??  fsl_smc.o [3]
SMC_SetPowerModeProtection
                             0x20a7    0x4  Code  Lc  clock_config.o [1]
SPI0_DriverIRQHandler        0x3615         Code  Wk  startup_MKV30F12810.o [7]
SPI0_IRQHandler              0x35e5         Code  Wk  startup_MKV30F12810.o [7]
SVC_Handler                  0x3641         Code  Wk  startup_MKV30F12810.o [7]
SWI_IRQHandler               0x3615         Code  Wk  startup_MKV30F12810.o [7]
SysTick_Handler              0x3647         Code  Wk  startup_MKV30F12810.o [7]
SystemCoreClock         0x1fff'e014    0x4  Data  ??  system_MKV30F12810.o [2]
SystemInit                   0x3049   0x28  Code  ??  system_MKV30F12810.o [2]
SystemInitHook               0x307d    0x2  Code  Wk  system_MKV30F12810.o [2]
UART0_DriverIRQHandler       0x27ed   0x14  Code  ??  fsl_uart.o [3]
UART0_ERR_DriverIRQHandler
                             0x3615         Code  Wk  startup_MKV30F12810.o [7]
UART0_ERR_IRQHandler         0x35f5         Code  Wk  startup_MKV30F12810.o [7]
UART0_RX_TX_DriverIRQHandler
                             0x2801    0xc  Code  ??  fsl_uart.o [3]
UART0_RX_TX_IRQHandler       0x35ed         Code  Wk  startup_MKV30F12810.o [7]
UART1_ERR_DriverIRQHandler
                             0x3615         Code  Wk  startup_MKV30F12810.o [7]
UART1_ERR_IRQHandler         0x35fd         Code  Wk  startup_MKV30F12810.o [7]
UART1_RX_TX_IRQHandler       0x1b53   0x84  Code  ??  ServoControl.o [6]
UART1_config                 0x3564    0xc  Data  ??  peripherals.o [1]
UART1_init                   0x22f7   0x26  Code  Lc  peripherals.o [1]
UART_ClearStatusFlags        0x2757   0x44  Code  ??  fsl_uart.o [3]
UART_EnableInterrupts        0x2713   0x2c  Code  ??  fsl_uart.o [3]
UART_GetInstance             0x2521   0x36  Code  ??  fsl_uart.o [3]
UART_GetStatusFlags          0x273f   0x18  Code  ??  fsl_uart.o [3]
UART_Init                    0x2557  0x1bc  Code  ??  fsl_uart.o [3]
UART_ReadByte                0x17c7    0x4  Code  Lc  ServoControl.o [6]
UART_WriteBlocking           0x279b   0x22  Code  ??  fsl_uart.o [3]
UsageFault_Handler           0x363f         Code  Wk  startup_MKV30F12810.o [7]
VREF_GetInstance             0x29b7   0x2e  Code  Lc  fsl_vref.o [3]
VREF_Init                    0x29e5   0x56  Code  ??  fsl_vref.o [3]
VREF_config                  0x3648    0x1  Data  ??  peripherals.o [1]
VREF_init                    0x22eb    0xc  Code  Lc  peripherals.o [1]
Vin                     0x1fff'e074    0x2  Data  ??  ServoControl.o [6]
WDOG_EWM_IRQHandler          0x3615         Code  Wk  startup_MKV30F12810.o [7]
__NVIC_EnableIRQ             0x2141   0x1e  Code  Lc  peripherals.o [1]
__Vectors                       0x0          --   ??  startup_MKV30F12810.o [7]
__Vectors_End                 0x400         Data  ??  startup_MKV30F12810.o [7]
__Vectors_Size {Abs}          0x400          --   ??  startup_MKV30F12810.o [7]
__aeabi_assert                0xc19   0x1c  Code  ??  fsl_assert.o [9]
__aeabi_memcpy               0x1031         Code  ??  ABImemcpy.o [12]
__aeabi_memcpy4              0x1051         Code  ??  ABImemcpy.o [12]
__aeabi_memcpy8              0x1051         Code  ??  ABImemcpy.o [12]
__cmain                      0x331d         Code  ??  cmain.o [12]
__exit                       0x3351   0x14  Code  ??  exit.o [13]
__iar_copy_init3             0x10d7   0x2a  Code  ??  copy_init3.o [12]
__iar_data_init3             0x3105   0x18  Code  ??  data_init.o [12]
__iar_init_vfp               0x3271         Code  ??  fpinit_M.o [11]
__iar_program_start          0x33b9         Code  ??  cstartup_M.o [12]
__iar_zero_init3             0x3011   0x38  Code  ??  zero_init3.o [12]
__low_level_init             0x333b    0x4  Code  ??  low_level_init.o [10]
__vector_table                  0x0         Data  ??  startup_MKV30F12810.o [7]
__vector_table_0x1c            0x1c         Data  ??  startup_MKV30F12810.o [7]
_call_main                   0x3329         Code  ??  cmain.o [12]
_exit                        0x3345         Code  ??  cexit.o [12]
act_value               0x1fff'e010    0x4  Data  ??  ServoControl.o [6]
error                   0x1fff'e076    0x2  Data  ??  ServoControl.o [6]
exit                         0x333f    0x4  Code  ??  exit.o [10]
g_xtal0Freq             0x1fff'e04c    0x4  Data  ??  fsl_clock.o [3]
g_xtal32Freq            0x1fff'e050    0x4  Data  ??  fsl_clock.o [3]
integral                0x1fff'e068    0x4  Data  ??  ServoControl.o [6]
is_PID_step_response    0x1fff'e078    0x2  Data  ??  ServoControl.o [6]
main                         0x1865  0x180  Code  ??  ServoControl.o [6]
mcgConfig_BOARD_BootClockRUN
                             0x34bc    0xc  Data  ??  clock_config.o [1]
motor1_set                   0x2435   0x68  Code  ??  Lab_pwm.o [4]
motor2_set                   0x249d   0x68  Code  ??  Lab_pwm.o [4]
msCounter               0x1fff'e058    0x4  Data  ??  ServoControl.o [6]
oscConfig_BOARD_BootClockRUN
                             0x3584    0x8  Data  ??  clock_config.o [1]
output_PID              0x1fff'e070    0x4  Data  ??  ServoControl.o [6]
prev_error              0x1fff'e06c    0x4  Data  ??  ServoControl.o [6]
s_adc16Bases                 0x358c    0x8  Data  Lc  fsl_adc16.o [3]
s_adc16Clocks                0x3594    0x8  Data  Lc  fsl_adc16.o [3]
s_dacBases                   0x359c    0x8  Data  Lc  fsl_dac.o [3]
s_dacClocks                  0x35a4    0x8  Data  Lc  fsl_dac.o [3]
s_debugConsole          0x1fff'e018   0x14  Data  Lc  fsl_debug_console.o [9]
s_fastIrcFreq           0x1fff'e00c    0x4  Data  Lc  fsl_clock.o [3]
s_ftmBases                   0x349c   0x10  Data  Lc  fsl_ftm.o [3]
s_ftmClocks                  0x34ac   0x10  Data  Lc  fsl_ftm.o [3]
s_pitBases                   0x3604    0x4  Data  Lc  fsl_pit.o [3]
s_pitClocks                  0x3608    0x4  Data  Lc  fsl_pit.o [3]
s_slowIrcFreq           0x1fff'e008    0x4  Data  Lc  fsl_clock.o [3]
s_uartBases                  0x351c    0xc  Data  Lc  fsl_uart.o [3]
s_uartClock                  0x3528    0xc  Data  Lc  fsl_uart.o [3]
s_uartHandle            0x1fff'e02c    0xc  Data  ??  fsl_uart.o [3]
s_uartIsr               0x1fff'e054    0x4  Data  ??  fsl_uart.o [3]
s_vrefBases                  0x360c    0x4  Data  Lc  fsl_vref.o [3]
s_vrefClocks                 0x3610    0x4  Data  Lc  fsl_vref.o [3]
simConfig_BOARD_BootClockRUN
                             0x357c    0x8  Data  ??  clock_config.o [1]
strlen                       0x1101         Code  ??  strlen.o [12]


[1] = D:\IAR.work\MKV30_t2\Debug\obj\board_15934270115374689736.dir
[2] = D:\IAR.work\MKV30_t2\Debug\obj\device_14273152114726608622.dir
[3] = D:\IAR.work\MKV30_t2\Debug\obj\drivers_4306295370633009451.dir
[4] = D:\IAR.work\MKV30_t2\Debug\obj\lab_5703968128717247585.dir
[5] = D:\IAR.work\MKV30_t2\Debug\obj\lists_3027992814978638235.dir
[6] = D:\IAR.work\MKV30_t2\Debug\obj\source_5634686896477721948.dir
[7] = D:\IAR.work\MKV30_t2\Debug\obj\startup_5253055491327143397.dir
[8] = D:\IAR.work\MKV30_t2\Debug\obj\uart_2732247187628865378.dir
[9] = D:\IAR.work\MKV30_t2\Debug\obj\utilities_9832829787270487890.dir
[10] = dl7M_tln.a
[11] = m7M_tls.a
[12] = rt7M_tl.a
[13] = shb_l.a

  11'876 bytes of readonly  code memory
   2'048 bytes of readonly  data memory
   1'157 bytes of readwrite data memory

Errors: none
Warnings: none

﻿D:\IAR.work\MKV30_test\lab\Lab_adc.c -D DEBUG -D CPU_MKV31F512VLL12 -D PRINTF_FLOAT_ENABLE=0 -D SCANF_FLOAT_ENABLE=0 -D PRINTF_ADVANCED_ENABLE=0 -D SCANF_ADVANCED_ENABLE=0 -D FRDM_KV31F -D FREEDOM -D MCUXPRESSO_SDK --diag_suppress Pa082,Pa050 -o D:\IAR.work\MKV30_test\debug\obj\lab_5703968128717247585.dir --no_cse --no_unroll --no_inline --no_code_motion --no_tbaa --no_clustering --no_scheduling --debug --endian=little --cpu=Cortex-M4 -e --fpu=VFPv4_sp --dlib_config "C:\Program Files\IAR Systems\Embedded Workbench 9.2\arm\inc\c\DLib_Config_Normal.h" -I D:\IAR.work\MKV30_test/board\ -I D:\IAR.work\MKV30_test/CMSIS\ -I D:\IAR.work\MKV30_test/component/lists\ -I D:\IAR.work\MKV30_test/component/uart\ -I D:\IAR.work\MKV30_test/device\ -I D:\IAR.work\MKV30_test/drivers\ -I D:\IAR.work\MKV30_test/utilities\ -I D:\IAR.work\MKV30_test\lab\ -On
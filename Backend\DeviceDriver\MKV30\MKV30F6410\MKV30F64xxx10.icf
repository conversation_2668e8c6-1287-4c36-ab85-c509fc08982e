/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0x00000000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__ = 0x00000000;
define symbol __ICFEDIT_region_ROM_end__   = 0x0000ffff;
define symbol __ICFEDIT_region_RAM_start__ = 0x1fffe000;
define symbol __ICFEDIT_region_RAM_end__   = 0x1fffffff;
/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__ = 0x400;
define symbol __ICFEDIT_size_heap__   = 0x800;
/**** End of ICF editor section. ###ICF###*/

define symbol __region_RAM2_start__ 		= 0x20000000;
define symbol __region_RAM2_end__ 			= 0x20001fff;

define symbol __FlashConfig_start__			= 0x00000400;
define symbol __FlashConfig_end__   		= 0x0000040f;

define memory mem with size = 4G;
define region ROM_region = mem:[from __ICFEDIT_region_ROM_start__ to (__FlashConfig_start__ - 1)] | mem:[from (__FlashConfig_end__+1)  to __ICFEDIT_region_ROM_end__];
define region RAM_region = mem:[from __ICFEDIT_region_RAM_start__ to __ICFEDIT_region_RAM_end__] | mem:[from __region_RAM2_start__ to __region_RAM2_end__];

define block CSTACK    with alignment = 8, size = __ICFEDIT_size_cstack__   { };
define block HEAP      with alignment = 8, size = __ICFEDIT_size_heap__     { };

define region FlashConfig_region = mem:[from __FlashConfig_start__ to __FlashConfig_end__];

initialize by copy { readwrite };


place at address mem:__ICFEDIT_intvec_start__ { readonly section .intvec };

place in FlashConfig_region {section FlashConfig};

place in ROM_region   { readonly };

place in RAM_region   { readwrite, block CSTACK, block HEAP };

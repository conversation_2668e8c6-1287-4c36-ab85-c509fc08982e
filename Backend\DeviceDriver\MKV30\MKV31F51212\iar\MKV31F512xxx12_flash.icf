/*
** ###################################################################
**     Processors:          MKV31F512VLH12
**                          MKV31F512VLL12
**
**     Compiler:            IAR ANSI C/C++ Compiler for ARM
**     Reference manual:    KV31P100M120SF7RM, Rev. 1, March 24, 2014
**     Version:             rev. 1.7, 2015-02-19
**     Build:               b210812
**
**     Abstract:
**         Linker file for the IAR ANSI C/C++ Compiler for ARM
**
**     Copyright 2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2021 NXP
**     All rights reserved.
**
**     SPDX-License-Identifier: BSD-3-Clause
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
** ###################################################################
*/

define symbol m_interrupts_start       = 0x00000000;
define symbol m_interrupts_end         = 0x000003FF;

define symbol m_flash_config_start     = 0x00000400;
define symbol m_flash_config_end       = 0x0000040F;

define symbol m_text_start             = 0x00000410;
define symbol m_text_end               = 0x0007FFFF;

define symbol m_data_start             = 0x1FFF8000;
define symbol m_data_end               = 0x1FFFFFFF;

define symbol m_data_2_start           = 0x20000000;
define symbol m_data_2_end             = 0x2000FFFF;

/* Sizes */
if (isdefinedsymbol(__stack_size__)) {
  define symbol __size_cstack__        = __stack_size__;
} else {
  define symbol __size_cstack__        = 0x0400;
}

if (isdefinedsymbol(__heap_size__)) {
  define symbol __size_heap__          = __heap_size__;
} else {
  define symbol __size_heap__          = 0x0400;
}


define memory mem with size = 4G;
define region m_flash_config_region = mem:[from m_flash_config_start to m_flash_config_end];
define region TEXT_region = mem:[from m_interrupts_start to m_interrupts_end]
                          | mem:[from m_text_start to m_text_end];
define region DATA_region = mem:[from m_data_start to m_data_end]
                          | mem:[from m_data_2_start to m_data_2_end-__size_cstack__];
define region CSTACK_region = mem:[from m_data_2_end-__size_cstack__+1 to m_data_2_end];

define block CSTACK    with alignment = 8, size = __size_cstack__   { };
define block HEAP      with alignment = 8, size = __size_heap__     { };
define block RW        { readwrite };
define block ZI        { zi };
define block NCACHE_VAR    { section NonCacheable , section NonCacheable.init };
define block QACCESS_CODE  { section CodeQuickAccess };
define block QACCESS_DATA  { section DataQuickAccess };

initialize by copy { readwrite, section .textrw, section CodeQuickAccess, section DataQuickAccess };
do not initialize  { section .noinit };

place at address mem: m_interrupts_start    { readonly section .intvec };
place in m_flash_config_region              { section FlashConfig };
place in TEXT_region                        { readonly };
place in DATA_region                        { block RW };
place in DATA_region                        { block ZI };
place in DATA_region                        { last block HEAP };
place in CSTACK_region                      { block CSTACK };
place in DATA_region                        { block NCACHE_VAR };
place in DATA_region                        { block QACCESS_CODE };
place in DATA_region                        { block QACCESS_DATA };


<?xml version="1.0" encoding= "UTF-8" ?>
<configuration name="MKV30_SC2024" xsi:schemaLocation="http://mcuxpresso.nxp.com/XSD/mex_configuration_17 http://mcuxpresso.nxp.com/XSD/mex_configuration_17.xsd" uuid="7b4b5cfe-6e33-488d-b66d-23f7571ff846" version="17" xmlns="http://mcuxpresso.nxp.com/XSD/mex_configuration_17" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
   <common>
      <processor>MKV30F64xxx10</processor>
      <package>MKV30F64VLF10</package>
      <mcu_data>ksdk2_0</mcu_data>
      <cores selected="core0">
         <core name="Cortex-M4F" id="core0" description="M4 core"/>
      </cores>
      <description></description>
   </common>
   <preferences>
      <validate_boot_init_only>true</validate_boot_init_only>
      <generate_code_modified_registers_only>false</generate_code_modified_registers_only>
      <update_include_paths>true</update_include_paths>
      <generate_registers_defines>false</generate_registers_defines>
   </preferences>
   <tools>
      <pins name="Pins" version="17.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/pin_mux.c" update_enabled="true"/>
            <file path="board/pin_mux.h" update_enabled="true"/>
         </generated_project_files>
         <pins_profile>
            <processor_version>24.12.10</processor_version>
            <pin_labels>
               <pin_label pin_num="21" pin_signal="PTA4/LLWU_P3/FTM0_CH1/FTM0_FLT3/NMI_b" label="S2" identifier="S2"/>
               <pin_label pin_num="29" pin_signal="ADC0_SE12/PTB2/I2C0_SCL/UART0_RTS_b/FTM0_FLT1/FTM0_FLT3" label="S8" identifier="S8"/>
               <pin_label pin_num="30" pin_signal="ADC0_SE13/PTB3/I2C0_SDA/UART0_CTS_b/FTM0_FLT0" label="S3" identifier="S3"/>
               <pin_label pin_num="31" pin_signal="PTB16/UART0_RX/FTM_CLKIN0/EWM_IN" label="S7" identifier="S7"/>
               <pin_label pin_num="32" pin_signal="PTB17/UART0_TX/FTM_CLKIN1/EWM_OUT_b" label="BUZZ" identifier="BUZZ"/>
               <pin_label pin_num="33" pin_signal="ADC0_SE14/PTC0/SPI0_PCS4/PDB0_EXTRG/CMP0_OUT/FTM0_FLT1/SPI0_PCS0" label="LED" identifier="LED"/>
               <pin_label pin_num="34" pin_signal="ADC0_SE15/PTC1/LLWU_P6/SPI0_PCS3/UART1_RTS_b/FTM0_CH0/FTM2_CH0" label="IS2" identifier="IS2"/>
               <pin_label pin_num="35" pin_signal="ADC0_SE4b/CMP1_IN0/PTC2/SPI0_PCS2/UART1_CTS_b/FTM0_CH1/FTM2_CH1" label="S9" identifier="S9"/>
               <pin_label pin_num="36" pin_signal="CMP1_IN1/PTC3/LLWU_P7/SPI0_PCS1/UART1_RX/FTM0_CH2/CLKOUT" label="UART1_RX" identifier="UART1_RX"/>
               <pin_label pin_num="37" pin_signal="PTC4/LLWU_P8/SPI0_PCS0/UART1_TX/FTM0_CH3/CMP1_OUT" label="UART1_TX" identifier="UART1_TX"/>
               <pin_label pin_num="38" pin_signal="PTC5/LLWU_P9/SPI0_SCK/LPTMR0_ALT2/CMP0_OUT/FTM0_CH2" label="EN2" identifier="EN2"/>
               <pin_label pin_num="39" pin_signal="CMP0_IN0/PTC6/LLWU_P10/SPI0_SOUT/PDB0_EXTRG/UART0_RX/I2C0_SCL" label="IS1" identifier="IS1"/>
               <pin_label pin_num="40" pin_signal="CMP0_IN1/PTC7/SPI0_SIN/UART0_TX/I2C0_SDA" label="EN1" identifier="EN1"/>
               <pin_label pin_num="41" pin_signal="PTD0/LLWU_P12/SPI0_PCS0/UART0_RTS_b/FTM0_CH0/UART1_RX" label="PWM0" identifier="PWM1;PWM0"/>
               <pin_label pin_num="42" pin_signal="ADC0_SE5b/PTD1/SPI0_SCK/UART0_CTS_b/FTM0_CH1/UART1_TX" label="PWM1" identifier="PWM2;PWM1"/>
               <pin_label pin_num="43" pin_signal="PTD2/LLWU_P13/SPI0_SOUT/UART0_RX/FTM0_CH2/I2C0_SCL" label="PWM2" identifier="PWM3;PWM2"/>
               <pin_label pin_num="44" pin_signal="PTD3/SPI0_SIN/UART0_TX/FTM0_CH3/I2C0_SDA" label="PWM3" identifier="PWM4;PWM3"/>
               <pin_label pin_num="45" pin_signal="PTD4/LLWU_P14/SPI0_PCS1/UART0_RTS_b/FTM0_CH4/FTM2_CH0/EWM_IN" label="PHB" identifier="PHB"/>
               <pin_label pin_num="27" pin_signal="ADC0_SE8/ADC1_SE8/PTB0/LLWU_P5/I2C0_SCL/FTM1_CH0/FTM1_QD_PHA/UART0_RX" label="PHA1" identifier="PHA1"/>
               <pin_label pin_num="28" pin_signal="ADC0_SE9/ADC1_SE9/PTB1/I2C0_SDA/FTM1_CH1/FTM0_FLT2/EWM_IN/FTM1_QD_PHB/UART0_TX" label="PHB1" identifier="PHB1"/>
               <pin_label pin_num="46" pin_signal="ADC0_SE6b/PTD5/SPI0_PCS2/UART0_CTS_b/FTM0_CH5/FTM2_CH1/EWM_OUT_b" label="PHA" identifier="PHA"/>
               <pin_label pin_num="47" pin_signal="ADC0_SE7b/PTD6/LLWU_P15/SPI0_PCS3/UART0_RX/FTM0_CH0/FTM1_CH0/FTM0_FLT0" label="PUSH" identifier="PUSH"/>
               <pin_label pin_num="48" pin_signal="PTD7/UART0_TX/FTM0_CH1/FTM1_CH1/FTM0_FLT1" label="S5" identifier="S5"/>
               <pin_label pin_num="3" pin_signal="ADC0_SE4a/ADC0_DP1/ADC1_DP2/PTE16/SPI0_PCS0/UART1_TX/FTM_CLKIN0/FTM0_FLT3" label="PE16" identifier="PE16"/>
               <pin_label pin_num="4" pin_signal="ADC0_SE5a/ADC0_DM1/ADC1_DM2/PTE17/SPI0_SCK/UART1_RX/FTM_CLKIN1/LPTMR0_ALT3" label="PE17" identifier="PE17"/>
               <pin_label pin_num="5" pin_signal="ADC0_SE6a/ADC1_DP1/ADC0_DP2/PTE18/SPI0_SOUT/UART1_CTS_b/I2C0_SDA" label="PE18" identifier="PE18"/>
               <pin_label pin_num="6" pin_signal="ADC0_SE7a/ADC1_DM1/ADC0_DM2/PTE19/SPI0_SIN/UART1_RTS_b/I2C0_SCL" label="PE19" identifier="PE19"/>
               <pin_label pin_num="15" pin_signal="ADC0_SE17/PTE24/FTM0_CH0/I2C0_SCL/EWM_OUT_b" label="PE24" identifier="PE24"/>
               <pin_label pin_num="16" pin_signal="ADC0_SE18/PTE25/FTM0_CH1/I2C0_SDA/EWM_IN" label="S4" identifier="S4"/>
               <pin_label pin_num="7" pin_signal="ADC0_DP0/ADC1_DP3" label="ADCDP" identifier="ADCDP"/>
               <pin_label pin_num="8" pin_signal="ADC0_DM0/ADC1_DM3" label="ADCDM" identifier="ADCDM"/>
               <pin_label pin_num="13" pin_signal="VREF_OUT/CMP1_IN5/CMP0_IN5/ADC1_SE18" label="VREF" identifier="VREF"/>
               <pin_label pin_num="14" pin_signal="DAC0_OUT/CMP1_IN3/ADC0_SE23" label="VOUT" identifier="VOUT"/>
            </pin_labels>
            <external_user_signals>
               <properties/>
            </external_user_signals>
         </pins_profile>
         <functions_list>
            <function name="BOARD_InitPins">
               <description>Configures pin routing and optionally pin electrical features.</description>
               <options>
                  <callFromInitBoot>true</callFromInitBoot>
                  <coreID>core0</coreID>
                  <enableClock>true</enableClock>
               </options>
               <dependencies>
                  <dependency resourceType="Peripheral" resourceId="JTAG" description="Peripheral JTAG signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="n/a" description="Peripheral n/a signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="SUPPLY" description="Peripheral SUPPLY signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="OSC" description="Peripheral OSC signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="FTM1" description="Peripheral FTM1 signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="UART1" description="Peripheral UART1 signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="FTM0" description="Peripheral FTM0 signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="ADC0" description="Peripheral ADC0 signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="VREF" description="Peripheral VREF signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="Peripheral" resourceId="DAC0" description="Peripheral DAC0 signals are routed in the Pins Tool, but the peripheral is not initialized in the Peripherals Tool." problem_level="1" source="Pins:BOARD_InitPins">
                     <feature name="initialized" evaluation="equal">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.common" description="Pins initialization requires the COMMON Driver in the project." problem_level="2" source="Pins:BOARD_InitPins">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.port" description="Pins initialization requires the PORT Driver in the project." problem_level="2" source="Pins:BOARD_InitPins">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.gpio" description="Pins initialization requires the GPIO Driver in the project." problem_level="2" source="Pins:BOARD_InitPins">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
               </dependencies>
               <pins>
                  <pin peripheral="JTAG" signal="JTAG_TCLK_SWD_CLK" pin_num="17" pin_signal="PTA0/UART0_CTS_b/FTM0_CH5/EWM_IN/JTAG_TCLK/SWD_CLK"/>
                  <pin peripheral="n/a" signal="disabled" pin_num="18" pin_signal="PTA1/UART0_RX/FTM2_CH0/CMP0_OUT/FTM2_QD_PHA/FTM1_CH1/JTAG_TDI"/>
                  <pin peripheral="n/a" signal="disabled" pin_num="19" pin_signal="PTA2/UART0_TX/FTM2_CH1/CMP1_OUT/FTM2_QD_PHB/FTM1_CH0/JTAG_TDO/TRACE_SWO"/>
                  <pin peripheral="JTAG" signal="JTAG_TMS_SWD_DIO" pin_num="20" pin_signal="PTA3/UART0_RTS_b/FTM0_CH0/FTM2_FLT0/EWM_OUT_b/JTAG_TMS/SWD_DIO"/>
                  <pin peripheral="GPIOA" signal="GPIO, 4" pin_num="21" pin_signal="PTA4/LLWU_P3/FTM0_CH1/FTM0_FLT3/NMI_b">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="SUPPLY" signal="VDD, 1" pin_num="22" pin_signal="VDD22"/>
                  <pin peripheral="SUPPLY" signal="VSS, 1" pin_num="23" pin_signal="VSS23"/>
                  <pin peripheral="SUPPLY" signal="VDD, 0" pin_num="1" pin_signal="VDD1"/>
                  <pin peripheral="SUPPLY" signal="VSS, 0" pin_num="2" pin_signal="VSS2"/>
                  <pin peripheral="OSC" signal="EXTAL0" pin_num="24" pin_signal="EXTAL0/PTA18/FTM0_FLT2/FTM_CLKIN0"/>
                  <pin peripheral="OSC" signal="XTAL0" pin_num="25" pin_signal="XTAL0/PTA19/FTM0_FLT0/FTM1_FLT0/FTM_CLKIN1/LPTMR0_ALT1"/>
                  <pin peripheral="FTM1" signal="QD_PH, A" pin_num="27" pin_signal="ADC0_SE8/ADC1_SE8/PTB0/LLWU_P5/I2C0_SCL/FTM1_CH0/FTM1_QD_PHA/UART0_RX"/>
                  <pin peripheral="FTM1" signal="QD_PH, B" pin_num="28" pin_signal="ADC0_SE9/ADC1_SE9/PTB1/I2C0_SDA/FTM1_CH1/FTM0_FLT2/EWM_IN/FTM1_QD_PHB/UART0_TX"/>
                  <pin peripheral="GPIOB" signal="GPIO, 2" pin_num="29" pin_signal="ADC0_SE12/PTB2/I2C0_SCL/UART0_RTS_b/FTM0_FLT1/FTM0_FLT3">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOB" signal="GPIO, 3" pin_num="30" pin_signal="ADC0_SE13/PTB3/I2C0_SDA/UART0_CTS_b/FTM0_FLT0">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOB" signal="GPIO, 16" pin_num="31" pin_signal="PTB16/UART0_RX/FTM_CLKIN0/EWM_IN">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOB" signal="GPIO, 17" pin_num="32" pin_signal="PTB17/UART0_TX/FTM_CLKIN1/EWM_OUT_b">
                     <pin_features>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOC" signal="GPIO, 0" pin_num="33" pin_signal="ADC0_SE14/PTC0/SPI0_PCS4/PDB0_EXTRG/CMP0_OUT/FTM0_FLT1/SPI0_PCS0">
                     <pin_features>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOC" signal="GPIO, 1" pin_num="34" pin_signal="ADC0_SE15/PTC1/LLWU_P6/SPI0_PCS3/UART1_RTS_b/FTM0_CH0/FTM2_CH0">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOC" signal="GPIO, 2" pin_num="35" pin_signal="ADC0_SE4b/CMP1_IN0/PTC2/SPI0_PCS2/UART1_CTS_b/FTM0_CH1/FTM2_CH1">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="UART1" signal="RX" pin_num="36" pin_signal="CMP1_IN1/PTC3/LLWU_P7/SPI0_PCS1/UART1_RX/FTM0_CH2/CLKOUT"/>
                  <pin peripheral="UART1" signal="TX" pin_num="37" pin_signal="PTC4/LLWU_P8/SPI0_PCS0/UART1_TX/FTM0_CH3/CMP1_OUT">
                     <pin_features>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOC" signal="GPIO, 5" pin_num="38" pin_signal="PTC5/LLWU_P9/SPI0_SCK/LPTMR0_ALT2/CMP0_OUT/FTM0_CH2">
                     <pin_features>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOC" signal="GPIO, 6" pin_num="39" pin_signal="CMP0_IN0/PTC6/LLWU_P10/SPI0_SOUT/PDB0_EXTRG/UART0_RX/I2C0_SCL">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOC" signal="GPIO, 7" pin_num="40" pin_signal="CMP0_IN1/PTC7/SPI0_SIN/UART0_TX/I2C0_SDA">
                     <pin_features>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="FTM0" signal="CH, 0" pin_num="41" pin_signal="PTD0/LLWU_P12/SPI0_PCS0/UART0_RTS_b/FTM0_CH0/UART1_RX">
                     <pin_features>
                        <pin_feature name="identifier" value="PWM0"/>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="FTM0" signal="CH, 1" pin_num="42" pin_signal="ADC0_SE5b/PTD1/SPI0_SCK/UART0_CTS_b/FTM0_CH1/UART1_TX">
                     <pin_features>
                        <pin_feature name="identifier" value="PWM1"/>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="FTM0" signal="CH, 2" pin_num="43" pin_signal="PTD2/LLWU_P13/SPI0_SOUT/UART0_RX/FTM0_CH2/I2C0_SCL">
                     <pin_features>
                        <pin_feature name="identifier" value="PWM2"/>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="FTM0" signal="CH, 3" pin_num="44" pin_signal="PTD3/SPI0_SIN/UART0_TX/FTM0_CH3/I2C0_SDA">
                     <pin_features>
                        <pin_feature name="identifier" value="PWM3"/>
                        <pin_feature name="direction" value="OUTPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOD" signal="GPIO, 4" pin_num="45" pin_signal="PTD4/LLWU_P14/SPI0_PCS1/UART0_RTS_b/FTM0_CH4/FTM2_CH0/EWM_IN">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOD" signal="GPIO, 5" pin_num="46" pin_signal="ADC0_SE6b/PTD5/SPI0_PCS2/UART0_CTS_b/FTM0_CH5/FTM2_CH1/EWM_OUT_b">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOD" signal="GPIO, 6" pin_num="47" pin_signal="ADC0_SE7b/PTD6/LLWU_P15/SPI0_PCS3/UART0_RX/FTM0_CH0/FTM1_CH0/FTM0_FLT0">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOD" signal="GPIO, 7" pin_num="48" pin_signal="PTD7/UART0_TX/FTM0_CH1/FTM1_CH1/FTM0_FLT1">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOE" signal="GPIO, 16" pin_num="3" pin_signal="ADC0_SE4a/ADC0_DP1/ADC1_DP2/PTE16/SPI0_PCS0/UART1_TX/FTM_CLKIN0/FTM0_FLT3">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                        <pin_feature name="slew_rate" value="slow"/>
                        <pin_feature name="pull_select" value="down"/>
                        <pin_feature name="pull_enable" value="enable"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOE" signal="GPIO, 17" pin_num="4" pin_signal="ADC0_SE5a/ADC0_DM1/ADC1_DM2/PTE17/SPI0_SCK/UART1_RX/FTM_CLKIN1/LPTMR0_ALT3">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                        <pin_feature name="slew_rate" value="slow"/>
                        <pin_feature name="pull_select" value="down"/>
                        <pin_feature name="pull_enable" value="enable"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOE" signal="GPIO, 18" pin_num="5" pin_signal="ADC0_SE6a/ADC1_DP1/ADC0_DP2/PTE18/SPI0_SOUT/UART1_CTS_b/I2C0_SDA">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                        <pin_feature name="slew_rate" value="slow"/>
                        <pin_feature name="pull_select" value="down"/>
                        <pin_feature name="pull_enable" value="enable"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOE" signal="GPIO, 19" pin_num="6" pin_signal="ADC0_SE7a/ADC1_DM1/ADC0_DM2/PTE19/SPI0_SIN/UART1_RTS_b/I2C0_SCL">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                        <pin_feature name="slew_rate" value="slow"/>
                        <pin_feature name="pull_select" value="down"/>
                        <pin_feature name="pull_enable" value="enable"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOE" signal="GPIO, 24" pin_num="15" pin_signal="ADC0_SE17/PTE24/FTM0_CH0/I2C0_SCL/EWM_OUT_b">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                        <pin_feature name="slew_rate" value="slow"/>
                        <pin_feature name="pull_select" value="down"/>
                        <pin_feature name="pull_enable" value="enable"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="GPIOE" signal="GPIO, 25" pin_num="16" pin_signal="ADC0_SE18/PTE25/FTM0_CH1/I2C0_SDA/EWM_IN">
                     <pin_features>
                        <pin_feature name="direction" value="INPUT"/>
                     </pin_features>
                  </pin>
                  <pin peripheral="ADC0" signal="DP, 0" pin_num="7" pin_signal="ADC0_DP0/ADC1_DP3"/>
                  <pin peripheral="ADC0" signal="DM, 0" pin_num="8" pin_signal="ADC0_DM0/ADC1_DM3"/>
                  <pin peripheral="VREF" signal="OUT" pin_num="13" pin_signal="VREF_OUT/CMP1_IN5/CMP0_IN5/ADC1_SE18"/>
                  <pin peripheral="DAC0" signal="OUT" pin_num="14" pin_signal="DAC0_OUT/CMP1_IN3/ADC0_SE23"/>
               </pins>
            </function>
         </functions_list>
      </pins>
      <clocks name="Clocks" version="15.0" enabled="true" update_project_code="true">
         <generated_project_files>
            <file path="board/clock_config.c" update_enabled="true"/>
            <file path="board/clock_config.h" update_enabled="true"/>
         </generated_project_files>
         <clocks_profile>
            <processor_version>24.12.10</processor_version>
         </clocks_profile>
         <clock_configurations>
            <clock_configuration name="BOARD_BootClockHSRUN" id_prefix="" prefix_user_defined="false">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="PinSignal" resourceId="OSC.EXTAL0" description="&apos;EXTAL0&apos; (Pins tool id: OSC.EXTAL0, Clocks tool id: OSC.EXTAL0) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockHSRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="OSC.EXTAL0" description="&apos;EXTAL0&apos; (Pins tool id: OSC.EXTAL0, Clocks tool id: OSC.EXTAL0) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockHSRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="OSC.XTAL0" description="&apos;XTAL0&apos; (Pins tool id: OSC.XTAL0, Clocks tool id: OSC.XTAL0) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockHSRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="OSC.XTAL0" description="&apos;XTAL0&apos; (Pins tool id: OSC.XTAL0, Clocks tool id: OSC.XTAL0) needs to have &apos;OUTPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockHSRUN">
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.common" description="Clocks initialization requires the COMMON Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockHSRUN">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.smc" description="Clocks initialization requires the SMC Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockHSRUN">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
               </dependencies>
               <clock_sources>
                  <clock_source id="IRC48M.IRC48M.outFreq" value="48 MHz" locked="false" enabled="false"/>
                  <clock_source id="OSC.OSC.outFreq" value="8 MHz" locked="false" enabled="true"/>
               </clock_sources>
               <clock_outputs>
                  <clock_output id="Bus_clock.outFreq" value="183.0625/4 MHz" locked="false" accuracy=""/>
                  <clock_output id="Core_clock.outFreq" value="183.0625/2 MHz" locked="false" accuracy=""/>
                  <clock_output id="ERCLK32K.outFreq" value="1 kHz" locked="false" accuracy=""/>
                  <clock_output id="Flash_clock.outFreq" value="18.30625 MHz" locked="false" accuracy=""/>
                  <clock_output id="IRC48MCLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="LPO_clock.outFreq" value="1 kHz" locked="false" accuracy=""/>
                  <clock_output id="MCGFFCLK.outFreq" value="31.25 kHz" locked="false" accuracy=""/>
                  <clock_output id="MCGIRCLK.outFreq" value="32.768 kHz" locked="false" accuracy=""/>
                  <clock_output id="OSCERCLK.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="OSCERCLK_UNDIV.outFreq" value="8 MHz" locked="false" accuracy=""/>
                  <clock_output id="PLLFLLCLK.outFreq" value="48 MHz" locked="false" accuracy=""/>
                  <clock_output id="System_clock.outFreq" value="183.0625/2 MHz" locked="false" accuracy=""/>
               </clock_outputs>
               <clock_settings>
                  <setting id="MCGMode" value="FEE" locked="false"/>
                  <setting id="powerMode" value="HSRUN" locked="false"/>
                  <setting id="MCG.FCRDIV.scale" value="1" locked="false"/>
                  <setting id="MCG.FLL_mul.scale" value="2929" locked="true"/>
                  <setting id="MCG.FRDIV.scale" value="256" locked="true"/>
                  <setting id="MCG.IREFS.sel" value="MCG.FRDIV" locked="false"/>
                  <setting id="MCG_C1_IRCLKEN_CFG" value="Enabled" locked="false"/>
                  <setting id="MCG_C2_OSC_MODE_CFG" value="ModeOscLowPower" locked="false"/>
                  <setting id="MCG_C2_RANGE0_CFG" value="High" locked="false"/>
                  <setting id="MCG_C2_RANGE0_FRDIV_CFG" value="High" locked="false"/>
                  <setting id="OSC_CR_ERCLKEN_CFG" value="Enabled" locked="false"/>
                  <setting id="OSC_CR_ERCLKEN_UNDIV_CFG" value="Enabled" locked="false"/>
                  <setting id="SIM.OSC32KSEL.sel" value="PMC.LPOCLK" locked="false"/>
                  <setting id="SIM.OUTDIV2.scale" value="2" locked="false"/>
                  <setting id="SIM.OUTDIV4.scale" value="5" locked="false"/>
                  <setting id="SIM.PLLFLLSEL.sel" value="N/A" locked="false"/>
               </clock_settings>
               <called_from_default_init>false</called_from_default_init>
            </clock_configuration>
            <clock_configuration name="BOARD_BootClockVLPR" id_prefix="" prefix_user_defined="false">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.common" description="Clocks initialization requires the COMMON Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockVLPR">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.smc" description="Clocks initialization requires the SMC Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockVLPR">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
               </dependencies>
               <clock_sources>
                  <clock_source id="OSC.OSC.outFreq" value="8 MHz" locked="false" enabled="false"/>
               </clock_sources>
               <clock_outputs>
                  <clock_output id="Bus_clock.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="Core_clock.outFreq" value="4 MHz" locked="true" accuracy="0.001"/>
                  <clock_output id="ERCLK32K.outFreq" value="1 kHz" locked="false" accuracy=""/>
                  <clock_output id="Flash_clock.outFreq" value="800 kHz" locked="false" accuracy=""/>
                  <clock_output id="LPO_clock.outFreq" value="1 kHz" locked="false" accuracy=""/>
                  <clock_output id="MCGIRCLK.outFreq" value="4 MHz" locked="false" accuracy=""/>
                  <clock_output id="System_clock.outFreq" value="4 MHz" locked="false" accuracy=""/>
               </clock_outputs>
               <clock_settings>
                  <setting id="MCGMode" value="BLPI" locked="false"/>
                  <setting id="powerMode" value="VLPR" locked="false"/>
                  <setting id="MCG.CLKS.sel" value="MCG.IRCS" locked="false"/>
                  <setting id="MCG.FCRDIV.scale" value="1" locked="false"/>
                  <setting id="MCG.FRDIV.scale" value="32" locked="false"/>
                  <setting id="MCG.IRCS.sel" value="MCG.FCRDIV" locked="false"/>
                  <setting id="MCG_C1_IRCLKEN_CFG" value="Enabled" locked="false"/>
                  <setting id="MCG_C2_OSC_MODE_CFG" value="ModeOscLowPower" locked="false"/>
                  <setting id="MCG_C2_RANGE0_CFG" value="Very_high" locked="false"/>
                  <setting id="MCG_C2_RANGE0_FRDIV_CFG" value="Very_high" locked="false"/>
                  <setting id="SIM.OSC32KSEL.sel" value="PMC.LPOCLK" locked="false"/>
                  <setting id="SIM.OUTDIV4.scale" value="5" locked="false"/>
                  <setting id="SIM.PLLFLLSEL.sel" value="IRC48M.IRC48MCLK" locked="false"/>
               </clock_settings>
               <called_from_default_init>false</called_from_default_init>
            </clock_configuration>
            <clock_configuration name="BOARD_BootClockRUN" id_prefix="" prefix_user_defined="false">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="PinSignal" resourceId="OSC.EXTAL0" description="&apos;EXTAL0&apos; (Pins tool id: OSC.EXTAL0, Clocks tool id: OSC.EXTAL0) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="OSC.EXTAL0" description="&apos;EXTAL0&apos; (Pins tool id: OSC.EXTAL0, Clocks tool id: OSC.EXTAL0) needs to have &apos;INPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="OSC.XTAL0" description="&apos;XTAL0&apos; (Pins tool id: OSC.XTAL0, Clocks tool id: OSC.XTAL0) needs to be routed" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="routed" evaluation="">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PinSignal" resourceId="OSC.XTAL0" description="&apos;XTAL0&apos; (Pins tool id: OSC.XTAL0, Clocks tool id: OSC.XTAL0) needs to have &apos;OUTPUT&apos; direction" problem_level="1" source="Clocks:BOARD_BootClockRUN">
                     <feature name="direction" evaluation="">
                        <data>OUTPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.common" description="Clocks initialization requires the COMMON Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="SWComponent" resourceId="platform.drivers.smc" description="Clocks initialization requires the SMC Driver in the project." problem_level="2" source="Clocks:BOARD_BootClockRUN">
                     <feature name="enabled" evaluation="equal" configuration="core0">
                        <data>true</data>
                     </feature>
                  </dependency>
               </dependencies>
               <clock_sources>
                  <clock_source id="OSC.OSC.outFreq" value="8 MHz" locked="false" enabled="true"/>
               </clock_sources>
               <clock_outputs>
                  <clock_output id="Bus_clock.outFreq" value="183.0625/4 MHz" locked="false" accuracy=""/>
                  <clock_output id="Core_clock.outFreq" value="183.0625/2 MHz" locked="false" accuracy=""/>
                  <clock_output id="Flash_clock.outFreq" value="45.765625/2 MHz" locked="false" accuracy=""/>
                  <clock_output id="LPO_clock.outFreq" value="1 kHz" locked="false" accuracy=""/>
                  <clock_output id="MCGFFCLK.outFreq" value="31.25 kHz" locked="false" accuracy=""/>
                  <clock_output id="PLLFLLCLK.outFreq" value="183.0625/2 MHz" locked="false" accuracy=""/>
                  <clock_output id="System_clock.outFreq" value="183.0625/2 MHz" locked="false" accuracy=""/>
               </clock_outputs>
               <clock_settings>
                  <setting id="MCGMode" value="FEE" locked="false"/>
                  <setting id="powerMode" value="HSRUN" locked="false"/>
                  <setting id="MCG.FLL_mul.scale" value="2929" locked="true"/>
                  <setting id="MCG.FRDIV.scale" value="256" locked="true"/>
                  <setting id="MCG.IREFS.sel" value="MCG.FRDIV" locked="false"/>
                  <setting id="MCG_C2_OSC_MODE_CFG" value="ModeOscLowPower" locked="false"/>
                  <setting id="MCG_C2_RANGE0_CFG" value="Very_high" locked="false"/>
                  <setting id="MCG_C2_RANGE0_FRDIV_CFG" value="Very_high" locked="false"/>
                  <setting id="SIM.OUTDIV1.scale" value="1" locked="true"/>
                  <setting id="SIM.OUTDIV2.scale" value="2" locked="false"/>
                  <setting id="SIM.OUTDIV4.scale" value="4" locked="false"/>
               </clock_settings>
               <called_from_default_init>true</called_from_default_init>
            </clock_configuration>
         </clock_configurations>
      </clocks>
      <dcdx name="DCDx" version="3.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <dcdx_profile>
            <processor_version>N/A</processor_version>
         </dcdx_profile>
         <dcdx_configurations/>
      </dcdx>
      <periphs name="Peripherals" version="15.0" enabled="true" update_project_code="true">
         <dependencies>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.ftm" description="工具链/IDE工程中未找到FTM Driver。工程不会被编译！" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.ftm" description="工具链/IDE工程不支持FTM Driver版本。所需值: ${required_value}, 实际值: ${actual_value}. 工程可能没有被正确编译。" problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">2.6.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.dac" description="工具链/IDE工程中未找到DAC Driver。工程不会被编译！" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.dac" description="工具链/IDE工程不支持DAC Driver版本。所需值: ${required_value}, 实际值: ${actual_value}. 工程可能没有被正确编译。" problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">2.0.1</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.vref" description="工具链/IDE工程中未找到VREF Driver。工程不会被编译！" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.vref" description="工具链/IDE工程不支持VREF Driver版本。所需值: ${required_value}, 实际值: ${actual_value}. 工程可能没有被正确编译。" problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">2.1.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.uart" description="工具链/IDE工程中未找到UART Driver。工程不会被编译！" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.uart" description="工具链/IDE工程不支持UART Driver版本。所需值: ${required_value}, 实际值: ${actual_value}. 工程可能没有被正确编译。" problem_level="1" source="Peripherals">
               <feature name="version" evaluation="equivalent">
                  <data type="Version">2.5.1</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.adc16" description="工具链/IDE工程中未找到ADC16 Driver。工程不会被编译！" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.adc16" description="工具链/IDE工程不支持ADC16 Driver版本。所需值: ${required_value}, 实际值: ${actual_value}. 工程可能没有被正确编译。" problem_level="1" source="Peripherals">
               <feature name="version" evaluation="greaterOrEqual">
                  <data type="Version" maxValue="2.3.9">2.2.0</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.pit" description="工具链/IDE工程中未找到PIT Driver。工程不会被编译！" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data type="Boolean">true</data>
               </feature>
            </dependency>
            <dependency resourceType="SWComponent" resourceId="platform.drivers.pit" description="工具链/IDE工程不支持PIT Driver版本。所需值: ${required_value}, 实际值: ${actual_value}. 工程可能没有被正确编译。" problem_level="1" source="Peripherals">
               <feature name="version" evaluation="compatible">
                  <data type="Version" maxValue="2.0.9">2.0.3</data>
               </feature>
            </dependency>
            <dependency resourceType="Tool" resourceId="Pins" description="外围设备工具需要 Pins 工具，但它已被禁用。" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data>true</data>
               </feature>
            </dependency>
            <dependency resourceType="Tool" resourceId="Clocks" description="外围设备工具需要 Clocks 工具，但它已被禁用。" problem_level="2" source="Peripherals">
               <feature name="enabled" evaluation="equal">
                  <data>true</data>
               </feature>
            </dependency>
         </dependencies>
         <generated_project_files>
            <file path="board/peripherals.c" update_enabled="true"/>
            <file path="board/peripherals.h" update_enabled="true"/>
         </generated_project_files>
         <peripherals_profile>
            <processor_version>24.12.10</processor_version>
            <ignored_component_migration_offer/>
         </peripherals_profile>
         <functional_groups>
            <functional_group name="BOARD_InitPeripherals" uuid="506aef54-2b12-4a47-a8be-266b1c70ce26" called_from_default_init="true" id_prefix="" core="core0">
               <description></description>
               <options/>
               <dependencies>
                  <dependency resourceType="ClockOutput" resourceId="Bus_clock" description="Bus clock is inactive." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="frequency" evaluation="greaterThan">
                        <data type="Frequency" unit="Hz">0</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="ClockOutput" resourceId="Bus_clock" description="Bus clock is inactive." problem_level="2" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="frequency" evaluation="greaterThan">
                        <data type="Frequency" unit="Hz">0</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM1.tmr_qd_ph.A" description="Quadrature decoder phase A signal is not supported for the device FTM1" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="exists" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM1.tmr_qd_ph.A" description="Quadrature decoder phase A signal is not routed (FTM1 device)." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM1.tmr_qd_ph.B" description="Quadrature decoder phase B signal is not supported for the device FTM1" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="exists" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM1.tmr_qd_ph.B" description="Quadrature decoder phase B signal is not routed (FTM1 device)." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="ClockOutput" resourceId="Bus_clock" description="Bus clock is inactive." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="frequency" evaluation="greaterThan">
                        <data type="Frequency" unit="Hz">0</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="ClockOutput" resourceId="Bus_clock" description="Bus clock is inactive." problem_level="2" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="frequency" evaluation="greaterThan">
                        <data type="Frequency" unit="Hz">0</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.0" description="Signal CH0 of peripheral FTM0 is not routed" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.0" description="Direction of signal CH0 of peripheral FTM0 is set as input but it will be used as output" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="direction" evaluation="notEqualIgnoreCase">
                        <data type="String">INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.1" description="Signal CH1 of peripheral FTM0 is not routed" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.1" description="Direction of signal CH1 of peripheral FTM0 is set as input but it will be used as output" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="direction" evaluation="notEqualIgnoreCase">
                        <data type="String">INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.2" description="Signal CH2 of peripheral FTM0 is not routed" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.2" description="Direction of signal CH2 of peripheral FTM0 is set as input but it will be used as output" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="direction" evaluation="notEqualIgnoreCase">
                        <data type="String">INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.3" description="Signal CH3 of peripheral FTM0 is not routed" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="FTM0.tmr_ch.3" description="Direction of signal CH3 of peripheral FTM0 is set as input but it will be used as output" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="direction" evaluation="notEqualIgnoreCase">
                        <data type="String">INPUT</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="DAC0.dac_ref_2" description="DACREF_2 signal not routed for DAC0" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="DAC0.dac_out" description="Output signal not routed for DAC0" problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="ClockOutput" resourceId="System_clock" description="System clock is inactive." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="frequency" evaluation="greaterThan">
                        <data type="Frequency" unit="Hz">0</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="UART1.uart_tx" description="Signal TX is not routed." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="UART1.uart_rx" description="Signal RX is not routed." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="equal">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="ADC0.adc_dp.0" description="Signal DP, channel 0 is not routed." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="PeripheralUnifiedSignal" resourceId="ADC0.adc_dm.0" description="Signal DM, channel 0 is not routed." problem_level="1" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="routed" evaluation="">
                        <data type="Boolean">true</data>
                     </feature>
                  </dependency>
                  <dependency resourceType="ClockOutput" resourceId="Bus_clock" description="Bus clock is inactive." problem_level="2" source="Peripherals:BOARD_InitPeripherals">
                     <feature name="frequency" evaluation="greaterThan">
                        <data type="Frequency" unit="Hz">0</data>
                     </feature>
                  </dependency>
               </dependencies>
               <instances>
                  <instance name="NVIC" uuid="96fceebd-b089-4e88-9ea6-5fbe1e2cf100" type="nvic" type_id="nvic" mode="general" peripheral="NVIC" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="nvic">
                        <array name="interrupt_table">
                           <struct name="0"/>
                           <struct name="1"/>
                           <struct name="2"/>
                        </array>
                        <array name="interrupts"/>
                     </config_set>
                  </instance>
                  <instance name="FTM1" uuid="0651612d-cf5d-41c6-a68c-297b7dda9a72" type="ftm" type_id="ftm_2.6.0" mode="QuadratureDecoder" peripheral="FTM1" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="ftm_main_config" quick_selection="QuickSelectionDefault">
                        <struct name="ftm_config">
                           <setting name="clockSource" value="kFTM_SystemClock"/>
                           <setting name="clockSourceFreq" value="GetFreq"/>
                           <setting name="timerPrescaler" value="1"/>
                           <setting name="systemClockSource" value="BusInterfaceClock"/>
                           <setting name="systemClockSourceFreq" value="mirrored_value"/>
                           <setting name="faultMode" value="kFTM_Fault_Disable"/>
                           <setting name="inputFilterPeriod" value="1"/>
                           <array name="faultInputs">
                              <struct name="0">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                              <struct name="1">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                              <struct name="2">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                              <struct name="3">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                           </array>
                           <setting name="deadTimePrescale" value="kFTM_Deadtime_Prescale_1"/>
                           <setting name="deadTimePeriod" value="0"/>
                           <set name="pwmSyncMode">
                              <selected>
                                 <id>kFTM_SoftwareTrigger</id>
                              </selected>
                           </set>
                           <setting name="swTriggerResetCount" value="true"/>
                           <setting name="hwTriggerResetCount" value="false"/>
                           <set name="reloadPoints">
                              <selected/>
                           </set>
                           <set name="extTriggers">
                              <selected/>
                           </set>
                           <set name="chnlInitState">
                              <selected/>
                           </set>
                           <set name="chnlPolarity">
                              <selected/>
                           </set>
                           <setting name="bdmMode" value="kFTM_BdmMode_0"/>
                           <setting name="useGlobalTimeBase" value="false"/>
                        </struct>
                        <set name="timer_interrupts">
                           <selected/>
                        </set>
                        <setting name="enable_irq" value="false"/>
                        <struct name="ftm_interrupt">
                           <setting name="IRQn" value="FTM1_IRQn"/>
                           <setting name="enable_interrrupt" value="enabled"/>
                           <setting name="enable_priority" value="false"/>
                           <setting name="priority" value="0"/>
                           <setting name="enable_custom_name" value="false"/>
                        </struct>
                        <setting name="EnableTimerInInit" value="true"/>
                     </config_set>
                     <config_set name="ftm_quadrature_decoder_mode" quick_selection="default">
                        <setting name="timerModuloVal" value="0xFFFF"/>
                        <setting name="timerInitVal" value="0"/>
                        <setting name="ftm_quad_decoder_mode" value="kFTM_QuadPhaseEncode"/>
                        <struct name="ftm_phase_a_params">
                           <setting name="enablePhaseFilter" value="false"/>
                           <setting name="phaseFilterPeriod" value="1"/>
                           <setting name="phasePolarity" value="kFTM_QuadPhaseNormal"/>
                        </struct>
                        <struct name="ftm_phase_b_params">
                           <setting name="enablePhaseFilter" value="false"/>
                           <setting name="phaseFilterPeriod" value="1"/>
                           <setting name="phasePolarity" value="kFTM_QuadPhaseNormal"/>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="FTM0" uuid="6b138877-db5c-4a51-8ec2-47ce2a3509a3" type="ftm" type_id="ftm_2.6.0" mode="EdgeAligned" peripheral="FTM0" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="ftm_main_config">
                        <struct name="ftm_config">
                           <setting name="clockSource" value="kFTM_SystemClock"/>
                           <setting name="clockSourceFreq" value="GetFreq"/>
                           <setting name="timerPrescaler" value="1"/>
                           <setting name="timerOutputFrequency" value="20 kHz"/>
                           <setting name="systemClockSource" value="BusInterfaceClock"/>
                           <setting name="systemClockSourceFreq" value="mirrored_value"/>
                           <setting name="faultMode" value="kFTM_Fault_Disable"/>
                           <setting name="inputFilterPeriod" value="1"/>
                           <array name="faultInputs">
                              <struct name="0">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                              <struct name="1">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                              <struct name="2">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                              <struct name="3">
                                 <setting name="enableFaultInput" value="false"/>
                                 <setting name="faultLevelVal" value="low"/>
                                 <setting name="useFaultFilter" value="false"/>
                              </struct>
                           </array>
                           <setting name="deadTimePrescale" value="kFTM_Deadtime_Prescale_1"/>
                           <setting name="deadTimePeriod" value="0"/>
                           <set name="pwmSyncMode">
                              <selected>
                                 <id>kFTM_SoftwareTrigger</id>
                              </selected>
                           </set>
                           <setting name="swTriggerResetCount" value="true"/>
                           <setting name="hwTriggerResetCount" value="false"/>
                           <set name="reloadPoints">
                              <selected/>
                           </set>
                           <set name="extTriggers">
                              <selected/>
                           </set>
                           <set name="chnlInitState">
                              <selected/>
                           </set>
                           <set name="chnlPolarity">
                              <selected/>
                           </set>
                           <setting name="bdmMode" value="kFTM_BdmMode_0"/>
                           <setting name="useGlobalTimeBase" value="false"/>
                        </struct>
                        <set name="timer_interrupts">
                           <selected/>
                        </set>
                        <setting name="enable_irq" value="false"/>
                        <struct name="ftm_interrupt">
                           <setting name="IRQn" value="FTM0_IRQn"/>
                           <setting name="enable_interrrupt" value="enabled"/>
                           <setting name="enable_priority" value="false"/>
                           <setting name="priority" value="0"/>
                           <setting name="enable_custom_name" value="false"/>
                        </struct>
                        <setting name="EnableTimerInInit" value="true"/>
                     </config_set>
                     <config_set name="ftm_edge_aligned_mode">
                        <array name="ftm_edge_aligned_channels_config">
                           <struct name="0">
                              <setting name="channelId" value="PWM1"/>
                              <setting name="edge_aligned_mode" value="kFTM_EdgeAlignedPwm"/>
                              <struct name="edge_aligned_pwm">
                                 <setting name="chnlNumber" value="kFTM_Chnl_0"/>
                                 <setting name="level" value="kFTM_HighTrue"/>
                                 <setting name="dutyValueStr" value="0"/>
                                 <setting name="enable_chan_irq" value="false"/>
                              </struct>
                           </struct>
                           <struct name="1">
                              <setting name="channelId" value="PWM2"/>
                              <setting name="edge_aligned_mode" value="kFTM_EdgeAlignedPwm"/>
                              <struct name="edge_aligned_pwm">
                                 <setting name="chnlNumber" value="kFTM_Chnl_1"/>
                                 <setting name="level" value="kFTM_HighTrue"/>
                                 <setting name="dutyValueStr" value="0"/>
                                 <setting name="enable_chan_irq" value="false"/>
                              </struct>
                           </struct>
                           <struct name="2">
                              <setting name="channelId" value="PWM3"/>
                              <setting name="edge_aligned_mode" value="kFTM_EdgeAlignedPwm"/>
                              <struct name="edge_aligned_pwm">
                                 <setting name="chnlNumber" value="kFTM_Chnl_2"/>
                                 <setting name="level" value="kFTM_HighTrue"/>
                                 <setting name="dutyValueStr" value="0"/>
                                 <setting name="enable_chan_irq" value="false"/>
                              </struct>
                           </struct>
                           <struct name="3">
                              <setting name="channelId" value="PWM4"/>
                              <setting name="edge_aligned_mode" value="kFTM_EdgeAlignedPwm"/>
                              <struct name="edge_aligned_pwm">
                                 <setting name="chnlNumber" value="kFTM_Chnl_3"/>
                                 <setting name="level" value="kFTM_HighTrue"/>
                                 <setting name="dutyValueStr" value="0"/>
                                 <setting name="enable_chan_irq" value="false"/>
                              </struct>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
                  <instance name="DAC0" uuid="f71dc744-e1f4-4819-8519-5d0757e87e82" type="dac" type_id="dac_2.0.1" mode="basic" peripheral="DAC0" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="fsl_dac" quick_selection="QS_DAC_1">
                        <struct name="dac_config">
                           <setting name="referenceVoltageSource" value="kDAC_ReferenceVoltageSourceVref2"/>
                           <setting name="enableLowPowerMode" value="false"/>
                        </struct>
                        <setting name="dac_enable" value="true"/>
                        <setting name="dac_value" value="0"/>
                     </config_set>
                  </instance>
                  <instance name="VREF" uuid="918251c2-9e5b-4a3d-bf26-f1c02f838bc8" type="vref" type_id="vref_2.1.0" mode="vref" peripheral="VREF" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="fsl_vref">
                        <struct name="vref_config">
                           <setting name="bufferMode" value="kVREF_ModeHighPowerBuffer"/>
                        </struct>
                        <setting name="vref_enable_trim" value="false"/>
                     </config_set>
                  </instance>
                  <instance name="UART1" uuid="bb8e6c35-7c1f-4c7a-9e8d-c20214b395d3" type="uart" type_id="uart_2.5.1" mode="interrupts" peripheral="UART1" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="uartConfig_t" quick_selection="QuickSelection2">
                        <struct name="uartConfig">
                           <setting name="clockSource" value="BusInterfaceClock"/>
                           <setting name="clockSourceFreq" value="GetFreq"/>
                           <setting name="baudRate_Bps" value="57600"/>
                           <setting name="parityMode" value="kUART_ParityDisabled"/>
                           <setting name="dataBitsCount" value="kUART_EightDataBits"/>
                           <setting name="enableMatchAddress1" value="false"/>
                           <setting name="matchAddress1" value="0"/>
                           <setting name="enableMatchAddress2" value="false"/>
                           <setting name="matchAddress2" value="0"/>
                           <setting name="txFifoWatermark" value="0"/>
                           <setting name="rxFifoWatermark" value="1"/>
                           <setting name="idleType" value="kUART_IdleTypeStartBit"/>
                           <setting name="enableTx" value="true"/>
                           <setting name="enableRx" value="true"/>
                        </struct>
                     </config_set>
                     <config_set name="interruptsCfg">
                        <set name="interrupts">
                           <selected>
                              <id>kUART_RxDataRegFullInterruptEnable</id>
                              <id>kUART_RxOverrunInterruptEnable</id>
                           </selected>
                        </set>
                        <struct name="interrupt_vectors">
                           <setting name="enable_rx_tx_irq" value="true"/>
                           <struct name="interrupt_rx_tx">
                              <setting name="IRQn" value="UART1_RX_TX_IRQn"/>
                              <setting name="enable_interrrupt" value="enabled"/>
                              <setting name="enable_priority" value="false"/>
                              <setting name="priority" value="0"/>
                              <setting name="enable_custom_name" value="false"/>
                           </struct>
                           <setting name="enable_err_irq" value="false"/>
                           <struct name="interrupt_err">
                              <setting name="IRQn" value="UART1_ERR_IRQn"/>
                              <setting name="enable_interrrupt" value="enabled"/>
                              <setting name="enable_priority" value="false"/>
                              <setting name="priority" value="0"/>
                              <setting name="enable_custom_name" value="false"/>
                           </struct>
                        </struct>
                     </config_set>
                  </instance>
                  <instance name="ADC0" uuid="efb1f5cd-a7e8-470e-867d-21818855eea7" type="adc16" type_id="adc16_2.2.0" mode="ADC" peripheral="ADC0" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="fsl_adc16">
                        <struct name="adc16_config">
                           <setting name="referenceVoltageSource" value="kADC16_ReferenceVoltageSourceVref"/>
                           <setting name="clockSource" value="kADC16_ClockSourceAsynchronousClock"/>
                           <setting name="enableAsynchronousClock" value="true"/>
                           <setting name="clockDivider" value="kADC16_ClockDivider8"/>
                           <setting name="resolution" value="kADC16_Resolution16Bit"/>
                           <setting name="longSampleMode" value="kADC16_LongSampleDisabled"/>
                           <setting name="hardwareAverageMode" value="kADC16_HardwareAverageDisabled"/>
                           <setting name="enableHighSpeed" value="true"/>
                           <setting name="enableLowPower" value="false"/>
                           <setting name="enableContinuousConversion" value="false"/>
                        </struct>
                        <setting name="adc16_channel_mux_mode" value="kADC16_ChannelMuxA"/>
                        <struct name="adc16_hardware_compare_config">
                           <setting name="hardwareCompareModeEnable" value="false"/>
                        </struct>
                        <setting name="doAutoCalibration" value="false"/>
                        <setting name="offset" value="0"/>
                        <setting name="trigger" value="false"/>
                        <setting name="enable_dma" value="false"/>
                        <setting name="enable_irq" value="false"/>
                        <struct name="adc_interrupt">
                           <setting name="IRQn" value="ADC0_IRQn"/>
                           <setting name="enable_interrrupt" value="enabled"/>
                           <setting name="enable_priority" value="false"/>
                           <setting name="priority" value="0"/>
                           <setting name="enable_custom_name" value="false"/>
                        </struct>
                        <array name="adc16_channels_config">
                           <struct name="0">
                              <setting name="channelName" value=""/>
                              <setting name="enableDifferentialConversion" value="true"/>
                              <setting name="channelNumber" value="DP.0"/>
                              <setting name="channelNumber2" value="DM.0"/>
                              <setting name="enableInterruptOnConversionCompleted" value="false"/>
                              <setting name="channelGroup" value="0"/>
                              <setting name="initializeChannel" value="true"/>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
                  <instance name="PIT" uuid="0e3b7c78-33b7-4f5e-883a-673417b7bf04" type="pit" type_id="pit_2.0.3" mode="LPTMR_GENERAL" peripheral="PIT" enabled="true" comment="" custom_name_enabled="false" editing_lock="false">
                     <config_set name="fsl_pit">
                        <setting name="enableRunInDebug" value="false"/>
                        <struct name="timingConfig">
                           <setting name="clockSource" value="BusInterfaceClock"/>
                           <setting name="clockSourceFreq" value="BOARD_BootClockHSRUN"/>
                        </struct>
                        <array name="channels">
                           <struct name="0">
                              <setting name="channel_id" value="CHANNEL_0"/>
                              <setting name="channelNumber" value="0"/>
                              <setting name="enableChain" value="false"/>
                              <setting name="timerPeriod" value="1ms"/>
                              <setting name="startTimer" value="true"/>
                              <setting name="enableInterrupt" value="true"/>
                              <struct name="interrupt">
                                 <setting name="IRQn" value="PIT0_IRQn"/>
                                 <setting name="enable_interrrupt" value="enabled"/>
                                 <setting name="enable_priority" value="false"/>
                                 <setting name="priority" value="0"/>
                                 <setting name="enable_custom_name" value="true"/>
                                 <setting name="handler_custom_name" value="PIT_1ms_IRQHANDLER"/>
                              </struct>
                           </struct>
                           <struct name="1">
                              <setting name="channel_id" value="CHANNEL_1"/>
                              <setting name="channelNumber" value="1"/>
                              <setting name="enableChain" value="false"/>
                              <setting name="timerPeriod" value="10ms"/>
                              <setting name="startTimer" value="true"/>
                              <setting name="enableInterrupt" value="true"/>
                              <struct name="interrupt">
                                 <setting name="IRQn" value="PIT1_IRQn"/>
                                 <setting name="enable_interrrupt" value="enabled"/>
                                 <setting name="enable_priority" value="false"/>
                                 <setting name="priority" value="0"/>
                                 <setting name="enable_custom_name" value="true"/>
                                 <setting name="handler_custom_name" value="PIT_10ms_IRQHANDLER"/>
                              </struct>
                           </struct>
                        </array>
                     </config_set>
                  </instance>
               </instances>
            </functional_group>
         </functional_groups>
         <components>
            <component name="system" uuid="b9e9d52f-44fc-4f26-8744-6989bf03a76b" type_id="system_54b53072540eeeb8f8e9343e71f28176">
               <config_set_global name="global_system_definitions">
                  <setting name="user_definitions" value=""/>
                  <setting name="user_includes" value=""/>
                  <setting name="global_init" value=""/>
               </config_set_global>
            </component>
            <component name="msg" uuid="998cb4df-f0ae-4eb8-a095-857028e21dc5" type_id="msg_6e2baaf3b97dbeef01c0043275f9a0e7">
               <config_set_global name="global_messages"/>
            </component>
            <component name="uart_cmsis_common" uuid="c768960f-29ac-462e-bb1c-f3a89e040840" type_id="uart_cmsis_common_9cb8e302497aa696fdbb5a4fd622c2a8">
               <config_set_global name="global_USART_CMSIS_common" quick_selection="default"/>
            </component>
            <component name="gpio_adapter_common" uuid="0cc07278-e8b7-4840-bc68-4bf4edee21e9" type_id="gpio_adapter_common_57579b9ac814fe26bf95df0a384c36b6">
               <config_set_global name="global_gpio_adapter_common" quick_selection="default"/>
            </component>
            <component name="generic_uart" uuid="6016ffc0-958d-493e-a522-0e5a7d5b79e2" type_id="generic_uart_8cae00565451cf2346eb1b8c624e73a6">
               <config_set_global name="global_uart"/>
            </component>
            <component name="generic_enet" uuid="1f059acf-c8cd-4222-92ac-b20b029c7b90" type_id="generic_enet_74db5c914f0ddbe47d86af40cb77a619">
               <config_set_global name="global_enet"/>
            </component>
            <component name="generic_can" uuid="2fbe7646-6787-4d28-9db1-198a6d875a38" type_id="generic_can_1bfdd78b1af214566c1f23cf6a582d80">
               <config_set_global name="global_can"/>
            </component>
         </components>
      </periphs>
      <tee name="TEE" version="6.0" enabled="false" update_project_code="true">
         <generated_project_files/>
         <tee_profile>
            <processor_version>N/A</processor_version>
         </tee_profile>
      </tee>
   </tools>
</configuration>
/*
 * Copyright (c) 2016, Freescale Semiconductor, Inc.
 * Copyright 2016-2018 NXP
 * All rights reserved.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */

#ifndef _BOARD_H_
#define _BOARD_H_

#include "clock_config.h"
#include "fsl_gpio.h"
#include "pin_mux.h"

/*******************************************************************************
 * Definitions
 ******************************************************************************/
#define S2()	GPIO_PinRead(BOARD_INITPINS_S2_GPIO, BOARD_INITPINS_S2_PIN)
#define S3()	GPIO_PinRead(BOARD_INITPINS_S3_GPIO, BOARD_INITPINS_S3_PIN)
#define S4()	GPIO_PinRead(BOARD_INITPINS_S4_GPIO, BOARD_INITPINS_S4_PIN)
#define S5()	GPIO_PinRead(BOARD_INITPINS_S5_GPIO, BOARD_INITPINS_S5_PIN)
#define S7()	GPIO_PinRead(BOARD_INITPINS_S7_GPIO, BOARD_INITPINS_S7_PIN)
#define S8()	GPIO_PinRead(BOARD_INITPINS_S8_GPIO, BOARD_INITPINS_S8_PIN)
#define S9()	GPIO_PinRead(BOARD_INITPINS_S9_GPIO, BOARD_INITPINS_S9_PIN)
#define PHA()	GPIO_PinRead(BOARD_INITPINS_PHA_GPIO, BOARD_INITPINS_PHA_PIN)
#define PHB()	GPIO_PinRead(BOARD_INITPINS_PHB_GPIO, BOARD_INITPINS_PHB_PIN)
#define PUSH()	GPIO_PinRead(BOARD_INITPINS_PUSH_GPIO, BOARD_INITPINS_PUSH_PIN)

#define LED_ON() 		GPIO_PortClear(BOARD_INITPINS_LED_GPIO, BOARD_INITPINS_LED_GPIO_PIN_MASK)
#define LED_OFF() 		GPIO_PortSet(BOARD_INITPINS_LED_GPIO, BOARD_INITPINS_LED_GPIO_PIN_MASK)
#define LED_TOGGLE() 	GPIO_PortToggle(BOARD_INITPINS_LED_GPIO, BOARD_INITPINS_LED_GPIO_PIN_MASK)
#define BUZZ_ON() 		GPIO_PortSet(BOARD_INITPINS_BUZZ_GPIO, BOARD_INITPINS_BUZZ_GPIO_PIN_MASK)
#define BUZZ_OFF() 		GPIO_PortClear(BOARD_INITPINS_BUZZ_GPIO, BOARD_INITPINS_BUZZ_GPIO_PIN_MASK)
#define BUZZ_TOGGLE() 	GPIO_PortToggle(BOARD_INITPINS_BUZZ_GPIO, BOARD_INITPINS_BUZZ_GPIO_PIN_MASK)
#define EN1_HIGH() 		GPIO_PortSet(BOARD_INITPINS_EN1_GPIO, BOARD_INITPINS_EN1_GPIO_PIN_MASK)
#define EN1_LOW() 		GPIO_PortClear(BOARD_INITPINS_EN1_GPIO, BOARD_INITPINS_EN1_GPIO_PIN_MASK)
#define EN2_HIGH() 		GPIO_PortSet(BOARD_INITPINS_EN2_GPIO, BOARD_INITPINS_EN2_GPIO_PIN_MASK)
#define EN2_LOW() 		GPIO_PortClear(BOARD_INITPINS_EN2_GPIO, BOARD_INITPINS_EN2_GPIO_PIN_MASK)


/*! @brief The board name */
#define BOARD_NAME "FRDM-KV31F"

/*! @brief The UART to use for debug messages. */
#define BOARD_USE_UART
#define BOARD_DEBUG_UART_TYPE     kSerialPort_Uart
#define BOARD_DEBUG_UART_BASEADDR (uint32_t) UART0
#define BOARD_DEBUG_UART_INSTANCE 0U
#define BOARD_DEBUG_UART_CLKSRC   kCLOCK_CoreSysClk
#define BOARD_DEBUG_UART_CLK_FREQ CLOCK_GetCoreSysClkFreq()
#define BOARD_UART_IRQ            UART0_RX_TX_IRQn
#define BOARD_UART_IRQ_HANDLER    UART0_RX_TX_IRQHandler

#ifndef BOARD_DEBUG_UART_BAUDRATE
#define BOARD_DEBUG_UART_BAUDRATE 115200
#endif /* BOARD_DEBUG_UART_BAUDRATE */

/*! @brief The Flextimer instance/channel used for board */
#define BOARD_FTM_BASEADDR FTM0

/*! @brief The i2c instance used for board. */
#define BOARD_I2C_COMM_BASEADDR I2C0
/*! @brief The i2c instance used for i2c connection by default */
#define BOARD_I2C_BASEADDR I2C0

/*! @brief The bubble level demo information */
#define BOARD_FXOS8700_ADDR        0x1D
#define BOARD_ACCEL_ADDR           BOARD_FXOS8700_ADDR
#define BOARD_ACCEL_BAUDRATE       100
#define BOARD_ACCEL_I2C_BASEADDR   I2C0
#define BOARD_ACCEL_I2C_CLOCK_FREQ CLOCK_GetFreq(I2C0_CLK_SRC)

/*! @brief The CMP instance/channel used for board. */
#define BOARD_CMP_BASEADDR CMP0
#define BOARD_CMP_CHANNEL  0U

/*! @brief Define the port interrupt number for the board switches */
#ifndef BOARD_SW2_GPIO
#define BOARD_SW2_GPIO GPIOA
#endif
#ifndef BOARD_SW2_PORT
#define BOARD_SW2_PORT PORTA
#endif
#ifndef BOARD_SW2_GPIO_PIN
#define BOARD_SW2_GPIO_PIN 4U
#endif
#define BOARD_SW2_IRQ         PORTA_IRQn
#define BOARD_SW2_IRQ_HANDLER PORTA_IRQHandler
#define BOARD_SW2_NAME        "SW2"

/* Board led color mapping */
#define LOGIC_LED_ON  0U
#define LOGIC_LED_OFF 1U
#ifndef BOARD_LED_RED_GPIO
#define BOARD_LED_RED_GPIO GPIOD
#endif
#define BOARD_LED_RED_GPIO_PORT PORTD
#ifndef BOARD_LED_RED_GPIO_PIN
#define BOARD_LED_RED_GPIO_PIN 1U
#endif
#ifndef BOARD_LED_GREEN_GPIO
#define BOARD_LED_GREEN_GPIO GPIOD
#endif
#define BOARD_LED_GREEN_GPIO_PORT PORTD
#ifndef BOARD_LED_GREEN_GPIO_PIN
#define BOARD_LED_GREEN_GPIO_PIN 7U
#endif
#ifndef BOARD_LED_BLUE_GPIO
#define BOARD_LED_BLUE_GPIO GPIOE
#endif
#define BOARD_LED_BLUE_GPIO_PORT PORTE
#ifndef BOARD_LED_BLUE_GPIO_PIN
#define BOARD_LED_BLUE_GPIO_PIN 25U
#endif

#define LED_RED_INIT(output)                                           \
    GPIO_PinWrite(BOARD_LED_RED_GPIO, BOARD_LED_RED_GPIO_PIN, output); \
    BOARD_LED_RED_GPIO->PDDR |= (1U << BOARD_LED_RED_GPIO_PIN)                         /*!< Enable target LED_RED */
#define LED_RED_ON()  GPIO_PortClear(BOARD_LED_RED_GPIO, 1U << BOARD_LED_RED_GPIO_PIN) /*!< Turn on target LED_RED */
#define LED_RED_OFF() GPIO_PortSet(BOARD_LED_RED_GPIO, 1U << BOARD_LED_RED_GPIO_PIN)   /*!< Turn off target LED_RED */
#define LED_RED_TOGGLE() \
    GPIO_PortToggle(BOARD_LED_RED_GPIO, 1U << BOARD_LED_RED_GPIO_PIN) /*!< Toggle on target LED_RED */

#define LED_GREEN_INIT(output)                                             \
    GPIO_PinWrite(BOARD_LED_GREEN_GPIO, BOARD_LED_GREEN_GPIO_PIN, output); \
    BOARD_LED_GREEN_GPIO->PDDR |= (1U << BOARD_LED_GREEN_GPIO_PIN) /*!< Enable target LED_GREEN */
#define LED_GREEN_ON() \
    GPIO_PortClear(BOARD_LED_GREEN_GPIO, 1U << BOARD_LED_GREEN_GPIO_PIN) /*!< Turn on target LED_GREEN */
#define LED_GREEN_OFF() \
    GPIO_PortSet(BOARD_LED_GREEN_GPIO, 1U << BOARD_LED_GREEN_GPIO_PIN) /*!< Turn off target LED_GREEN */
#define LED_GREEN_TOGGLE() \
    GPIO_PortToggle(BOARD_LED_GREEN_GPIO, 1U << BOARD_LED_GREEN_GPIO_PIN) /*!< Toggle on target LED_GREEN */

#define LED_BLUE_INIT(output)                                            \
    GPIO_PinWrite(BOARD_LED_BLUE_GPIO, BOARD_LED_BLUE_GPIO_PIN, output); \
    BOARD_LED_BLUE_GPIO->PDDR |= (1U << BOARD_LED_BLUE_GPIO_PIN) /*!< Enable target LED_BLUE */
#define LED_BLUE_ON()                                                                               \
    GPIO_PortClear(BOARD_LED_BLUE_GPIO, 1U << BOARD_LED_BLUE_GPIO_PIN) /*!< Turn on target LED_BLUE \
                                                                        */
#define LED_BLUE_OFF()                                                                             \
    GPIO_PortSet(BOARD_LED_BLUE_GPIO, 1U << BOARD_LED_BLUE_GPIO_PIN) /*!< Turn off target LED_BLUE \
                                                                      */
#define LED_BLUE_TOGGLE() \
    GPIO_PortToggle(BOARD_LED_BLUE_GPIO, 1U << BOARD_LED_BLUE_GPIO_PIN) /*!< Toggle on target LED_BLUE */

#if defined(__cplusplus)
extern "C" {
#endif /* __cplusplus */

/*******************************************************************************
 * API
 ******************************************************************************/

void BOARD_InitDebugConsole(void);
#if defined(SDK_I2C_BASED_COMPONENT_USED) && SDK_I2C_BASED_COMPONENT_USED
void BOARD_I2C_Init(I2C_Type *base, uint32_t clkSrc_Hz);
status_t BOARD_I2C_Send(I2C_Type *base,
                        uint8_t deviceAddress,
                        uint32_t subAddress,
                        uint8_t subaddressSize,
                        uint8_t *txBuff,
                        uint8_t txBuffSize);
status_t BOARD_I2C_Receive(I2C_Type *base,
                           uint8_t deviceAddress,
                           uint32_t subAddress,
                           uint8_t subaddressSize,
                           uint8_t *rxBuff,
                           uint8_t rxBuffSize);
void BOARD_Accel_I2C_Init(void);
status_t BOARD_Accel_I2C_Send(uint8_t deviceAddress, uint32_t subAddress, uint8_t subaddressSize, uint32_t txBuff);
status_t BOARD_Accel_I2C_Receive(
    uint8_t deviceAddress, uint32_t subAddress, uint8_t subaddressSize, uint8_t *rxBuff, uint8_t rxBuffSize);
#endif /* SDK_I2C_BASED_COMPONENT_USED */

#if defined(__cplusplus)
}
#endif /* __cplusplus */

#endif /* _BOARD_H_ */

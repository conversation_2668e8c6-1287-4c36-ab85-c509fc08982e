/***********************************************************************************************************************
 * This file was generated by the MCUXpresso Config Tools. Any manual edits made to this file
 * will be overwritten if the respective MCUXpresso Config Tools is used to update this file.
 **********************************************************************************************************************/

#ifndef _PERIPHERALS_H_
#define _PERIPHERALS_H_

/***********************************************************************************************************************
 * Included files
 **********************************************************************************************************************/
#include "fsl_common.h"
#include "fsl_clock.h"
#include "fsl_ftm.h"
#include "fsl_dac.h"
#include "fsl_vref.h"
#include "fsl_uart.h"
#include "fsl_adc16.h"
#include "fsl_pit.h"

#if defined(__cplusplus)
extern "C" {
#endif /* __cplusplus */

/***********************************************************************************************************************
 * Definitions
 **********************************************************************************************************************/
/* Definitions for BOARD_InitPeripherals functional group */
/* Definition of peripheral ID */
#define FTM1_PERIPHERAL FTM1
/* Definition of the clock source frequency */
#define FTM1_CLOCK_SOURCE CLOCK_GetFreq(kCLOCK_BusClk)
/* Definition of peripheral ID */
#define FTM0_PERIPHERAL FTM0
/* Definition of the clock source frequency */
#define FTM0_CLOCK_SOURCE CLOCK_GetFreq(kCLOCK_BusClk)
/* Definition of the clock source frequency */
#define FTM0_TIMER_MODULO_VALUE (((FTM0_CLOCK_SOURCE/ (1U << (FTM0_PERIPHERAL->SC & FTM_SC_PS_MASK))) / 20000) - 1)
/* Definition of FTM0 channel/pair number 0 on Edge-aligned PWM */
#define FTM0_PWM1_CHANNEL kFTM_Chnl_0
/* Definition of FTM0 channel/pair number 1 on Edge-aligned PWM */
#define FTM0_PWM2_CHANNEL kFTM_Chnl_1
/* Definition of FTM0 channel/pair number 2 on Edge-aligned PWM */
#define FTM0_PWM3_CHANNEL kFTM_Chnl_2
/* Definition of FTM0 channel/pair number 3 on Edge-aligned PWM */
#define FTM0_PWM4_CHANNEL kFTM_Chnl_3
/* Alias for DAC0 peripheral */
#define DAC0_PERIPHERAL DAC0
/* Alias for VREF peripheral */
#define VREF_PERIPHERAL VREF
/* Definition of peripheral ID */
#define UART1_PERIPHERAL UART1
/* Definition of the clock source frequency */
#define UART1_CLOCK_SOURCE CLOCK_GetFreq(UART1_CLK_SRC)
/* Alias for ADC0 peripheral */
#define ADC0_PERIPHERAL ADC0
/* ADC0 interrupt vector ID (number). */
#define ADC0_IRQN ADC0_IRQn
/* ADC0 interrupt handler identifier. */
#define ADC0_IRQHANDLER ADC0_IRQHandler
/* Channel 0 (DP.0) conversion control group. */
#define ADC0_CH0_CONTROL_GROUP 0
/* BOARD_InitPeripherals defines for PIT */
/* Definition of peripheral ID. */
#define PIT_PERIPHERAL PIT
/* Definition of clock source. */
#define PIT_CLOCK_SOURCE kCLOCK_BusClk
/* Definition of clock source frequency. */
#define PIT_CLK_FREQ 45765625UL
/* Definition of ticks count for channel 0 - deprecated. */
#define PIT_0_TICKS 45766U
/* Definition of ticks count for channel 1 - deprecated. */
#define PIT_1_TICKS 457656U
/* PIT interrupt vector ID (number) - deprecated. */
#define PIT_0_IRQN PIT0_IRQn
/* PIT interrupt vector ID (number) - deprecated. */
#define PIT_1_IRQN PIT1_IRQn
/* Definition of channel number for channel 0. */
#define PIT_CHANNEL_0 kPIT_Chnl_0
/* Definition of channel number for channel 1. */
#define PIT_CHANNEL_1 kPIT_Chnl_1
/* Definition of ticks count for channel 0. */
#define PIT_CHANNEL_0_TICKS 45766U
/* Definition of ticks count for channel 1. */
#define PIT_CHANNEL_1_TICKS 457656U
/* PIT interrupt vector ID (number). */
#define PIT_CHANNEL_0_IRQN PIT0_IRQn
/* PIT interrupt handler identifier. */
#define PIT_1ms_IRQHANDLER PIT0_IRQHandler
/* PIT interrupt vector ID (number). */
#define PIT_CHANNEL_1_IRQN PIT1_IRQn
/* PIT interrupt handler identifier. */
#define PIT_10ms_IRQHANDLER PIT1_IRQHandler

/***********************************************************************************************************************
 * Global variables
 **********************************************************************************************************************/
extern const ftm_config_t FTM1_config;
extern const ftm_config_t FTM0_config;
extern const dac_config_t DAC0_config;
extern const vref_config_t VREF_config;
extern const uart_config_t UART1_config;
extern adc16_channel_config_t ADC0_channelsConfig[1];
extern const adc16_config_t ADC0_config;
extern const adc16_channel_mux_mode_t ADC0_muxMode;
extern const pit_config_t PIT_config;

/***********************************************************************************************************************
 * Initialization functions
 **********************************************************************************************************************/

void BOARD_InitPeripherals(void);

/***********************************************************************************************************************
 * BOARD_InitBootPeripherals function
 **********************************************************************************************************************/
void BOARD_InitBootPeripherals(void);

#if defined(__cplusplus)
}
#endif

#endif /* _PERIPHERALS_H_ */

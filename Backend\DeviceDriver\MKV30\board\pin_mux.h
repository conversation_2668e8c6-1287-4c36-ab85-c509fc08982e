/***********************************************************************************************************************
 * This file was generated by the MCUXpresso Config Tools. Any manual edits made to this file
 * will be overwritten if the respective MCUXpresso Config Tools is used to update this file.
 **********************************************************************************************************************/

#ifndef _PIN_MUX_H_
#define _PIN_MUX_H_

/*!
 * @addtogroup pin_mux
 * @{
 */

/***********************************************************************************************************************
 * API
 **********************************************************************************************************************/

#if defined(__cplusplus)
extern "C" {
#endif

/*!
 * @brief Calls initialization functions.
 *
 */
void BOARD_InitBootPins(void);

/*!
 * @brief UART 1 transmit data source select: UART1_TX pin */
#define SOPT5_UART1TXSRC_UART_TX 0x00u
/*!
 * @brief FTM0 channel 0 output source: FTM0_CH0 pin is output of FTM0 channel 0 output */
#define SOPT8_FTM0OCH0SRC_FTM 0x00u
/*!
 * @brief FTM0 channel 1 output source: FTM0_CH1 pin is output of FTM0 channel 1 output */
#define SOPT8_FTM0OCH1SRC_FTM 0x00u
/*!
 * @brief FTM0 channel 2 output source: FTM0_CH2 pin is output of FTM0 channel 2 output */
#define SOPT8_FTM0OCH2SRC_FTM 0x00u
/*!
 * @brief FTM0 channel 3 output source: FTM0_CH3 pin is output of FTM0 channel 3 output */
#define SOPT8_FTM0OCH3SRC_FTM 0x00u

/*! @name PORTA4 (number 21), S2
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_S2_GPIO GPIOA               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_S2_GPIO_PIN_MASK (1U << 4U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_S2_PORT PORTA               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_S2_PIN 4U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_S2_PIN_MASK (1U << 4U)      /*!<@brief PORT pin mask */
                                                   /* @} */

/*! @name PORTB0 (number 27), PHA1
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PHA1_PORT PORTB               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PHA1_PIN 0U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PHA1_PIN_MASK (1U << 0U)      /*!<@brief PORT pin mask */
                                                     /* @} */

/*! @name PORTB1 (number 28), PHB1
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PHB1_PORT PORTB               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PHB1_PIN 1U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PHB1_PIN_MASK (1U << 1U)      /*!<@brief PORT pin mask */
                                                     /* @} */

/*! @name PORTB2 (number 29), S8
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_S8_GPIO GPIOB               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_S8_GPIO_PIN_MASK (1U << 2U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_S8_PORT PORTB               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_S8_PIN 2U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_S8_PIN_MASK (1U << 2U)      /*!<@brief PORT pin mask */
                                                   /* @} */

/*! @name PORTB3 (number 30), S3
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_S3_GPIO GPIOB               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_S3_GPIO_PIN_MASK (1U << 3U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_S3_PORT PORTB               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_S3_PIN 3U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_S3_PIN_MASK (1U << 3U)      /*!<@brief PORT pin mask */
                                                   /* @} */

/*! @name PORTB16 (number 31), S7
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_S7_GPIO GPIOB                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_S7_GPIO_PIN_MASK (1U << 16U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_S7_PORT PORTB                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_S7_PIN 16U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_S7_PIN_MASK (1U << 16U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTB17 (number 32), BUZZ
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_BUZZ_GPIO GPIOB                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_BUZZ_INIT_GPIO_VALUE 0U        /*!<@brief GPIO output initial state */
#define BOARD_INITPINS_BUZZ_GPIO_PIN_MASK (1U << 17U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_BUZZ_PORT PORTB                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_BUZZ_PIN 17U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_BUZZ_PIN_MASK (1U << 17U)      /*!<@brief PORT pin mask */
                                                      /* @} */

/*! @name PORTC0 (number 33), LED
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_LED_GPIO GPIOC               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_LED_INIT_GPIO_VALUE 0U       /*!<@brief GPIO output initial state */
#define BOARD_INITPINS_LED_GPIO_PIN_MASK (1U << 0U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_LED_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_LED_PIN 0U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_LED_PIN_MASK (1U << 0U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTC1 (number 34), IS2
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_IS2_GPIO GPIOC               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_IS2_GPIO_PIN_MASK (1U << 1U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_IS2_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_IS2_PIN 1U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_IS2_PIN_MASK (1U << 1U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTC2 (number 35), S9
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_S9_GPIO GPIOC               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_S9_GPIO_PIN_MASK (1U << 2U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_S9_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_S9_PIN 2U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_S9_PIN_MASK (1U << 2U)      /*!<@brief PORT pin mask */
                                                   /* @} */

/*! @name PORTC3 (number 36), UART1_RX
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_UART1_RX_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_UART1_RX_PIN 3U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_UART1_RX_PIN_MASK (1U << 3U)      /*!<@brief PORT pin mask */
                                                         /* @} */

/*! @name PORTC4 (number 37), UART1_TX
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_UART1_TX_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_UART1_TX_PIN 4U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_UART1_TX_PIN_MASK (1U << 4U)      /*!<@brief PORT pin mask */
                                                         /* @} */

/*! @name PORTC5 (number 38), EN2
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_EN2_GPIO GPIOC               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_EN2_INIT_GPIO_VALUE 0U       /*!<@brief GPIO output initial state */
#define BOARD_INITPINS_EN2_GPIO_PIN_MASK (1U << 5U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_EN2_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_EN2_PIN 5U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_EN2_PIN_MASK (1U << 5U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTC6 (number 39), IS1
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_IS1_GPIO GPIOC               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_IS1_GPIO_PIN_MASK (1U << 6U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_IS1_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_IS1_PIN 6U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_IS1_PIN_MASK (1U << 6U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTC7 (number 40), EN1
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_EN1_GPIO GPIOC               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_EN1_INIT_GPIO_VALUE 0U       /*!<@brief GPIO output initial state */
#define BOARD_INITPINS_EN1_GPIO_PIN_MASK (1U << 7U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_EN1_PORT PORTC               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_EN1_PIN 7U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_EN1_PIN_MASK (1U << 7U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTD0 (number 41), PWM0
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PWM0_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PWM0_PIN 0U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PWM0_PIN_MASK (1U << 0U)      /*!<@brief PORT pin mask */
                                                     /* @} */

/*! @name PORTD1 (number 42), PWM1
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PWM1_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PWM1_PIN 1U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PWM1_PIN_MASK (1U << 1U)      /*!<@brief PORT pin mask */
                                                     /* @} */

/*! @name PORTD2 (number 43), PWM2
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PWM2_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PWM2_PIN 2U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PWM2_PIN_MASK (1U << 2U)      /*!<@brief PORT pin mask */
                                                     /* @} */

/*! @name PORTD3 (number 44), PWM3
  @{ */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PWM3_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PWM3_PIN 3U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PWM3_PIN_MASK (1U << 3U)      /*!<@brief PORT pin mask */
                                                     /* @} */

/*! @name PORTD4 (number 45), PHB
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PHB_GPIO GPIOD               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PHB_GPIO_PIN_MASK (1U << 4U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PHB_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PHB_PIN 4U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PHB_PIN_MASK (1U << 4U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTD5 (number 46), PHA
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PHA_GPIO GPIOD               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PHA_GPIO_PIN_MASK (1U << 5U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PHA_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PHA_PIN 5U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PHA_PIN_MASK (1U << 5U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name PORTD6 (number 47), PUSH
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PUSH_GPIO GPIOD               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PUSH_GPIO_PIN_MASK (1U << 6U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PUSH_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PUSH_PIN 6U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PUSH_PIN_MASK (1U << 6U)      /*!<@brief PORT pin mask */
                                                     /* @} */

/*! @name PORTD7 (number 48), S5
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_S5_GPIO GPIOD               /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_S5_GPIO_PIN_MASK (1U << 7U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_S5_PORT PORTD               /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_S5_PIN 7U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_S5_PIN_MASK (1U << 7U)      /*!<@brief PORT pin mask */
                                                   /* @} */

/*! @name PORTE16 (number 3), PE16
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PE16_GPIO GPIOE                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PE16_GPIO_PIN_MASK (1U << 16U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PE16_PORT PORTE                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PE16_PIN 16U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PE16_PIN_MASK (1U << 16U)      /*!<@brief PORT pin mask */
                                                      /* @} */

/*! @name PORTE17 (number 4), PE17
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PE17_GPIO GPIOE                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PE17_GPIO_PIN_MASK (1U << 17U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PE17_PORT PORTE                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PE17_PIN 17U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PE17_PIN_MASK (1U << 17U)      /*!<@brief PORT pin mask */
                                                      /* @} */

/*! @name PORTE18 (number 5), PE18
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PE18_GPIO GPIOE                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PE18_GPIO_PIN_MASK (1U << 18U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PE18_PORT PORTE                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PE18_PIN 18U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PE18_PIN_MASK (1U << 18U)      /*!<@brief PORT pin mask */
                                                      /* @} */

/*! @name PORTE19 (number 6), PE19
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PE19_GPIO GPIOE                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PE19_GPIO_PIN_MASK (1U << 19U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PE19_PORT PORTE                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PE19_PIN 19U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PE19_PIN_MASK (1U << 19U)      /*!<@brief PORT pin mask */
                                                      /* @} */

/*! @name PORTE24 (number 15), PE24
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_PE24_GPIO GPIOE                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_PE24_GPIO_PIN_MASK (1U << 24U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_PE24_PORT PORTE                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_PE24_PIN 24U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_PE24_PIN_MASK (1U << 24U)      /*!<@brief PORT pin mask */
                                                      /* @} */

/*! @name PORTE25 (number 16), S4
  @{ */

/* Symbols to be used with GPIO driver */
#define BOARD_INITPINS_S4_GPIO GPIOE                /*!<@brief GPIO peripheral base pointer */
#define BOARD_INITPINS_S4_GPIO_PIN_MASK (1U << 25U) /*!<@brief GPIO pin mask */

/* Symbols to be used with PORT driver */
#define BOARD_INITPINS_S4_PORT PORTE                /*!<@brief PORT peripheral base pointer */
#define BOARD_INITPINS_S4_PIN 25U                   /*!<@brief PORT pin number */
#define BOARD_INITPINS_S4_PIN_MASK (1U << 25U)      /*!<@brief PORT pin mask */
                                                    /* @} */

/*! @name ADC0_DP0 (number 7), ADCDP
  @{ */
/* @} */

/*! @name ADC0_DM0 (number 8), ADCDM
  @{ */
/* @} */

/*! @name VREF_OUT (number 13), VREF
  @{ */
/* @} */

/*! @name DAC0_OUT (number 14), VOUT
  @{ */
/* @} */

/*!
 * @brief Configures pin routing and optionally pin electrical features.
 *
 */
void BOARD_InitPins(void);

#if defined(__cplusplus)
}
#endif

/*!
 * @}
 */
#endif /* _PIN_MUX_H_ */

/***********************************************************************************************************************
 * EOF
 **********************************************************************************************************************/

<?xml version="1.0"?>
<settings>
    <TerminalIO>
        <InputSource>1</InputSource>
        <InputMode2>10</InputMode2>
        <Filename>$PROJ_DIR$\TermIOInput.txt</Filename>
        <InputEcho>1</InputEcho>
        <ShowReset>0</ShowReset>
        <InputEncodingICU>0</InputEncodingICU>
        <OutputEncodingICU>0</OutputEncodingICU>
    </TerminalIO>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <PlDriver>
        <FirstRun>0</FirstRun>
        <MemConfigValue>C:\Program Files\IAR Systems\Embedded Workbench 9.2\arm\config\debugger\NXP\MKV30F64xxx10.ddf</MemConfigValue>
    </PlDriver>
    <Jet>
        <JetConnSerialNo>CMSIS-DAP v2:00000080066DFF575256867067222841A5A5A5A597969908</JetConnSerialNo>
        <JetConnFoundProbes>CMSIS-DAP v1:00000080066dff575256867067222841a5a5a5a597969908:CMSIS-DAP v2:00000080066DFF575256867067222841A5A5A5A597969908:</JetConnFoundProbes>
        <PrevWtdReset>Hardware</PrevWtdReset>
        <OnlineReset>Software</OnlineReset>
        <DisableInterrupts>0</DisableInterrupts>
        <LeaveRunning>0</LeaveRunning>
        <MultiCoreRunAll>0</MultiCoreRunAll>
        <CpuHaltOnBreakpointSet>0</CpuHaltOnBreakpointSet>
    </Jet>
    <ArmDriver>
        <EnableCache>1</EnableCache>
        <EnforceMemoryConfiguration>1</EnforceMemoryConfiguration>
    </ArmDriver>
    <DebugChecksum>
        <Checksum>866385039</Checksum>
    </DebugChecksum>
    <Exceptions>
        <StopOnUncaught>_ 0</StopOnUncaught>
        <StopOnThrow>_ 0</StopOnThrow>
    </Exceptions>
    <Disassembly>
        <InstrCount>0</InstrCount>
        <MixedMode>1</MixedMode>
    </Disassembly>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <JLinkDriver>
        <jlinkResetStyle>12</jlinkResetStyle>
        <jlinkResetStrategy>0</jlinkResetStrategy>
        <MultiCoreRunAll>0</MultiCoreRunAll>
        <TraceBufferSize>0x10000</TraceBufferSize>
        <TraceStallIfFIFOFull>0x0</TraceStallIfFIFOFull>
        <TracePortSize>0x0</TracePortSize>
        <LeaveTargetRunning>_ 0</LeaveTargetRunning>
        <CStepIntDis>_ 0</CStepIntDis>
    </JLinkDriver>
    <SWOTraceHWSettings>
        <OverrideDefaultClocks>0</OverrideDefaultClocks>
        <CpuClock>72000000</CpuClock>
        <ClockAutoDetect>0</ClockAutoDetect>
        <ClockWanted>2000000</ClockWanted>
        <JtagSpeed>2000000</JtagSpeed>
        <Prescaler>36</Prescaler>
        <TimeStampPrescIndex>0</TimeStampPrescIndex>
        <TimeStampPrescData>0</TimeStampPrescData>
        <PcSampCYCTAP>1</PcSampCYCTAP>
        <PcSampPOSTCNT>15</PcSampPOSTCNT>
        <PcSampIndex>0</PcSampIndex>
        <DataLogMode>0</DataLogMode>
        <ITMportsEnable>0</ITMportsEnable>
        <ITMportsTermIO>0</ITMportsTermIO>
        <ITMportsLogFile>0</ITMportsLogFile>
        <ITMlogFile>$PROJ_DIR$\ITM.log</ITMlogFile>
    </SWOTraceHWSettings>
    <PowerLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <Title_0>I0</Title_0>
        <Symbol_0>0 4 0</Symbol_0>
        <LiveEnabled>0</LiveEnabled>
        <LiveFile />
    </PowerLog>
    <DataLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
    </DataLog>
    <InterruptLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <EventLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <Title_0>Ch3</Title_0>
        <Symbol_0>0 0 1</Symbol_0>
        <Title_1>Ch2</Title_1>
        <Symbol_1>0 0 1</Symbol_1>
        <Title_2>Ch1</Title_2>
        <Symbol_2>0 0 1</Symbol_2>
        <Title_3>Ch0</Title_3>
        <Symbol_3>0 0 1</Symbol_3>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </EventLog>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>1</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
    </DriverProfiling>
    <PowerProbe>
        <Frequency>10000</Frequency>
        <Probe0>I0</Probe0>
        <ProbeSetup0>2 1 1 2 0 0</ProbeSetup0>
    </PowerProbe>
    <SWOManager>
        <SamplingDivider>8192</SamplingDivider>
        <OverrideClock>0</OverrideClock>
        <CpuClock>15</CpuClock>
        <SwoClock>5932173452988339529</SwoClock>
        <DataLogMode>0</DataLogMode>
        <ItmPortsEnabled>63</ItmPortsEnabled>
        <ItmTermIOPorts>1</ItmTermIOPorts>
        <ItmLogPorts>0</ItmLogPorts>
        <ItmLogFile>$PROJ_DIR$\ITM.log</ItmLogFile>
        <PowerForcePC>1</PowerForcePC>
        <PowerConnectPC>1</PowerConnectPC>
    </SWOManager>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <ETMTraceWindow>
        <PortWidth>4</PortWidth>
        <PortMode>0</PortMode>
        <CaptureDataValues>0</CaptureDataValues>
        <CaptureDataAddresses>0</CaptureDataAddresses>
        <CaptureDataRange>0</CaptureDataRange>
        <DataFirst>0</DataFirst>
        <DataLast>4294967295</DataLast>
        <StopWhen>0</StopWhen>
        <StallCPU>0</StallCPU>
        <NoPCCapture>0</NoPCCapture>
    </ETMTraceWindow>
    <Trace2>
        <Enabled>0</Enabled>
        <ShowSource>0</ShowSource>
    </Trace2>
    <SWOTraceWindow>
        <ForcedPcSampling>0</ForcedPcSampling>
        <ForcedInterruptLogs>0</ForcedInterruptLogs>
        <ForcedItmLogs>0</ForcedItmLogs>
        <EventCPI>0</EventCPI>
        <EventEXC>0</EventEXC>
        <EventFOLD>0</EventFOLD>
        <EventLSU>0</EventLSU>
        <EventSLEEP>0</EventSLEEP>
    </SWOTraceWindow>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <Breakpoints2>
        <Count>0</Count>
    </Breakpoints2>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
</settings>

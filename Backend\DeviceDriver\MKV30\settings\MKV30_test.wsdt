<?xml version="1.0"?>
<Workspace>
    <ConfigDictionary>
        <CurrentConfigs>
            <Project>MKV30_test/debug</Project>
        </CurrentConfigs>
    </ConfigDictionary>
    <WindowStorage>
        <ChildIdMap>
            <TB_CMSISPACK>34048</TB_CMSISPACK>
            <TB_MAIN3>34049</TB_MAIN3>
            <WIN_BREAKPOINTS>34050</WIN_BREAKPOINTS>
            <WIN_BUILD>34051</WIN_BUILD>
            <WIN_CALL_GRAPH>34052</WIN_CALL_GRAPH>
            <WIN_CMAKE_CMSIS_LOG>34053</WIN_CMAKE_CMSIS_LOG>
            <WIN_CUSTOM_SFR>34054</WIN_CUSTOM_SFR>
            <WIN_C_STAT>34055</WIN_C_STAT>
            <WIN_DEBUG_LOG>34056</WIN_DEBUG_LOG>
            <WIN_FIND_ALL_DECLARATIONS>34057</WIN_FIND_ALL_DECLARATIONS>
            <WIN_FIND_ALL_REFERENCES>34058</WIN_FIND_ALL_REFERENCES>
            <WIN_FIND_IN_FILES>34059</WIN_FIND_IN_FILES>
            <WIN_SELECT_AMBIGUOUS_DEFINITIONS>34060</WIN_SELECT_AMBIGUOUS_DEFINITIONS>
            <WIN_SOURCEBROWSE_LOG>34061</WIN_SOURCEBROWSE_LOG>
            <WIN_SOURCE_BROWSE2>34062</WIN_SOURCE_BROWSE2>
            <WIN_TOOL_OUTPUT>34063</WIN_TOOL_OUTPUT>
            <WIN_WORKSPACE>34064</WIN_WORKSPACE>
            <WIN_POWER_LOG_SETUP>34065</WIN_POWER_LOG_SETUP>
        </ChildIdMap>
        <Desktop>
            <IarPane-34048>
                <ToolBarCmdIds>
                    <item>34001</item>
                    <item>0</item>
                </ToolBarCmdIds>
            </IarPane-34048>
            <IarPane-34049>
                <ToolBarCmdIds>
                    <item>57600</item>
                    <item>57601</item>
                    <item>57603</item>
                    <item>33024</item>
                    <item>0</item>
                    <item>57607</item>
                    <item>0</item>
                    <item>57635</item>
                    <item>57634</item>
                    <item>57637</item>
                    <item>0</item>
                    <item>57643</item>
                    <item>57644</item>
                    <item>0</item>
                    <item>33090</item>
                    <item>33057</item>
                    <item>57636</item>
                    <item>57640</item>
                    <item>57641</item>
                    <item>33026</item>
                    <item>33065</item>
                    <item>33063</item>
                    <item>33064</item>
                    <item>33053</item>
                    <item>33054</item>
                    <item>0</item>
                    <item>33035</item>
                    <item>33036</item>
                    <item>34399</item>
                    <item>0</item>
                    <item>33038</item>
                    <item>33039</item>
                    <item>0</item>
                </ToolBarCmdIds>
            </IarPane-34049>
            <IarPane-34056>
                <col-names>
                    <item>Log</item>
                    <item>_I0</item>
                </col-names>
                <col-widths>
                    <item>1878</item>
                    <item>20</item>
                </col-widths>
                <DebugLogLevel>2</DebugLogLevel>
                <LiveFile>$PROJ_DIR$\DebugLog.log</LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>0</LiveFilterLevel>
            </IarPane-34056>
            <IarPane-34064>
                <NodeDict>
                    <ExpandedNode>MKV30_test</ExpandedNode>
                </NodeDict>
            </IarPane-34064>
            <ControlBarVersion>
                <Major>14</Major>
                <Minor>27</Minor>
            </ControlBarVersion>
            <MFCToolBarParameters>
                <Tooltips>1</Tooltips>
                <ShortcutKeys>1</ShortcutKeys>
                <LargeIcons>0</LargeIcons>
                <MenuAnimation>0</MenuAnimation>
                <RecentlyUsedMenus>1</RecentlyUsedMenus>
                <MenuShadows>1</MenuShadows>
                <ShowAllMenusAfterDelay>1</ShowAllMenusAfterDelay>
                <CommandsUsage>BA000000320040E1000001000000029700000100000013860000010000002CE10000030000001581000002000000268100000200000010860000010000000481000007000000AF060000010000004E9700000100000004E100000100000020810000010000000F810000030000005E8400000D0000003A970000010000000D800000020000000C81000015000000128600000100000028810000010000005686000017000000178100000800000077840000010000002BE1000003000000008400000100000020970000010000001184000003000000558400000200000014810000020000000C970000020000000E8400000300000000810000030000000E810000A70000001F810000010000005E8600000700000000E1000001000000D184000001000000058400000100000014860000010000007C840000020000003697000001000000039700000A000000028400000100000016810000020000000581000022000000118600000100000024810000010000000BDE0000010000005D8600000100000002E10000010000000784000003000000</CommandsUsage>
            </MFCToolBarParameters>
            <CommandManager>
                <CommandsWithoutImages>43000D8400000F84000008840000FFFFFFFF54840000328100001C8100000984000053840000818400007D8400008284000083840000848400000C8400003384000078840000118400005E84000008800000098000000A8000000B8000000C800000158000000A81000001E8000012DE000002DE000003DE00000BDE000005DE000006DE000004DE0000259200001E920000249200001D92000077840000078400007F8400008784000086840000808C000044D50000D6840000D7840000D8840000D9840000DA840000DB840000DC840000DD840000DE840000DF840000E0840000E1840000E9840000EA8400002481000045D5000046D5000047D5000048D5000049D500004AD500004BD50000</CommandsWithoutImages>
                <MenuUserImages>780013970000F107000024970000E604000035970000F504000002970000F80A000004840000D5030000298100002F000000048100001B0000002CE100008B000000219700003F07000026810000B5030000DC860000F807000010970000F207000032970000F204000015810000240000002F9700006B00000031840000DC030000458100000400000007E100003E0000002C9700003D07000000900000E00300003D970000FC0400000A970000DB0A000020810000B30300000F810000AB03000004E100003C00000023E1000088000000299700003A07000018970000FB0700003A970000F90400004B970000600000000C810000A80300001D8100002700000001E10000390000000D80000018000000198200005B00000009810000A603000015970000F907000026970000F0040000379700008A00000004970000FA0A000006840000D70300001682000059000000239700004107000012970000F5070000349700008800000001970000F70A000003840000D40300001A8F000006000000288100002E00000017810000260000002BE100008A000000209700003E070000DB860000F70700000F970000EF07000031970000720000000084000034050000148100002300000044920000950300002E9700006A0000004481000002000000858400000500000030840000520000000E84000050000000008100001900000025E10000890000002F8200005C0000002B9700003C0700001F920000920300003C970000FB0400004D9700006000000009970000DA0A00001F810000B20300000E810000AA03000003E100003B00000022E1000087000000069700007F00000017970000FC07000028970000E7040000399700008C0000005D840000010000000B8100000F01000000E1000038000000188200005A00000014970000F707000025970000E304000003970000F90A0000D18400009103000005840000D603000041E1000045000000058100001C000000229700004007000011970000F0070000339700008700000000970000F00A000002840000D3030000278100002D00000016810000250000000E970000EE0700001F970000DD040000309700006C00000010840000DA03000032840000DD030000028100001A0000002D970000690000000B97000017000000438100000300000005E100003D00000051840000560000002A9700003B07000035E10000CD0300003B970000FA0400000A840000D80300000D810000110100001E8100002800000002E100003A000000059700007E00000016970000F607000027970000F1040000389700008B0000005C84000000000000</MenuUserImages>
            </CommandManager>
            <Pane-59393>
                <ID>0</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>00000000E803000080070000F9030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-59393>
            <BasePane-59393>
                <IsVisible>1</IsVisible>
            </BasePane-59393>
            <Pane-34050>
                <ID>34050</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>1</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34050>
            <BasePane-34050>
                <IsVisible>0</IsVisible>
            </BasePane-34050>
            <IarPane-34050 />
            <Pane--1>
                <ID>4294967295</ID>
                <RectRecentFloat>1501000051020000800700004A030000</RectRecentFloat>
                <RectRecentDocked>150100003A0200008007000033030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane--1>
            <BasePane--1>
                <IsVisible>1</IsVisible>
            </BasePane--1>
            <Pane-34051>
                <ID>34051</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>150100004E0200008007000033030000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34051>
            <BasePane-34051>
                <IsVisible>1</IsVisible>
            </BasePane-34051>
            <IarPane-34051>
                <col-names>
                    <item>File</item>
                    <item>Line</item>
                    <item>Messages</item>
                    <item>_I0</item>
                </col-names>
                <col-widths>
                    <item>476</item>
                    <item>40</item>
                    <item>1101</item>
                    <item>20</item>
                </col-widths>
                <BuildLogLevel>2</BuildLogLevel>
                <LiveFile>$WS_DIR$\BuildLog.log</LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>0</LiveFilterLevel>
            </IarPane-34051>
            <Pane-34053>
                <ID>34053</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>04000000CF030000FC070000BE040000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34053>
            <BasePane-34053>
                <IsVisible>0</IsVisible>
            </BasePane-34053>
            <IarPane-34053 />
            <Pane-34056>
                <ID>34056</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000004B03000080070000E8030000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34056>
            <BasePane-34056>
                <IsVisible>1</IsVisible>
            </BasePane-34056>
            <Pane-34057>
                <ID>34057</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>04000000CF030000FC070000BE040000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34057>
            <BasePane-34057>
                <IsVisible>0</IsVisible>
            </BasePane-34057>
            <IarPane-34057>
                <ColumnWidth0>666</ColumnWidth0>
                <ColumnWidth1>95</ColumnWidth1>
                <ColumnWidth2>1142</ColumnWidth2>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34057>
            <Pane-34058>
                <ID>34058</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>04000000CF030000FC070000BE040000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34058>
            <BasePane-34058>
                <IsVisible>0</IsVisible>
            </BasePane-34058>
            <IarPane-34058>
                <ColumnWidth0>666</ColumnWidth0>
                <ColumnWidth1>95</ColumnWidth1>
                <ColumnWidth2>1142</ColumnWidth2>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34058>
            <Pane-34059>
                <ID>34059</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>04000000CF030000FC070000BE040000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34059>
            <BasePane-34059>
                <IsVisible>0</IsVisible>
            </BasePane-34059>
            <IarPane-34059 />
            <Pane-34060>
                <ID>34060</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>04000000CF030000FC070000BE040000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34060>
            <BasePane-34060>
                <IsVisible>0</IsVisible>
            </BasePane-34060>
            <IarPane-34060>
                <ColumnWidth0>666</ColumnWidth0>
                <ColumnWidth1>95</ColumnWidth1>
                <ColumnWidth2>1142</ColumnWidth2>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34060>
            <Pane-34061>
                <ID>34061</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>04000000CF030000FC070000BE040000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34061>
            <BasePane-34061>
                <IsVisible>0</IsVisible>
            </BasePane-34061>
            <IarPane-34061>
                <FilterLevel>2</FilterLevel>
                <LiveFile>D:\IAR.work\MKV30_test/SourceBrowseLog.log</LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34061>
            <Pane-34063>
                <ID>34063</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>04000000CF030000FC070000BE040000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34063>
            <BasePane-34063>
                <IsVisible>0</IsVisible>
            </BasePane-34063>
            <IarPane-34063>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34063>
            <Pane-34052>
                <ID>34052</ID>
                <RectRecentFloat>000000001700000080020000A8000000</RectRecentFloat>
                <RectRecentDocked>00000000000000008002000091000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34052>
            <BasePane-34052>
                <IsVisible>0</IsVisible>
            </BasePane-34052>
            <IarPane-34052>
                <cg_type>
                    <item>2</item>
                </cg_type>
                <cg_symbol>
                    <item />
                </cg_symbol>
                <cg_user>
                    <item />
                </cg_user>
                <cg_display>
                    <item>&lt;Right-click on a symbol in the editor to show a call graph&gt;</item>
                </cg_display>
                <cg_def_file>
                    <item />
                </cg_def_file>
                <cg_def_line>
                    <item>0</item>
                </cg_def_line>
                <cg_def_col>
                    <item>0</item>
                </cg_def_col>
                <cg_call_file>
                    <item />
                </cg_call_file>
                <cg_call_line>
                    <item>0</item>
                </cg_call_line>
                <cg_call_col>
                    <item>0</item>
                </cg_call_col>
                <col-names>
                    <item>File</item>
                    <item>Function</item>
                    <item>Line</item>
                </col-names>
                <col-widths>
                    <item>200</item>
                    <item>700</item>
                    <item>100</item>
                </col-widths>
            </IarPane-34052>
            <Pane-34054>
                <ID>34054</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>1</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34054>
            <BasePane-34054>
                <IsVisible>0</IsVisible>
            </BasePane-34054>
            <IarPane-34054 />
            <Pane-34055>
                <ID>34055</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34055>
            <BasePane-34055>
                <IsVisible>0</IsVisible>
            </BasePane-34055>
            <IarPane-34055>
                <kCStatFilterHistoryKey />
                <col-names>
                    <item>Check</item>
                    <item>File</item>
                    <item>Line</item>
                    <item>Message</item>
                    <item>Severity</item>
                </col-names>
                <col-widths>
                    <item>200</item>
                    <item>200</item>
                    <item>100</item>
                    <item>500</item>
                    <item>100</item>
                </col-widths>
            </IarPane-34055>
            <Pane-34062>
                <ID>34062</ID>
                <RectRecentFloat>000000001700000080020000A8000000</RectRecentFloat>
                <RectRecentDocked>00000000000000008002000091000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34062>
            <BasePane-34062>
                <IsVisible>0</IsVisible>
            </BasePane-34062>
            <IarPane-34062>
                <SB_FileFilter>
                    <item>2</item>
                </SB_FileFilter>
                <SB_TypeFilter>
                    <item>0</item>
                </SB_TypeFilter>
                <SB_SBW_File>
                    <item>D:\IAR.work\MKV30_t2\Debug\BrowseInfo\MKV30_test.pbw</item>
                </SB_SBW_File>
                <col-names>
                    <item>File</item>
                    <item>Name</item>
                    <item>Scope</item>
                    <item>Symbol type</item>
                </col-names>
                <col-widths>
                    <item>300</item>
                    <item>300</item>
                    <item>300</item>
                    <item>300</item>
                </col-widths>
            </IarPane-34062>
            <Pane-34064>
                <ID>34064</ID>
                <RectRecentFloat>00000000170000000601000078010000</RectRecentFloat>
                <RectRecentDocked>00000000320000001101000033030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34064>
            <BasePane-34064>
                <IsVisible>1</IsVisible>
            </BasePane-34064>
            <DockingManager-256>
                <DockingPaneAndPaneDividers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ockingPaneAndPaneDividers>
            </DockingManager-256>
            <MFCToolBar-34048>
                <Name>CMSIS-Pack</Name>
                <Buttons>00200000010000000100FFFF01001100434D4643546F6F6C426172427574746F6ED18400000200000085020000FFFEFF00000000000000000000000000010000000100000000000000FFFEFF0A43004D005300490053002D005000610063006B0018000000</Buttons>
            </MFCToolBar-34048>
            <Pane-34048>
                <ID>34048</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>3A03000000000000680300001A000000</RectRecentDocked>
                <RecentFrameAlignment>8192</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>24</MRUWidth>
                <PinState>0</PinState>
            </Pane-34048>
            <BasePane-34048>
                <IsVisible>1</IsVisible>
            </BasePane-34048>
            <MFCToolBar-34049>
                <Name>Main</Name>
                <Buttons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uttons>
            </MFCToolBar-34049>
            <Pane-34049>
                <ID>34049</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>00000000000000003A0300001A000000</RectRecentDocked>
                <RecentFrameAlignment>8192</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>804</MRUWidth>
                <PinState>0</PinState>
            </Pane-34049>
            <BasePane-34049>
                <IsVisible>1</IsVisible>
            </BasePane-34049>
            <Pane-34065>
                <ID>34065</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>1</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34065>
            <BasePane-34065>
                <IsVisible>0</IsVisible>
            </BasePane-34065>
            <IarPane-34065 />
        </Desktop>
        <MDIWindows>
            <MDIClientArea-0>
                <MDITabsState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absState>
            </MDIClientArea-0>
        </MDIWindows>
    </WindowStorage>
</Workspace>

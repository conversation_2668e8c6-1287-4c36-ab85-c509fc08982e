/*
 * Copyright (c) 2013 - 2015, Freescale Semiconductor, Inc.
 * Copyright 2016-2017 NXP
 * All rights reserved.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */

#include "stdio.h"
#include "fsl_device_registers.h"
#include "fsl_debug_console.h"
#include "pin_mux.h"
#include "clock_config.h"
#include "board.h"
#include "peripherals.h"
#include "fsl_common.h"
#include "fsl_adc16.h"
#include "fsl_pit.h"
#include "fsl_dac.h"
#include "Lab_pwm.h"


/*******************************************************************************
 * Definitions
 ******************************************************************************/
#define RESET	0
#define SET		1

uint32_t msCounter=0;
uint32_t ms10Counter=0;
int16_t Vin, Vin_t, Vcmd;
float Vp, Vi, Vd;
uint16_t Vout=0;
uint8_t motor_dir, motor_dir_t;
uint8_t motor_speed_target=0;		// PWM duty cycle, range: 0~100
uint8_t motor_speed_target_t=0;
uint8_t motor_speed_set = 0; // Default speed set value
uint8_t output = 0; //PID PWM duty cycle

uint8_t DC1 = 0, DC1_t = 0;
uint8_t DC2 = 0, DC2_t = 0;
int16_t error=0;

typedef enum {
  P_CTRL,
  PI_CTRL,
  PD_CTRL,
  PID_CTRL,
  MANUAL		// manual control, setting speed by QES_value
} MODE;
MODE control_mode = P_CTRL;		// default control mode: proportional

// PID parameters
float Kp_input = 1.0, Ki_input = 0.0, Kd_input = 0.0;
float Kp, Ki, Kd;
float integral = 0, prev_error = 0;
uint16_t is_PID_step_response = 0;
uint16_t is_PID_step_response_2 = 0;
uint16_t QES_value=0, QES_value_t;
int16_t offset_test = 0;

void PID_init() {
    prev_error = 0;
    integral = 0;
}

void PID_param_set() {
  Kp = Kp_input*500.0/32768;
  Ki = Ki_input*500.0/32768.0;
  Kd = Kd_input*500.0/32768.0;
}

float act_value = 1;

float PID_update(float setpoint, float actual_value) {
    float error = setpoint - actual_value;
    integral += error;
    float derivative = error - prev_error;
    act_value = Kp;
    float output = Kp*error + Ki*integral + Kd*derivative;
    prev_error = error;
    msCounter++;
    return output;
}

float output_PID = 0;

//adc16_channel_config_t adc16ChannelConfigStruct;
adc16_channel_config_t ADC_channelsConfig = {
	.channelNumber = 0U,
	.enableDifferentialConversion = true,
	.enableInterruptOnConversionCompleted = false,
};

/*******************************************************************************
 * Prototypes
 ******************************************************************************/

/*******************************************************************************
 * Code
 ******************************************************************************/
void delay()
{
	uint32_t delay_count=800000;
	
	while(--delay_count);
}

/*!
 * @brief Main function
 */
int main(void)
{
	uint16_t counter;
	uint8_t dc0, dc1, dc2, dc3;
	MODE control_mode_t;		// used to monitor the change of control mode

	// variables for Quadrature Encoder Switch (QES)
	uint8_t state_pha, state_phb;
	uint8_t state_pha_t, state_phb_t;

	/* Init board hardware. */
	BOARD_InitBootPins();
	BOARD_InitBootClocks();
	BOARD_InitPeripherals();

	PID_init();

	state_pha = PHA();			state_phb = PHB();
	state_pha_t = state_pha;	state_phb_t = state_phb;
	QES_value_t = QES_value;

	while (1)
	{
    // use HMI components properly to organize your program in an effective as well as friendly way.

//    control_mode = PID_CTRL;
//    PID_param_set();

    // Read Vin as error
//		ADC16_SetChannelConfig(ADC0, ADC0_CH0_CONTROL_GROUP, &ADC_channelsConfig);
//		while (0U == (kADC16_ChannelConversionDoneFlag & ADC16_GetChannelStatusFlags(ADC0, ADC0_CH0_CONTROL_GROUP)))
//		{
//		}
//		Vin = ADC16_GetChannelConversionValue(ADC0, ADC0_CH0_CONTROL_GROUP);
//		error = Vin;
		state_pha = PHA();
		state_phb = PHB();
		if((state_pha_t != state_pha) || (state_phb_t != state_phb))
		{
			if(state_phb_t == state_phb)
			{
				if(SET == state_phb)
				{
					if(RESET == state_pha) QES_value++;
					else if(QES_value>0) QES_value--;
				}
				else
				{
					if(SET == state_pha) QES_value++;
					else if(QES_value>0) QES_value--;
				}
			}
			else
			{
				if(SET == state_pha)
				{
					if(SET == state_phb) QES_value++;
					else if(QES_value>0) QES_value--;
				}
				else
				{
					if(RESET == state_pha) QES_value++;
					else if(QES_value>0) QES_value--;
				}
			}
			state_pha_t = state_pha;
			state_phb_t = state_phb;
		}

		error = QES_value;
	  
		if(DC1_t != DC1)
		{
			motor1_set(DC1, 1);
			DC1_t = DC1;
		}

		if(DC2_t != DC2)
		{
			motor2_set(DC2, 1);
			DC2_t = DC2;
		}

//    if(Vin >= 0)	// suppose to drive motor in CW direction
//    {
//     motor_dir = 0;
//     motor_speed_target = (Vin+1)*500/32768;
//    }
//    else			// drive motor in CCW direction
//    {
//     motor_dir = 1;
//     motor_speed_target = -(Vin+1)*500/32768;
//    }
//
//    if(motor_speed_target > 100)
//       motor_speed_target = 100;
//    if(motor_speed_target_t != motor_speed_target)
//    {
//       motor1_set(motor_speed_target, motor_dir);
//       motor_speed_target_t = motor_speed_target;
//    }
//    if(QES_value_t != QES_value)
//    {
//       motor2_set(QES_value, !(motor_dir));
//       QES_value_t = QES_value;
//    }
  }
}

/* PIT0_IRQn interrupt handler */
void PIT_1ms_IRQHANDLER(void) {
	uint32_t intStatus;
	/* Reading all interrupt flags of status register */
	intStatus = PIT_GetStatusFlags(PIT_PERIPHERAL, PIT_CHANNEL_0);
	PIT_ClearStatusFlags(PIT_PERIPHERAL, PIT_CHANNEL_0, intStatus);

	/* Place your code here */


	/* Add for ARM errata 838869, affects Cortex-M4, Cortex-M4F
		Store immediate overlapping exception return operation might vector to incorrect interrupt. */
	#if defined __CORTEX_M && (__CORTEX_M == 4U)
		__DSB();
	#endif
}

/* PIT1_IRQn interrupt handler */
void PIT_10ms_IRQHANDLER(void) {
	uint32_t intStatus;
	/* Reading all interrupt flags of status register */
	intStatus = PIT_GetStatusFlags(PIT_PERIPHERAL, PIT_CHANNEL_1);
	PIT_ClearStatusFlags(PIT_PERIPHERAL, PIT_CHANNEL_1, intStatus);

	/* Place your code here */
	output_PID = PID_update(0, Vin);
	if(is_PID_step_response)
	{
		LED_ON();
	}
   else
   {
      LED_OFF();
   }

	/* Add for ARM errata 838869, affects Cortex-M4, Cortex-M4F
		Store immediate overlapping exception return operation might vector to incorrect interrupt. */
	#if defined __CORTEX_M && (__CORTEX_M == 4U)
		__DSB();
	#endif
}

#define CmdDataLenMax	(7U)
#define OutDataLenMax	(10U)
uint8_t CmdDataBuffer[CmdDataLenMax];	// buffer to hold cmd data from PC
uint8_t OutDataBuffer[OutDataLenMax];	// buffer to hold response data to PC
uint8_t CmdDataLen;		// PC cmd data length counter
uint8_t CmdChar;			// PC cmd 
bool CmdDataReady;		// new cmd data flag, 1: new cmd received
bool CmdReceiving = false;

void PcCmdProcess(void)
{
	if ((CmdDataLen==6) && (CmdDataBuffer[4]==0x0D) && (CmdDataBuffer[5]==0x0A))
	{
		// cmd 0x01: setting duty cycles
		if((CmdDataBuffer[0]==0x01) && (CmdDataBuffer[1]==0x01))
		{
			if((CmdDataBuffer[2]<=100U) && (CmdDataBuffer[2]<=100U))
			{
				// set dc1/dc2
				DC1 = CmdDataBuffer[2];
				DC2 = CmdDataBuffer[3];
				OutDataBuffer[0] = 0x01;
				OutDataBuffer[1] = 0x01;
				OutDataBuffer[2] = DC1;
				OutDataBuffer[3] = DC2;
				OutDataBuffer[4] = 0x0D;
				OutDataBuffer[5] = 0x0A;
				OutDataBuffer[6] = 0xFF;
				UART_WriteBlocking(UART1_PERIPHERAL, OutDataBuffer, 7);
			}
		}
	}
	else if ((CmdDataLen==4) && (CmdDataBuffer[2]==0x0D) && (CmdDataBuffer[3]==0x0A))
	{
		// cmd 0x02: readback duty cycles
		if((CmdDataBuffer[0]==0x01) && (CmdDataBuffer[1]==0x02))
		{
			OutDataBuffer[0] = 0x01;
			OutDataBuffer[1] = 0x02;
			OutDataBuffer[2] = DC1;
			OutDataBuffer[3] = DC2;
			OutDataBuffer[4] = 0x0D;
			OutDataBuffer[5] = 0x0A;
			OutDataBuffer[6] = 0xFF;
			UART_WriteBlocking(UART1_PERIPHERAL, OutDataBuffer, 7);
		}
		// cmd 0x10: readback shaft angle error
		else if((CmdDataBuffer[0]==0x01) && (CmdDataBuffer[1]==0x10))
		{
			OutDataBuffer[0] = 0x01;
			OutDataBuffer[1] = 0x10;
			OutDataBuffer[2] = (error>0) ? 0x00 : 0xFF;
			OutDataBuffer[3] = (error>>8) & 0xFF;
			OutDataBuffer[4] = error & 0xFF;
			OutDataBuffer[5] = 0x0D;
			OutDataBuffer[6] = 0x0A;
			OutDataBuffer[7] = 0xFF;
			UART_WriteBlocking(UART1_PERIPHERAL, OutDataBuffer, 8);
		}
	}
}
									  
/* UART1_RX_TX_IRQn interrupt handler */
void UART1_SERIAL_RX_TX_IRQHANDLER(void) {
	uint32_t intStatus;
	uint8_t data;
	
	/* Reading all interrupt flags of status registers */
	intStatus = UART_GetStatusFlags(UART1_PERIPHERAL);

  /* Place your code here */
	if ((kUART_RxDataRegFullFlag | kUART_RxOverrunFlag) & intStatus)	// If new data arrived?
	{
		data = UART_ReadByte(UART1_PERIPHERAL);
		//UART_WriteByte(UART0_PERIPHERAL, data);	// send data back for loop test

		/* PC command data framing */
		if ((data==0x01) && (CmdReceiving==false)) 	// start of a data frame?
		{
			CmdDataLen = 0;			// reset cmd data counter
			CmdDataBuffer[0] = data;
			CmdReceiving = true;
		}
		else if (CmdReceiving  == true)
		{
			CmdDataLen++;
			CmdDataBuffer[CmdDataLen] = data;
			if (CmdDataLen < CmdDataLenMax)	// valid cmd string
			{
				if (data == 0xFF)
				{
					CmdDataReady = true;
					CmdReceiving = false;	// wait for next cmd
					PcCmdProcess();		// cmd processing
				}
			}
			else	// no cmd received when data buffer overflows
			{
				CmdDataLen = 0;			// reset cmd data counter
				CmdReceiving = false;
			}
		}
	}

	UART_ClearStatusFlags(UART1_PERIPHERAL, intStatus);

	/* Add for ARM errata 838869, affects Cortex-M4, Cortex-M4F
	Store immediate overlapping exception return operation might vector to incorrect interrupt. */
	#if defined __CORTEX_M && (__CORTEX_M == 4U)
		__DSB();
	#endif
}

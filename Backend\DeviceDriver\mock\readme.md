# 虚拟电机设备文档

## 概述

虚拟电机设备是一个模拟真实电机设备的软件组件，用于在没有物理硬件的情况下进行系统测试和开发。该驱动支持多种数据传输方式（UDP、Kafka、MQTT），并提供设备控制和数据生成功能。

## 架构设计

### 核心组件

1. **VirtualMotor**: 主要的虚拟电机类，负责整体协调和管理
2. **DataSender**: 数据发送器抽象基类，定义了数据传输接口
3. **UDPSender**: UDP数据发送器实现
4. **KafkaSender**: Kafka数据发送器实现
5. **MQTTSender**: MQTT数据发送器实现（待完善）

## 功能模块

### 1. 设备管理器

#### handle_server_commands()

处理来自服务器的控制和读取指令，支持以下命令：

- **0x0100**: 正弦波参数设置（幅值、频率、相位）
- **0x0101**: 查看设备状态（是否在发送数据）
- **0x0102**: 设备开始发送数据
- **0x0103**: 设备停止发送数据

#### 通信协议

**请求帧格式**:

```
SEQ/ACK(2字节) + HEADER(2字节) + SEQ/ACK ID(2字节) + [参数数据]
```

**响应帧格式**:

```
SEQ/ACK(2字节) + HEADER(2字节) + SEQ/ACK ID(2字节) + 状态码(4字节)
```

### 2. 数据管理器

#### generate_motor_data()

根据当前时间和正弦波参数生成模拟电机数据：

- 使用正弦函数生成周期性数据
- 支持自定义幅值、频率、相位参数
- 输出4字节浮点数格式

#### send_motor_data()

发送虚拟电机数据到指定目标：

- 支持多种传输方式（UDP、Kafka、MQTT）
- 可配置发送频率（默认100ms间隔）
- 支持动态启停控制

## 使用方法

### 基本使用

```python
# 使用UDP方式（默认）
device = VirtualMotor(send_mode='udp')
device.start()
```

### 高级配置

#### UDP模式

```python
device = VirtualMotor(
    send_mode='udp',            # 传输模式
    data_ip='127.0.0.1',        # 数据目标IP
    data_port=6001              # 数据目标端口
)
device.start()
```

#### Kafka模式

```python
device = VirtualMotor(
    send_mode='kafka',
    kafka_bootstrap_servers='*************:9092',
    kafka_topic='motor_data'
)
device.start()
```

### 正弦波参数

| 参数      | 类型 | 默认值 | 说明             |
| --------- | ---- | ------ | ---------------- |
| amplitude | int  | 1000   | 正弦波幅值       |
| frequency | int  | 50     | 正弦波频率（Hz） |
| phase     | int  | 0      | 正弦波相位（度） |

## 数据格式

### 生成的电机数据

- **格式**: 4字节IEEE 754浮点数
- **内容**: 基于正弦函数的模拟电机数值
- **公式**: `amplitude * sin(2π * frequency * time + phase_rad)`

### Kafka消息格式

```json
{
    "timestamp": 1234567890.123,
    "data": "base64编码的二进制数据"
}
```

## 依赖项

```
kafka-python (第三方)
```

## 安装依赖

```bash
pip install kafka-python
```

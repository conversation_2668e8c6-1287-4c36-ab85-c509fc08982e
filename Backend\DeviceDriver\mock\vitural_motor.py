import socket
import threading
import time
import struct
import math
import json
import base64
from abc import ABC, abstractmethod
from kafka import KafkaProducer

class DataSender(ABC):
    """数据发送器抽象基类"""
    @abstractmethod
    def connect(self):
        pass
    
    @abstractmethod
    def send(self, data):
        pass
    
    @abstractmethod
    def close(self):
        pass


class UDPSender(DataSender):
    """UDP数据发送器"""
    def __init__(self, target_ip, target_port):
        self.target_ip = target_ip
        self.target_port = target_port
        self.socket = None
    
    def connect(self):
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    def send(self, data):
        if self.socket:
            self.socket.sendto(data, (self.target_ip, self.target_port))
    
    def close(self):
        if self.socket:
            self.socket.close()
            self.socket = None


class KafkaSender(DataSender):
    """Kafka数据发送器"""
    def __init__(self, bootstrap_servers, topic):
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.producer = None
    
    def connect(self):
        self.producer = KafkaProducer(
            bootstrap_servers=self.bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode('utf-8')
        )
    
    def send(self, data):
        if self.producer:
            message = {
                'timestamp': time.time(),
                'data': base64.b64encode(data).decode('utf-8')
            }
            self.producer.send(self.topic, message)
    
    def close(self):
        if self.producer:
            self.producer.close()
            self.producer = None


class MQTTSender(DataSender):
    """MQTT数据发送器 - 扩展方案"""
    def __init__(self, broker, port, topic):
        self.broker = broker
        self.port = port
        self.topic = topic
        self.client = None
    
    def connect(self):
        # TODO: 实现MQTT连接逻辑
        pass
    
    def send(self, data):
        # TODO: 实现MQTT数据发送逻辑
        pass
    
    def close(self):
        # TODO: 实现MQTT连接关闭逻辑
        pass

class VirtualMotor:
    def __init__(self, server_ip='127.0.0.1', server_port=8, client_ip='127.0.0.1', client_port=8002, device_ip='127.0.0.1', device_port=6005, 
                send_mode='udp',
                data_ip='127.0.0.1', data_port=6001, 
                kafka_bootstrap_servers='localhost:9092', kafka_topic='motor_data',
                mqtt_broker='', mqtt_port=0, mqtt_topic=''):
        self.server_ip = server_ip
        self.server_port = server_port
        self.client_ip = client_ip
        self.client_port = client_port
        self.device_ip = device_ip
        self.device_port = device_port

        self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.client_socket.bind((self.client_ip, self.client_port))
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.server_socket.bind((self.server_ip, self.server_port))

        # 发送方式配置
        self.send_mode = send_mode.lower()  # 'udp', 'kafka', 'mqtt'
        
        # 初始化数据发送器
        self.data_sender = self._create_data_sender(
            send_mode, 
            data_ip, data_port, 
            kafka_bootstrap_servers, kafka_topic,
            mqtt_broker, mqtt_port, mqtt_topic
        )
        self.data_sender.connect()
        
        # 设备参数
        self.running = True
        self.data_sending = True
        
        # 正弦波参数
        self.amplitude = 1000
        self.frequency = 50
        self.phase = 0
        self.start_time = time.time()
        
    def handle_server_commands(self):
        """处理来自服务器的控制和读取指令"""
        while self.running:
            try:
                data, addr = self.server_socket.recvfrom(1024)
                # 解析接收到的数据：SEQ/ACK(2字节) + HEADER(2字节) + SEQ/ACK ID(2字节)
                print(f"receive data: {data}")
                print(f"seq addr: {addr}")
                seq_ack, header, seq_ack_id = struct.unpack('!HHH', data[:6])
                
                # 只处理控制帧(0x0000)
                if seq_ack == 0x0000:
                    # 根据HEADER类型构造应答帧(0x0001)
                    if header == 0x0100:  # 正弦波参数设置
                        # 解析正弦波参数：幅值(2字节) + 频率(2字节) + 相位(2字节)
                        if len(data) >= 12:
                            amplitude, frequency, phase = struct.unpack('!HHH', data[6:12])
                            self.amplitude = amplitude
                            self.frequency = frequency
                            self.phase = phase
                            print(f"设置正弦波参数: 幅值={amplitude}, 频率={frequency}, 相位={phase}")
                        response = struct.pack('!HHHI', 0x0001, 0x0100, seq_ack_id, 0x00000000)  # 无错误
                    elif header == 0x0101:  # 查看设备状态（是否在发数据）
                        status = 1 if self.data_sending else 0
                        response = struct.pack('!HHHI', 0x0001, 0x0101, seq_ack_id, status)
                        print(f"查看设备状态: {'正在发送数据' if self.data_sending else '未发送数据'}")
                    elif header == 0x0102:  # 设备开始发数据
                        self.data_sending = True
                        response = struct.pack('!HHHI', 0x0001, 0x0102, seq_ack_id, 0x00000000)  # 无错误
                        print("设备开始发送数据")
                    elif header == 0x0103:  # 设备停止发数据
                        self.data_sending = False
                        response = struct.pack('!HHHI', 0x0001, 0x0103, seq_ack_id, 0x00000000)  # 无错误
                        print("设备停止发送数据")
                    else:
                        # 未知指令，返回错误
                        response = struct.pack('!HHHI', 0x0001, header, seq_ack_id, 0xFFFFFFFF)  # 错误码
                        print(f"未知指令: header={hex(header)}")
                    time.sleep(1)
                    # 发送响应
                    self.client_socket.sendto(response, (self.device_ip, self.device_port))
                    print("发送响应成功")
                    print(f"response data: {response}")
                    
            except Exception as e:
                print(f'处理指令错误: {e}')
    
    def generate_motor_data(self):
        """生成模拟电机数据"""
        current_time = time.time() - self.start_time
        
        # 将相位从度转换为弧度
        phase_rad = math.radians(self.phase)
        sin_value = self.amplitude * math.sin(2 * math.pi * self.frequency * current_time + phase_rad)
        
        # 将正弦值转换为整数并打包为4字节数据
        sin_data = struct.pack('!f', sin_value)
        
        return sin_data
    
    def _create_data_sender(self, send_mode, data_ip, data_port, 
                           kafka_bootstrap_servers, kafka_topic,
                           mqtt_broker, mqtt_port, mqtt_topic):
        """创建数据发送器"""
        if send_mode == 'udp':
            return UDPSender(data_ip, data_port)
        elif send_mode == 'kafka':
            return KafkaSender(kafka_bootstrap_servers, kafka_topic)
        elif send_mode == 'mqtt':
            return MQTTSender(mqtt_broker, mqtt_port, mqtt_topic)
        else:
            raise ValueError(f"不支持的发送方式: {send_mode}")
    
    def send_motor_data(self):
        """发送模拟电机数据"""
        while self.running:
            if self.data_sending:
                try:
                    data = self.generate_motor_data()
                    self.data_sender.send(data)
                    print(f"已通过{self.send_mode.upper()}发送模拟电机数据，长度: {len(data)}字节")
                except Exception as e:
                    print(f"发送数据时出错: {e}")

                time.sleep(0.1)  # 100ms间隔发送
            else:
                # 如果不发送数据，稍微延长等待时间
                time.sleep(0.5)
    
    def start(self):
        """启动虚拟设备"""
        command_thread = threading.Thread(target=self.handle_server_commands)
        command_thread.daemon = True
        command_thread.start()
        
        data_thread = threading.Thread(target=self.send_motor_data)
        data_thread.daemon = True
        data_thread.start()
        
        print('虚拟设备已启动...')
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """停止虚拟设备"""
        self.running = False
        self.data_sending = False
        self.client_socket.close()
        self.server_socket.close()

        if self.data_sender:
            self.data_sender.close()
        
        print('虚拟设备已停止')

if __name__ == '__main__':
    # device = VirtualMotor(send_mode='udp')  # 默认使用UDP方式
    device = VirtualMotor(send_mode='kafka', kafka_bootstrap_servers='*************:9092', kafka_topic='motor_data')
    # device = VirtualMotor(send_mode='mqtt', mqtt_broker='localhost', mqtt_port=1883, mqtt_topic='motor/data')
    device.start()
Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to bldc_tim.o(i.TIM1_UP_TIM10_IRQHandler) for TIM1_UP_TIM10_IRQHandler
    startup_stm32f407xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to bldc_tim.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to adc.o(i.DMA2_Stream4_IRQHandler) for DMA2_Stream4_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.main) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to sys.o(i.sys_stm32_clock_init) for sys_stm32_clock_init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.usart_init) for usart_init
    main.o(i.main) refers to led.o(i.led_init) for led_init
    main.o(i.main) refers to key.o(i.key_init) for key_init
    main.o(i.main) refers to lcd.o(i.lcd_init) for lcd_init
    main.o(i.main) refers to bldc.o(i.bldc_init) for bldc_init
    main.o(i.main) refers to bldc.o(i.bldc_ctrl) for bldc_ctrl
    main.o(i.main) refers to adc.o(i.adc_nch_dma_init) for adc_nch_dma_init
    main.o(i.main) refers to main.o(i.parse_command) for parse_command
    main.o(i.main) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(i.main) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.main) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.main) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.main) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.main) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.main) refers to lcd.o(i.lcd_show_string) for lcd_show_string
    main.o(i.main) refers to bldc.o(i.get_temp) for get_temp
    main.o(i.main) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to lcd.o(.data) for g_point_color
    main.o(i.main) refers to usart.o(.data) for g_usart_rx_sta
    main.o(i.main) refers to usart.o(.bss) for g_usart_rx_buf
    main.o(i.main) refers to bldc.o(.data) for g_bldc_motor1
    main.o(i.main) refers to adc.o(.bss) for g_adc_val
    main.o(i.main) refers to bldc_tim.o(.data) for adc_amp_un
    main.o(i.parse_command) refers to _scanf_int.o(.text) for _scanf_int
    main.o(i.parse_command) refers to _scanf_str.o(.text) for _scanf_string
    main.o(i.parse_command) refers to __0sscanf.o(.text) for __0sscanf
    main.o(i.parse_command) refers to strcmpv7m.o(.text) for strcmp
    main.o(i.parse_command) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    main.o(i.parse_command) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    main.o(i.parse_command) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    main.o(i.parse_command) refers to bldc.o(i.stop_motor1) for stop_motor1
    main.o(i.parse_command) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.parse_command) refers to bldc.o(i.start_motor1) for start_motor1
    main.o(i.parse_command) refers to main.o(.data) for last_dir
    main.o(i.parse_command) refers to bldc.o(.data) for g_bldc_motor1
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    delay.o(i.HAL_Delay) refers to delay.o(i.delay_ms) for delay_ms
    delay.o(i.delay_init) refers to delay.o(.data) for g_fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for g_fac_us
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    sys.o(i.sys_stm32_clock_init) refers to stm32f4xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.rrx_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_RxCpltCallback) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.data) for g_usart_rx_sta
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for g_usart_rx_buf
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for g_uart1_handle
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for g_rx_buffer
    usart.o(i._sys_command_string) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i._ttywrch) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.usart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.usart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.usart_init) refers to usart.o(.bss) for g_uart1_handle
    usart.o(i.usart_init) refers to usart.o(.data) for g_rx_buffer
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback) for USART_DMATxAbortCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback) for USART_DMARxAbortCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_DeInit) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit) for HAL_USART_MspDeInit
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Receive_IT) for USART_Receive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) for USART_TransmitReceive_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_Transmit_IT) for USART_Transmit_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_EndTransmit_IT) for USART_EndTransmit_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError) for USART_DMAAbortOnError
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.HAL_USART_MspInit) for HAL_USART_MspInit
    stm32f4xx_hal_usart.o(i.HAL_USART_Init) refers to stm32f4xx_hal_usart.o(i.USART_SetConfig) for USART_SetConfig
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32f4xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.USART_EndTxTransfer) for USART_EndTxTransfer
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.USART_EndRxTransfer) for USART_EndRxTransfer
    stm32f4xx_hal_usart.o(i.USART_DMAError) refers to stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback) for HAL_USART_RxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback) refers to stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback) for HAL_USART_TxHalfCpltCallback
    stm32f4xx_hal_usart.o(i.USART_EndTransmit_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_Receive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_usart.o(i.USART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT) refers to stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit) for FSMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to lcd.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init) for FSMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init) for FSMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable) for FSMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable) for FSMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to bldc_tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to bldc_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    led.o(i.led_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    led.o(i.led_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    key.o(i.key_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    key.o(i.key_scan) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.key_scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.key_scan) refers to key.o(.data) for key_up
    lcd.o(i.HAL_SRAM_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.lcd_clear) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_clear) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_clear) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_color_fill) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_color_fill) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_display_dir) refers to lcd.o(i.lcd_scan_dir) for lcd_scan_dir
    lcd.o(i.lcd_display_dir) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_display_off) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_display_off) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_display_on) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_display_on) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_draw_circle) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_draw_hline) refers to lcd.o(i.lcd_fill) for lcd_fill
    lcd.o(i.lcd_draw_hline) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_draw_line) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_draw_point) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_draw_point) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_draw_rectangle) refers to lcd.o(i.lcd_draw_line) for lcd_draw_line
    lcd.o(i.lcd_ex_ili9341_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ili9341_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ili9341_reginit) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_ili9806_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ili9806_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ili9806_reginit) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_nt35310_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_nt35310_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_nt35310_reginit) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_nt35510_reginit) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_ex_nt35510_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_nt35510_reginit) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.lcd_ex_ssd1963_reginit) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_st7789_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_st7789_reginit) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_st7789_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ex_st7796_reginit) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ex_st7796_reginit) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_ex_st7796_reginit) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_fill) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_fill) refers to lcd.o(i.lcd_write_ram_prepare) for lcd_write_ram_prepare
    lcd.o(i.lcd_fill_circle) refers to lcd.o(i.lcd_draw_hline) for lcd_draw_hline
    lcd.o(i.lcd_init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(i.lcd_init) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(i.lcd_init) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(i.lcd_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd.o(i.lcd_init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    lcd.o(i.lcd_init) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_rd_data) for lcd_rd_data
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_init) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_st7789_reginit) for lcd_ex_st7789_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ili9341_reginit) for lcd_ex_ili9341_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_nt35310_reginit) for lcd_ex_nt35310_reginit
    lcd.o(i.lcd_init) refers to lcd.o(.bss) for g_sram_handle
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_st7796_reginit) for lcd_ex_st7796_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_nt35510_reginit) for lcd_ex_nt35510_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ili9806_reginit) for lcd_ex_ili9806_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ex_ssd1963_reginit) for lcd_ex_ssd1963_reginit
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_ssd_backlight_set) for lcd_ssd_backlight_set
    lcd.o(i.lcd_init) refers to stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init) for FSMC_NORSRAM_Extended_Timing_Init
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_display_dir) for lcd_display_dir
    lcd.o(i.lcd_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd.o(i.lcd_init) refers to lcd.o(i.lcd_clear) for lcd_clear
    lcd.o(i.lcd_rd_data) refers to lcd.o(i.lcd_opt_delay) for lcd_opt_delay
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_set_cursor) for lcd_set_cursor
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_read_point) refers to lcd.o(i.lcd_rd_data) for lcd_rd_data
    lcd.o(i.lcd_read_point) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_write_reg) for lcd_write_reg
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_scan_dir) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_scan_dir) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_set_cursor) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_set_cursor) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_set_cursor) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_set_window) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_set_window) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_set_window) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_show_char) refers to lcd.o(i.lcd_draw_point) for lcd_draw_point
    lcd.o(i.lcd_show_char) refers to lcd.o(.constdata) for asc2_1206
    lcd.o(i.lcd_show_char) refers to lcd.o(.data) for g_back_color
    lcd.o(i.lcd_show_char) refers to lcd.o(.bss) for lcddev
    lcd.o(i.lcd_show_num) refers to lcd.o(i.lcd_pow) for lcd_pow
    lcd.o(i.lcd_show_num) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_show_string) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_show_xnum) refers to lcd.o(i.lcd_pow) for lcd_pow
    lcd.o(i.lcd_show_xnum) refers to lcd.o(i.lcd_show_char) for lcd_show_char
    lcd.o(i.lcd_ssd_backlight_set) refers to lcd.o(i.lcd_wr_regno) for lcd_wr_regno
    lcd.o(i.lcd_ssd_backlight_set) refers to lcd.o(i.lcd_wr_data) for lcd_wr_data
    lcd.o(i.lcd_ssd_backlight_set) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(i.lcd_ssd_backlight_set) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(i.lcd_ssd_backlight_set) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(i.lcd_write_ram_prepare) refers to lcd.o(.bss) for lcddev
    bldc.o(i.bldc_ctrl) refers to bldc.o(.data) for g_bldc_motor1
    bldc.o(i.bldc_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    bldc.o(i.bldc_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.bldc_init) refers to bldc.o(i.hall_gpio_init) for hall_gpio_init
    bldc.o(i.bldc_init) refers to bldc_tim.o(i.atim_timx_oc_chy_init) for atim_timx_oc_chy_init
    bldc.o(i.bldc_init) refers to bldc_tim.o(i.btim_timx_int_init) for btim_timx_int_init
    bldc.o(i.calc_adc_val) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    bldc.o(i.calc_adc_val) refers to adc.o(.bss) for g_adc_value
    bldc.o(i.check_hall_dir) refers to bldc.o(.constdata) for hall_ccw_table
    bldc.o(i.get_temp) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bldc.o(i.get_temp) refers to log.o(i.__hardfp_log) for __hardfp_log
    bldc.o(i.get_temp) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    bldc.o(i.hall_gpio_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    bldc.o(i.hallsensor_get_state) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    bldc.o(i.hallsensor_get_state) refers to bldc.o(.data) for state
    bldc.o(i.m1_uhvl) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.m1_uhvl) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.m1_uhvl) refers to bldc.o(.data) for g_bldc_motor1
    bldc.o(i.m1_uhwl) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.m1_uhwl) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.m1_uhwl) refers to bldc.o(.data) for g_bldc_motor1
    bldc.o(i.m1_vhul) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.m1_vhul) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.m1_vhul) refers to bldc.o(.data) for g_bldc_motor1
    bldc.o(i.m1_vhwl) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.m1_vhwl) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.m1_vhwl) refers to bldc.o(.data) for g_bldc_motor1
    bldc.o(i.m1_whul) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.m1_whul) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.m1_whul) refers to bldc.o(.data) for g_bldc_motor1
    bldc.o(i.m1_whvl) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.m1_whvl) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.m1_whvl) refers to bldc.o(.data) for g_bldc_motor1
    bldc.o(i.start_motor1) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.start_motor1) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    bldc.o(i.start_motor1) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.stop_motor1) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bldc.o(i.stop_motor1) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) for HAL_TIM_PWM_Stop
    bldc.o(i.stop_motor1) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc.o(i.uemf_edge) refers to bldc.o(.data) for oldval
    bldc.o(.data) refers to bldc.o(i.m1_uhwl) for m1_uhwl
    bldc.o(.data) refers to bldc.o(i.m1_vhul) for m1_vhul
    bldc.o(.data) refers to bldc.o(i.m1_vhwl) for m1_vhwl
    bldc.o(.data) refers to bldc.o(i.m1_whvl) for m1_whvl
    bldc.o(.data) refers to bldc.o(i.m1_uhvl) for m1_uhvl
    bldc.o(.data) refers to bldc.o(i.m1_whul) for m1_whul
    bldc_tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    bldc_tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    bldc_tim.o(i.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to bldc.o(i.hallsensor_get_state) for hallsensor_get_state
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to bldc.o(i.stop_motor1) for stop_motor1
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to bldc.o(i.uemf_edge) for uemf_edge
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to bldc.o(i.check_hall_dir) for check_hall_dir
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to bldc.o(.data) for g_bldc_motor1
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to adc.o(.bss) for g_adc_val
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to bldc_tim.o(.bss) for adc_val_m1
    bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback) refers to bldc_tim.o(.data) for adc_amp
    bldc_tim.o(i.TIM1_UP_TIM10_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    bldc_tim.o(i.TIM1_UP_TIM10_IRQHandler) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc_tim.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    bldc_tim.o(i.TIM6_DAC_IRQHandler) refers to bldc_tim.o(.bss) for timx_handler
    bldc_tim.o(i.atim_timx_oc_chy_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    bldc_tim.o(i.atim_timx_oc_chy_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    bldc_tim.o(i.atim_timx_oc_chy_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    bldc_tim.o(i.atim_timx_oc_chy_init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    bldc_tim.o(i.atim_timx_oc_chy_init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    bldc_tim.o(i.atim_timx_oc_chy_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    bldc_tim.o(i.atim_timx_oc_chy_init) refers to bldc_tim.o(.bss) for g_atimx_handle
    bldc_tim.o(i.btim_timx_int_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    bldc_tim.o(i.btim_timx_int_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    bldc_tim.o(i.btim_timx_int_init) refers to bldc_tim.o(.bss) for timx_handler
    adc.o(i.DMA2_Stream4_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    adc.o(i.DMA2_Stream4_IRQHandler) refers to adc.o(.bss) for g_dma_nch_adc_handle
    adc.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc.o(i.HAL_ADC_ConvCpltCallback) refers to bldc.o(i.calc_adc_val) for calc_adc_val
    adc.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for g_adc_nch_dma_handle
    adc.o(i.adc_get_result_average) refers to adc.o(.bss) for g_adc_value
    adc.o(i.adc_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.adc_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.adc_init) refers to adc.o(.bss) for g_adc_nch_dma_handle
    adc.o(i.adc_nch_dma_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc.o(i.adc_nch_dma_init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.adc_nch_dma_init) refers to adc.o(i.adc_init) for adc_init
    adc.o(i.adc_nch_dma_init) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.adc_nch_dma_init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.adc_nch_dma_init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.adc_nch_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc.o(i.adc_nch_dma_init) refers to adc.o(.bss) for g_dma_nch_adc_handle
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    log.o(i.__hardfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.__hardfp_log) refers to _rserrno.o(.text) for __set_errno
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.__hardfp_log) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    log.o(i.__hardfp_log) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log.o(i.__hardfp_log) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log.o(i.__hardfp_log) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log.o(i.__hardfp_log) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log.o(i.__hardfp_log) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log.o(i.__hardfp_log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.__hardfp_log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.__hardfp_log) refers to log.o(.constdata) for .constdata
    log.o(i.__softfp_log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(i.log) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log.o(i.log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.____hardfp_log$lsc) refers to _rserrno.o(.text) for __set_errno
    log_x.o(i.____hardfp_log$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    log_x.o(i.____hardfp_log$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    log_x.o(i.____hardfp_log$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    log_x.o(i.____hardfp_log$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    log_x.o(i.____hardfp_log$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    log_x.o(i.____hardfp_log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.____hardfp_log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.____hardfp_log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(i.____softfp_log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(i.__log$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    log_x.o(i.__log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to usart.o(i._ttywrch) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to usart.o(i._sys_command_string) for _sys_command_string
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.HAL_Delay), (12 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(i.sys_intx_disable), (4 bytes).
    Removing sys.o(i.sys_intx_enable), (4 bytes).
    Removing sys.o(i.sys_msr_msp), (10 bytes).
    Removing sys.o(i.sys_nvic_set_vector_table), (16 bytes).
    Removing sys.o(i.sys_soft_reset), (68 bytes).
    Removing sys.o(i.sys_standby), (56 bytes).
    Removing sys.o(i.sys_wfi_set), (4 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i._sys_command_string), (6 bytes).
    Removing usart.o(i._ttywrch), (4 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (40 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (64 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (346 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (94 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (124 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (356 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (244 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (192 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (144 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (416 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (384 bytes).
    Removing stm32f4xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Abort), (126 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Abort_IT), (220 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAPause), (50 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAResume), (50 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DMAStop), (102 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetError), (6 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_GetState), (8 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_IRQHandler), (392 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Init), (90 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive), (238 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_DMA), (248 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Receive_IT), (108 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit), (204 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive), (344 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA), (268 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT), (132 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_DMA), (156 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_Transmit_IT), (88 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAAbortOnError), (20 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAError), (84 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMAReceiveCplt), (122 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMARxAbortCallback), (48 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMARxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATransmitCplt), (70 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATxAbortCallback), (48 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndRxTransfer), (32 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndTransmit_IT), (44 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_EndTxTransfer), (20 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Receive_IT), (166 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_SetConfig), (596 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_TransmitReceive_IT), (258 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_Transmit_IT), (96 bytes).
    Removing stm32f4xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout), (128 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (120 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (118 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (142 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (154 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (320 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (192 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (124 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (90 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (108 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (248 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (124 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (126 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (102 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (80 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (32 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (108 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (4056 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (34 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (8 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (70 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (68 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (70 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (96 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (54 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (108 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_AttributeSpace_Timing_Init), (54 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_CommonSpace_Timing_Init), (54 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_DeInit), (66 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Disable), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_ECC_Enable), (28 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_GetECC), (88 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NAND_Init), (76 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_DeInit), (56 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Disable), (18 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_WriteOperation_Enable), (18 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_AttributeSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_CommonSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_IOSpace_Timing_Init), (38 bytes).
    Removing stm32f4xx_ll_fsmc.o(i.FSMC_PCCARD_Init), (44 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (156 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (248 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (264 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (270 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (564 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (214 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (256 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (212 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (316 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (600 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (392 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (238 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (572 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (344 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (286 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (184 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (572 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (344 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (170 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (40 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (116 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (164 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (216 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (228 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (488 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (312 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (218 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (488 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (312 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (218 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (42 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (68 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (136 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (68 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (8 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (372 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (206 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (128 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (332 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (348 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (72 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (88 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (110 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (26 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (14 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (552 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (70 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (172 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (288 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (300 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (104 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (116 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (96 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (316 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (112 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i.key_scan), (152 bytes).
    Removing key.o(.data), (1 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rrx_text), (6 bytes).
    Removing lcd.o(i.lcd_color_fill), (96 bytes).
    Removing lcd.o(i.lcd_display_off), (36 bytes).
    Removing lcd.o(i.lcd_display_on), (36 bytes).
    Removing lcd.o(i.lcd_draw_circle), (170 bytes).
    Removing lcd.o(i.lcd_draw_hline), (56 bytes).
    Removing lcd.o(i.lcd_draw_line), (172 bytes).
    Removing lcd.o(i.lcd_draw_rectangle), (74 bytes).
    Removing lcd.o(i.lcd_fill), (84 bytes).
    Removing lcd.o(i.lcd_fill_circle), (176 bytes).
    Removing lcd.o(i.lcd_pow), (22 bytes).
    Removing lcd.o(i.lcd_read_point), (152 bytes).
    Removing lcd.o(i.lcd_set_window), (368 bytes).
    Removing lcd.o(i.lcd_show_num), (152 bytes).
    Removing lcd.o(i.lcd_show_xnum), (198 bytes).
    Removing bldc.o(.rev16_text), (4 bytes).
    Removing bldc.o(.revsh_text), (4 bytes).
    Removing bldc.o(.rrx_text), (6 bytes).
    Removing bldc_tim.o(.rev16_text), (4 bytes).
    Removing bldc_tim.o(.revsh_text), (4 bytes).
    Removing bldc_tim.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.adc_get_result_average), (36 bytes).
    Removing adc.o(.data), (1 bytes).

497 unused section(s) (total 45792 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log_x.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\..\Drivers\BSP\ADC\adc.c              0x00000000   Number         0  adc.o ABSOLUTE
    ..\..\Drivers\BSP\BLDC\bldc.c            0x00000000   Number         0  bldc.o ABSOLUTE
    ..\..\Drivers\BSP\KEY\key.c              0x00000000   Number         0  key.o ABSOLUTE
    ..\..\Drivers\BSP\LCD\lcd.c              0x00000000   Number         0  lcd.o ABSOLUTE
    ..\..\Drivers\BSP\LED\led.c              0x00000000   Number         0  led.o ABSOLUTE
    ..\..\Drivers\BSP\TIMER\bldc_tim.c       0x00000000   Number         0  bldc_tim.o ABSOLUTE
    ..\..\Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f407xx.s 0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    ..\..\Drivers\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\..\Drivers\SYSTEM\delay\delay.c       0x00000000   Number         0  delay.o ABSOLUTE
    ..\..\Drivers\SYSTEM\sys\sys.c           0x00000000   Number         0  sys.o ABSOLUTE
    ..\..\Drivers\SYSTEM\usart\usart.c       0x00000000   Number         0  usart.o ABSOLUTE
    ..\..\User\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    ..\..\User\stm32f4xx_it.c                0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\ADC\\adc.c         0x00000000   Number         0  adc.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\BLDC\\bldc.c       0x00000000   Number         0  bldc.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\KEY\\key.c         0x00000000   Number         0  key.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\LCD\\lcd.c         0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\LED\\led.c         0x00000000   Number         0  led.o ABSOLUTE
    ..\\..\\Drivers\\BSP\\TIMER\\bldc_tim.c  0x00000000   Number         0  bldc_tim.o ABSOLUTE
    ..\\..\\Drivers\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_usart.c 0x00000000   Number         0  stm32f4xx_hal_usart.o ABSOLUTE
    ..\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fsmc.c 0x00000000   Number         0  stm32f4xx_ll_fsmc.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\delay\\delay.c  0x00000000   Number         0  delay.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\sys\\sys.c      0x00000000   Number         0  sys.o ABSOLUTE
    ..\\..\\Drivers\\SYSTEM\\usart\\usart.c  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\..\\User\\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\User\\stm32f4xx_it.c             0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000202   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000017  0x08000208   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800020c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800020e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000212   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000218   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000224   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800022e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800022e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000230   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000232   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000232   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000234   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000234   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000234   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800023a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800023a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800023e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800023e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000246   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000248   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000248   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800024c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000254   Section       76  startup_stm32f407xx.o(.text)
    $v0                                      0x08000254   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080002a0   Section        2  use_no_semi_2.o(.text)
    .text                                    0x080002a2   Section      238  lludivv7m.o(.text)
    .text                                    0x08000390   Section        0  noretval__2printf.o(.text)
    .text                                    0x080003a8   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x080003d0   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08000428   Section        0  __printf_wp.o(.text)
    .text                                    0x08000538   Section        0  __0sscanf.o(.text)
    .text                                    0x08000574   Section        0  _scanf_int.o(.text)
    .text                                    0x080006c0   Section        0  _scanf_str.o(.text)
    .text                                    0x080007a0   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080007f0   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000870   Section        0  heapauxi.o(.text)
    .text                                    0x08000876   Section        2  use_no_semi.o(.text)
    .text                                    0x08000878   Section        0  _rserrno.o(.text)
    .text                                    0x0800088e   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000940   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000943   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000d60   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000d61   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000d90   Section        0  _sputc.o(.text)
    .text                                    0x08000d9c   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000dc0   Section        0  _chval.o(.text)
    .text                                    0x08000ddc   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000ddd   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000e08   Section        0  _sgetc.o(.text)
    .text                                    0x08000e48   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000e50   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000e58   Section      138  lludiv10.o(.text)
    .text                                    0x08000ee2   Section        0  isspace.o(.text)
    .text                                    0x08000ef4   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000f74   Section        0  _scanf.o(.text)
    .text                                    0x080012e8   Section        0  bigflt0.o(.text)
    .text                                    0x080013cc   Section        0  ferror.o(.text)
    .text                                    0x080013d4   Section        8  libspace.o(.text)
    .text                                    0x080013dc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001428   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001438   Section        0  exit.o(.text)
    CL$$btod_d2e                             0x0800144a   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001488   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080014ce   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800152e   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001866   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001942   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800196c   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001996   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_DMAConvCplt                        0x08001bda   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x08001bdb   Thumb Code   126  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08001c58   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x08001c59   Thumb Code    26  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08001c72   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x08001c73   Thumb Code    14  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Init                               0x08001c80   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08001c81   Thumb Code   344  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x08001de0   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream4_IRQHandler                0x08001de4   Section        0  adc.o(i.DMA2_Stream4_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08001df4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08001df5   Thumb Code    46  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08001e28   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08001e29   Thumb Code   170  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08001ed2   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08001ed3   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08001efe   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.FSMC_NORSRAM_Extended_Timing_Init      0x08001f00   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    i.FSMC_NORSRAM_Init                      0x08001f48   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    i.FSMC_NORSRAM_Timing_Init               0x08001fa4   Section        0  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    i.HAL_ADC_ConfigChannel                  0x08001fe8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08002174   Section        0  adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x080021a8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x080021aa   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x080021ac   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08002214   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08002218   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x080023cc   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DMA_Abort                          0x0800244c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080024f6   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002520   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002760   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x0800284c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x080028e0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08002ae4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_TogglePin                     0x08002af4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08002b08   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetREVID                           0x08002b14   Section        0  stm32f4xx_hal.o(i.HAL_GetREVID)
    i.HAL_GetTick                            0x08002b20   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002b2c   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002b44   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002b74   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002bc0   Section        0  stm32f4xx_hal.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002bc2   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002be2   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002c60   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002c88   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08002e0c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08002e18   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002e38   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002e58   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002f08   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SRAM_Init                          0x080033a4   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x080033fc   Section        0  lcd.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_Config                     0x080034a0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x080034d4   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x080034d6   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIM_Base_Init                      0x080034d8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003540   Section        0  bldc_tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x0800358c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_IC_CaptureCallback             0x08003634   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08003636   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080037cc   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x080037ce   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080038ca   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08003930   Section        0  bldc_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08003ac0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08003ac4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PWM_Stop                       0x08003bd0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop)
    i.HAL_TIM_PeriodElapsedCallback          0x08003c78   Section        0  bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08004084   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_RxEventCallback             0x08004086   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08004088   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_GetState                      0x0800408a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    i.HAL_UART_IRQHandler                    0x080040a0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080042f0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004364   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x0800442c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004470   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_TxCpltCallback                0x080044f8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080044fa   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x080044fe   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004502   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08004504   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08004506   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08004508   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08004510   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM1_UP_TIM10_IRQHandler               0x08004524   Section        0  bldc_tim.o(i.TIM1_UP_TIM10_IRQHandler)
    i.TIM6_DAC_IRQHandler                    0x08004534   Section        0  bldc_tim.o(i.TIM6_DAC_IRQHandler)
    i.TIM_Base_SetConfig                     0x08004544   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x0800461c   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x08004640   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08004641   Thumb Code   104  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080046b0   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x0800472c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x0800472d   Thumb Code   112  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x080047a4   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x080047a5   Thumb Code    74  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.UART_DMAAbortOnError                   0x080047f8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080047f9   Thumb Code    20  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x0800480c   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800480d   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08004842   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08004843   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_Receive_IT                        0x08004862   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08004863   Thumb Code   190  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08004920   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08004921   Thumb Code   546  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08004b4c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x08004b92   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08004b93   Thumb Code    96  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.USART1_IRQHandler                      0x08004bf4   Section        0  usart.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08004c44   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08004c48   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_GetPriorityGrouping             0x08004c78   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08004c79   Thumb Code    10  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x08004c88   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08004c89   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_log                           0x08004cb0   Section        0  log.o(i.__hardfp_log)
    i.__kernel_poly                          0x08005074   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08005170   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan                   0x080051a0   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x080051b8   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i._is_digit                              0x080051d8   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x080051e6   Section        0  usart.o(i._sys_exit)
    i.adc_init                               0x080051ec   Section        0  adc.o(i.adc_init)
    i.adc_nch_dma_init                       0x08005294   Section        0  adc.o(i.adc_nch_dma_init)
    i.atim_timx_oc_chy_init                  0x08005458   Section        0  bldc_tim.o(i.atim_timx_oc_chy_init)
    i.bldc_ctrl                              0x0800550c   Section        0  bldc.o(i.bldc_ctrl)
    i.bldc_init                              0x08005524   Section        0  bldc.o(i.bldc_init)
    i.btim_timx_int_init                     0x080055dc   Section        0  bldc_tim.o(i.btim_timx_int_init)
    i.calc_adc_val                           0x08005614   Section        0  bldc.o(i.calc_adc_val)
    i.check_hall_dir                         0x08005680   Section        0  bldc.o(i.check_hall_dir)
    i.delay_init                             0x08005710   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x0800571c   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08005730   Section        0  delay.o(i.delay_us)
    i.fputc                                  0x08005778   Section        0  usart.o(i.fputc)
    i.get_temp                               0x08005794   Section        0  bldc.o(i.get_temp)
    i.hall_gpio_init                         0x0800585c   Section        0  bldc.o(i.hall_gpio_init)
    i.hallsensor_get_state                   0x0800596c   Section        0  bldc.o(i.hallsensor_get_state)
    i.key_init                               0x080059d0   Section        0  key.o(i.key_init)
    i.lcd_clear                              0x08005a90   Section        0  lcd.o(i.lcd_clear)
    i.lcd_display_dir                        0x08005ac4   Section        0  lcd.o(i.lcd_display_dir)
    i.lcd_draw_point                         0x08005c30   Section        0  lcd.o(i.lcd_draw_point)
    i.lcd_ex_ili9341_reginit                 0x08005c50   Section        0  lcd.o(i.lcd_ex_ili9341_reginit)
    i.lcd_ex_ili9806_reginit                 0x08005e7c   Section        0  lcd.o(i.lcd_ex_ili9806_reginit)
    i.lcd_ex_nt35310_reginit                 0x080061bc   Section        0  lcd.o(i.lcd_ex_nt35310_reginit)
    i.lcd_ex_nt35510_reginit                 0x080070ae   Section        0  lcd.o(i.lcd_ex_nt35510_reginit)
    i.lcd_ex_ssd1963_reginit                 0x08007faa   Section        0  lcd.o(i.lcd_ex_ssd1963_reginit)
    i.lcd_ex_st7789_reginit                  0x08008118   Section        0  lcd.o(i.lcd_ex_st7789_reginit)
    i.lcd_ex_st7796_reginit                  0x080082c0   Section        0  lcd.o(i.lcd_ex_st7796_reginit)
    i.lcd_init                               0x08008488   Section        0  lcd.o(i.lcd_init)
    i.lcd_opt_delay                          0x08008994   Section        0  lcd.o(i.lcd_opt_delay)
    lcd_opt_delay                            0x08008995   Thumb Code    12  lcd.o(i.lcd_opt_delay)
    i.lcd_rd_data                            0x080089a0   Section        0  lcd.o(i.lcd_rd_data)
    lcd_rd_data                              0x080089a1   Thumb Code    20  lcd.o(i.lcd_rd_data)
    i.lcd_scan_dir                           0x080089b8   Section        0  lcd.o(i.lcd_scan_dir)
    i.lcd_set_cursor                         0x08008bf4   Section        0  lcd.o(i.lcd_set_cursor)
    i.lcd_show_char                          0x08008d14   Section        0  lcd.o(i.lcd_show_char)
    i.lcd_show_string                        0x08008e54   Section        0  lcd.o(i.lcd_show_string)
    i.lcd_ssd_backlight_set                  0x08008ec0   Section        0  lcd.o(i.lcd_ssd_backlight_set)
    i.lcd_wr_data                            0x08008f20   Section        0  lcd.o(i.lcd_wr_data)
    i.lcd_wr_regno                           0x08008f38   Section        0  lcd.o(i.lcd_wr_regno)
    i.lcd_write_ram_prepare                  0x08008f50   Section        0  lcd.o(i.lcd_write_ram_prepare)
    i.lcd_write_reg                          0x08008f64   Section        0  lcd.o(i.lcd_write_reg)
    i.led_init                               0x08008f74   Section        0  led.o(i.led_init)
    i.m1_uhvl                                0x08009008   Section        0  bldc.o(i.m1_uhvl)
    i.m1_uhwl                                0x08009054   Section        0  bldc.o(i.m1_uhwl)
    i.m1_vhul                                0x080090a0   Section        0  bldc.o(i.m1_vhul)
    i.m1_vhwl                                0x080090ec   Section        0  bldc.o(i.m1_vhwl)
    i.m1_whul                                0x08009138   Section        0  bldc.o(i.m1_whul)
    i.m1_whvl                                0x08009184   Section        0  bldc.o(i.m1_whvl)
    i.main                                   0x080091d0   Section        0  main.o(i.main)
    i.parse_command                          0x080095e8   Section        0  main.o(i.parse_command)
    i.start_motor1                           0x0800971c   Section        0  bldc.o(i.start_motor1)
    i.stop_motor1                            0x0800974c   Section        0  bldc.o(i.stop_motor1)
    i.sys_stm32_clock_init                   0x080097b8   Section        0  sys.o(i.sys_stm32_clock_init)
    i.uemf_edge                              0x08009884   Section        0  bldc.o(i.uemf_edge)
    i.usart_init                             0x080098a4   Section        0  usart.o(i.usart_init)
    locale$$code                             0x080098dc   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08009908   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$basic                              0x08009934   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x08009934   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x0800994c   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800994c   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x080099b0   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x080099b0   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080099c1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x08009b00   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08009b00   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08009b18   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08009b18   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08009b1f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x08009dc8   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x08009dc8   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dfix                               0x08009e40   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x08009e40   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x08009ea0   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08009ea0   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x08009efa   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x08009efa   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08009f28   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08009f28   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08009f50   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08009f50   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08009fc8   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08009fc8   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800a11c   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x0800a11c   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800a1b8   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x0800a1b8   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800a1c4   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800a1c4   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x0800a230   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x0800a230   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x0800a248   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x0800a248   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800a259   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800a41c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800a41c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800a472   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800a472   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800a4fe   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800a4fe   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x0800a508   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800a508   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800a512   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800a512   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x0800a516   Section       24  system_stm32f4xx.o(.constdata)
    x$fpl$usenofp                            0x0800a516   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800a52e   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x0800a52e   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800a536   Section    12160  lcd.o(.constdata)
    .constdata                               0x0800d4b8   Section       52  bldc.o(.constdata)
    .constdata                               0x0800d4ec   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x0800d4ec   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0800d500   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0800d518   Section       56  log.o(.constdata)
    Lg2                                      0x0800d518   Data          24  log.o(.constdata)
    Lg                                       0x0800d530   Data          32  log.o(.constdata)
    .constdata                               0x0800d550   Section        8  qnan.o(.constdata)
    .constdata                               0x0800d558   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800d558   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800d594   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800d60c   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800d610   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800d618   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800d624   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800d626   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800d627   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800d628   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800d628   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800d62c   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800d634   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800d738   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        5  main.o(.data)
    last_dir                                 0x20000004   Data           1  main.o(.data)
    .data                                    0x20000008   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x2000000c   Section        4  delay.o(.data)
    g_fac_us                                 0x2000000c   Data           4  delay.o(.data)
    .data                                    0x20000010   Section        7  usart.o(.data)
    .data                                    0x20000018   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x20000024   Section        8  lcd.o(.data)
    .data                                    0x20000030   Section       77  bldc.o(.data)
    state                                    0x20000078   Data           4  bldc.o(.data)
    oldval                                   0x2000007c   Data           1  bldc.o(.data)
    .data                                    0x20000080   Section       29  bldc_tim.o(.data)
    times_count                              0x2000009c   Data           1  bldc_tim.o(.data)
    .bss                                     0x200000a0   Section      268  usart.o(.bss)
    .bss                                     0x200001ac   Section       94  lcd.o(.bss)
    .bss                                     0x2000020c   Section      488  bldc_tim.o(.bss)
    .bss                                     0x200003f4   Section      698  adc.o(.bss)
    .bss                                     0x200006b0   Section       96  libspace.o(.bss)
    HEAP                                     0x20000710   Section        0  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000710   Section     1024  startup_stm32f407xx.o(STACK)
    Heap_Mem                                 0x20000710   Data           0  startup_stm32f407xx.o(HEAP)
    Stack_Mem                                0x20000710   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000b10   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_x                                0x08000203   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_percent_end                      0x08000209   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800020d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800022f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000231   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000233   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000235   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000235   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800023b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800023f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000247   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000249   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800024d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000255   Thumb Code    18  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08000279   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x0800027d   Thumb Code     0  startup_stm32f407xx.o(.text)
    __use_no_semihosting                     0x080002a1   Thumb Code     2  use_no_semi_2.o(.text)
    __aeabi_uldivmod                         0x080002a3   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002a3   Thumb Code   238  lludivv7m.o(.text)
    __2printf                                0x08000391   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x080003a9   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_int_hex                          0x080003d1   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x080003d1   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08000429   Thumb Code   270  __printf_wp.o(.text)
    __0sscanf                                0x08000539   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08000575   Thumb Code   332  _scanf_int.o(.text)
    _scanf_string                            0x080006c1   Thumb Code   224  _scanf_str.o(.text)
    __aeabi_memclr4                          0x080007a1   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080007a1   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080007a1   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080007a5   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x080007f1   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000871   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000873   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000875   Thumb Code     2  heapauxi.o(.text)
    __semihosting$guard                      0x08000877   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000877   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x08000879   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000883   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0800088f   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000941   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000af3   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000d6b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000d91   Thumb Code    10  _sputc.o(.text)
    _printf_char_file                        0x08000d9d   Thumb Code    32  _printf_char_file.o(.text)
    _chval                                   0x08000dc1   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000de9   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000e09   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000e27   Thumb Code    34  _sgetc.o(.text)
    __rt_locale                              0x08000e49   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08000e51   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000e51   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000e51   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000e59   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08000ee3   Thumb Code    18  isspace.o(.text)
    _printf_fp_infnan                        0x08000ef5   Thumb Code   112  _printf_fp_infnan.o(.text)
    __vfscanf                                0x08000f75   Thumb Code   880  _scanf.o(.text)
    _btod_etento                             0x080012e9   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x080013cd   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x080013d5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080013d5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080013d5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080013dd   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001429   Thumb Code    16  rt_ctype_table.o(.text)
    exit                                     0x08001439   Thumb Code    18  exit.o(.text)
    _btod_d2e                                0x0800144b   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001489   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080014cf   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800152f   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001867   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001943   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800196d   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001997   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08001de1   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream4_IRQHandler                  0x08001de5   Thumb Code    10  adc.o(i.DMA2_Stream4_IRQHandler)
    DebugMon_Handler                         0x08001eff   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    FSMC_NORSRAM_Extended_Timing_Init        0x08001f01   Thumb Code    66  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Extended_Timing_Init)
    FSMC_NORSRAM_Init                        0x08001f49   Thumb Code    88  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Init)
    FSMC_NORSRAM_Timing_Init                 0x08001fa5   Thumb Code    68  stm32f4xx_ll_fsmc.o(i.FSMC_NORSRAM_Timing_Init)
    HAL_ADC_ConfigChannel                    0x08001fe9   Thumb Code   380  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08002175   Thumb Code    36  adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x080021a9   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x080021ab   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x080021ad   Thumb Code   100  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08002215   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08002219   Thumb Code   398  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x080023cd   Thumb Code   128  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DMA_Abort                            0x0800244d   Thumb Code   170  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080024f7   Thumb Code    40  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002521   Thumb Code   570  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002761   Thumb Code   232  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x0800284d   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x080028e1   Thumb Code   470  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08002ae5   Thumb Code    16  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x08002af5   Thumb Code    20  stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08002b09   Thumb Code    12  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetREVID                             0x08002b15   Thumb Code     8  stm32f4xx_hal.o(i.HAL_GetREVID)
    HAL_GetTick                              0x08002b21   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002b2d   Thumb Code    16  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002b45   Thumb Code    44  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002b75   Thumb Code    64  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002bc1   Thumb Code     2  stm32f4xx_hal.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002bc3   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002be3   Thumb Code   124  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002c61   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002c89   Thumb Code   368  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08002e0d   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08002e19   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002e39   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002e59   Thumb Code   162  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002f09   Thumb Code  1172  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x080033a5   Thumb Code    88  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x080033fd   Thumb Code   152  lcd.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x080034a1   Thumb Code    52  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x080034d5   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x080034d7   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIM_Base_Init                        0x080034d9   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003541   Thumb Code    66  bldc_tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x0800358d   Thumb Code   138  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_IC_CaptureCallback               0x08003635   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08003637   Thumb Code   406  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_OC_DelayElapsedCallback          0x080037cd   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x080037cf   Thumb Code   252  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080038cb   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003931   Thumb Code   382  bldc_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003ac1   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08003ac5   Thumb Code   238  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PWM_Stop                         0x08003bd1   Thumb Code   160  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop)
    HAL_TIM_PeriodElapsedCallback            0x08003c79   Thumb Code   964  bldc_tim.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08004085   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_RxEventCallback               0x08004087   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08004089   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_GetState                        0x0800408b   Thumb Code    20  stm32f4xx_hal_uart.o(i.HAL_UART_GetState)
    HAL_UART_IRQHandler                      0x080040a1   Thumb Code   586  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080042f1   Thumb Code   114  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004365   Thumb Code   188  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x0800442d   Thumb Code    66  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004471   Thumb Code   120  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x080044f9   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080044fb   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x080044ff   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004503   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08004505   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08004507   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08004509   Thumb Code     8  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08004511   Thumb Code    14  system_stm32f4xx.o(i.SystemInit)
    TIM1_UP_TIM10_IRQHandler                 0x08004525   Thumb Code    10  bldc_tim.o(i.TIM1_UP_TIM10_IRQHandler)
    TIM6_DAC_IRQHandler                      0x08004535   Thumb Code    10  bldc_tim.o(i.TIM6_DAC_IRQHandler)
    TIM_Base_SetConfig                       0x08004545   Thumb Code   170  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x0800461d   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x080046b1   Thumb Code   114  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_IT                    0x08004b4d   Thumb Code    70  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08004bf5   Thumb Code    66  usart.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08004c45   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08004c49   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_log                             0x08004cb1   Thumb Code   872  log.o(i.__hardfp_log)
    __kernel_poly                            0x08005075   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08005171   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan                     0x080051a1   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x080051b9   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    _is_digit                                0x080051d9   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x080051e7   Thumb Code     4  usart.o(i._sys_exit)
    adc_init                                 0x080051ed   Thumb Code   156  adc.o(i.adc_init)
    adc_nch_dma_init                         0x08005295   Thumb Code   422  adc.o(i.adc_nch_dma_init)
    atim_timx_oc_chy_init                    0x08005459   Thumb Code   164  bldc_tim.o(i.atim_timx_oc_chy_init)
    bldc_ctrl                                0x0800550d   Thumb Code    20  bldc.o(i.bldc_ctrl)
    bldc_init                                0x08005525   Thumb Code   170  bldc.o(i.bldc_init)
    btim_timx_int_init                       0x080055dd   Thumb Code    46  bldc_tim.o(i.btim_timx_int_init)
    calc_adc_val                             0x08005615   Thumb Code   102  bldc.o(i.calc_adc_val)
    check_hall_dir                           0x08005681   Thumb Code   134  bldc.o(i.check_hall_dir)
    delay_init                               0x08005711   Thumb Code     6  delay.o(i.delay_init)
    delay_ms                                 0x0800571d   Thumb Code    18  delay.o(i.delay_ms)
    delay_us                                 0x08005731   Thumb Code    68  delay.o(i.delay_us)
    fputc                                    0x08005779   Thumb Code    22  usart.o(i.fputc)
    get_temp                                 0x08005795   Thumb Code   166  bldc.o(i.get_temp)
    hall_gpio_init                           0x0800585d   Thumb Code   262  bldc.o(i.hall_gpio_init)
    hallsensor_get_state                     0x0800596d   Thumb Code    90  bldc.o(i.hallsensor_get_state)
    key_init                                 0x080059d1   Thumb Code   184  key.o(i.key_init)
    lcd_clear                                0x08005a91   Thumb Code    42  lcd.o(i.lcd_clear)
    lcd_display_dir                          0x08005ac5   Thumb Code   360  lcd.o(i.lcd_display_dir)
    lcd_draw_point                           0x08005c31   Thumb Code    26  lcd.o(i.lcd_draw_point)
    lcd_ex_ili9341_reginit                   0x08005c51   Thumb Code   556  lcd.o(i.lcd_ex_ili9341_reginit)
    lcd_ex_ili9806_reginit                   0x08005e7d   Thumb Code   832  lcd.o(i.lcd_ex_ili9806_reginit)
    lcd_ex_nt35310_reginit                   0x080061bd   Thumb Code  3826  lcd.o(i.lcd_ex_nt35310_reginit)
    lcd_ex_nt35510_reginit                   0x080070af   Thumb Code  3836  lcd.o(i.lcd_ex_nt35510_reginit)
    lcd_ex_ssd1963_reginit                   0x08007fab   Thumb Code   366  lcd.o(i.lcd_ex_ssd1963_reginit)
    lcd_ex_st7789_reginit                    0x08008119   Thumb Code   424  lcd.o(i.lcd_ex_st7789_reginit)
    lcd_ex_st7796_reginit                    0x080082c1   Thumb Code   454  lcd.o(i.lcd_ex_st7796_reginit)
    lcd_init                                 0x08008489   Thumb Code  1278  lcd.o(i.lcd_init)
    lcd_scan_dir                             0x080089b9   Thumb Code   568  lcd.o(i.lcd_scan_dir)
    lcd_set_cursor                           0x08008bf5   Thumb Code   282  lcd.o(i.lcd_set_cursor)
    lcd_show_char                            0x08008d15   Thumb Code   294  lcd.o(i.lcd_show_char)
    lcd_show_string                          0x08008e55   Thumb Code   106  lcd.o(i.lcd_show_string)
    lcd_ssd_backlight_set                    0x08008ec1   Thumb Code    88  lcd.o(i.lcd_ssd_backlight_set)
    lcd_wr_data                              0x08008f21   Thumb Code    18  lcd.o(i.lcd_wr_data)
    lcd_wr_regno                             0x08008f39   Thumb Code    18  lcd.o(i.lcd_wr_regno)
    lcd_write_ram_prepare                    0x08008f51   Thumb Code    10  lcd.o(i.lcd_write_ram_prepare)
    lcd_write_reg                            0x08008f65   Thumb Code    10  lcd.o(i.lcd_write_reg)
    led_init                                 0x08008f75   Thumb Code   138  led.o(i.led_init)
    m1_uhvl                                  0x08009009   Thumb Code    64  bldc.o(i.m1_uhvl)
    m1_uhwl                                  0x08009055   Thumb Code    64  bldc.o(i.m1_uhwl)
    m1_vhul                                  0x080090a1   Thumb Code    64  bldc.o(i.m1_vhul)
    m1_vhwl                                  0x080090ed   Thumb Code    64  bldc.o(i.m1_vhwl)
    m1_whul                                  0x08009139   Thumb Code    64  bldc.o(i.m1_whul)
    m1_whvl                                  0x08009185   Thumb Code    64  bldc.o(i.m1_whvl)
    main                                     0x080091d1   Thumb Code   888  main.o(i.main)
    parse_command                            0x080095e9   Thumb Code   264  main.o(i.parse_command)
    start_motor1                             0x0800971d   Thumb Code    38  bldc.o(i.start_motor1)
    stop_motor1                              0x0800974d   Thumb Code    96  bldc.o(i.stop_motor1)
    sys_stm32_clock_init                     0x080097b9   Thumb Code   192  sys.o(i.sys_stm32_clock_init)
    uemf_edge                                0x08009885   Thumb Code    28  bldc.o(i.uemf_edge)
    usart_init                               0x080098a5   Thumb Code    44  usart.o(i.usart_init)
    _get_lc_numeric                          0x080098dd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08009909   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_dneg                             0x08009935   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x08009935   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x0800993b   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x0800993b   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x08009941   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x08009947   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x0800994d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800994d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x080099b1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080099b1   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x08009b01   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08009b19   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08009b19   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x08009dc9   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08009dc9   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_d2iz                             0x08009e41   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08009e41   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x08009ea1   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08009ea1   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x08009efb   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08009efb   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08009f29   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08009f29   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08009f51   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08009f51   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08009fb3   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08009fc9   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08009fc9   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800a11d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800a1b9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800a1c5   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800a1c5   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x0800a231   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800a231   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x0800a249   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800a249   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800a41d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800a41d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800a473   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800a4ff   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800a507   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800a507   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x0800a509   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800a513   Thumb Code     4  printf1.o(x$fpl$printf1)
    AHBPrescTable                            0x0800a516   Data          16  system_stm32f4xx.o(.constdata)
    __I$use$fp                               0x0800a516   Number         0  usenofp.o(x$fpl$usenofp)
    APBPrescTable                            0x0800a526   Data           8  system_stm32f4xx.o(.constdata)
    asc2_1206                                0x0800a536   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800a9aa   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800af9a   Data        3420  lcd.o(.constdata)
    asc2_3216                                0x0800bcf6   Data        6080  lcd.o(.constdata)
    hall_table_cw                            0x0800d4b8   Data           6  bldc.o(.constdata)
    hall_table_ccw                           0x0800d4be   Data           6  bldc.o(.constdata)
    hall_cw_table                            0x0800d4c4   Data          12  bldc.o(.constdata)
    hall_ccw_table                           0x0800d4d0   Data          12  bldc.o(.constdata)
    Rp                                       0x0800d4dc   Data           4  bldc.o(.constdata)
    T2                                       0x0800d4e0   Data           4  bldc.o(.constdata)
    Bx                                       0x0800d4e4   Data           4  bldc.o(.constdata)
    Ka                                       0x0800d4e8   Data           4  bldc.o(.constdata)
    __mathlib_zero                           0x0800d550   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800d5ec   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800d60c   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800d635   Data           0  lc_ctype_c.o(locale$$data)
    g_send_timer                             0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f4xx.o(.data)
    __stdout                                 0x20000010   Data           4  usart.o(.data)
    g_usart_rx_sta                           0x20000014   Data           2  usart.o(.data)
    g_rx_buffer                              0x20000016   Data           1  usart.o(.data)
    uwTick                                   0x20000018   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x2000001c   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x20000020   Data           1  stm32f4xx_hal.o(.data)
    g_point_color                            0x20000024   Data           4  lcd.o(.data)
    g_back_color                             0x20000028   Data           4  lcd.o(.data)
    g_bldc_motor1                            0x20000030   Data          48  bldc.o(.data)
    pfunclist_m1                             0x20000060   Data          24  bldc.o(.data)
    temp_pwm1                                0x20000080   Data           4  bldc_tim.o(.data)
    motor_pwm_s                              0x20000084   Data           4  bldc_tim.o(.data)
    adc_amp_offset_p                         0x20000088   Data           1  bldc_tim.o(.data)
    adc_amp                                  0x2000008a   Data           6  bldc_tim.o(.data)
    adc_amp_un                               0x20000090   Data           6  bldc_tim.o(.data)
    adc_amp_bus                              0x20000098   Data           4  bldc_tim.o(.data)
    g_usart_rx_buf                           0x200000a0   Data         200  usart.o(.bss)
    g_uart1_handle                           0x20000168   Data          68  usart.o(.bss)
    g_sram_handle                            0x200001ac   Data          80  lcd.o(.bss)
    lcddev                                   0x200001fc   Data          14  lcd.o(.bss)
    g_atimx_handle                           0x2000020c   Data          72  bldc_tim.o(.bss)
    g_atimx_oc_chy_handle                    0x20000254   Data          28  bldc_tim.o(.bss)
    timx_handler                             0x20000270   Data          72  bldc_tim.o(.bss)
    adc_amp_offset                           0x200002b8   Data         306  bldc_tim.o(.bss)
    adc_val_m1                               0x200003ea   Data          10  bldc_tim.o(.bss)
    g_adc_nch_dma_handle                     0x200003f4   Data          72  adc.o(.bss)
    g_dma_nch_adc_handle                     0x2000043c   Data          96  adc.o(.bss)
    g_adc_value                              0x2000049c   Data         500  adc.o(.bss)
    g_adc_u_value                            0x20000690   Data          20  adc.o(.bss)
    g_adc_val                                0x200006a4   Data          10  adc.o(.bss)
    __libspace_start                         0x200006b0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000710   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000d7d8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000d738, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         4239  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         4563    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         4565    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         4567    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         4226    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         4225    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         4224    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000208   0x08000208   0x00000004   Code   RO         4310    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800020c   0x0800020c   0x00000002   Code   RO         4440    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         4441    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         4444    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         4447    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         4449    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         4451    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000212   0x08000212   0x00000006   Code   RO         4452    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         4454    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x0000000c   Code   RO         4455    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         4456    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         4458    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x0000000a   Code   RO         4459    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4460    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4462    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4464    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4466    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4468    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4470    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4472    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4474    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4478    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4480    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4482    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         4484    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800022e   0x0800022e   0x00000002   Code   RO         4485    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000002   Code   RO         4526    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000232   0x08000232   0x00000000   Code   RO         4544    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         4546    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         4548    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         4551    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         4554    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         4556    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000000   Code   RO         4559    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000232   0x08000232   0x00000002   Code   RO         4560    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000234   0x08000234   0x00000000   Code   RO         4293    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000234   0x08000234   0x00000000   Code   RO         4350    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000234   0x08000234   0x00000006   Code   RO         4362    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         4352    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800023a   0x0800023a   0x00000004   Code   RO         4353    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         4355    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800023e   0x0800023e   0x00000008   Code   RO         4356    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000246   0x08000246   0x00000002   Code   RO         4486    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000248   0x08000248   0x00000000   Code   RO         4502    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000248   0x08000248   0x00000004   Code   RO         4503    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800024c   0x0800024c   0x00000006   Code   RO         4504    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000252   0x08000252   0x00000002   PAD
    0x08000254   0x08000254   0x0000004c   Code   RO            4    .text               startup_stm32f407xx.o
    0x080002a0   0x080002a0   0x00000002   Code   RO         4169    .text               c_w.l(use_no_semi_2.o)
    0x080002a2   0x080002a2   0x000000ee   Code   RO         4171    .text               c_w.l(lludivv7m.o)
    0x08000390   0x08000390   0x00000018   Code   RO         4177    .text               c_w.l(noretval__2printf.o)
    0x080003a8   0x080003a8   0x00000028   Code   RO         4179    .text               c_w.l(noretval__2sprintf.o)
    0x080003d0   0x080003d0   0x00000058   Code   RO         4186    .text               c_w.l(_printf_hex_int.o)
    0x08000428   0x08000428   0x0000010e   Code   RO         4212    .text               c_w.l(__printf_wp.o)
    0x08000536   0x08000536   0x00000002   PAD
    0x08000538   0x08000538   0x0000003c   Code   RO         4227    .text               c_w.l(__0sscanf.o)
    0x08000574   0x08000574   0x0000014c   Code   RO         4229    .text               c_w.l(_scanf_int.o)
    0x080006c0   0x080006c0   0x000000e0   Code   RO         4231    .text               c_w.l(_scanf_str.o)
    0x080007a0   0x080007a0   0x0000004e   Code   RO         4233    .text               c_w.l(rt_memclr_w.o)
    0x080007ee   0x080007ee   0x00000002   PAD
    0x080007f0   0x080007f0   0x00000080   Code   RO         4235    .text               c_w.l(strcmpv7m.o)
    0x08000870   0x08000870   0x00000006   Code   RO         4237    .text               c_w.l(heapauxi.o)
    0x08000876   0x08000876   0x00000002   Code   RO         4291    .text               c_w.l(use_no_semi.o)
    0x08000878   0x08000878   0x00000016   Code   RO         4298    .text               c_w.l(_rserrno.o)
    0x0800088e   0x0800088e   0x000000b2   Code   RO         4300    .text               c_w.l(_printf_intcommon.o)
    0x08000940   0x08000940   0x0000041e   Code   RO         4302    .text               c_w.l(_printf_fp_dec.o)
    0x08000d5e   0x08000d5e   0x00000002   PAD
    0x08000d60   0x08000d60   0x00000030   Code   RO         4304    .text               c_w.l(_printf_char_common.o)
    0x08000d90   0x08000d90   0x0000000a   Code   RO         4306    .text               c_w.l(_sputc.o)
    0x08000d9a   0x08000d9a   0x00000002   PAD
    0x08000d9c   0x08000d9c   0x00000024   Code   RO         4308    .text               c_w.l(_printf_char_file.o)
    0x08000dc0   0x08000dc0   0x0000001c   Code   RO         4311    .text               c_w.l(_chval.o)
    0x08000ddc   0x08000ddc   0x0000002c   Code   RO         4313    .text               c_w.l(scanf_char.o)
    0x08000e08   0x08000e08   0x00000040   Code   RO         4315    .text               c_w.l(_sgetc.o)
    0x08000e48   0x08000e48   0x00000008   Code   RO         4369    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000e50   0x08000e50   0x00000008   Code   RO         4374    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000e58   0x08000e58   0x0000008a   Code   RO         4376    .text               c_w.l(lludiv10.o)
    0x08000ee2   0x08000ee2   0x00000012   Code   RO         4378    .text               c_w.l(isspace.o)
    0x08000ef4   0x08000ef4   0x00000080   Code   RO         4380    .text               c_w.l(_printf_fp_infnan.o)
    0x08000f74   0x08000f74   0x00000374   Code   RO         4384    .text               c_w.l(_scanf.o)
    0x080012e8   0x080012e8   0x000000e4   Code   RO         4386    .text               c_w.l(bigflt0.o)
    0x080013cc   0x080013cc   0x00000008   Code   RO         4411    .text               c_w.l(ferror.o)
    0x080013d4   0x080013d4   0x00000008   Code   RO         4422    .text               c_w.l(libspace.o)
    0x080013dc   0x080013dc   0x0000004a   Code   RO         4425    .text               c_w.l(sys_stackheap_outer.o)
    0x08001426   0x08001426   0x00000002   PAD
    0x08001428   0x08001428   0x00000010   Code   RO         4427    .text               c_w.l(rt_ctype_table.o)
    0x08001438   0x08001438   0x00000012   Code   RO         4429    .text               c_w.l(exit.o)
    0x0800144a   0x0800144a   0x0000003e   Code   RO         4389    CL$$btod_d2e        c_w.l(btod.o)
    0x08001488   0x08001488   0x00000046   Code   RO         4391    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080014ce   0x080014ce   0x00000060   Code   RO         4390    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800152e   0x0800152e   0x00000338   Code   RO         4399    CL$$btod_div_common  c_w.l(btod.o)
    0x08001866   0x08001866   0x000000dc   Code   RO         4396    CL$$btod_e2e        c_w.l(btod.o)
    0x08001942   0x08001942   0x0000002a   Code   RO         4393    CL$$btod_ediv       c_w.l(btod.o)
    0x0800196c   0x0800196c   0x0000002a   Code   RO         4392    CL$$btod_emul       c_w.l(btod.o)
    0x08001996   0x08001996   0x00000244   Code   RO         4398    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001bda   0x08001bda   0x0000007e   Code   RO         3323    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x08001c58   0x08001c58   0x0000001a   Code   RO         3324    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x08001c72   0x08001c72   0x0000000e   Code   RO         3325    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x08001c80   0x08001c80   0x00000160   Code   RO         3326    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08001de0   0x08001de0   0x00000004   Code   RO          285    i.BusFault_Handler  stm32f4xx_it.o
    0x08001de4   0x08001de4   0x00000010   Code   RO         4112    i.DMA2_Stream4_IRQHandler  adc.o
    0x08001df4   0x08001df4   0x00000034   Code   RO          917    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08001e28   0x08001e28   0x000000aa   Code   RO          918    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08001ed2   0x08001ed2   0x0000002c   Code   RO          919    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08001efe   0x08001efe   0x00000002   Code   RO          286    i.DebugMon_Handler  stm32f4xx_it.o
    0x08001f00   0x08001f00   0x00000048   Code   RO         2221    i.FSMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08001f48   0x08001f48   0x0000005c   Code   RO         2222    i.FSMC_NORSRAM_Init  stm32f4xx_ll_fsmc.o
    0x08001fa4   0x08001fa4   0x00000044   Code   RO         2223    i.FSMC_NORSRAM_Timing_Init  stm32f4xx_ll_fsmc.o
    0x08001fe8   0x08001fe8   0x0000018c   Code   RO         3328    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08002174   0x08002174   0x00000034   Code   RO         4113    i.HAL_ADC_ConvCpltCallback  adc.o
    0x080021a8   0x080021a8   0x00000002   Code   RO         3330    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x080021aa   0x080021aa   0x00000002   Code   RO         3332    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x080021ac   0x080021ac   0x00000068   Code   RO         3337    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08002214   0x08002214   0x00000002   Code   RO         3340    i.HAL_ADC_MspInit   stm32f4xx_hal_adc.o
    0x08002216   0x08002216   0x00000002   PAD
    0x08002218   0x08002218   0x000001b4   Code   RO         3344    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x080023cc   0x080023cc   0x00000080   Code   RO         3347    i.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x0800244c   0x0800244c   0x000000aa   Code   RO          920    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080024f6   0x080024f6   0x00000028   Code   RO          921    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x0800251e   0x0800251e   0x00000002   PAD
    0x08002520   0x08002520   0x00000240   Code   RO          925    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002760   0x08002760   0x000000ec   Code   RO          926    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x0800284c   0x0800284c   0x00000092   Code   RO          930    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x080028de   0x080028de   0x00000002   PAD
    0x080028e0   0x080028e0   0x00000204   Code   RO         1026    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08002ae4   0x08002ae4   0x00000010   Code   RO         1028    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08002af4   0x08002af4   0x00000014   Code   RO         1029    i.HAL_GPIO_TogglePin  stm32f4xx_hal_gpio.o
    0x08002b08   0x08002b08   0x0000000c   Code   RO         1030    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08002b14   0x08002b14   0x0000000c   Code   RO          603    i.HAL_GetREVID      stm32f4xx_hal.o
    0x08002b20   0x08002b20   0x0000000c   Code   RO          604    i.HAL_GetTick       stm32f4xx_hal.o
    0x08002b2c   0x08002b2c   0x00000018   Code   RO          610    i.HAL_IncTick       stm32f4xx_hal.o
    0x08002b44   0x08002b44   0x00000030   Code   RO          611    i.HAL_Init          stm32f4xx_hal.o
    0x08002b74   0x08002b74   0x0000004c   Code   RO          612    i.HAL_InitTick      stm32f4xx_hal.o
    0x08002bc0   0x08002bc0   0x00000002   Code   RO          614    i.HAL_MspInit       stm32f4xx_hal.o
    0x08002bc2   0x08002bc2   0x00000020   Code   RO          782    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002be2   0x08002be2   0x0000007c   Code   RO          788    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002c5e   0x08002c5e   0x00000002   PAD
    0x08002c60   0x08002c60   0x00000028   Code   RO          789    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002c88   0x08002c88   0x00000184   Code   RO         1090    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002e0c   0x08002e0c   0x0000000c   Code   RO         1095    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08002e18   0x08002e18   0x00000020   Code   RO         1097    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002e38   0x08002e38   0x00000020   Code   RO         1098    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002e58   0x08002e58   0x000000b0   Code   RO         1099    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08002f08   0x08002f08   0x0000049c   Code   RO         1102    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080033a4   0x080033a4   0x00000058   Code   RO         2097    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x080033fc   0x080033fc   0x000000a4   Code   RO         3653    i.HAL_SRAM_MspInit  lcd.o
    0x080034a0   0x080034a0   0x00000034   Code   RO          793    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080034d4   0x080034d4   0x00000002   Code   RO         3056    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x080034d6   0x080034d6   0x00000002   Code   RO         3057    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x080034d8   0x080034d8   0x00000066   Code   RO         2341    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800353e   0x0800353e   0x00000002   PAD
    0x08003540   0x08003540   0x0000004c   Code   RO         4041    i.HAL_TIM_Base_MspInit  bldc_tim.o
    0x0800358c   0x0800358c   0x000000a8   Code   RO         2346    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08003634   0x08003634   0x00000002   Code   RO         2375    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x08003636   0x08003636   0x00000196   Code   RO         2389    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x080037cc   0x080037cc   0x00000002   Code   RO         2392    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x080037ce   0x080037ce   0x000000fc   Code   RO         2413    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x080038ca   0x080038ca   0x00000066   Code   RO         2416    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003930   0x08003930   0x00000190   Code   RO         4042    i.HAL_TIM_PWM_MspInit  bldc_tim.o
    0x08003ac0   0x08003ac0   0x00000002   Code   RO         2419    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08003ac2   0x08003ac2   0x00000002   PAD
    0x08003ac4   0x08003ac4   0x0000010c   Code   RO         2421    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08003bd0   0x08003bd0   0x000000a8   Code   RO         2424    i.HAL_TIM_PWM_Stop  stm32f4xx_hal_tim.o
    0x08003c78   0x08003c78   0x0000040c   Code   RO         4043    i.HAL_TIM_PeriodElapsedCallback  bldc_tim.o
    0x08004084   0x08004084   0x00000002   Code   RO         2432    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08004086   0x08004086   0x00000002   Code   RO         1696    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08004088   0x08004088   0x00000002   Code   RO         1710    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800408a   0x0800408a   0x00000014   Code   RO         1712    i.HAL_UART_GetState  stm32f4xx_hal_uart.o
    0x0800409e   0x0800409e   0x00000002   PAD
    0x080040a0   0x080040a0   0x00000250   Code   RO         1713    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080042f0   0x080042f0   0x00000072   Code   RO         1714    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08004362   0x08004362   0x00000002   PAD
    0x08004364   0x08004364   0x000000c8   Code   RO          514    i.HAL_UART_MspInit  usart.o
    0x0800442c   0x0800442c   0x00000042   Code   RO         1719    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x0800446e   0x0800446e   0x00000002   PAD
    0x08004470   0x08004470   0x00000088   Code   RO          515    i.HAL_UART_RxCpltCallback  usart.o
    0x080044f8   0x080044f8   0x00000002   Code   RO         1725    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080044fa   0x080044fa   0x00000004   Code   RO          287    i.HardFault_Handler  stm32f4xx_it.o
    0x080044fe   0x080044fe   0x00000004   Code   RO          288    i.MemManage_Handler  stm32f4xx_it.o
    0x08004502   0x08004502   0x00000002   Code   RO          289    i.NMI_Handler       stm32f4xx_it.o
    0x08004504   0x08004504   0x00000002   Code   RO          290    i.PendSV_Handler    stm32f4xx_it.o
    0x08004506   0x08004506   0x00000002   Code   RO          291    i.SVC_Handler       stm32f4xx_it.o
    0x08004508   0x08004508   0x00000008   Code   RO          292    i.SysTick_Handler   stm32f4xx_it.o
    0x08004510   0x08004510   0x00000014   Code   RO          361    i.SystemInit        system_stm32f4xx.o
    0x08004524   0x08004524   0x00000010   Code   RO         4044    i.TIM1_UP_TIM10_IRQHandler  bldc_tim.o
    0x08004534   0x08004534   0x00000010   Code   RO         4045    i.TIM6_DAC_IRQHandler  bldc_tim.o
    0x08004544   0x08004544   0x000000d8   Code   RO         2434    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x0800461c   0x0800461c   0x00000022   Code   RO         2435    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x0800463e   0x0800463e   0x00000002   PAD
    0x08004640   0x08004640   0x00000070   Code   RO         2447    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x080046b0   0x080046b0   0x0000007c   Code   RO         2448    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x0800472c   0x0800472c   0x00000078   Code   RO         2449    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x080047a4   0x080047a4   0x00000054   Code   RO         2450    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x080047f8   0x080047f8   0x00000014   Code   RO         1727    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800480c   0x0800480c   0x00000036   Code   RO         1737    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08004842   0x08004842   0x00000020   Code   RO         1738    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08004862   0x08004862   0x000000be   Code   RO         1740    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08004920   0x08004920   0x0000022c   Code   RO         1741    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08004b4c   0x08004b4c   0x00000046   Code   RO         1743    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x08004b92   0x08004b92   0x00000060   Code   RO         1744    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08004bf2   0x08004bf2   0x00000002   PAD
    0x08004bf4   0x08004bf4   0x00000050   Code   RO          516    i.USART1_IRQHandler  usart.o
    0x08004c44   0x08004c44   0x00000004   Code   RO          293    i.UsageFault_Handler  stm32f4xx_it.o
    0x08004c48   0x08004c48   0x00000030   Code   RO         4420    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08004c78   0x08004c78   0x00000010   Code   RO          795    i.__NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08004c88   0x08004c88   0x00000028   Code   RO          796    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08004cb0   0x08004cb0   0x000003c4   Code   RO         4277    i.__hardfp_log      m_wm.l(log.o)
    0x08005074   0x08005074   0x000000f8   Code   RO         4346    i.__kernel_poly     m_wm.l(poly.o)
    0x0800516c   0x0800516c   0x00000004   PAD
    0x08005170   0x08005170   0x00000030   Code   RO         4332    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x080051a0   0x080051a0   0x00000014   Code   RO         4333    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x080051b4   0x080051b4   0x00000004   PAD
    0x080051b8   0x080051b8   0x00000020   Code   RO         4335    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x080051d8   0x080051d8   0x0000000e   Code   RO         4214    i._is_digit         c_w.l(__printf_wp.o)
    0x080051e6   0x080051e6   0x00000004   Code   RO          518    i._sys_exit         usart.o
    0x080051ea   0x080051ea   0x00000002   PAD
    0x080051ec   0x080051ec   0x000000a8   Code   RO         4115    i.adc_init          adc.o
    0x08005294   0x08005294   0x000001c4   Code   RO         4116    i.adc_nch_dma_init  adc.o
    0x08005458   0x08005458   0x000000b4   Code   RO         4046    i.atim_timx_oc_chy_init  bldc_tim.o
    0x0800550c   0x0800550c   0x00000018   Code   RO         3914    i.bldc_ctrl         bldc.o
    0x08005524   0x08005524   0x000000b8   Code   RO         3915    i.bldc_init         bldc.o
    0x080055dc   0x080055dc   0x00000038   Code   RO         4047    i.btim_timx_int_init  bldc_tim.o
    0x08005614   0x08005614   0x0000006c   Code   RO         3916    i.calc_adc_val      bldc.o
    0x08005680   0x08005680   0x00000090   Code   RO         3917    i.check_hall_dir    bldc.o
    0x08005710   0x08005710   0x0000000c   Code   RO          398    i.delay_init        delay.o
    0x0800571c   0x0800571c   0x00000012   Code   RO          399    i.delay_ms          delay.o
    0x0800572e   0x0800572e   0x00000002   PAD
    0x08005730   0x08005730   0x00000048   Code   RO          400    i.delay_us          delay.o
    0x08005778   0x08005778   0x0000001c   Code   RO          520    i.fputc             usart.o
    0x08005794   0x08005794   0x000000c8   Code   RO         3918    i.get_temp          bldc.o
    0x0800585c   0x0800585c   0x00000110   Code   RO         3919    i.hall_gpio_init    bldc.o
    0x0800596c   0x0800596c   0x00000064   Code   RO         3920    i.hallsensor_get_state  bldc.o
    0x080059d0   0x080059d0   0x000000c0   Code   RO         3622    i.key_init          key.o
    0x08005a90   0x08005a90   0x00000034   Code   RO         3654    i.lcd_clear         lcd.o
    0x08005ac4   0x08005ac4   0x0000016c   Code   RO         3656    i.lcd_display_dir   lcd.o
    0x08005c30   0x08005c30   0x00000020   Code   RO         3662    i.lcd_draw_point    lcd.o
    0x08005c50   0x08005c50   0x0000022c   Code   RO         3664    i.lcd_ex_ili9341_reginit  lcd.o
    0x08005e7c   0x08005e7c   0x00000340   Code   RO         3665    i.lcd_ex_ili9806_reginit  lcd.o
    0x080061bc   0x080061bc   0x00000ef2   Code   RO         3666    i.lcd_ex_nt35310_reginit  lcd.o
    0x080070ae   0x080070ae   0x00000efc   Code   RO         3667    i.lcd_ex_nt35510_reginit  lcd.o
    0x08007faa   0x08007faa   0x0000016e   Code   RO         3668    i.lcd_ex_ssd1963_reginit  lcd.o
    0x08008118   0x08008118   0x000001a8   Code   RO         3669    i.lcd_ex_st7789_reginit  lcd.o
    0x080082c0   0x080082c0   0x000001c6   Code   RO         3670    i.lcd_ex_st7796_reginit  lcd.o
    0x08008486   0x08008486   0x00000002   PAD
    0x08008488   0x08008488   0x0000050c   Code   RO         3673    i.lcd_init          lcd.o
    0x08008994   0x08008994   0x0000000c   Code   RO         3674    i.lcd_opt_delay     lcd.o
    0x080089a0   0x080089a0   0x00000018   Code   RO         3676    i.lcd_rd_data       lcd.o
    0x080089b8   0x080089b8   0x0000023c   Code   RO         3678    i.lcd_scan_dir      lcd.o
    0x08008bf4   0x08008bf4   0x00000120   Code   RO         3679    i.lcd_set_cursor    lcd.o
    0x08008d14   0x08008d14   0x00000140   Code   RO         3681    i.lcd_show_char     lcd.o
    0x08008e54   0x08008e54   0x0000006a   Code   RO         3683    i.lcd_show_string   lcd.o
    0x08008ebe   0x08008ebe   0x00000002   PAD
    0x08008ec0   0x08008ec0   0x00000060   Code   RO         3685    i.lcd_ssd_backlight_set  lcd.o
    0x08008f20   0x08008f20   0x00000018   Code   RO         3686    i.lcd_wr_data       lcd.o
    0x08008f38   0x08008f38   0x00000018   Code   RO         3687    i.lcd_wr_regno      lcd.o
    0x08008f50   0x08008f50   0x00000014   Code   RO         3688    i.lcd_write_ram_prepare  lcd.o
    0x08008f64   0x08008f64   0x00000010   Code   RO         3689    i.lcd_write_reg     lcd.o
    0x08008f74   0x08008f74   0x00000094   Code   RO         3598    i.led_init          led.o
    0x08009008   0x08009008   0x0000004c   Code   RO         3921    i.m1_uhvl           bldc.o
    0x08009054   0x08009054   0x0000004c   Code   RO         3922    i.m1_uhwl           bldc.o
    0x080090a0   0x080090a0   0x0000004c   Code   RO         3923    i.m1_vhul           bldc.o
    0x080090ec   0x080090ec   0x0000004c   Code   RO         3924    i.m1_vhwl           bldc.o
    0x08009138   0x08009138   0x0000004c   Code   RO         3925    i.m1_whul           bldc.o
    0x08009184   0x08009184   0x0000004c   Code   RO         3926    i.m1_whvl           bldc.o
    0x080091d0   0x080091d0   0x00000418   Code   RO           13    i.main              main.o
    0x080095e8   0x080095e8   0x00000134   Code   RO           14    i.parse_command     main.o
    0x0800971c   0x0800971c   0x00000030   Code   RO         3927    i.start_motor1      bldc.o
    0x0800974c   0x0800974c   0x0000006c   Code   RO         3928    i.stop_motor1       bldc.o
    0x080097b8   0x080097b8   0x000000cc   Code   RO          447    i.sys_stm32_clock_init  sys.o
    0x08009884   0x08009884   0x00000020   Code   RO         3929    i.uemf_edge         bldc.o
    0x080098a4   0x080098a4   0x00000038   Code   RO          521    i.usart_init        usart.o
    0x080098dc   0x080098dc   0x0000002c   Code   RO         4416    locale$$code        c_w.l(lc_numeric_c.o)
    0x08009908   0x08009908   0x0000002c   Code   RO         4491    locale$$code        c_w.l(lc_ctype_c.o)
    0x08009934   0x08009934   0x00000018   Code   RO         4317    x$fpl$basic         fz_wm.l(basic.o)
    0x0800994c   0x0800994c   0x00000062   Code   RO         4241    x$fpl$d2f           fz_wm.l(d2f.o)
    0x080099ae   0x080099ae   0x00000002   PAD
    0x080099b0   0x080099b0   0x00000150   Code   RO         4243    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08009b00   0x08009b00   0x00000018   Code   RO         4319    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08009b18   0x08009b18   0x000002b0   Code   RO         4250    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08009dc8   0x08009dc8   0x00000078   Code   RO         4321    x$fpl$deqf          fz_wm.l(deqf.o)
    0x08009e40   0x08009e40   0x0000005e   Code   RO         4253    x$fpl$dfix          fz_wm.l(dfix.o)
    0x08009e9e   0x08009e9e   0x00000002   PAD
    0x08009ea0   0x08009ea0   0x0000005a   Code   RO         4257    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x08009efa   0x08009efa   0x0000002e   Code   RO         4262    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x08009f28   0x08009f28   0x00000026   Code   RO         4261    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08009f4e   0x08009f4e   0x00000002   PAD
    0x08009f50   0x08009f50   0x00000078   Code   RO         4267    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08009fc8   0x08009fc8   0x00000154   Code   RO         4269    x$fpl$dmul          fz_wm.l(dmul.o)
    0x0800a11c   0x0800a11c   0x0000009c   Code   RO         4323    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x0800a1b8   0x0800a1b8   0x0000000c   Code   RO         4325    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800a1c4   0x0800a1c4   0x0000006c   Code   RO         4271    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x0800a230   0x0800a230   0x00000016   Code   RO         4244    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800a246   0x0800a246   0x00000002   PAD
    0x0800a248   0x0800a248   0x000001d4   Code   RO         4245    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800a41c   0x0800a41c   0x00000056   Code   RO         4273    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800a472   0x0800a472   0x0000008c   Code   RO         4327    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800a4fe   0x0800a4fe   0x0000000a   Code   RO         4499    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800a508   0x0800a508   0x0000000a   Code   RO         4329    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800a512   0x0800a512   0x00000004   Code   RO         4275    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800a516   0x0800a516   0x00000000   Code   RO         4331    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800a516   0x0800a516   0x00000018   Data   RO          362    .constdata          system_stm32f4xx.o
    0x0800a52e   0x0800a52e   0x00000008   Data   RO          932    .constdata          stm32f4xx_hal_dma.o
    0x0800a536   0x0800a536   0x00002f80   Data   RO         3691    .constdata          lcd.o
    0x0800d4b6   0x0800d4b6   0x00000002   PAD
    0x0800d4b8   0x0800d4b8   0x00000034   Data   RO         3930    .constdata          bldc.o
    0x0800d4ec   0x0800d4ec   0x00000028   Data   RO         4187    .constdata          c_w.l(_printf_hex_int.o)
    0x0800d514   0x0800d514   0x00000004   PAD
    0x0800d518   0x0800d518   0x00000038   Data   RO         4280    .constdata          m_wm.l(log.o)
    0x0800d550   0x0800d550   0x00000008   Data   RO         4348    .constdata          m_wm.l(qnan.o)
    0x0800d558   0x0800d558   0x00000094   Data   RO         4387    .constdata          c_w.l(bigflt0.o)
    0x0800d5ec   0x0800d5ec   0x00000020   Data   RO         4561    Region$$Table       anon$$obj.o
    0x0800d60c   0x0800d60c   0x0000001c   Data   RO         4415    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800d628   0x0800d628   0x00000110   Data   RO         4490    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800d738, Size: 0x00000b10, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800d738   0x00000005   Data   RW           15    .data               main.o
    0x20000005   0x0800d73d   0x00000003   PAD
    0x20000008   0x0800d740   0x00000004   Data   RW          363    .data               system_stm32f4xx.o
    0x2000000c   0x0800d744   0x00000004   Data   RW          401    .data               delay.o
    0x20000010   0x0800d748   0x00000007   Data   RW          523    .data               usart.o
    0x20000017   0x0800d74f   0x00000001   PAD
    0x20000018   0x0800d750   0x00000009   Data   RW          618    .data               stm32f4xx_hal.o
    0x20000021   0x0800d759   0x00000003   PAD
    0x20000024   0x0800d75c   0x00000008   Data   RW         3692    .data               lcd.o
    0x2000002c   0x0800d764   0x00000004   PAD
    0x20000030   0x0800d768   0x0000004d   Data   RW         3931    .data               bldc.o
    0x2000007d   0x0800d7b5   0x00000003   PAD
    0x20000080   0x0800d7b8   0x0000001d   Data   RW         4049    .data               bldc_tim.o
    0x2000009d   0x0800d7d5   0x00000003   PAD
    0x200000a0        -       0x0000010c   Zero   RW          522    .bss                usart.o
    0x200001ac        -       0x0000005e   Zero   RW         3690    .bss                lcd.o
    0x2000020a   0x0800d7d5   0x00000002   PAD
    0x2000020c        -       0x000001e8   Zero   RW         4048    .bss                bldc_tim.o
    0x200003f4        -       0x000002ba   Zero   RW         4117    .bss                adc.o
    0x200006ae   0x0800d7d5   0x00000002   PAD
    0x200006b0        -       0x00000060   Zero   RW         4423    .bss                c_w.l(libspace.o)
    0x20000710        -       0x00000000   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000710        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       688         64          0          0        698       4653   adc.o
      1676        186         52         77          0      16720   bldc.o
      1780        138          0         29        488       8135   bldc_tim.o
       102         10          0          4          0       2651   delay.o
       192          8          0          0          0       1387   key.o
     13700        178      12160          8         94      29551   lcd.o
       148         10          0          0          0       1291   led.o
      1356        204          0          5          0     722687   main.o
        76         28        392          0       1024       1112   startup_stm32f407xx.o
       174         34          0          9          0      11442   stm32f4xx_hal.o
      1588         66          0          0          0      10327   stm32f4xx_hal_adc.o
       304         22          0          0          0      35763   stm32f4xx_hal_cortex.o
      1434         16          8          0          0       8678   stm32f4xx_hal_dma.o
       564         46          0          0          0       4348   stm32f4xx_hal_gpio.o
      1820         84          0          0          0       6908   stm32f4xx_hal_rcc.o
        88          0          0          0          0       1576   stm32f4xx_hal_sram.o
      2164        150          0          0          0      16073   stm32f4xx_hal_tim.o
         4          0          0          0          0       2121   stm32f4xx_hal_tim_ex.o
      1816         16          0          0          0      13196   stm32f4xx_hal_uart.o
        32          0          0          0          0       5798   stm32f4xx_it.o
       232         10          0          0          0       3463   stm32f4xx_ll_fsmc.o
       204         12          0          0          0       1707   sys.o
        20          6         24          4          0       1891   system_stm32f4xx.o
       504         60          0          7        268       7117   usart.o

    ----------------------------------------------------------------------
     30696       <USER>      <GROUP>        160       2576     918595   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        30          0          2         17          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
       224          0          0          0          0         96   _scanf_str.o
        64          0          0          0          0         84   _sgetc.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
        94          4          0          0          0        140   dfix.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
       100         28          0          0          0        372   dunder.o
        48          0          0          0          0        124   fpclassify.o
       964         92         56          0          0        316   log.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o

    ----------------------------------------------------------------------
     11174        <USER>        <GROUP>          0         96       8016   Library Totals
        30          0          4          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      6750        236        488          0         96       3944   c_w.l
      3034        200          0          0          0       3108   fz_wm.l
      1360        120         64          0          0        964   m_wm.l

    ----------------------------------------------------------------------
     11174        <USER>        <GROUP>          0         96       8016   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     41870       1904      13226        160       2672     907275   Grand Totals
     41870       1904      13226        160       2672     907275   ELF Image Totals
     41870       1904      13226        160          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                55096 (  53.80kB)
    Total RW  Size (RW Data + ZI Data)              2832 (   2.77kB)
    Total ROM Size (Code + RO Data + RW Data)      55256 (  53.96kB)

==============================================================================


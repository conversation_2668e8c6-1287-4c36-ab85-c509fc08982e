syntax = "v1"

info (
	title:  "data Service"
	desc:   "data service related api"
	author: ""
	email:  ""
)
//数据结构体
type (

)
//响应-请求结构体
type (
	ListCommonDataRequest {
		Pager Pager `json:"pager"`
	}
	ListCommonDataResponse {
		Pager              Pager            `json:"pager"`
		CommonDataInfoList []CommonDataInfo `json:"commonDataInfoList"`
	}
)
//api
service data-api {
	@handler ListCommonDataHandler
	post /api/data/common_data/list (ListCommonDataRequest) returns (ListCommonDataResponse)
	
}
{"openapi": "3.0.0", "info": {"title": "Device Service API", "description": "API for managing device resources and frames", "version": "1.0.0"}, "paths": {"/api/v1/device/resource/create": {"post": {"summary": "Create a new device resource", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeviceResourceRequest"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeviceResourceResponse"}}}}}}}, "/api/v1/device/resource/delete": {"post": {"summary": "Delete a device resource", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDeviceResourceRequest"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDeviceResourceResponse"}}}}}}}, "/api/v1/device/frame/create": {"post": {"summary": "Create a new device frame", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeviceFrameRequest"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDeviceFrameResponse"}}}}}}}, "/api/v1/device/frame/delete": {"post": {"summary": "Delete a device frame", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDeviceFrameRequest"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDeviceFrameResponse"}}}}}}}, "/api/v1/device/device/list": {"post": {"summary": "List devices", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDeviceRequest"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDeviceResponse"}}}}}}}}, "components": {"schemas": {"CreateDeviceResourceRequest": {"type": "object", "properties": {"deviceResourceInfo": {"$ref": "#/components/schemas/DeviceResourceInfo"}}}, "CreateDeviceResourceResponse": {"type": "object", "properties": {"deviceMeta": {"$ref": "#/components/schemas/DeviceMeta"}, "deviceWorkStatus": {"$ref": "#/components/schemas/DeviceWorkStatus"}}}, "DeleteDeviceResourceRequest": {"type": "object", "properties": {"deviceUID": {"type": "string"}}}, "DeleteDeviceResourceResponse": {"type": "object", "properties": {"deviceMeta": {"$ref": "#/components/schemas/DeviceMeta"}, "deviceWorkStatus": {"$ref": "#/components/schemas/DeviceWorkStatus"}, "deviceResourceInfo": {"$ref": "#/components/schemas/DeviceResourceInfo"}}}, "CreateDeviceFrameRequest": {"type": "object", "properties": {"deviceUID": {"type": "string"}, "frameInfos": {"type": "array", "items": {"$ref": "#/components/schemas/FrameInfo"}}}}, "CreateDeviceFrameResponse": {"type": "object", "properties": {"deviceMeta": {"$ref": "#/components/schemas/DeviceMeta"}, "deviceWorkStatus": {"$ref": "#/components/schemas/DeviceWorkStatus"}}}, "DeleteDeviceFrameRequest": {"type": "object", "properties": {"deviceUID": {"type": "string"}, "frameMetas": {"type": "array", "items": {"$ref": "#/components/schemas/FrameMeta"}}}}, "DeleteDeviceFrameResponse": {"type": "object", "properties": {"deviceMeta": {"$ref": "#/components/schemas/DeviceMeta"}, "frameInfos": {"type": "array", "items": {"$ref": "#/components/schemas/FrameInfo"}}, "deviceWorkStatus": {"$ref": "#/components/schemas/DeviceWorkStatus"}}}, "ListDeviceRequest": {"type": "object", "properties": {"deviceUID": {"type": "string", "nullable": true}, "frameMetas": {"type": "array", "items": {"$ref": "#/components/schemas/FrameMeta"}, "nullable": true}, "healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"], "nullable": true}}}, "ListDeviceResponse": {"type": "object", "properties": {"devices": {"type": "array", "items": {"$ref": "#/components/schemas/ListDevice"}}}}, "DeviceResourceInfo": {"type": "object", "properties": {"ip": {"type": "string"}, "hostname": {"type": "string", "nullable": true}, "type": {"type": "string", "enum": ["fan", "motor", "virtual", "pc", "edge"], "nullable": true}, "os": {"type": "string", "nullable": true}, "cpu": {"$ref": "#/components/schemas/ResourceDef", "nullable": true}, "gpu": {"$ref": "#/components/schemas/ResourceDef", "nullable": true}, "disk": {"$ref": "#/components/schemas/ResourceDef", "nullable": true}, "mem": {"$ref": "#/components/schemas/ResourceDef", "nullable": true}, "protocol": {"type": "array", "items": {"type": "string"}}}}, "ResourceDef": {"type": "object", "properties": {"amount": {"type": "integer", "format": "int64"}, "type": {"type": "string"}, "unit": {"type": "string"}}}, "DeviceMeta": {"type": "object", "properties": {"deviceUID": {"type": "string"}, "frameMetas": {"type": "array", "items": {"$ref": "#/components/schemas/FrameMeta"}}, "deviceDesc": {"type": "string", "nullable": true}}}, "DeviceWorkStatus": {"type": "object", "properties": {"healthStatus": {"type": "string", "enum": ["init", "ready", "running", "pending", "error"]}, "timestamp": {"type": "integer", "format": "int64"}, "workMode": {"type": "string", "enum": ["distributed", "centralized"]}}}, "FrameInfo": {"type": "object", "properties": {"frameMeta": {"$ref": "#/components/schemas/FrameMeta"}, "frameLibs": {"$ref": "#/components/schemas/FrameLibs"}}}, "FrameMeta": {"type": "object", "properties": {"frameUID": {"type": "string", "nullable": true}, "frameType": {"type": "string", "enum": ["modbus", "uart"]}}}, "FrameLibs": {"type": "object", "properties": {"modbusInfo": {"$ref": "#/components/schemas/ModbusInfo", "nullable": true}, "uartInfo": {"$ref": "#/components/schemas/UartInfo", "nullable": true}}}, "ModbusInfo": {"type": "object", "properties": {"tid": {"type": "string"}, "pid": {"type": "string"}, "len": {"type": "string"}, "uid": {"type": "string"}, "fc": {"type": "string"}, "datas": {"type": "array", "items": {"$ref": "#/components/schemas/DataDef"}, "nullable": true}}}, "UartInfo": {"type": "object", "properties": {"header": {"type": "string"}, "addr": {"type": "string"}, "cmd": {"type": "string"}, "tail": {"type": "string"}, "datas": {"type": "array", "items": {"$ref": "#/components/schemas/DataDef"}, "nullable": true}}}, "DataDef": {"type": "object", "properties": {"index": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}}}, "ListDevice": {"type": "object", "properties": {"deviceMeta": {"$ref": "#/components/schemas/DeviceMeta"}, "deviceWorkStatus": {"$ref": "#/components/schemas/DeviceWorkStatus"}, "deviceResourceInfo": {"$ref": "#/components/schemas/DeviceResourceInfo"}, "frameInfos": {"type": "array", "items": {"$ref": "#/components/schemas/FrameInfo"}}}}}}}
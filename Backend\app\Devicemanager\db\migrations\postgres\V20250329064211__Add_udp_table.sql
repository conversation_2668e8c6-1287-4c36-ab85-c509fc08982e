-- create "udps" table
CREATE TABLE "udps" ("id" character varying NOT NULL, "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, "device_id" character varying NOT NULL, PRIMARY KEY ("id"), CONSTRAINT "udps_devices_udpConfig" FOREIGN KEY ("device_id") REFERENCES "devices" ("id") ON DELETE NO ACTION);
-- create index "udps_device_id_key" to table: "udps"
CREATE UNIQUE INDEX "udps_device_id_key" ON "udps" ("device_id");
-- modify "data" table
ALTER TABLE "data" ADD CONSTRAINT "data_udps_data_points" FOREIGN KEY ("frame_id") REFERENCES "udps" ("id") ON DELETE NO ACTION;

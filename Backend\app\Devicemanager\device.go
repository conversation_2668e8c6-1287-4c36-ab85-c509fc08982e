package main

import (
	"flag"

	"GCF/app/Devicemanager/internal/config"
	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/handler"
	"GCF/app/Devicemanager/internal/server"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/pkg/servicex"

	"github.com/zeromicro/go-zero/rest"
	"google.golang.org/grpc"
)

func main() {
	flag.Parse()

	var c config.Config
	servicex.MustLoadConfigFile(&c)
	svcCtx := svc.NewServiceContext(c)

	servicex.Main(
		&c,
		func(server *rest.Server) {
			handler.RegisterHandlers(server, svcCtx)
		},
		func(grpcServer *grpc.Server) {
			devicemanager.RegisterDeviceServer(grpcServer, server.NewDeviceServer(svcCtx))
		},
	)
}

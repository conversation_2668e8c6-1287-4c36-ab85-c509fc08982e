// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1
// Source: device.proto

package device

import (
	"context"

	"GCF/app/Devicemanager/internal/devicemanager"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CheckDeviceHealthRequest        = devicemanager.CheckDeviceHealthRequest
	CheckDeviceHealthResponse       = devicemanager.CheckDeviceHealthResponse
	ControlDeviceByDeviceIDRequest  = devicemanager.ControlDeviceByDeviceIDRequest
	ControlDeviceByDeviceIDResponse = devicemanager.ControlDeviceByDeviceIDResponse
	DeviceResourceInfo              = devicemanager.DeviceResourceInfo
	DeviceWorkStatus                = devicemanager.DeviceWorkStatus
	FrameInfo                       = devicemanager.FrameInfo
	FrameLibs                       = devicemanager.FrameLibs
	FrameMeta                       = devicemanager.FrameMeta
	GetDeviceByIDRequest            = devicemanager.GetDeviceByIDRequest
	GetDeviceByIDResponse           = devicemanager.GetDeviceByIDResponse
	GetFramesByDeviceIDRequest      = devicemanager.GetFramesByDeviceIDRequest
	GetFramesByDeviceIDResponse     = devicemanager.GetFramesByDeviceIDResponse
	ModbusInfo                      = devicemanager.ModbusInfo
	ResourceDef                     = devicemanager.ResourceDef
	UartInfo                        = devicemanager.UartInfo
	UdpInfo                         = devicemanager.UdpInfo

	Device interface {
		GetFramesByDeviceID(ctx context.Context, in *GetFramesByDeviceIDRequest, opts ...grpc.CallOption) (*GetFramesByDeviceIDResponse, error)
		GetDeviceByID(ctx context.Context, in *GetDeviceByIDRequest, opts ...grpc.CallOption) (*GetDeviceByIDResponse, error)
		CheckDeviceHealth(ctx context.Context, in *CheckDeviceHealthRequest, opts ...grpc.CallOption) (*CheckDeviceHealthResponse, error)
		ControlDeviceByDeviceID(ctx context.Context, in *ControlDeviceByDeviceIDRequest, opts ...grpc.CallOption) (*ControlDeviceByDeviceIDResponse, error)
	}

	defaultDevice struct {
		cli zrpc.Client
	}
)

func NewDevice(cli zrpc.Client) Device {
	return &defaultDevice{
		cli: cli,
	}
}

func (m *defaultDevice) GetFramesByDeviceID(ctx context.Context, in *GetFramesByDeviceIDRequest, opts ...grpc.CallOption) (*GetFramesByDeviceIDResponse, error) {
	client := devicemanager.NewDeviceClient(m.cli.Conn())
	return client.GetFramesByDeviceID(ctx, in, opts...)
}

func (m *defaultDevice) GetDeviceByID(ctx context.Context, in *GetDeviceByIDRequest, opts ...grpc.CallOption) (*GetDeviceByIDResponse, error) {
	client := devicemanager.NewDeviceClient(m.cli.Conn())
	return client.GetDeviceByID(ctx, in, opts...)
}

func (m *defaultDevice) CheckDeviceHealth(ctx context.Context, in *CheckDeviceHealthRequest, opts ...grpc.CallOption) (*CheckDeviceHealthResponse, error) {
	client := devicemanager.NewDeviceClient(m.cli.Conn())
	return client.CheckDeviceHealth(ctx, in, opts...)
}

func (m *defaultDevice) ControlDeviceByDeviceID(ctx context.Context, in *ControlDeviceByDeviceIDRequest, opts ...grpc.CallOption) (*ControlDeviceByDeviceIDResponse, error) {
	client := devicemanager.NewDeviceClient(m.cli.Conn())
	return client.ControlDeviceByDeviceID(ctx, in, opts...)
}

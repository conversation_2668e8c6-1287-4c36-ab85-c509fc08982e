// Code generated by MockGen. DO NOT EDIT.
// Source: GCF/app/Devicemanager/device (interfaces: Device)

// Package mock is a generated GoMock package.
package mock

import (
	devicemanager "GCF/app/Devicemanager/internal/devicemanager"
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevice is a mock of Device interface.
type MockDevice struct {
	ctrl     *gomock.Controller
	recorder *MockDeviceMockRecorder
}

// MockDeviceMockRecorder is the mock recorder for MockDevice.
type MockDeviceMockRecorder struct {
	mock *MockDevice
}

// NewMockDevice creates a new mock instance.
func NewMockDevice(ctrl *gomock.Controller) *MockDevice {
	mock := &MockDevice{ctrl: ctrl}
	mock.recorder = &MockDeviceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevice) EXPECT() *MockDeviceMockRecorder {
	return m.recorder
}

// CheckDeviceHealth mocks base method.
func (m *MockDevice) CheckDeviceHealth(arg0 context.Context, arg1 *devicemanager.CheckDeviceHealthRequest, arg2 ...grpc.CallOption) (*devicemanager.CheckDeviceHealthResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckDeviceHealth", varargs...)
	ret0, _ := ret[0].(*devicemanager.CheckDeviceHealthResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckDeviceHealth indicates an expected call of CheckDeviceHealth.
func (mr *MockDeviceMockRecorder) CheckDeviceHealth(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDeviceHealth", reflect.TypeOf((*MockDevice)(nil).CheckDeviceHealth), varargs...)
}

// ControlDeviceByDeviceID mocks base method.
func (m *MockDevice) ControlDeviceByDeviceID(arg0 context.Context, arg1 *devicemanager.ControlDeviceByDeviceIDRequest, arg2 ...grpc.CallOption) (*devicemanager.ControlDeviceByDeviceIDResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ControlDeviceByDeviceID", varargs...)
	ret0, _ := ret[0].(*devicemanager.ControlDeviceByDeviceIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ControlDeviceByDeviceID indicates an expected call of ControlDeviceByDeviceID.
func (mr *MockDeviceMockRecorder) ControlDeviceByDeviceID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ControlDeviceByDeviceID", reflect.TypeOf((*MockDevice)(nil).ControlDeviceByDeviceID), varargs...)
}

// GetDeviceByID mocks base method.
func (m *MockDevice) GetDeviceByID(arg0 context.Context, arg1 *devicemanager.GetDeviceByIDRequest, arg2 ...grpc.CallOption) (*devicemanager.GetDeviceByIDResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDeviceByID", varargs...)
	ret0, _ := ret[0].(*devicemanager.GetDeviceByIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeviceByID indicates an expected call of GetDeviceByID.
func (mr *MockDeviceMockRecorder) GetDeviceByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceByID", reflect.TypeOf((*MockDevice)(nil).GetDeviceByID), varargs...)
}

// GetFramesByDeviceID mocks base method.
func (m *MockDevice) GetFramesByDeviceID(arg0 context.Context, arg1 *devicemanager.GetFramesByDeviceIDRequest, arg2 ...grpc.CallOption) (*devicemanager.GetFramesByDeviceIDResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFramesByDeviceID", varargs...)
	ret0, _ := ret[0].(*devicemanager.GetFramesByDeviceIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFramesByDeviceID indicates an expected call of GetFramesByDeviceID.
func (mr *MockDeviceMockRecorder) GetFramesByDeviceID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFramesByDeviceID", reflect.TypeOf((*MockDevice)(nil).GetFramesByDeviceID), varargs...)
}

# Device Etcd 写入架构

基于 zetcd 包的设备数据 etcd 写入架构，支持灵活的数据格式和完整的设备生命周期管理。

## 功能特性

### 1. 核心功能
- ✅ 服务注册与发现
- ✅ 设备数据写入/读取
- ✅ 设备配置管理
- ✅ 设备状态管理
- ✅ 批量数据操作
- ✅ 实时数据监听
- ✅ 多种数据格式支持（JSON、KV、CSV）

### 2. 数据格式支持
- **JSON 格式**: 适合复杂数据结构
- **KV 格式**: 适合简单键值对查询
- **CSV 格式**: 适合数据导出和分析

### 3. etcd 键结构
```
device/{device_id}/data     - 设备数据
device/{device_id}/config   - 设备配置
device/{device_id}/status   - 设备状态
device/{device_id}/commands - 控制命令历史
```

## 快速开始

### 1. 配置 etcd 连接

在 `device.yaml` 中配置：
```yaml
ZEtcdConf:
  Endpoints:
    - 127.0.0.1:2379
  ID: 12345
```

### 2. 初始化管理器

```go
import "GCF/app/Devicemanager/etcd"

// 创建设备 etcd 管理器
etcdManager := etcdregister.NewDeviceEtcdManager(svcCtx)

// 注册设备服务
err := etcdManager.RegisterDeviceService("device-manager", "localhost:8080")
```

### 3. 写入设备数据

```go
// 定义数据表字段
tableFields := []string{"device_id", "name", "type", "temperature", "humidity"}

// 准备设备数据
deviceData := map[string]interface{}{
    "device_id":   "device001",
    "name":        "Temperature Sensor 1",
    "type":        "sensor",
    "temperature": 25.5,
    "humidity":    60.2,
}

// 写入到 etcd
err := etcdManager.WriteDeviceData("device001", tableFields, deviceData)
```

### 4. 读取设备数据

```go
// 从 etcd 读取设备数据
data, err := etcdManager.ReadDeviceData("device001")
if err != nil {
    log.Printf("Failed to read device data: %v", err)
    return
}

fmt.Printf("Device data: %+v\n", data)
```

## 高级用法

### 1. 自定义数据格式

```go
// 创建数据构建器
builder := &etcdregister.DeviceDataBuilder{
    TableFields:    []string{"device_id", "temperature"},
    DeviceData:     map[string]interface{}{"device_id": "dev001", "temperature": 25.5},
    AdditionalData: map[string]interface{}{"timestamp": time.Now().Unix()},
    Format:         "json", // 或 "kv", "csv"
}

// 构建数据字符串
dataString, err := etcdManager.BuildDataString(builder)
```

### 2. 批量操作

```go
// 批量写入多个设备数据
devices := map[string]map[string]interface{}{
    "device001": {"temperature": 25.5, "humidity": 60.2},
    "device002": {"temperature": 22.3, "humidity": 55.8},
}

err := etcdManager.BatchWriteDeviceData(devices, tableFields)
```

### 3. 实时监听

```go
// 监听设备变化
etcdManager.WatchDeviceChanges("device001", func(key, value string) {
    fmt.Printf("Device change: %s = %s\n", key, value)
})
```

### 4. 设备配置管理

```go
// 写入设备配置
config := map[string]interface{}{
    "sampling_rate": 1000,
    "threshold":     30.0,
    "enabled":       true,
}

err := etcdManager.WriteDeviceConfig("device001", config)

// 更新设备状态
err = etcdManager.WriteDeviceStatus("device001", "online")
```

## 在业务逻辑中集成

### 1. 在 Logic 中使用

```go
// internal/logic/registerdevicelogic.go
func (l *RegisterDeviceLogic) RegisterDevice(req *types.RegisterDeviceRequest) error {
    // 创建 etcd 管理器
    etcdManager := etcdregister.NewDeviceEtcdManager(l.svcCtx)
    
    // 处理设备注册
    processor := etcdregister.NewDeviceDataProcessor(l.svcCtx)
    return processor.ProcessDeviceRegistration(req)
}
```

### 2. 在主程序中初始化

```go
// device.go
func main() {
    var c config.Config
    servicex.MustLoadConfigFile(&c)
    
    // 初始化 etcd 架构
    processor, err := etcdregister.InitializeEtcdArchitecture(c)
    if err != nil {
        log.Fatal(err)
    }
    
    // 启动设备监控
    processor.StartDeviceMonitoring("device001")
    
    // 启动服务...
}
```

## 数据格式示例

### JSON 格式输出
```json
{
  "device_id": "device001",
  "temperature": 25.5,
  "humidity": 60.2,
  "timestamp": 1704879000,
  "source": "device_manager",
  "version": "v1.0"
}
```

### KV 格式输出
```
device_id=device001&temperature=25.5&humidity=60.2&timestamp=1704879000&source=device_manager&version=v1.0
```

### CSV 格式输出
```
device001,25.5,60.2,1704879000,device_manager,v1.0
```

## 错误处理

所有方法都返回 error，建议进行适当的错误处理：

```go
err := etcdManager.WriteDeviceData(deviceID, tableFields, deviceData)
if err != nil {
    logx.Errorf("Failed to write device data: %v", err)
    return err
}
```

## 性能优化建议

1. **批量操作**: 对于大量设备，使用 `BatchWriteDeviceData`
2. **数据格式**: JSON 格式功能最全，KV 格式性能最好
3. **监听管理**: 合理使用 Watch 功能，避免过多的监听器
4. **键设计**: 使用有意义的键结构，便于查询和管理

## 注意事项

1. 确保 etcd 服务正常运行
2. 合理设计数据表字段，避免频繁变更
3. 监听回调函数要处理异常，避免阻塞
4. 定期清理过期数据，避免 etcd 存储过载

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v5.29.3
// source: rpc/device.proto

package devicemanager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFramesByDeviceIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFramesByDeviceIDRequest) Reset() {
	*x = GetFramesByDeviceIDRequest{}
	mi := &file_rpc_device_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFramesByDeviceIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFramesByDeviceIDRequest) ProtoMessage() {}

func (x *GetFramesByDeviceIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFramesByDeviceIDRequest.ProtoReflect.Descriptor instead.
func (*GetFramesByDeviceIDRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{0}
}

func (x *GetFramesByDeviceIDRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetFramesByDeviceIDResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Frames        []*FrameInfo           `protobuf:"bytes,1,rep,name=frames,proto3" json:"frames,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFramesByDeviceIDResponse) Reset() {
	*x = GetFramesByDeviceIDResponse{}
	mi := &file_rpc_device_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFramesByDeviceIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFramesByDeviceIDResponse) ProtoMessage() {}

func (x *GetFramesByDeviceIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFramesByDeviceIDResponse.ProtoReflect.Descriptor instead.
func (*GetFramesByDeviceIDResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{1}
}

func (x *GetFramesByDeviceIDResponse) GetFrames() []*FrameInfo {
	if x != nil {
		return x.Frames
	}
	return nil
}

type FrameInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FrameMeta     *FrameMeta             `protobuf:"bytes,1,opt,name=frame_meta,json=frameMeta,proto3" json:"frame_meta,omitempty"`
	FrameLib      *FrameLibs             `protobuf:"bytes,2,opt,name=frame_lib,json=frameLib,proto3" json:"frame_lib,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrameInfo) Reset() {
	*x = FrameInfo{}
	mi := &file_rpc_device_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameInfo) ProtoMessage() {}

func (x *FrameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameInfo.ProtoReflect.Descriptor instead.
func (*FrameInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{2}
}

func (x *FrameInfo) GetFrameMeta() *FrameMeta {
	if x != nil {
		return x.FrameMeta
	}
	return nil
}

func (x *FrameInfo) GetFrameLib() *FrameLibs {
	if x != nil {
		return x.FrameLib
	}
	return nil
}

type FrameMeta struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FrameUid      string                 `protobuf:"bytes,1,opt,name=frame_uid,json=frameUid,proto3" json:"frame_uid,omitempty"`
	FrameType     string                 `protobuf:"bytes,2,opt,name=frame_type,json=frameType,proto3" json:"frame_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrameMeta) Reset() {
	*x = FrameMeta{}
	mi := &file_rpc_device_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameMeta) ProtoMessage() {}

func (x *FrameMeta) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameMeta.ProtoReflect.Descriptor instead.
func (*FrameMeta) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{3}
}

func (x *FrameMeta) GetFrameUid() string {
	if x != nil {
		return x.FrameUid
	}
	return ""
}

func (x *FrameMeta) GetFrameType() string {
	if x != nil {
		return x.FrameType
	}
	return ""
}

type FrameLibs struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ModbusInfo    *ModbusInfo            `protobuf:"bytes,1,opt,name=modbus_info,json=modbusInfo,proto3" json:"modbus_info,omitempty"`
	UartInfo      *UartInfo              `protobuf:"bytes,2,opt,name=uart_info,json=uartInfo,proto3" json:"uart_info,omitempty"`
	UdpInfo       *UdpInfo               `protobuf:"bytes,3,opt,name=udp_info,json=udpInfo,proto3" json:"udp_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FrameLibs) Reset() {
	*x = FrameLibs{}
	mi := &file_rpc_device_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FrameLibs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrameLibs) ProtoMessage() {}

func (x *FrameLibs) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrameLibs.ProtoReflect.Descriptor instead.
func (*FrameLibs) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{4}
}

func (x *FrameLibs) GetModbusInfo() *ModbusInfo {
	if x != nil {
		return x.ModbusInfo
	}
	return nil
}

func (x *FrameLibs) GetUartInfo() *UartInfo {
	if x != nil {
		return x.UartInfo
	}
	return nil
}

func (x *FrameLibs) GetUdpInfo() *UdpInfo {
	if x != nil {
		return x.UdpInfo
	}
	return nil
}

type ModbusInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tid           string                 `protobuf:"bytes,1,opt,name=tid,proto3" json:"tid,omitempty"`
	Pid           string                 `protobuf:"bytes,2,opt,name=pid,proto3" json:"pid,omitempty"`
	Len           string                 `protobuf:"bytes,3,opt,name=len,proto3" json:"len,omitempty"`
	Uid           string                 `protobuf:"bytes,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Fc            string                 `protobuf:"bytes,5,opt,name=fc,proto3" json:"fc,omitempty"`
	Datas         string                 `protobuf:"bytes,6,opt,name=datas,proto3" json:"datas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModbusInfo) Reset() {
	*x = ModbusInfo{}
	mi := &file_rpc_device_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModbusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModbusInfo) ProtoMessage() {}

func (x *ModbusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModbusInfo.ProtoReflect.Descriptor instead.
func (*ModbusInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{5}
}

func (x *ModbusInfo) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *ModbusInfo) GetPid() string {
	if x != nil {
		return x.Pid
	}
	return ""
}

func (x *ModbusInfo) GetLen() string {
	if x != nil {
		return x.Len
	}
	return ""
}

func (x *ModbusInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ModbusInfo) GetFc() string {
	if x != nil {
		return x.Fc
	}
	return ""
}

func (x *ModbusInfo) GetDatas() string {
	if x != nil {
		return x.Datas
	}
	return ""
}

type UartInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        string                 `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Cmd           string                 `protobuf:"bytes,3,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Tail          string                 `protobuf:"bytes,4,opt,name=tail,proto3" json:"tail,omitempty"`
	Datas         string                 `protobuf:"bytes,5,opt,name=datas,proto3" json:"datas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UartInfo) Reset() {
	*x = UartInfo{}
	mi := &file_rpc_device_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UartInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UartInfo) ProtoMessage() {}

func (x *UartInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UartInfo.ProtoReflect.Descriptor instead.
func (*UartInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{6}
}

func (x *UartInfo) GetHeader() string {
	if x != nil {
		return x.Header
	}
	return ""
}

func (x *UartInfo) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *UartInfo) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *UartInfo) GetTail() string {
	if x != nil {
		return x.Tail
	}
	return ""
}

func (x *UartInfo) GetDatas() string {
	if x != nil {
		return x.Datas
	}
	return ""
}

type UdpInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Header        string                 `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	TypeId        string                 `protobuf:"bytes,3,opt,name=type_id,json=typeId,proto3" json:"type_id,omitempty"`
	Datas         string                 `protobuf:"bytes,4,opt,name=datas,proto3" json:"datas,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UdpInfo) Reset() {
	*x = UdpInfo{}
	mi := &file_rpc_device_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UdpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UdpInfo) ProtoMessage() {}

func (x *UdpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UdpInfo.ProtoReflect.Descriptor instead.
func (*UdpInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{7}
}

func (x *UdpInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UdpInfo) GetHeader() string {
	if x != nil {
		return x.Header
	}
	return ""
}

func (x *UdpInfo) GetTypeId() string {
	if x != nil {
		return x.TypeId
	}
	return ""
}

func (x *UdpInfo) GetDatas() string {
	if x != nil {
		return x.Datas
	}
	return ""
}

type GetDeviceByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceByIDRequest) Reset() {
	*x = GetDeviceByIDRequest{}
	mi := &file_rpc_device_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceByIDRequest) ProtoMessage() {}

func (x *GetDeviceByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceByIDRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceByIDRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{8}
}

func (x *GetDeviceByIDRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetDeviceByIDResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	DeviceResourceInfo *DeviceResourceInfo    `protobuf:"bytes,1,opt,name=device_resource_info,json=deviceResourceInfo,proto3" json:"device_resource_info,omitempty"`
	DeviceWorkStatus   *DeviceWorkStatus      `protobuf:"bytes,2,opt,name=device_work_status,json=deviceWorkStatus,proto3" json:"device_work_status,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetDeviceByIDResponse) Reset() {
	*x = GetDeviceByIDResponse{}
	mi := &file_rpc_device_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceByIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceByIDResponse) ProtoMessage() {}

func (x *GetDeviceByIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceByIDResponse.ProtoReflect.Descriptor instead.
func (*GetDeviceByIDResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{9}
}

func (x *GetDeviceByIDResponse) GetDeviceResourceInfo() *DeviceResourceInfo {
	if x != nil {
		return x.DeviceResourceInfo
	}
	return nil
}

func (x *GetDeviceByIDResponse) GetDeviceWorkStatus() *DeviceWorkStatus {
	if x != nil {
		return x.DeviceWorkStatus
	}
	return nil
}

type DeviceResourceInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Hostname      string                 `protobuf:"bytes,2,opt,name=hostname,proto3" json:"hostname,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Os            string                 `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`
	Cpu           *ResourceDef           `protobuf:"bytes,5,opt,name=cpu,proto3" json:"cpu,omitempty"`
	Gpu           *ResourceDef           `protobuf:"bytes,6,opt,name=gpu,proto3" json:"gpu,omitempty"`
	Disk          *ResourceDef           `protobuf:"bytes,7,opt,name=disk,proto3" json:"disk,omitempty"`
	Mem           *ResourceDef           `protobuf:"bytes,8,opt,name=mem,proto3" json:"mem,omitempty"`
	Protocol      []string               `protobuf:"bytes,9,rep,name=protocol,proto3" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceResourceInfo) Reset() {
	*x = DeviceResourceInfo{}
	mi := &file_rpc_device_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceResourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceResourceInfo) ProtoMessage() {}

func (x *DeviceResourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceResourceInfo.ProtoReflect.Descriptor instead.
func (*DeviceResourceInfo) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{10}
}

func (x *DeviceResourceInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DeviceResourceInfo) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *DeviceResourceInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DeviceResourceInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *DeviceResourceInfo) GetCpu() *ResourceDef {
	if x != nil {
		return x.Cpu
	}
	return nil
}

func (x *DeviceResourceInfo) GetGpu() *ResourceDef {
	if x != nil {
		return x.Gpu
	}
	return nil
}

func (x *DeviceResourceInfo) GetDisk() *ResourceDef {
	if x != nil {
		return x.Disk
	}
	return nil
}

func (x *DeviceResourceInfo) GetMem() *ResourceDef {
	if x != nil {
		return x.Mem
	}
	return nil
}

func (x *DeviceResourceInfo) GetProtocol() []string {
	if x != nil {
		return x.Protocol
	}
	return nil
}

type ResourceDef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Amount        int64                  `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Unit          string                 `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceDef) Reset() {
	*x = ResourceDef{}
	mi := &file_rpc_device_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceDef) ProtoMessage() {}

func (x *ResourceDef) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceDef.ProtoReflect.Descriptor instead.
func (*ResourceDef) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{11}
}

func (x *ResourceDef) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ResourceDef) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResourceDef) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

type DeviceWorkStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HealthStatus  string                 `protobuf:"bytes,1,opt,name=health_status,json=healthStatus,proto3" json:"health_status,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	WorkMode      string                 `protobuf:"bytes,3,opt,name=work_mode,json=workMode,proto3" json:"work_mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceWorkStatus) Reset() {
	*x = DeviceWorkStatus{}
	mi := &file_rpc_device_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceWorkStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceWorkStatus) ProtoMessage() {}

func (x *DeviceWorkStatus) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceWorkStatus.ProtoReflect.Descriptor instead.
func (*DeviceWorkStatus) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{12}
}

func (x *DeviceWorkStatus) GetHealthStatus() string {
	if x != nil {
		return x.HealthStatus
	}
	return ""
}

func (x *DeviceWorkStatus) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DeviceWorkStatus) GetWorkMode() string {
	if x != nil {
		return x.WorkMode
	}
	return ""
}

type CheckDeviceHealthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckDeviceHealthRequest) Reset() {
	*x = CheckDeviceHealthRequest{}
	mi := &file_rpc_device_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDeviceHealthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDeviceHealthRequest) ProtoMessage() {}

func (x *CheckDeviceHealthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDeviceHealthRequest.ProtoReflect.Descriptor instead.
func (*CheckDeviceHealthRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{13}
}

func (x *CheckDeviceHealthRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type CheckDeviceHealthResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DeviceWorkStatus *DeviceWorkStatus      `protobuf:"bytes,1,opt,name=device_work_status,json=deviceWorkStatus,proto3" json:"device_work_status,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CheckDeviceHealthResponse) Reset() {
	*x = CheckDeviceHealthResponse{}
	mi := &file_rpc_device_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDeviceHealthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDeviceHealthResponse) ProtoMessage() {}

func (x *CheckDeviceHealthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDeviceHealthResponse.ProtoReflect.Descriptor instead.
func (*CheckDeviceHealthResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{14}
}

func (x *CheckDeviceHealthResponse) GetDeviceWorkStatus() *DeviceWorkStatus {
	if x != nil {
		return x.DeviceWorkStatus
	}
	return nil
}

type ControlDeviceByDeviceIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	FrameInfo     *FrameInfo             `protobuf:"bytes,2,opt,name=frame_info,json=frameInfo,proto3" json:"frame_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlDeviceByDeviceIDRequest) Reset() {
	*x = ControlDeviceByDeviceIDRequest{}
	mi := &file_rpc_device_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlDeviceByDeviceIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlDeviceByDeviceIDRequest) ProtoMessage() {}

func (x *ControlDeviceByDeviceIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlDeviceByDeviceIDRequest.ProtoReflect.Descriptor instead.
func (*ControlDeviceByDeviceIDRequest) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{15}
}

func (x *ControlDeviceByDeviceIDRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ControlDeviceByDeviceIDRequest) GetFrameInfo() *FrameInfo {
	if x != nil {
		return x.FrameInfo
	}
	return nil
}

type ControlDeviceByDeviceIDResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DeviceWorkStatus *DeviceWorkStatus      `protobuf:"bytes,1,opt,name=device_work_status,json=deviceWorkStatus,proto3" json:"device_work_status,omitempty"`
	FrameInfos       *FrameInfo             `protobuf:"bytes,2,opt,name=frame_infos,json=frameInfos,proto3" json:"frame_infos,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ControlDeviceByDeviceIDResponse) Reset() {
	*x = ControlDeviceByDeviceIDResponse{}
	mi := &file_rpc_device_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlDeviceByDeviceIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlDeviceByDeviceIDResponse) ProtoMessage() {}

func (x *ControlDeviceByDeviceIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_device_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlDeviceByDeviceIDResponse.ProtoReflect.Descriptor instead.
func (*ControlDeviceByDeviceIDResponse) Descriptor() ([]byte, []int) {
	return file_rpc_device_proto_rawDescGZIP(), []int{16}
}

func (x *ControlDeviceByDeviceIDResponse) GetDeviceWorkStatus() *DeviceWorkStatus {
	if x != nil {
		return x.DeviceWorkStatus
	}
	return nil
}

func (x *ControlDeviceByDeviceIDResponse) GetFrameInfos() *FrameInfo {
	if x != nil {
		return x.FrameInfos
	}
	return nil
}

var File_rpc_device_proto protoreflect.FileDescriptor

var file_rpc_device_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x72, 0x70, 0x63, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x22, 0x39, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x42, 0x79,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x72, 0x61, 0x6d,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x7b, 0x0a,
	0x09, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x0a, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x09, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x62,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x62, 0x73,
	0x52, 0x08, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x62, 0x22, 0x47, 0x0a, 0x09, 0x46, 0x72,
	0x61, 0x6d, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x5f, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x55, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x09, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x62,
	0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x62, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x64, 0x62, 0x75, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x62, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a,
	0x09, 0x75, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x55, 0x61, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x61, 0x72, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x08, 0x75, 0x64, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x64, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x75,
	0x64, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x7a, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x62, 0x75, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x66, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x66, 0x63, 0x12, 0x14, 0x0a, 0x05,
	0x64, 0x61, 0x74, 0x61, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x22, 0x72, 0x0a, 0x08, 0x55, 0x61, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x22, 0x64, 0x0a, 0x07, 0x55, 0x64, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x79, 0x70, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x61, 0x74, 0x61, 0x73, 0x22, 0x33, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x22, 0xbb, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x79, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x14, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x4d, 0x0a, 0x12, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xba, 0x02, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x2c, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x03, 0x63, 0x70, 0x75, 0x12, 0x2c, 0x0a, 0x03, 0x67, 0x70, 0x75, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x03, 0x67,
	0x70, 0x75, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x69, 0x73, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x04, 0x64, 0x69,
	0x73, 0x6b, 0x12, 0x2c, 0x0a, 0x03, 0x6d, 0x65, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x03, 0x6d, 0x65, 0x6d,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x4d, 0x0a, 0x0b,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x22, 0x72, 0x0a, 0x10, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x22,
	0x37, 0x0a, 0x18, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x12, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x76, 0x0a, 0x1e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0a, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xab, 0x01, 0x0a,
	0x1f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4d, 0x0a, 0x12, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x39, 0x0a, 0x0b, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a,
	0x66, 0x72, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x32, 0xb4, 0x03, 0x0a, 0x06, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6c, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x46, 0x72, 0x61, 0x6d,
	0x65, 0x73, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x12, 0x29, 0x2e, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x73, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x79, 0x49, 0x44, 0x12, 0x23, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x79, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x66, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x12, 0x27, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x17, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x44, 0x12, 0x2d, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2e, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x11, 0x5a, 0x0f, 0x2e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_rpc_device_proto_rawDescOnce sync.Once
	file_rpc_device_proto_rawDescData []byte
)

func file_rpc_device_proto_rawDescGZIP() []byte {
	file_rpc_device_proto_rawDescOnce.Do(func() {
		file_rpc_device_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_device_proto_rawDesc), len(file_rpc_device_proto_rawDesc)))
	})
	return file_rpc_device_proto_rawDescData
}

var file_rpc_device_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_rpc_device_proto_goTypes = []any{
	(*GetFramesByDeviceIDRequest)(nil),      // 0: devicemanager.GetFramesByDeviceIDRequest
	(*GetFramesByDeviceIDResponse)(nil),     // 1: devicemanager.GetFramesByDeviceIDResponse
	(*FrameInfo)(nil),                       // 2: devicemanager.FrameInfo
	(*FrameMeta)(nil),                       // 3: devicemanager.FrameMeta
	(*FrameLibs)(nil),                       // 4: devicemanager.FrameLibs
	(*ModbusInfo)(nil),                      // 5: devicemanager.ModbusInfo
	(*UartInfo)(nil),                        // 6: devicemanager.UartInfo
	(*UdpInfo)(nil),                         // 7: devicemanager.UdpInfo
	(*GetDeviceByIDRequest)(nil),            // 8: devicemanager.GetDeviceByIDRequest
	(*GetDeviceByIDResponse)(nil),           // 9: devicemanager.GetDeviceByIDResponse
	(*DeviceResourceInfo)(nil),              // 10: devicemanager.DeviceResourceInfo
	(*ResourceDef)(nil),                     // 11: devicemanager.ResourceDef
	(*DeviceWorkStatus)(nil),                // 12: devicemanager.DeviceWorkStatus
	(*CheckDeviceHealthRequest)(nil),        // 13: devicemanager.CheckDeviceHealthRequest
	(*CheckDeviceHealthResponse)(nil),       // 14: devicemanager.CheckDeviceHealthResponse
	(*ControlDeviceByDeviceIDRequest)(nil),  // 15: devicemanager.ControlDeviceByDeviceIDRequest
	(*ControlDeviceByDeviceIDResponse)(nil), // 16: devicemanager.ControlDeviceByDeviceIDResponse
}
var file_rpc_device_proto_depIdxs = []int32{
	2,  // 0: devicemanager.GetFramesByDeviceIDResponse.frames:type_name -> devicemanager.FrameInfo
	3,  // 1: devicemanager.FrameInfo.frame_meta:type_name -> devicemanager.FrameMeta
	4,  // 2: devicemanager.FrameInfo.frame_lib:type_name -> devicemanager.FrameLibs
	5,  // 3: devicemanager.FrameLibs.modbus_info:type_name -> devicemanager.ModbusInfo
	6,  // 4: devicemanager.FrameLibs.uart_info:type_name -> devicemanager.UartInfo
	7,  // 5: devicemanager.FrameLibs.udp_info:type_name -> devicemanager.UdpInfo
	10, // 6: devicemanager.GetDeviceByIDResponse.device_resource_info:type_name -> devicemanager.DeviceResourceInfo
	12, // 7: devicemanager.GetDeviceByIDResponse.device_work_status:type_name -> devicemanager.DeviceWorkStatus
	11, // 8: devicemanager.DeviceResourceInfo.cpu:type_name -> devicemanager.ResourceDef
	11, // 9: devicemanager.DeviceResourceInfo.gpu:type_name -> devicemanager.ResourceDef
	11, // 10: devicemanager.DeviceResourceInfo.disk:type_name -> devicemanager.ResourceDef
	11, // 11: devicemanager.DeviceResourceInfo.mem:type_name -> devicemanager.ResourceDef
	12, // 12: devicemanager.CheckDeviceHealthResponse.device_work_status:type_name -> devicemanager.DeviceWorkStatus
	2,  // 13: devicemanager.ControlDeviceByDeviceIDRequest.frame_info:type_name -> devicemanager.FrameInfo
	12, // 14: devicemanager.ControlDeviceByDeviceIDResponse.device_work_status:type_name -> devicemanager.DeviceWorkStatus
	2,  // 15: devicemanager.ControlDeviceByDeviceIDResponse.frame_infos:type_name -> devicemanager.FrameInfo
	0,  // 16: devicemanager.Device.GetFramesByDeviceID:input_type -> devicemanager.GetFramesByDeviceIDRequest
	8,  // 17: devicemanager.Device.GetDeviceByID:input_type -> devicemanager.GetDeviceByIDRequest
	13, // 18: devicemanager.Device.CheckDeviceHealth:input_type -> devicemanager.CheckDeviceHealthRequest
	15, // 19: devicemanager.Device.ControlDeviceByDeviceID:input_type -> devicemanager.ControlDeviceByDeviceIDRequest
	1,  // 20: devicemanager.Device.GetFramesByDeviceID:output_type -> devicemanager.GetFramesByDeviceIDResponse
	9,  // 21: devicemanager.Device.GetDeviceByID:output_type -> devicemanager.GetDeviceByIDResponse
	14, // 22: devicemanager.Device.CheckDeviceHealth:output_type -> devicemanager.CheckDeviceHealthResponse
	16, // 23: devicemanager.Device.ControlDeviceByDeviceID:output_type -> devicemanager.ControlDeviceByDeviceIDResponse
	20, // [20:24] is the sub-list for method output_type
	16, // [16:20] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_rpc_device_proto_init() }
func file_rpc_device_proto_init() {
	if File_rpc_device_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_device_proto_rawDesc), len(file_rpc_device_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_device_proto_goTypes,
		DependencyIndexes: file_rpc_device_proto_depIdxs,
		MessageInfos:      file_rpc_device_proto_msgTypes,
	}.Build()
	File_rpc_device_proto = out.File
	file_rpc_device_proto_goTypes = nil
	file_rpc_device_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: rpc/device.proto

package devicemanager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Device_GetFramesByDeviceID_FullMethodName     = "/devicemanager.Device/GetFramesByDeviceID"
	Device_GetDeviceByID_FullMethodName           = "/devicemanager.Device/GetDeviceByID"
	Device_CheckDeviceHealth_FullMethodName       = "/devicemanager.Device/CheckDeviceHealth"
	Device_ControlDeviceByDeviceID_FullMethodName = "/devicemanager.Device/ControlDeviceByDeviceID"
)

// DeviceClient is the client API for Device service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DeviceClient interface {
	GetFramesByDeviceID(ctx context.Context, in *GetFramesByDeviceIDRequest, opts ...grpc.CallOption) (*GetFramesByDeviceIDResponse, error)
	GetDeviceByID(ctx context.Context, in *GetDeviceByIDRequest, opts ...grpc.CallOption) (*GetDeviceByIDResponse, error)
	CheckDeviceHealth(ctx context.Context, in *CheckDeviceHealthRequest, opts ...grpc.CallOption) (*CheckDeviceHealthResponse, error)
	ControlDeviceByDeviceID(ctx context.Context, in *ControlDeviceByDeviceIDRequest, opts ...grpc.CallOption) (*ControlDeviceByDeviceIDResponse, error)
}

type deviceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeviceClient(cc grpc.ClientConnInterface) DeviceClient {
	return &deviceClient{cc}
}

func (c *deviceClient) GetFramesByDeviceID(ctx context.Context, in *GetFramesByDeviceIDRequest, opts ...grpc.CallOption) (*GetFramesByDeviceIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFramesByDeviceIDResponse)
	err := c.cc.Invoke(ctx, Device_GetFramesByDeviceID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) GetDeviceByID(ctx context.Context, in *GetDeviceByIDRequest, opts ...grpc.CallOption) (*GetDeviceByIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDeviceByIDResponse)
	err := c.cc.Invoke(ctx, Device_GetDeviceByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) CheckDeviceHealth(ctx context.Context, in *CheckDeviceHealthRequest, opts ...grpc.CallOption) (*CheckDeviceHealthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckDeviceHealthResponse)
	err := c.cc.Invoke(ctx, Device_CheckDeviceHealth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deviceClient) ControlDeviceByDeviceID(ctx context.Context, in *ControlDeviceByDeviceIDRequest, opts ...grpc.CallOption) (*ControlDeviceByDeviceIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ControlDeviceByDeviceIDResponse)
	err := c.cc.Invoke(ctx, Device_ControlDeviceByDeviceID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeviceServer is the server API for Device service.
// All implementations must embed UnimplementedDeviceServer
// for forward compatibility.
type DeviceServer interface {
	GetFramesByDeviceID(context.Context, *GetFramesByDeviceIDRequest) (*GetFramesByDeviceIDResponse, error)
	GetDeviceByID(context.Context, *GetDeviceByIDRequest) (*GetDeviceByIDResponse, error)
	CheckDeviceHealth(context.Context, *CheckDeviceHealthRequest) (*CheckDeviceHealthResponse, error)
	ControlDeviceByDeviceID(context.Context, *ControlDeviceByDeviceIDRequest) (*ControlDeviceByDeviceIDResponse, error)
	mustEmbedUnimplementedDeviceServer()
}

// UnimplementedDeviceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDeviceServer struct{}

func (UnimplementedDeviceServer) GetFramesByDeviceID(context.Context, *GetFramesByDeviceIDRequest) (*GetFramesByDeviceIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFramesByDeviceID not implemented")
}
func (UnimplementedDeviceServer) GetDeviceByID(context.Context, *GetDeviceByIDRequest) (*GetDeviceByIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeviceByID not implemented")
}
func (UnimplementedDeviceServer) CheckDeviceHealth(context.Context, *CheckDeviceHealthRequest) (*CheckDeviceHealthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckDeviceHealth not implemented")
}
func (UnimplementedDeviceServer) ControlDeviceByDeviceID(context.Context, *ControlDeviceByDeviceIDRequest) (*ControlDeviceByDeviceIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ControlDeviceByDeviceID not implemented")
}
func (UnimplementedDeviceServer) mustEmbedUnimplementedDeviceServer() {}
func (UnimplementedDeviceServer) testEmbeddedByValue()                {}

// UnsafeDeviceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeviceServer will
// result in compilation errors.
type UnsafeDeviceServer interface {
	mustEmbedUnimplementedDeviceServer()
}

func RegisterDeviceServer(s grpc.ServiceRegistrar, srv DeviceServer) {
	// If the following call pancis, it indicates UnimplementedDeviceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Device_ServiceDesc, srv)
}

func _Device_GetFramesByDeviceID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFramesByDeviceIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).GetFramesByDeviceID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Device_GetFramesByDeviceID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).GetFramesByDeviceID(ctx, req.(*GetFramesByDeviceIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_GetDeviceByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeviceByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).GetDeviceByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Device_GetDeviceByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).GetDeviceByID(ctx, req.(*GetDeviceByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_CheckDeviceHealth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDeviceHealthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).CheckDeviceHealth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Device_CheckDeviceHealth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).CheckDeviceHealth(ctx, req.(*CheckDeviceHealthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Device_ControlDeviceByDeviceID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ControlDeviceByDeviceIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeviceServer).ControlDeviceByDeviceID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Device_ControlDeviceByDeviceID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeviceServer).ControlDeviceByDeviceID(ctx, req.(*ControlDeviceByDeviceIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Device_ServiceDesc is the grpc.ServiceDesc for Device service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Device_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "devicemanager.Device",
	HandlerType: (*DeviceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFramesByDeviceID",
			Handler:    _Device_GetFramesByDeviceID_Handler,
		},
		{
			MethodName: "GetDeviceByID",
			Handler:    _Device_GetDeviceByID_Handler,
		},
		{
			MethodName: "CheckDeviceHealth",
			Handler:    _Device_CheckDeviceHealth_Handler,
		},
		{
			MethodName: "ControlDeviceByDeviceID",
			Handler:    _Device_ControlDeviceByDeviceID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/device.proto",
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Data is the model entity for the Data schema.
type Data struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// Foreign key to frame
	FrameID string `json:"frame_id,omitempty"`
	// Index of the data
	Index string `json:"index,omitempty"`
	// name of the index data
	Name string `json:"name,omitempty"`
	// type of the index data
	Type string `json:"type,omitempty"`
	// value of the index data
	Value string `json:"value,omitempty"`
	// timestamp of the index data
	UpdateUnix time.Time `json:"update_unix,omitempty"`
	// <PERSON><PERSON> holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DataQuery when eager-loading is set.
	Edges        DataEdges `json:"edges"`
	selectValues sql.SelectValues
}

// DataEdges holds the relations/edges for other nodes in the graph.
type DataEdges struct {
	// ModbusFrame holds the value of the modbus_frame edge.
	ModbusFrame *Modbus `json:"modbus_frame,omitempty"`
	// UartFrame holds the value of the uart_frame edge.
	UartFrame *Uart `json:"uart_frame,omitempty"`
	// UDPFrame holds the value of the udp_frame edge.
	UDPFrame *Udp `json:"udp_frame,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [3]bool
}

// ModbusFrameOrErr returns the ModbusFrame value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DataEdges) ModbusFrameOrErr() (*Modbus, error) {
	if e.ModbusFrame != nil {
		return e.ModbusFrame, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: modbus.Label}
	}
	return nil, &NotLoadedError{edge: "modbus_frame"}
}

// UartFrameOrErr returns the UartFrame value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DataEdges) UartFrameOrErr() (*Uart, error) {
	if e.UartFrame != nil {
		return e.UartFrame, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: uart.Label}
	}
	return nil, &NotLoadedError{edge: "uart_frame"}
}

// UDPFrameOrErr returns the UDPFrame value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DataEdges) UDPFrameOrErr() (*Udp, error) {
	if e.UDPFrame != nil {
		return e.UDPFrame, nil
	} else if e.loadedTypes[2] {
		return nil, &NotFoundError{label: udp.Label}
	}
	return nil, &NotLoadedError{edge: "udp_frame"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Data) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case data.FieldID:
			values[i] = new(sql.NullInt64)
		case data.FieldFrameID, data.FieldIndex, data.FieldName, data.FieldType, data.FieldValue:
			values[i] = new(sql.NullString)
		case data.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Data fields.
func (d *Data) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case data.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			d.ID = int(value.Int64)
		case data.FieldFrameID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field frame_id", values[i])
			} else if value.Valid {
				d.FrameID = value.String
			}
		case data.FieldIndex:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field index", values[i])
			} else if value.Valid {
				d.Index = value.String
			}
		case data.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				d.Name = value.String
			}
		case data.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				d.Type = value.String
			}
		case data.FieldValue:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field value", values[i])
			} else if value.Valid {
				d.Value = value.String
			}
		case data.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				d.UpdateUnix = value.Time
			}
		default:
			d.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// GetValue returns the ent.Value that was dynamically selected and assigned to the Data.
// This includes values selected through modifiers, order, etc.
func (d *Data) GetValue(name string) (ent.Value, error) {
	return d.selectValues.Get(name)
}

// QueryModbusFrame queries the "modbus_frame" edge of the Data entity.
func (d *Data) QueryModbusFrame() *ModbusQuery {
	return NewDataClient(d.config).QueryModbusFrame(d)
}

// QueryUartFrame queries the "uart_frame" edge of the Data entity.
func (d *Data) QueryUartFrame() *UartQuery {
	return NewDataClient(d.config).QueryUartFrame(d)
}

// QueryUDPFrame queries the "udp_frame" edge of the Data entity.
func (d *Data) QueryUDPFrame() *UDPQuery {
	return NewDataClient(d.config).QueryUDPFrame(d)
}

// Update returns a builder for updating this Data.
// Note that you need to call Data.Unwrap() before calling this method if this Data
// was returned from a transaction, and the transaction was committed or rolled back.
func (d *Data) Update() *DataUpdateOne {
	return NewDataClient(d.config).UpdateOne(d)
}

// Unwrap unwraps the Data entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (d *Data) Unwrap() *Data {
	_tx, ok := d.config.driver.(*txDriver)
	if !ok {
		panic("ent: Data is not a transactional entity")
	}
	d.config.driver = _tx.drv
	return d
}

// String implements the fmt.Stringer.
func (d *Data) String() string {
	var builder strings.Builder
	builder.WriteString("Data(")
	builder.WriteString(fmt.Sprintf("id=%v, ", d.ID))
	builder.WriteString("frame_id=")
	builder.WriteString(d.FrameID)
	builder.WriteString(", ")
	builder.WriteString("index=")
	builder.WriteString(d.Index)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(d.Name)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(d.Type)
	builder.WriteString(", ")
	builder.WriteString("value=")
	builder.WriteString(d.Value)
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(d.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// DataSlice is a parsable slice of Data.
type DataSlice []*Data

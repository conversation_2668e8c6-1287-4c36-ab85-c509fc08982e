// Code generated by ent, DO NOT EDIT.

package data

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the data type in the database.
	Label = "data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldFrameID holds the string denoting the frame_id field in the database.
	FieldFrameID = "frame_id"
	// FieldIndex holds the string denoting the index field in the database.
	FieldIndex = "index"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldValue holds the string denoting the value field in the database.
	FieldValue = "value"
	// FieldUpdateUnix holds the string denoting the update_unix field in the database.
	FieldUpdateUnix = "update_unix"
	// EdgeModbusFrame holds the string denoting the modbus_frame edge name in mutations.
	EdgeModbusFrame = "modbus_frame"
	// EdgeUartFrame holds the string denoting the uart_frame edge name in mutations.
	EdgeUartFrame = "uart_frame"
	// EdgeUDPFrame holds the string denoting the udp_frame edge name in mutations.
	EdgeUDPFrame = "udp_frame"
	// Table holds the table name of the data in the database.
	Table = "data"
	// ModbusFrameTable is the table that holds the modbus_frame relation/edge.
	ModbusFrameTable = "data"
	// ModbusFrameInverseTable is the table name for the Modbus entity.
	// It exists in this package in order to avoid circular dependency with the "modbus" package.
	ModbusFrameInverseTable = "modbuses"
	// ModbusFrameColumn is the table column denoting the modbus_frame relation/edge.
	ModbusFrameColumn = "frame_id"
	// UartFrameTable is the table that holds the uart_frame relation/edge.
	UartFrameTable = "data"
	// UartFrameInverseTable is the table name for the Uart entity.
	// It exists in this package in order to avoid circular dependency with the "uart" package.
	UartFrameInverseTable = "uarts"
	// UartFrameColumn is the table column denoting the uart_frame relation/edge.
	UartFrameColumn = "frame_id"
	// UDPFrameTable is the table that holds the udp_frame relation/edge.
	UDPFrameTable = "data"
	// UDPFrameInverseTable is the table name for the Udp entity.
	// It exists in this package in order to avoid circular dependency with the "udp" package.
	UDPFrameInverseTable = "udps"
	// UDPFrameColumn is the table column denoting the udp_frame relation/edge.
	UDPFrameColumn = "frame_id"
)

// Columns holds all SQL columns for data fields.
var Columns = []string{
	FieldID,
	FieldFrameID,
	FieldIndex,
	FieldName,
	FieldType,
	FieldValue,
	FieldUpdateUnix,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// FrameIDValidator is a validator for the "frame_id" field. It is called by the builders before save.
	FrameIDValidator func(string) error
	// IndexValidator is a validator for the "index" field. It is called by the builders before save.
	IndexValidator func(string) error
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType string
	// DefaultValue holds the default value on creation for the "value" field.
	DefaultValue string
	// DefaultUpdateUnix holds the default value on creation for the "update_unix" field.
	DefaultUpdateUnix func() time.Time
	// UpdateDefaultUpdateUnix holds the default value on update for the "update_unix" field.
	UpdateDefaultUpdateUnix func() time.Time
)

// OrderOption defines the ordering options for the Data queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByFrameID orders the results by the frame_id field.
func ByFrameID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFrameID, opts...).ToFunc()
}

// ByIndex orders the results by the index field.
func ByIndex(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIndex, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByValue orders the results by the value field.
func ByValue(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldValue, opts...).ToFunc()
}

// ByUpdateUnix orders the results by the update_unix field.
func ByUpdateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateUnix, opts...).ToFunc()
}

// ByModbusFrameField orders the results by modbus_frame field.
func ByModbusFrameField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newModbusFrameStep(), sql.OrderByField(field, opts...))
	}
}

// ByUartFrameField orders the results by uart_frame field.
func ByUartFrameField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUartFrameStep(), sql.OrderByField(field, opts...))
	}
}

// ByUDPFrameField orders the results by udp_frame field.
func ByUDPFrameField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUDPFrameStep(), sql.OrderByField(field, opts...))
	}
}
func newModbusFrameStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ModbusFrameInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ModbusFrameTable, ModbusFrameColumn),
	)
}
func newUartFrameStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UartFrameInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, UartFrameTable, UartFrameColumn),
	)
}
func newUDPFrameStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UDPFrameInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, UDPFrameTable, UDPFrameColumn),
	)
}

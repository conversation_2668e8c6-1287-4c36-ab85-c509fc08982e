// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DataCreate is the builder for creating a Data entity.
type DataCreate struct {
	config
	mutation *DataMutation
	hooks    []Hook
}

// SetFrameID sets the "frame_id" field.
func (dc *DataCreate) SetFrameID(s string) *DataCreate {
	dc.mutation.SetFrameID(s)
	return dc
}

// SetIndex sets the "index" field.
func (dc *DataCreate) SetIndex(s string) *DataCreate {
	dc.mutation.SetIndex(s)
	return dc
}

// SetName sets the "name" field.
func (dc *DataCreate) SetName(s string) *DataCreate {
	dc.mutation.SetName(s)
	return dc
}

// SetType sets the "type" field.
func (dc *DataCreate) SetType(s string) *DataCreate {
	dc.mutation.SetType(s)
	return dc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (dc *DataCreate) SetNillableType(s *string) *DataCreate {
	if s != nil {
		dc.SetType(*s)
	}
	return dc
}

// SetValue sets the "value" field.
func (dc *DataCreate) SetValue(s string) *DataCreate {
	dc.mutation.SetValue(s)
	return dc
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (dc *DataCreate) SetNillableValue(s *string) *DataCreate {
	if s != nil {
		dc.SetValue(*s)
	}
	return dc
}

// SetUpdateUnix sets the "update_unix" field.
func (dc *DataCreate) SetUpdateUnix(t time.Time) *DataCreate {
	dc.mutation.SetUpdateUnix(t)
	return dc
}

// SetNillableUpdateUnix sets the "update_unix" field if the given value is not nil.
func (dc *DataCreate) SetNillableUpdateUnix(t *time.Time) *DataCreate {
	if t != nil {
		dc.SetUpdateUnix(*t)
	}
	return dc
}

// SetModbusFrameID sets the "modbus_frame" edge to the Modbus entity by ID.
func (dc *DataCreate) SetModbusFrameID(id string) *DataCreate {
	dc.mutation.SetModbusFrameID(id)
	return dc
}

// SetModbusFrame sets the "modbus_frame" edge to the Modbus entity.
func (dc *DataCreate) SetModbusFrame(m *Modbus) *DataCreate {
	return dc.SetModbusFrameID(m.ID)
}

// SetUartFrameID sets the "uart_frame" edge to the Uart entity by ID.
func (dc *DataCreate) SetUartFrameID(id string) *DataCreate {
	dc.mutation.SetUartFrameID(id)
	return dc
}

// SetUartFrame sets the "uart_frame" edge to the Uart entity.
func (dc *DataCreate) SetUartFrame(u *Uart) *DataCreate {
	return dc.SetUartFrameID(u.ID)
}

// SetUDPFrameID sets the "udp_frame" edge to the Udp entity by ID.
func (dc *DataCreate) SetUDPFrameID(id string) *DataCreate {
	dc.mutation.SetUDPFrameID(id)
	return dc
}

// SetUDPFrame sets the "udp_frame" edge to the Udp entity.
func (dc *DataCreate) SetUDPFrame(u *Udp) *DataCreate {
	return dc.SetUDPFrameID(u.ID)
}

// Mutation returns the DataMutation object of the builder.
func (dc *DataCreate) Mutation() *DataMutation {
	return dc.mutation
}

// Save creates the Data in the database.
func (dc *DataCreate) Save(ctx context.Context) (*Data, error) {
	dc.defaults()
	return withHooks(ctx, dc.sqlSave, dc.mutation, dc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dc *DataCreate) SaveX(ctx context.Context) *Data {
	v, err := dc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dc *DataCreate) Exec(ctx context.Context) error {
	_, err := dc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dc *DataCreate) ExecX(ctx context.Context) {
	if err := dc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dc *DataCreate) defaults() {
	if _, ok := dc.mutation.GetType(); !ok {
		v := data.DefaultType
		dc.mutation.SetType(v)
	}
	if _, ok := dc.mutation.Value(); !ok {
		v := data.DefaultValue
		dc.mutation.SetValue(v)
	}
	if _, ok := dc.mutation.UpdateUnix(); !ok {
		v := data.DefaultUpdateUnix()
		dc.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dc *DataCreate) check() error {
	if _, ok := dc.mutation.FrameID(); !ok {
		return &ValidationError{Name: "frame_id", err: errors.New(`ent: missing required field "Data.frame_id"`)}
	}
	if v, ok := dc.mutation.FrameID(); ok {
		if err := data.FrameIDValidator(v); err != nil {
			return &ValidationError{Name: "frame_id", err: fmt.Errorf(`ent: validator failed for field "Data.frame_id": %w`, err)}
		}
	}
	if _, ok := dc.mutation.Index(); !ok {
		return &ValidationError{Name: "index", err: errors.New(`ent: missing required field "Data.index"`)}
	}
	if v, ok := dc.mutation.Index(); ok {
		if err := data.IndexValidator(v); err != nil {
			return &ValidationError{Name: "index", err: fmt.Errorf(`ent: validator failed for field "Data.index": %w`, err)}
		}
	}
	if _, ok := dc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Data.name"`)}
	}
	if v, ok := dc.mutation.Name(); ok {
		if err := data.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Data.name": %w`, err)}
		}
	}
	if _, ok := dc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Data.type"`)}
	}
	if _, ok := dc.mutation.Value(); !ok {
		return &ValidationError{Name: "value", err: errors.New(`ent: missing required field "Data.value"`)}
	}
	if _, ok := dc.mutation.UpdateUnix(); !ok {
		return &ValidationError{Name: "update_unix", err: errors.New(`ent: missing required field "Data.update_unix"`)}
	}
	if len(dc.mutation.ModbusFrameIDs()) == 0 {
		return &ValidationError{Name: "modbus_frame", err: errors.New(`ent: missing required edge "Data.modbus_frame"`)}
	}
	if len(dc.mutation.UartFrameIDs()) == 0 {
		return &ValidationError{Name: "uart_frame", err: errors.New(`ent: missing required edge "Data.uart_frame"`)}
	}
	if len(dc.mutation.UDPFrameIDs()) == 0 {
		return &ValidationError{Name: "udp_frame", err: errors.New(`ent: missing required edge "Data.udp_frame"`)}
	}
	return nil
}

func (dc *DataCreate) sqlSave(ctx context.Context) (*Data, error) {
	if err := dc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	dc.mutation.id = &_node.ID
	dc.mutation.done = true
	return _node, nil
}

func (dc *DataCreate) createSpec() (*Data, *sqlgraph.CreateSpec) {
	var (
		_node = &Data{config: dc.config}
		_spec = sqlgraph.NewCreateSpec(data.Table, sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt))
	)
	if value, ok := dc.mutation.Index(); ok {
		_spec.SetField(data.FieldIndex, field.TypeString, value)
		_node.Index = value
	}
	if value, ok := dc.mutation.Name(); ok {
		_spec.SetField(data.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := dc.mutation.GetType(); ok {
		_spec.SetField(data.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := dc.mutation.Value(); ok {
		_spec.SetField(data.FieldValue, field.TypeString, value)
		_node.Value = value
	}
	if value, ok := dc.mutation.UpdateUnix(); ok {
		_spec.SetField(data.FieldUpdateUnix, field.TypeTime, value)
		_node.UpdateUnix = value
	}
	if nodes := dc.mutation.ModbusFrameIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   data.ModbusFrameTable,
			Columns: []string{data.ModbusFrameColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.FrameID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.UartFrameIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   data.UartFrameTable,
			Columns: []string{data.UartFrameColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.FrameID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.UDPFrameIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   data.UDPFrameTable,
			Columns: []string{data.UDPFrameColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.FrameID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// DataCreateBulk is the builder for creating many Data entities in bulk.
type DataCreateBulk struct {
	config
	err      error
	builders []*DataCreate
}

// Save creates the Data entities in the database.
func (dcb *DataCreateBulk) Save(ctx context.Context) ([]*Data, error) {
	if dcb.err != nil {
		return nil, dcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dcb.builders))
	nodes := make([]*Data, len(dcb.builders))
	mutators := make([]Mutator, len(dcb.builders))
	for i := range dcb.builders {
		func(i int, root context.Context) {
			builder := dcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dcb *DataCreateBulk) SaveX(ctx context.Context) []*Data {
	v, err := dcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dcb *DataCreateBulk) Exec(ctx context.Context) error {
	_, err := dcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dcb *DataCreateBulk) ExecX(ctx context.Context) {
	if err := dcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DataQuery is the builder for querying Data entities.
type DataQuery struct {
	config
	ctx             *QueryContext
	order           []data.OrderOption
	inters          []Interceptor
	predicates      []predicate.Data
	withModbusFrame *ModbusQuery
	withUartFrame   *UartQuery
	withUDPFrame    *UDPQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DataQuery builder.
func (dq *DataQuery) Where(ps ...predicate.Data) *DataQuery {
	dq.predicates = append(dq.predicates, ps...)
	return dq
}

// Limit the number of records to be returned by this query.
func (dq *DataQuery) Limit(limit int) *DataQuery {
	dq.ctx.Limit = &limit
	return dq
}

// Offset to start from.
func (dq *DataQuery) Offset(offset int) *DataQuery {
	dq.ctx.Offset = &offset
	return dq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (dq *DataQuery) Unique(unique bool) *DataQuery {
	dq.ctx.Unique = &unique
	return dq
}

// Order specifies how the records should be ordered.
func (dq *DataQuery) Order(o ...data.OrderOption) *DataQuery {
	dq.order = append(dq.order, o...)
	return dq
}

// QueryModbusFrame chains the current query on the "modbus_frame" edge.
func (dq *DataQuery) QueryModbusFrame() *ModbusQuery {
	query := (&ModbusClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(data.Table, data.FieldID, selector),
			sqlgraph.To(modbus.Table, modbus.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, data.ModbusFrameTable, data.ModbusFrameColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUartFrame chains the current query on the "uart_frame" edge.
func (dq *DataQuery) QueryUartFrame() *UartQuery {
	query := (&UartClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(data.Table, data.FieldID, selector),
			sqlgraph.To(uart.Table, uart.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, data.UartFrameTable, data.UartFrameColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUDPFrame chains the current query on the "udp_frame" edge.
func (dq *DataQuery) QueryUDPFrame() *UDPQuery {
	query := (&UDPClient{config: dq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := dq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := dq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(data.Table, data.FieldID, selector),
			sqlgraph.To(udp.Table, udp.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, data.UDPFrameTable, data.UDPFrameColumn),
		)
		fromU = sqlgraph.SetNeighbors(dq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Data entity from the query.
// Returns a *NotFoundError when no Data was found.
func (dq *DataQuery) First(ctx context.Context) (*Data, error) {
	nodes, err := dq.Limit(1).All(setContextOp(ctx, dq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{data.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (dq *DataQuery) FirstX(ctx context.Context) *Data {
	node, err := dq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Data ID from the query.
// Returns a *NotFoundError when no Data ID was found.
func (dq *DataQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = dq.Limit(1).IDs(setContextOp(ctx, dq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{data.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (dq *DataQuery) FirstIDX(ctx context.Context) int {
	id, err := dq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Data entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Data entity is found.
// Returns a *NotFoundError when no Data entities are found.
func (dq *DataQuery) Only(ctx context.Context) (*Data, error) {
	nodes, err := dq.Limit(2).All(setContextOp(ctx, dq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{data.Label}
	default:
		return nil, &NotSingularError{data.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (dq *DataQuery) OnlyX(ctx context.Context) *Data {
	node, err := dq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Data ID in the query.
// Returns a *NotSingularError when more than one Data ID is found.
// Returns a *NotFoundError when no entities are found.
func (dq *DataQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = dq.Limit(2).IDs(setContextOp(ctx, dq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{data.Label}
	default:
		err = &NotSingularError{data.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (dq *DataQuery) OnlyIDX(ctx context.Context) int {
	id, err := dq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of DataSlice.
func (dq *DataQuery) All(ctx context.Context) ([]*Data, error) {
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryAll)
	if err := dq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Data, *DataQuery]()
	return withInterceptors[[]*Data](ctx, dq, qr, dq.inters)
}

// AllX is like All, but panics if an error occurs.
func (dq *DataQuery) AllX(ctx context.Context) []*Data {
	nodes, err := dq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Data IDs.
func (dq *DataQuery) IDs(ctx context.Context) (ids []int, err error) {
	if dq.ctx.Unique == nil && dq.path != nil {
		dq.Unique(true)
	}
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryIDs)
	if err = dq.Select(data.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (dq *DataQuery) IDsX(ctx context.Context) []int {
	ids, err := dq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (dq *DataQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryCount)
	if err := dq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, dq, querierCount[*DataQuery](), dq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (dq *DataQuery) CountX(ctx context.Context) int {
	count, err := dq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (dq *DataQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, dq.ctx, ent.OpQueryExist)
	switch _, err := dq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (dq *DataQuery) ExistX(ctx context.Context) bool {
	exist, err := dq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DataQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (dq *DataQuery) Clone() *DataQuery {
	if dq == nil {
		return nil
	}
	return &DataQuery{
		config:          dq.config,
		ctx:             dq.ctx.Clone(),
		order:           append([]data.OrderOption{}, dq.order...),
		inters:          append([]Interceptor{}, dq.inters...),
		predicates:      append([]predicate.Data{}, dq.predicates...),
		withModbusFrame: dq.withModbusFrame.Clone(),
		withUartFrame:   dq.withUartFrame.Clone(),
		withUDPFrame:    dq.withUDPFrame.Clone(),
		// clone intermediate query.
		sql:  dq.sql.Clone(),
		path: dq.path,
	}
}

// WithModbusFrame tells the query-builder to eager-load the nodes that are connected to
// the "modbus_frame" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DataQuery) WithModbusFrame(opts ...func(*ModbusQuery)) *DataQuery {
	query := (&ModbusClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withModbusFrame = query
	return dq
}

// WithUartFrame tells the query-builder to eager-load the nodes that are connected to
// the "uart_frame" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DataQuery) WithUartFrame(opts ...func(*UartQuery)) *DataQuery {
	query := (&UartClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withUartFrame = query
	return dq
}

// WithUDPFrame tells the query-builder to eager-load the nodes that are connected to
// the "udp_frame" edge. The optional arguments are used to configure the query builder of the edge.
func (dq *DataQuery) WithUDPFrame(opts ...func(*UDPQuery)) *DataQuery {
	query := (&UDPClient{config: dq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	dq.withUDPFrame = query
	return dq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		FrameID string `json:"frame_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Data.Query().
//		GroupBy(data.FieldFrameID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (dq *DataQuery) GroupBy(field string, fields ...string) *DataGroupBy {
	dq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DataGroupBy{build: dq}
	grbuild.flds = &dq.ctx.Fields
	grbuild.label = data.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		FrameID string `json:"frame_id,omitempty"`
//	}
//
//	client.Data.Query().
//		Select(data.FieldFrameID).
//		Scan(ctx, &v)
func (dq *DataQuery) Select(fields ...string) *DataSelect {
	dq.ctx.Fields = append(dq.ctx.Fields, fields...)
	sbuild := &DataSelect{DataQuery: dq}
	sbuild.label = data.Label
	sbuild.flds, sbuild.scan = &dq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DataSelect configured with the given aggregations.
func (dq *DataQuery) Aggregate(fns ...AggregateFunc) *DataSelect {
	return dq.Select().Aggregate(fns...)
}

func (dq *DataQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range dq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, dq); err != nil {
				return err
			}
		}
	}
	for _, f := range dq.ctx.Fields {
		if !data.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if dq.path != nil {
		prev, err := dq.path(ctx)
		if err != nil {
			return err
		}
		dq.sql = prev
	}
	return nil
}

func (dq *DataQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Data, error) {
	var (
		nodes       = []*Data{}
		_spec       = dq.querySpec()
		loadedTypes = [3]bool{
			dq.withModbusFrame != nil,
			dq.withUartFrame != nil,
			dq.withUDPFrame != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Data).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Data{config: dq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, dq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := dq.withModbusFrame; query != nil {
		if err := dq.loadModbusFrame(ctx, query, nodes, nil,
			func(n *Data, e *Modbus) { n.Edges.ModbusFrame = e }); err != nil {
			return nil, err
		}
	}
	if query := dq.withUartFrame; query != nil {
		if err := dq.loadUartFrame(ctx, query, nodes, nil,
			func(n *Data, e *Uart) { n.Edges.UartFrame = e }); err != nil {
			return nil, err
		}
	}
	if query := dq.withUDPFrame; query != nil {
		if err := dq.loadUDPFrame(ctx, query, nodes, nil,
			func(n *Data, e *Udp) { n.Edges.UDPFrame = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (dq *DataQuery) loadModbusFrame(ctx context.Context, query *ModbusQuery, nodes []*Data, init func(*Data), assign func(*Data, *Modbus)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*Data)
	for i := range nodes {
		fk := nodes[i].FrameID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(modbus.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "frame_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (dq *DataQuery) loadUartFrame(ctx context.Context, query *UartQuery, nodes []*Data, init func(*Data), assign func(*Data, *Uart)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*Data)
	for i := range nodes {
		fk := nodes[i].FrameID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(uart.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "frame_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (dq *DataQuery) loadUDPFrame(ctx context.Context, query *UDPQuery, nodes []*Data, init func(*Data), assign func(*Data, *Udp)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*Data)
	for i := range nodes {
		fk := nodes[i].FrameID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(udp.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "frame_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (dq *DataQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := dq.querySpec()
	_spec.Node.Columns = dq.ctx.Fields
	if len(dq.ctx.Fields) > 0 {
		_spec.Unique = dq.ctx.Unique != nil && *dq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, dq.driver, _spec)
}

func (dq *DataQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(data.Table, data.Columns, sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt))
	_spec.From = dq.sql
	if unique := dq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if dq.path != nil {
		_spec.Unique = true
	}
	if fields := dq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, data.FieldID)
		for i := range fields {
			if fields[i] != data.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if dq.withModbusFrame != nil {
			_spec.Node.AddColumnOnce(data.FieldFrameID)
		}
		if dq.withUartFrame != nil {
			_spec.Node.AddColumnOnce(data.FieldFrameID)
		}
		if dq.withUDPFrame != nil {
			_spec.Node.AddColumnOnce(data.FieldFrameID)
		}
	}
	if ps := dq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := dq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := dq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := dq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (dq *DataQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(dq.driver.Dialect())
	t1 := builder.Table(data.Table)
	columns := dq.ctx.Fields
	if len(columns) == 0 {
		columns = data.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if dq.sql != nil {
		selector = dq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if dq.ctx.Unique != nil && *dq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range dq.predicates {
		p(selector)
	}
	for _, p := range dq.order {
		p(selector)
	}
	if offset := dq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := dq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// DataGroupBy is the group-by builder for Data entities.
type DataGroupBy struct {
	selector
	build *DataQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (dgb *DataGroupBy) Aggregate(fns ...AggregateFunc) *DataGroupBy {
	dgb.fns = append(dgb.fns, fns...)
	return dgb
}

// Scan applies the selector query and scans the result into the given value.
func (dgb *DataGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, dgb.build.ctx, ent.OpQueryGroupBy)
	if err := dgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DataQuery, *DataGroupBy](ctx, dgb.build, dgb, dgb.build.inters, v)
}

func (dgb *DataGroupBy) sqlScan(ctx context.Context, root *DataQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(dgb.fns))
	for _, fn := range dgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*dgb.flds)+len(dgb.fns))
		for _, f := range *dgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*dgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := dgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DataSelect is the builder for selecting fields of Data entities.
type DataSelect struct {
	*DataQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ds *DataSelect) Aggregate(fns ...AggregateFunc) *DataSelect {
	ds.fns = append(ds.fns, fns...)
	return ds
}

// Scan applies the selector query and scans the result into the given value.
func (ds *DataSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ds.ctx, ent.OpQuerySelect)
	if err := ds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DataQuery, *DataSelect](ctx, ds.DataQuery, ds, ds.inters, v)
}

func (ds *DataSelect) sqlScan(ctx context.Context, root *DataQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ds.fns))
	for _, fn := range ds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

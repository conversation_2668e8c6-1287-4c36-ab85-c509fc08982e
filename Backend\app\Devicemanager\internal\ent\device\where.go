// Code generated by ent, DO NOT EDIT.

package device

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldName, v))
}

// IP applies equality check predicate on the "ip" field. It's identical to IPEQ.
func IP(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldIP, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldType, v))
}

// Os applies equality check predicate on the "os" field. It's identical to OsEQ.
func Os(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldOs, v))
}

// CPU applies equality check predicate on the "cpu" field. It's identical to CPUEQ.
func CPU(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldCPU, v))
}

// Gpu applies equality check predicate on the "gpu" field. It's identical to GpuEQ.
func Gpu(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldGpu, v))
}

// Memory applies equality check predicate on the "memory" field. It's identical to MemoryEQ.
func Memory(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldMemory, v))
}

// Disk applies equality check predicate on the "disk" field. It's identical to DiskEQ.
func Disk(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldDisk, v))
}

// Protocol applies equality check predicate on the "protocol" field. It's identical to ProtocolEQ.
func Protocol(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldProtocol, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldStatus, v))
}

// Healthtimestamp applies equality check predicate on the "healthtimestamp" field. It's identical to HealthtimestampEQ.
func Healthtimestamp(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldHealthtimestamp, v))
}

// Workmode applies equality check predicate on the "workmode" field. It's identical to WorkmodeEQ.
func Workmode(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldWorkmode, v))
}

// CreateUnix applies equality check predicate on the "create_unix" field. It's identical to CreateUnixEQ.
func CreateUnix(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldCreateUnix, v))
}

// UpdateUnix applies equality check predicate on the "update_unix" field. It's identical to UpdateUnixEQ.
func UpdateUnix(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldUpdateUnix, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldName, v))
}

// IPEQ applies the EQ predicate on the "ip" field.
func IPEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldIP, v))
}

// IPNEQ applies the NEQ predicate on the "ip" field.
func IPNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldIP, v))
}

// IPIn applies the In predicate on the "ip" field.
func IPIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldIP, vs...))
}

// IPNotIn applies the NotIn predicate on the "ip" field.
func IPNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldIP, vs...))
}

// IPGT applies the GT predicate on the "ip" field.
func IPGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldIP, v))
}

// IPGTE applies the GTE predicate on the "ip" field.
func IPGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldIP, v))
}

// IPLT applies the LT predicate on the "ip" field.
func IPLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldIP, v))
}

// IPLTE applies the LTE predicate on the "ip" field.
func IPLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldIP, v))
}

// IPContains applies the Contains predicate on the "ip" field.
func IPContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldIP, v))
}

// IPHasPrefix applies the HasPrefix predicate on the "ip" field.
func IPHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldIP, v))
}

// IPHasSuffix applies the HasSuffix predicate on the "ip" field.
func IPHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldIP, v))
}

// IPEqualFold applies the EqualFold predicate on the "ip" field.
func IPEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldIP, v))
}

// IPContainsFold applies the ContainsFold predicate on the "ip" field.
func IPContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldIP, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldType, v))
}

// OsEQ applies the EQ predicate on the "os" field.
func OsEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldOs, v))
}

// OsNEQ applies the NEQ predicate on the "os" field.
func OsNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldOs, v))
}

// OsIn applies the In predicate on the "os" field.
func OsIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldOs, vs...))
}

// OsNotIn applies the NotIn predicate on the "os" field.
func OsNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldOs, vs...))
}

// OsGT applies the GT predicate on the "os" field.
func OsGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldOs, v))
}

// OsGTE applies the GTE predicate on the "os" field.
func OsGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldOs, v))
}

// OsLT applies the LT predicate on the "os" field.
func OsLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldOs, v))
}

// OsLTE applies the LTE predicate on the "os" field.
func OsLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldOs, v))
}

// OsContains applies the Contains predicate on the "os" field.
func OsContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldOs, v))
}

// OsHasPrefix applies the HasPrefix predicate on the "os" field.
func OsHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldOs, v))
}

// OsHasSuffix applies the HasSuffix predicate on the "os" field.
func OsHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldOs, v))
}

// OsEqualFold applies the EqualFold predicate on the "os" field.
func OsEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldOs, v))
}

// OsContainsFold applies the ContainsFold predicate on the "os" field.
func OsContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldOs, v))
}

// CPUEQ applies the EQ predicate on the "cpu" field.
func CPUEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldCPU, v))
}

// CPUNEQ applies the NEQ predicate on the "cpu" field.
func CPUNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldCPU, v))
}

// CPUIn applies the In predicate on the "cpu" field.
func CPUIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldCPU, vs...))
}

// CPUNotIn applies the NotIn predicate on the "cpu" field.
func CPUNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldCPU, vs...))
}

// CPUGT applies the GT predicate on the "cpu" field.
func CPUGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldCPU, v))
}

// CPUGTE applies the GTE predicate on the "cpu" field.
func CPUGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldCPU, v))
}

// CPULT applies the LT predicate on the "cpu" field.
func CPULT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldCPU, v))
}

// CPULTE applies the LTE predicate on the "cpu" field.
func CPULTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldCPU, v))
}

// CPUContains applies the Contains predicate on the "cpu" field.
func CPUContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldCPU, v))
}

// CPUHasPrefix applies the HasPrefix predicate on the "cpu" field.
func CPUHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldCPU, v))
}

// CPUHasSuffix applies the HasSuffix predicate on the "cpu" field.
func CPUHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldCPU, v))
}

// CPUEqualFold applies the EqualFold predicate on the "cpu" field.
func CPUEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldCPU, v))
}

// CPUContainsFold applies the ContainsFold predicate on the "cpu" field.
func CPUContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldCPU, v))
}

// GpuEQ applies the EQ predicate on the "gpu" field.
func GpuEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldGpu, v))
}

// GpuNEQ applies the NEQ predicate on the "gpu" field.
func GpuNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldGpu, v))
}

// GpuIn applies the In predicate on the "gpu" field.
func GpuIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldGpu, vs...))
}

// GpuNotIn applies the NotIn predicate on the "gpu" field.
func GpuNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldGpu, vs...))
}

// GpuGT applies the GT predicate on the "gpu" field.
func GpuGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldGpu, v))
}

// GpuGTE applies the GTE predicate on the "gpu" field.
func GpuGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldGpu, v))
}

// GpuLT applies the LT predicate on the "gpu" field.
func GpuLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldGpu, v))
}

// GpuLTE applies the LTE predicate on the "gpu" field.
func GpuLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldGpu, v))
}

// GpuContains applies the Contains predicate on the "gpu" field.
func GpuContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldGpu, v))
}

// GpuHasPrefix applies the HasPrefix predicate on the "gpu" field.
func GpuHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldGpu, v))
}

// GpuHasSuffix applies the HasSuffix predicate on the "gpu" field.
func GpuHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldGpu, v))
}

// GpuEqualFold applies the EqualFold predicate on the "gpu" field.
func GpuEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldGpu, v))
}

// GpuContainsFold applies the ContainsFold predicate on the "gpu" field.
func GpuContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldGpu, v))
}

// MemoryEQ applies the EQ predicate on the "memory" field.
func MemoryEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldMemory, v))
}

// MemoryNEQ applies the NEQ predicate on the "memory" field.
func MemoryNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldMemory, v))
}

// MemoryIn applies the In predicate on the "memory" field.
func MemoryIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldMemory, vs...))
}

// MemoryNotIn applies the NotIn predicate on the "memory" field.
func MemoryNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldMemory, vs...))
}

// MemoryGT applies the GT predicate on the "memory" field.
func MemoryGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldMemory, v))
}

// MemoryGTE applies the GTE predicate on the "memory" field.
func MemoryGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldMemory, v))
}

// MemoryLT applies the LT predicate on the "memory" field.
func MemoryLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldMemory, v))
}

// MemoryLTE applies the LTE predicate on the "memory" field.
func MemoryLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldMemory, v))
}

// MemoryContains applies the Contains predicate on the "memory" field.
func MemoryContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldMemory, v))
}

// MemoryHasPrefix applies the HasPrefix predicate on the "memory" field.
func MemoryHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldMemory, v))
}

// MemoryHasSuffix applies the HasSuffix predicate on the "memory" field.
func MemoryHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldMemory, v))
}

// MemoryEqualFold applies the EqualFold predicate on the "memory" field.
func MemoryEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldMemory, v))
}

// MemoryContainsFold applies the ContainsFold predicate on the "memory" field.
func MemoryContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldMemory, v))
}

// DiskEQ applies the EQ predicate on the "disk" field.
func DiskEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldDisk, v))
}

// DiskNEQ applies the NEQ predicate on the "disk" field.
func DiskNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldDisk, v))
}

// DiskIn applies the In predicate on the "disk" field.
func DiskIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldDisk, vs...))
}

// DiskNotIn applies the NotIn predicate on the "disk" field.
func DiskNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldDisk, vs...))
}

// DiskGT applies the GT predicate on the "disk" field.
func DiskGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldDisk, v))
}

// DiskGTE applies the GTE predicate on the "disk" field.
func DiskGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldDisk, v))
}

// DiskLT applies the LT predicate on the "disk" field.
func DiskLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldDisk, v))
}

// DiskLTE applies the LTE predicate on the "disk" field.
func DiskLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldDisk, v))
}

// DiskContains applies the Contains predicate on the "disk" field.
func DiskContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldDisk, v))
}

// DiskHasPrefix applies the HasPrefix predicate on the "disk" field.
func DiskHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldDisk, v))
}

// DiskHasSuffix applies the HasSuffix predicate on the "disk" field.
func DiskHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldDisk, v))
}

// DiskEqualFold applies the EqualFold predicate on the "disk" field.
func DiskEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldDisk, v))
}

// DiskContainsFold applies the ContainsFold predicate on the "disk" field.
func DiskContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldDisk, v))
}

// ProtocolEQ applies the EQ predicate on the "protocol" field.
func ProtocolEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldProtocol, v))
}

// ProtocolNEQ applies the NEQ predicate on the "protocol" field.
func ProtocolNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldProtocol, v))
}

// ProtocolIn applies the In predicate on the "protocol" field.
func ProtocolIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldProtocol, vs...))
}

// ProtocolNotIn applies the NotIn predicate on the "protocol" field.
func ProtocolNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldProtocol, vs...))
}

// ProtocolGT applies the GT predicate on the "protocol" field.
func ProtocolGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldProtocol, v))
}

// ProtocolGTE applies the GTE predicate on the "protocol" field.
func ProtocolGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldProtocol, v))
}

// ProtocolLT applies the LT predicate on the "protocol" field.
func ProtocolLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldProtocol, v))
}

// ProtocolLTE applies the LTE predicate on the "protocol" field.
func ProtocolLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldProtocol, v))
}

// ProtocolContains applies the Contains predicate on the "protocol" field.
func ProtocolContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldProtocol, v))
}

// ProtocolHasPrefix applies the HasPrefix predicate on the "protocol" field.
func ProtocolHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldProtocol, v))
}

// ProtocolHasSuffix applies the HasSuffix predicate on the "protocol" field.
func ProtocolHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldProtocol, v))
}

// ProtocolEqualFold applies the EqualFold predicate on the "protocol" field.
func ProtocolEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldProtocol, v))
}

// ProtocolContainsFold applies the ContainsFold predicate on the "protocol" field.
func ProtocolContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldProtocol, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldStatus, v))
}

// HealthtimestampEQ applies the EQ predicate on the "healthtimestamp" field.
func HealthtimestampEQ(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldHealthtimestamp, v))
}

// HealthtimestampNEQ applies the NEQ predicate on the "healthtimestamp" field.
func HealthtimestampNEQ(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldHealthtimestamp, v))
}

// HealthtimestampIn applies the In predicate on the "healthtimestamp" field.
func HealthtimestampIn(vs ...time.Time) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldHealthtimestamp, vs...))
}

// HealthtimestampNotIn applies the NotIn predicate on the "healthtimestamp" field.
func HealthtimestampNotIn(vs ...time.Time) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldHealthtimestamp, vs...))
}

// HealthtimestampGT applies the GT predicate on the "healthtimestamp" field.
func HealthtimestampGT(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldHealthtimestamp, v))
}

// HealthtimestampGTE applies the GTE predicate on the "healthtimestamp" field.
func HealthtimestampGTE(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldHealthtimestamp, v))
}

// HealthtimestampLT applies the LT predicate on the "healthtimestamp" field.
func HealthtimestampLT(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldHealthtimestamp, v))
}

// HealthtimestampLTE applies the LTE predicate on the "healthtimestamp" field.
func HealthtimestampLTE(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldHealthtimestamp, v))
}

// WorkmodeEQ applies the EQ predicate on the "workmode" field.
func WorkmodeEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldWorkmode, v))
}

// WorkmodeNEQ applies the NEQ predicate on the "workmode" field.
func WorkmodeNEQ(v string) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldWorkmode, v))
}

// WorkmodeIn applies the In predicate on the "workmode" field.
func WorkmodeIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldWorkmode, vs...))
}

// WorkmodeNotIn applies the NotIn predicate on the "workmode" field.
func WorkmodeNotIn(vs ...string) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldWorkmode, vs...))
}

// WorkmodeGT applies the GT predicate on the "workmode" field.
func WorkmodeGT(v string) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldWorkmode, v))
}

// WorkmodeGTE applies the GTE predicate on the "workmode" field.
func WorkmodeGTE(v string) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldWorkmode, v))
}

// WorkmodeLT applies the LT predicate on the "workmode" field.
func WorkmodeLT(v string) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldWorkmode, v))
}

// WorkmodeLTE applies the LTE predicate on the "workmode" field.
func WorkmodeLTE(v string) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldWorkmode, v))
}

// WorkmodeContains applies the Contains predicate on the "workmode" field.
func WorkmodeContains(v string) predicate.Device {
	return predicate.Device(sql.FieldContains(FieldWorkmode, v))
}

// WorkmodeHasPrefix applies the HasPrefix predicate on the "workmode" field.
func WorkmodeHasPrefix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasPrefix(FieldWorkmode, v))
}

// WorkmodeHasSuffix applies the HasSuffix predicate on the "workmode" field.
func WorkmodeHasSuffix(v string) predicate.Device {
	return predicate.Device(sql.FieldHasSuffix(FieldWorkmode, v))
}

// WorkmodeEqualFold applies the EqualFold predicate on the "workmode" field.
func WorkmodeEqualFold(v string) predicate.Device {
	return predicate.Device(sql.FieldEqualFold(FieldWorkmode, v))
}

// WorkmodeContainsFold applies the ContainsFold predicate on the "workmode" field.
func WorkmodeContainsFold(v string) predicate.Device {
	return predicate.Device(sql.FieldContainsFold(FieldWorkmode, v))
}

// CreateUnixEQ applies the EQ predicate on the "create_unix" field.
func CreateUnixEQ(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldCreateUnix, v))
}

// CreateUnixNEQ applies the NEQ predicate on the "create_unix" field.
func CreateUnixNEQ(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldCreateUnix, v))
}

// CreateUnixIn applies the In predicate on the "create_unix" field.
func CreateUnixIn(vs ...time.Time) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldCreateUnix, vs...))
}

// CreateUnixNotIn applies the NotIn predicate on the "create_unix" field.
func CreateUnixNotIn(vs ...time.Time) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldCreateUnix, vs...))
}

// CreateUnixGT applies the GT predicate on the "create_unix" field.
func CreateUnixGT(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldCreateUnix, v))
}

// CreateUnixGTE applies the GTE predicate on the "create_unix" field.
func CreateUnixGTE(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldCreateUnix, v))
}

// CreateUnixLT applies the LT predicate on the "create_unix" field.
func CreateUnixLT(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldCreateUnix, v))
}

// CreateUnixLTE applies the LTE predicate on the "create_unix" field.
func CreateUnixLTE(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldCreateUnix, v))
}

// UpdateUnixEQ applies the EQ predicate on the "update_unix" field.
func UpdateUnixEQ(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldEQ(FieldUpdateUnix, v))
}

// UpdateUnixNEQ applies the NEQ predicate on the "update_unix" field.
func UpdateUnixNEQ(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldNEQ(FieldUpdateUnix, v))
}

// UpdateUnixIn applies the In predicate on the "update_unix" field.
func UpdateUnixIn(vs ...time.Time) predicate.Device {
	return predicate.Device(sql.FieldIn(FieldUpdateUnix, vs...))
}

// UpdateUnixNotIn applies the NotIn predicate on the "update_unix" field.
func UpdateUnixNotIn(vs ...time.Time) predicate.Device {
	return predicate.Device(sql.FieldNotIn(FieldUpdateUnix, vs...))
}

// UpdateUnixGT applies the GT predicate on the "update_unix" field.
func UpdateUnixGT(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldGT(FieldUpdateUnix, v))
}

// UpdateUnixGTE applies the GTE predicate on the "update_unix" field.
func UpdateUnixGTE(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldGTE(FieldUpdateUnix, v))
}

// UpdateUnixLT applies the LT predicate on the "update_unix" field.
func UpdateUnixLT(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldLT(FieldUpdateUnix, v))
}

// UpdateUnixLTE applies the LTE predicate on the "update_unix" field.
func UpdateUnixLTE(v time.Time) predicate.Device {
	return predicate.Device(sql.FieldLTE(FieldUpdateUnix, v))
}

// HasModbusConfig applies the HasEdge predicate on the "modbusConfig" edge.
func HasModbusConfig() predicate.Device {
	return predicate.Device(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, ModbusConfigTable, ModbusConfigColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasModbusConfigWith applies the HasEdge predicate on the "modbusConfig" edge with a given conditions (other predicates).
func HasModbusConfigWith(preds ...predicate.Modbus) predicate.Device {
	return predicate.Device(func(s *sql.Selector) {
		step := newModbusConfigStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUartConfig applies the HasEdge predicate on the "uartConfig" edge.
func HasUartConfig() predicate.Device {
	return predicate.Device(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, UartConfigTable, UartConfigColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUartConfigWith applies the HasEdge predicate on the "uartConfig" edge with a given conditions (other predicates).
func HasUartConfigWith(preds ...predicate.Uart) predicate.Device {
	return predicate.Device(func(s *sql.Selector) {
		step := newUartConfigStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUdpConfig applies the HasEdge predicate on the "udpConfig" edge.
func HasUdpConfig() predicate.Device {
	return predicate.Device(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, UdpConfigTable, UdpConfigColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUdpConfigWith applies the HasEdge predicate on the "udpConfig" edge with a given conditions (other predicates).
func HasUdpConfigWith(preds ...predicate.Udp) predicate.Device {
	return predicate.Device(func(s *sql.Selector) {
		step := newUdpConfigStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Device) predicate.Device {
	return predicate.Device(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Device) predicate.Device {
	return predicate.Device(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Device) predicate.Device {
	return predicate.Device(sql.NotPredicates(p))
}

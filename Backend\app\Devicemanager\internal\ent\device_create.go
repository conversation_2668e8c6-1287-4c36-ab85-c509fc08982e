// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DeviceC<PERSON> is the builder for creating a Device entity.
type DeviceCreate struct {
	config
	mutation *DeviceMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (dc *DeviceCreate) SetName(s string) *DeviceCreate {
	dc.mutation.SetName(s)
	return dc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableName(s *string) *DeviceCreate {
	if s != nil {
		dc.SetName(*s)
	}
	return dc
}

// SetIP sets the "ip" field.
func (dc *DeviceCreate) SetIP(s string) *DeviceCreate {
	dc.mutation.SetIP(s)
	return dc
}

// SetType sets the "type" field.
func (dc *DeviceCreate) SetType(s string) *DeviceCreate {
	dc.mutation.SetType(s)
	return dc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableType(s *string) *DeviceCreate {
	if s != nil {
		dc.SetType(*s)
	}
	return dc
}

// SetOs sets the "os" field.
func (dc *DeviceCreate) SetOs(s string) *DeviceCreate {
	dc.mutation.SetOs(s)
	return dc
}

// SetNillableOs sets the "os" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableOs(s *string) *DeviceCreate {
	if s != nil {
		dc.SetOs(*s)
	}
	return dc
}

// SetCPU sets the "cpu" field.
func (dc *DeviceCreate) SetCPU(s string) *DeviceCreate {
	dc.mutation.SetCPU(s)
	return dc
}

// SetNillableCPU sets the "cpu" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableCPU(s *string) *DeviceCreate {
	if s != nil {
		dc.SetCPU(*s)
	}
	return dc
}

// SetGpu sets the "gpu" field.
func (dc *DeviceCreate) SetGpu(s string) *DeviceCreate {
	dc.mutation.SetGpu(s)
	return dc
}

// SetNillableGpu sets the "gpu" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableGpu(s *string) *DeviceCreate {
	if s != nil {
		dc.SetGpu(*s)
	}
	return dc
}

// SetMemory sets the "memory" field.
func (dc *DeviceCreate) SetMemory(s string) *DeviceCreate {
	dc.mutation.SetMemory(s)
	return dc
}

// SetNillableMemory sets the "memory" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableMemory(s *string) *DeviceCreate {
	if s != nil {
		dc.SetMemory(*s)
	}
	return dc
}

// SetDisk sets the "disk" field.
func (dc *DeviceCreate) SetDisk(s string) *DeviceCreate {
	dc.mutation.SetDisk(s)
	return dc
}

// SetNillableDisk sets the "disk" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableDisk(s *string) *DeviceCreate {
	if s != nil {
		dc.SetDisk(*s)
	}
	return dc
}

// SetProtocol sets the "protocol" field.
func (dc *DeviceCreate) SetProtocol(s string) *DeviceCreate {
	dc.mutation.SetProtocol(s)
	return dc
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableProtocol(s *string) *DeviceCreate {
	if s != nil {
		dc.SetProtocol(*s)
	}
	return dc
}

// SetStatus sets the "status" field.
func (dc *DeviceCreate) SetStatus(s string) *DeviceCreate {
	dc.mutation.SetStatus(s)
	return dc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableStatus(s *string) *DeviceCreate {
	if s != nil {
		dc.SetStatus(*s)
	}
	return dc
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (dc *DeviceCreate) SetHealthtimestamp(t time.Time) *DeviceCreate {
	dc.mutation.SetHealthtimestamp(t)
	return dc
}

// SetNillableHealthtimestamp sets the "healthtimestamp" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableHealthtimestamp(t *time.Time) *DeviceCreate {
	if t != nil {
		dc.SetHealthtimestamp(*t)
	}
	return dc
}

// SetWorkmode sets the "workmode" field.
func (dc *DeviceCreate) SetWorkmode(s string) *DeviceCreate {
	dc.mutation.SetWorkmode(s)
	return dc
}

// SetNillableWorkmode sets the "workmode" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableWorkmode(s *string) *DeviceCreate {
	if s != nil {
		dc.SetWorkmode(*s)
	}
	return dc
}

// SetCreateUnix sets the "create_unix" field.
func (dc *DeviceCreate) SetCreateUnix(t time.Time) *DeviceCreate {
	dc.mutation.SetCreateUnix(t)
	return dc
}

// SetNillableCreateUnix sets the "create_unix" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableCreateUnix(t *time.Time) *DeviceCreate {
	if t != nil {
		dc.SetCreateUnix(*t)
	}
	return dc
}

// SetUpdateUnix sets the "update_unix" field.
func (dc *DeviceCreate) SetUpdateUnix(t time.Time) *DeviceCreate {
	dc.mutation.SetUpdateUnix(t)
	return dc
}

// SetNillableUpdateUnix sets the "update_unix" field if the given value is not nil.
func (dc *DeviceCreate) SetNillableUpdateUnix(t *time.Time) *DeviceCreate {
	if t != nil {
		dc.SetUpdateUnix(*t)
	}
	return dc
}

// SetID sets the "id" field.
func (dc *DeviceCreate) SetID(s string) *DeviceCreate {
	dc.mutation.SetID(s)
	return dc
}

// SetModbusConfigID sets the "modbusConfig" edge to the Modbus entity by ID.
func (dc *DeviceCreate) SetModbusConfigID(id string) *DeviceCreate {
	dc.mutation.SetModbusConfigID(id)
	return dc
}

// SetNillableModbusConfigID sets the "modbusConfig" edge to the Modbus entity by ID if the given value is not nil.
func (dc *DeviceCreate) SetNillableModbusConfigID(id *string) *DeviceCreate {
	if id != nil {
		dc = dc.SetModbusConfigID(*id)
	}
	return dc
}

// SetModbusConfig sets the "modbusConfig" edge to the Modbus entity.
func (dc *DeviceCreate) SetModbusConfig(m *Modbus) *DeviceCreate {
	return dc.SetModbusConfigID(m.ID)
}

// SetUartConfigID sets the "uartConfig" edge to the Uart entity by ID.
func (dc *DeviceCreate) SetUartConfigID(id string) *DeviceCreate {
	dc.mutation.SetUartConfigID(id)
	return dc
}

// SetNillableUartConfigID sets the "uartConfig" edge to the Uart entity by ID if the given value is not nil.
func (dc *DeviceCreate) SetNillableUartConfigID(id *string) *DeviceCreate {
	if id != nil {
		dc = dc.SetUartConfigID(*id)
	}
	return dc
}

// SetUartConfig sets the "uartConfig" edge to the Uart entity.
func (dc *DeviceCreate) SetUartConfig(u *Uart) *DeviceCreate {
	return dc.SetUartConfigID(u.ID)
}

// SetUdpConfigID sets the "udpConfig" edge to the Udp entity by ID.
func (dc *DeviceCreate) SetUdpConfigID(id string) *DeviceCreate {
	dc.mutation.SetUdpConfigID(id)
	return dc
}

// SetNillableUdpConfigID sets the "udpConfig" edge to the Udp entity by ID if the given value is not nil.
func (dc *DeviceCreate) SetNillableUdpConfigID(id *string) *DeviceCreate {
	if id != nil {
		dc = dc.SetUdpConfigID(*id)
	}
	return dc
}

// SetUdpConfig sets the "udpConfig" edge to the Udp entity.
func (dc *DeviceCreate) SetUdpConfig(u *Udp) *DeviceCreate {
	return dc.SetUdpConfigID(u.ID)
}

// Mutation returns the DeviceMutation object of the builder.
func (dc *DeviceCreate) Mutation() *DeviceMutation {
	return dc.mutation
}

// Save creates the Device in the database.
func (dc *DeviceCreate) Save(ctx context.Context) (*Device, error) {
	dc.defaults()
	return withHooks(ctx, dc.sqlSave, dc.mutation, dc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dc *DeviceCreate) SaveX(ctx context.Context) *Device {
	v, err := dc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dc *DeviceCreate) Exec(ctx context.Context) error {
	_, err := dc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dc *DeviceCreate) ExecX(ctx context.Context) {
	if err := dc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dc *DeviceCreate) defaults() {
	if _, ok := dc.mutation.Name(); !ok {
		v := device.DefaultName
		dc.mutation.SetName(v)
	}
	if _, ok := dc.mutation.GetType(); !ok {
		v := device.DefaultType
		dc.mutation.SetType(v)
	}
	if _, ok := dc.mutation.Os(); !ok {
		v := device.DefaultOs
		dc.mutation.SetOs(v)
	}
	if _, ok := dc.mutation.CPU(); !ok {
		v := device.DefaultCPU
		dc.mutation.SetCPU(v)
	}
	if _, ok := dc.mutation.Gpu(); !ok {
		v := device.DefaultGpu
		dc.mutation.SetGpu(v)
	}
	if _, ok := dc.mutation.Memory(); !ok {
		v := device.DefaultMemory
		dc.mutation.SetMemory(v)
	}
	if _, ok := dc.mutation.Disk(); !ok {
		v := device.DefaultDisk
		dc.mutation.SetDisk(v)
	}
	if _, ok := dc.mutation.Protocol(); !ok {
		v := device.DefaultProtocol
		dc.mutation.SetProtocol(v)
	}
	if _, ok := dc.mutation.Status(); !ok {
		v := device.DefaultStatus
		dc.mutation.SetStatus(v)
	}
	if _, ok := dc.mutation.Healthtimestamp(); !ok {
		v := device.DefaultHealthtimestamp
		dc.mutation.SetHealthtimestamp(v)
	}
	if _, ok := dc.mutation.Workmode(); !ok {
		v := device.DefaultWorkmode
		dc.mutation.SetWorkmode(v)
	}
	if _, ok := dc.mutation.CreateUnix(); !ok {
		v := device.DefaultCreateUnix()
		dc.mutation.SetCreateUnix(v)
	}
	if _, ok := dc.mutation.UpdateUnix(); !ok {
		v := device.DefaultUpdateUnix()
		dc.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dc *DeviceCreate) check() error {
	if _, ok := dc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Device.name"`)}
	}
	if _, ok := dc.mutation.IP(); !ok {
		return &ValidationError{Name: "ip", err: errors.New(`ent: missing required field "Device.ip"`)}
	}
	if v, ok := dc.mutation.IP(); ok {
		if err := device.IPValidator(v); err != nil {
			return &ValidationError{Name: "ip", err: fmt.Errorf(`ent: validator failed for field "Device.ip": %w`, err)}
		}
	}
	if _, ok := dc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Device.type"`)}
	}
	if _, ok := dc.mutation.Os(); !ok {
		return &ValidationError{Name: "os", err: errors.New(`ent: missing required field "Device.os"`)}
	}
	if _, ok := dc.mutation.CPU(); !ok {
		return &ValidationError{Name: "cpu", err: errors.New(`ent: missing required field "Device.cpu"`)}
	}
	if _, ok := dc.mutation.Gpu(); !ok {
		return &ValidationError{Name: "gpu", err: errors.New(`ent: missing required field "Device.gpu"`)}
	}
	if _, ok := dc.mutation.Memory(); !ok {
		return &ValidationError{Name: "memory", err: errors.New(`ent: missing required field "Device.memory"`)}
	}
	if _, ok := dc.mutation.Disk(); !ok {
		return &ValidationError{Name: "disk", err: errors.New(`ent: missing required field "Device.disk"`)}
	}
	if _, ok := dc.mutation.Protocol(); !ok {
		return &ValidationError{Name: "protocol", err: errors.New(`ent: missing required field "Device.protocol"`)}
	}
	if _, ok := dc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Device.status"`)}
	}
	if _, ok := dc.mutation.Healthtimestamp(); !ok {
		return &ValidationError{Name: "healthtimestamp", err: errors.New(`ent: missing required field "Device.healthtimestamp"`)}
	}
	if _, ok := dc.mutation.Workmode(); !ok {
		return &ValidationError{Name: "workmode", err: errors.New(`ent: missing required field "Device.workmode"`)}
	}
	if _, ok := dc.mutation.CreateUnix(); !ok {
		return &ValidationError{Name: "create_unix", err: errors.New(`ent: missing required field "Device.create_unix"`)}
	}
	if _, ok := dc.mutation.UpdateUnix(); !ok {
		return &ValidationError{Name: "update_unix", err: errors.New(`ent: missing required field "Device.update_unix"`)}
	}
	if v, ok := dc.mutation.ID(); ok {
		if err := device.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Device.id": %w`, err)}
		}
	}
	return nil
}

func (dc *DeviceCreate) sqlSave(ctx context.Context) (*Device, error) {
	if err := dc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Device.ID type: %T", _spec.ID.Value)
		}
	}
	dc.mutation.id = &_node.ID
	dc.mutation.done = true
	return _node, nil
}

func (dc *DeviceCreate) createSpec() (*Device, *sqlgraph.CreateSpec) {
	var (
		_node = &Device{config: dc.config}
		_spec = sqlgraph.NewCreateSpec(device.Table, sqlgraph.NewFieldSpec(device.FieldID, field.TypeString))
	)
	if id, ok := dc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := dc.mutation.Name(); ok {
		_spec.SetField(device.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := dc.mutation.IP(); ok {
		_spec.SetField(device.FieldIP, field.TypeString, value)
		_node.IP = value
	}
	if value, ok := dc.mutation.GetType(); ok {
		_spec.SetField(device.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := dc.mutation.Os(); ok {
		_spec.SetField(device.FieldOs, field.TypeString, value)
		_node.Os = value
	}
	if value, ok := dc.mutation.CPU(); ok {
		_spec.SetField(device.FieldCPU, field.TypeString, value)
		_node.CPU = value
	}
	if value, ok := dc.mutation.Gpu(); ok {
		_spec.SetField(device.FieldGpu, field.TypeString, value)
		_node.Gpu = value
	}
	if value, ok := dc.mutation.Memory(); ok {
		_spec.SetField(device.FieldMemory, field.TypeString, value)
		_node.Memory = value
	}
	if value, ok := dc.mutation.Disk(); ok {
		_spec.SetField(device.FieldDisk, field.TypeString, value)
		_node.Disk = value
	}
	if value, ok := dc.mutation.Protocol(); ok {
		_spec.SetField(device.FieldProtocol, field.TypeString, value)
		_node.Protocol = value
	}
	if value, ok := dc.mutation.Status(); ok {
		_spec.SetField(device.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := dc.mutation.Healthtimestamp(); ok {
		_spec.SetField(device.FieldHealthtimestamp, field.TypeTime, value)
		_node.Healthtimestamp = value
	}
	if value, ok := dc.mutation.Workmode(); ok {
		_spec.SetField(device.FieldWorkmode, field.TypeString, value)
		_node.Workmode = value
	}
	if value, ok := dc.mutation.CreateUnix(); ok {
		_spec.SetField(device.FieldCreateUnix, field.TypeTime, value)
		_node.CreateUnix = value
	}
	if value, ok := dc.mutation.UpdateUnix(); ok {
		_spec.SetField(device.FieldUpdateUnix, field.TypeTime, value)
		_node.UpdateUnix = value
	}
	if nodes := dc.mutation.ModbusConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.ModbusConfigTable,
			Columns: []string{device.ModbusConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.UartConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UartConfigTable,
			Columns: []string{device.UartConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := dc.mutation.UdpConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UdpConfigTable,
			Columns: []string{device.UdpConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// DeviceCreateBulk is the builder for creating many Device entities in bulk.
type DeviceCreateBulk struct {
	config
	err      error
	builders []*DeviceCreate
}

// Save creates the Device entities in the database.
func (dcb *DeviceCreateBulk) Save(ctx context.Context) ([]*Device, error) {
	if dcb.err != nil {
		return nil, dcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dcb.builders))
	nodes := make([]*Device, len(dcb.builders))
	mutators := make([]Mutator, len(dcb.builders))
	for i := range dcb.builders {
		func(i int, root context.Context) {
			builder := dcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DeviceMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dcb *DeviceCreateBulk) SaveX(ctx context.Context) []*Device {
	v, err := dcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dcb *DeviceCreateBulk) Exec(ctx context.Context) error {
	_, err := dcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dcb *DeviceCreateBulk) ExecX(ctx context.Context) {
	if err := dcb.Exec(ctx); err != nil {
		panic(err)
	}
}

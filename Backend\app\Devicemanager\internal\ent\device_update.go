// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DeviceUpdate is the builder for updating Device entities.
type DeviceUpdate struct {
	config
	hooks    []Hook
	mutation *DeviceMutation
}

// Where appends a list predicates to the DeviceUpdate builder.
func (du *DeviceUpdate) Where(ps ...predicate.Device) *DeviceUpdate {
	du.mutation.Where(ps...)
	return du
}

// SetName sets the "name" field.
func (du *DeviceUpdate) SetName(s string) *DeviceUpdate {
	du.mutation.SetName(s)
	return du
}

// SetNillableName sets the "name" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableName(s *string) *DeviceUpdate {
	if s != nil {
		du.SetName(*s)
	}
	return du
}

// SetIP sets the "ip" field.
func (du *DeviceUpdate) SetIP(s string) *DeviceUpdate {
	du.mutation.SetIP(s)
	return du
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableIP(s *string) *DeviceUpdate {
	if s != nil {
		du.SetIP(*s)
	}
	return du
}

// SetType sets the "type" field.
func (du *DeviceUpdate) SetType(s string) *DeviceUpdate {
	du.mutation.SetType(s)
	return du
}

// SetNillableType sets the "type" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableType(s *string) *DeviceUpdate {
	if s != nil {
		du.SetType(*s)
	}
	return du
}

// SetOs sets the "os" field.
func (du *DeviceUpdate) SetOs(s string) *DeviceUpdate {
	du.mutation.SetOs(s)
	return du
}

// SetNillableOs sets the "os" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableOs(s *string) *DeviceUpdate {
	if s != nil {
		du.SetOs(*s)
	}
	return du
}

// SetCPU sets the "cpu" field.
func (du *DeviceUpdate) SetCPU(s string) *DeviceUpdate {
	du.mutation.SetCPU(s)
	return du
}

// SetNillableCPU sets the "cpu" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableCPU(s *string) *DeviceUpdate {
	if s != nil {
		du.SetCPU(*s)
	}
	return du
}

// SetGpu sets the "gpu" field.
func (du *DeviceUpdate) SetGpu(s string) *DeviceUpdate {
	du.mutation.SetGpu(s)
	return du
}

// SetNillableGpu sets the "gpu" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableGpu(s *string) *DeviceUpdate {
	if s != nil {
		du.SetGpu(*s)
	}
	return du
}

// SetMemory sets the "memory" field.
func (du *DeviceUpdate) SetMemory(s string) *DeviceUpdate {
	du.mutation.SetMemory(s)
	return du
}

// SetNillableMemory sets the "memory" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableMemory(s *string) *DeviceUpdate {
	if s != nil {
		du.SetMemory(*s)
	}
	return du
}

// SetDisk sets the "disk" field.
func (du *DeviceUpdate) SetDisk(s string) *DeviceUpdate {
	du.mutation.SetDisk(s)
	return du
}

// SetNillableDisk sets the "disk" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableDisk(s *string) *DeviceUpdate {
	if s != nil {
		du.SetDisk(*s)
	}
	return du
}

// SetProtocol sets the "protocol" field.
func (du *DeviceUpdate) SetProtocol(s string) *DeviceUpdate {
	du.mutation.SetProtocol(s)
	return du
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableProtocol(s *string) *DeviceUpdate {
	if s != nil {
		du.SetProtocol(*s)
	}
	return du
}

// SetStatus sets the "status" field.
func (du *DeviceUpdate) SetStatus(s string) *DeviceUpdate {
	du.mutation.SetStatus(s)
	return du
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableStatus(s *string) *DeviceUpdate {
	if s != nil {
		du.SetStatus(*s)
	}
	return du
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (du *DeviceUpdate) SetHealthtimestamp(t time.Time) *DeviceUpdate {
	du.mutation.SetHealthtimestamp(t)
	return du
}

// SetNillableHealthtimestamp sets the "healthtimestamp" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableHealthtimestamp(t *time.Time) *DeviceUpdate {
	if t != nil {
		du.SetHealthtimestamp(*t)
	}
	return du
}

// SetWorkmode sets the "workmode" field.
func (du *DeviceUpdate) SetWorkmode(s string) *DeviceUpdate {
	du.mutation.SetWorkmode(s)
	return du
}

// SetNillableWorkmode sets the "workmode" field if the given value is not nil.
func (du *DeviceUpdate) SetNillableWorkmode(s *string) *DeviceUpdate {
	if s != nil {
		du.SetWorkmode(*s)
	}
	return du
}

// SetUpdateUnix sets the "update_unix" field.
func (du *DeviceUpdate) SetUpdateUnix(t time.Time) *DeviceUpdate {
	du.mutation.SetUpdateUnix(t)
	return du
}

// SetModbusConfigID sets the "modbusConfig" edge to the Modbus entity by ID.
func (du *DeviceUpdate) SetModbusConfigID(id string) *DeviceUpdate {
	du.mutation.SetModbusConfigID(id)
	return du
}

// SetNillableModbusConfigID sets the "modbusConfig" edge to the Modbus entity by ID if the given value is not nil.
func (du *DeviceUpdate) SetNillableModbusConfigID(id *string) *DeviceUpdate {
	if id != nil {
		du = du.SetModbusConfigID(*id)
	}
	return du
}

// SetModbusConfig sets the "modbusConfig" edge to the Modbus entity.
func (du *DeviceUpdate) SetModbusConfig(m *Modbus) *DeviceUpdate {
	return du.SetModbusConfigID(m.ID)
}

// SetUartConfigID sets the "uartConfig" edge to the Uart entity by ID.
func (du *DeviceUpdate) SetUartConfigID(id string) *DeviceUpdate {
	du.mutation.SetUartConfigID(id)
	return du
}

// SetNillableUartConfigID sets the "uartConfig" edge to the Uart entity by ID if the given value is not nil.
func (du *DeviceUpdate) SetNillableUartConfigID(id *string) *DeviceUpdate {
	if id != nil {
		du = du.SetUartConfigID(*id)
	}
	return du
}

// SetUartConfig sets the "uartConfig" edge to the Uart entity.
func (du *DeviceUpdate) SetUartConfig(u *Uart) *DeviceUpdate {
	return du.SetUartConfigID(u.ID)
}

// SetUdpConfigID sets the "udpConfig" edge to the Udp entity by ID.
func (du *DeviceUpdate) SetUdpConfigID(id string) *DeviceUpdate {
	du.mutation.SetUdpConfigID(id)
	return du
}

// SetNillableUdpConfigID sets the "udpConfig" edge to the Udp entity by ID if the given value is not nil.
func (du *DeviceUpdate) SetNillableUdpConfigID(id *string) *DeviceUpdate {
	if id != nil {
		du = du.SetUdpConfigID(*id)
	}
	return du
}

// SetUdpConfig sets the "udpConfig" edge to the Udp entity.
func (du *DeviceUpdate) SetUdpConfig(u *Udp) *DeviceUpdate {
	return du.SetUdpConfigID(u.ID)
}

// Mutation returns the DeviceMutation object of the builder.
func (du *DeviceUpdate) Mutation() *DeviceMutation {
	return du.mutation
}

// ClearModbusConfig clears the "modbusConfig" edge to the Modbus entity.
func (du *DeviceUpdate) ClearModbusConfig() *DeviceUpdate {
	du.mutation.ClearModbusConfig()
	return du
}

// ClearUartConfig clears the "uartConfig" edge to the Uart entity.
func (du *DeviceUpdate) ClearUartConfig() *DeviceUpdate {
	du.mutation.ClearUartConfig()
	return du
}

// ClearUdpConfig clears the "udpConfig" edge to the Udp entity.
func (du *DeviceUpdate) ClearUdpConfig() *DeviceUpdate {
	du.mutation.ClearUdpConfig()
	return du
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (du *DeviceUpdate) Save(ctx context.Context) (int, error) {
	du.defaults()
	return withHooks(ctx, du.sqlSave, du.mutation, du.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (du *DeviceUpdate) SaveX(ctx context.Context) int {
	affected, err := du.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (du *DeviceUpdate) Exec(ctx context.Context) error {
	_, err := du.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (du *DeviceUpdate) ExecX(ctx context.Context) {
	if err := du.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (du *DeviceUpdate) defaults() {
	if _, ok := du.mutation.UpdateUnix(); !ok {
		v := device.UpdateDefaultUpdateUnix()
		du.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (du *DeviceUpdate) check() error {
	if v, ok := du.mutation.IP(); ok {
		if err := device.IPValidator(v); err != nil {
			return &ValidationError{Name: "ip", err: fmt.Errorf(`ent: validator failed for field "Device.ip": %w`, err)}
		}
	}
	return nil
}

func (du *DeviceUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := du.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(device.Table, device.Columns, sqlgraph.NewFieldSpec(device.FieldID, field.TypeString))
	if ps := du.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := du.mutation.Name(); ok {
		_spec.SetField(device.FieldName, field.TypeString, value)
	}
	if value, ok := du.mutation.IP(); ok {
		_spec.SetField(device.FieldIP, field.TypeString, value)
	}
	if value, ok := du.mutation.GetType(); ok {
		_spec.SetField(device.FieldType, field.TypeString, value)
	}
	if value, ok := du.mutation.Os(); ok {
		_spec.SetField(device.FieldOs, field.TypeString, value)
	}
	if value, ok := du.mutation.CPU(); ok {
		_spec.SetField(device.FieldCPU, field.TypeString, value)
	}
	if value, ok := du.mutation.Gpu(); ok {
		_spec.SetField(device.FieldGpu, field.TypeString, value)
	}
	if value, ok := du.mutation.Memory(); ok {
		_spec.SetField(device.FieldMemory, field.TypeString, value)
	}
	if value, ok := du.mutation.Disk(); ok {
		_spec.SetField(device.FieldDisk, field.TypeString, value)
	}
	if value, ok := du.mutation.Protocol(); ok {
		_spec.SetField(device.FieldProtocol, field.TypeString, value)
	}
	if value, ok := du.mutation.Status(); ok {
		_spec.SetField(device.FieldStatus, field.TypeString, value)
	}
	if value, ok := du.mutation.Healthtimestamp(); ok {
		_spec.SetField(device.FieldHealthtimestamp, field.TypeTime, value)
	}
	if value, ok := du.mutation.Workmode(); ok {
		_spec.SetField(device.FieldWorkmode, field.TypeString, value)
	}
	if value, ok := du.mutation.UpdateUnix(); ok {
		_spec.SetField(device.FieldUpdateUnix, field.TypeTime, value)
	}
	if du.mutation.ModbusConfigCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.ModbusConfigTable,
			Columns: []string{device.ModbusConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.ModbusConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.ModbusConfigTable,
			Columns: []string{device.ModbusConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.UartConfigCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UartConfigTable,
			Columns: []string{device.UartConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.UartConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UartConfigTable,
			Columns: []string{device.UartConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if du.mutation.UdpConfigCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UdpConfigTable,
			Columns: []string{device.UdpConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := du.mutation.UdpConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UdpConfigTable,
			Columns: []string{device.UdpConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, du.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{device.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	du.mutation.done = true
	return n, nil
}

// DeviceUpdateOne is the builder for updating a single Device entity.
type DeviceUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *DeviceMutation
}

// SetName sets the "name" field.
func (duo *DeviceUpdateOne) SetName(s string) *DeviceUpdateOne {
	duo.mutation.SetName(s)
	return duo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableName(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetName(*s)
	}
	return duo
}

// SetIP sets the "ip" field.
func (duo *DeviceUpdateOne) SetIP(s string) *DeviceUpdateOne {
	duo.mutation.SetIP(s)
	return duo
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableIP(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetIP(*s)
	}
	return duo
}

// SetType sets the "type" field.
func (duo *DeviceUpdateOne) SetType(s string) *DeviceUpdateOne {
	duo.mutation.SetType(s)
	return duo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableType(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetType(*s)
	}
	return duo
}

// SetOs sets the "os" field.
func (duo *DeviceUpdateOne) SetOs(s string) *DeviceUpdateOne {
	duo.mutation.SetOs(s)
	return duo
}

// SetNillableOs sets the "os" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableOs(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetOs(*s)
	}
	return duo
}

// SetCPU sets the "cpu" field.
func (duo *DeviceUpdateOne) SetCPU(s string) *DeviceUpdateOne {
	duo.mutation.SetCPU(s)
	return duo
}

// SetNillableCPU sets the "cpu" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableCPU(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetCPU(*s)
	}
	return duo
}

// SetGpu sets the "gpu" field.
func (duo *DeviceUpdateOne) SetGpu(s string) *DeviceUpdateOne {
	duo.mutation.SetGpu(s)
	return duo
}

// SetNillableGpu sets the "gpu" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableGpu(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetGpu(*s)
	}
	return duo
}

// SetMemory sets the "memory" field.
func (duo *DeviceUpdateOne) SetMemory(s string) *DeviceUpdateOne {
	duo.mutation.SetMemory(s)
	return duo
}

// SetNillableMemory sets the "memory" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableMemory(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetMemory(*s)
	}
	return duo
}

// SetDisk sets the "disk" field.
func (duo *DeviceUpdateOne) SetDisk(s string) *DeviceUpdateOne {
	duo.mutation.SetDisk(s)
	return duo
}

// SetNillableDisk sets the "disk" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableDisk(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetDisk(*s)
	}
	return duo
}

// SetProtocol sets the "protocol" field.
func (duo *DeviceUpdateOne) SetProtocol(s string) *DeviceUpdateOne {
	duo.mutation.SetProtocol(s)
	return duo
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableProtocol(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetProtocol(*s)
	}
	return duo
}

// SetStatus sets the "status" field.
func (duo *DeviceUpdateOne) SetStatus(s string) *DeviceUpdateOne {
	duo.mutation.SetStatus(s)
	return duo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableStatus(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetStatus(*s)
	}
	return duo
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (duo *DeviceUpdateOne) SetHealthtimestamp(t time.Time) *DeviceUpdateOne {
	duo.mutation.SetHealthtimestamp(t)
	return duo
}

// SetNillableHealthtimestamp sets the "healthtimestamp" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableHealthtimestamp(t *time.Time) *DeviceUpdateOne {
	if t != nil {
		duo.SetHealthtimestamp(*t)
	}
	return duo
}

// SetWorkmode sets the "workmode" field.
func (duo *DeviceUpdateOne) SetWorkmode(s string) *DeviceUpdateOne {
	duo.mutation.SetWorkmode(s)
	return duo
}

// SetNillableWorkmode sets the "workmode" field if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableWorkmode(s *string) *DeviceUpdateOne {
	if s != nil {
		duo.SetWorkmode(*s)
	}
	return duo
}

// SetUpdateUnix sets the "update_unix" field.
func (duo *DeviceUpdateOne) SetUpdateUnix(t time.Time) *DeviceUpdateOne {
	duo.mutation.SetUpdateUnix(t)
	return duo
}

// SetModbusConfigID sets the "modbusConfig" edge to the Modbus entity by ID.
func (duo *DeviceUpdateOne) SetModbusConfigID(id string) *DeviceUpdateOne {
	duo.mutation.SetModbusConfigID(id)
	return duo
}

// SetNillableModbusConfigID sets the "modbusConfig" edge to the Modbus entity by ID if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableModbusConfigID(id *string) *DeviceUpdateOne {
	if id != nil {
		duo = duo.SetModbusConfigID(*id)
	}
	return duo
}

// SetModbusConfig sets the "modbusConfig" edge to the Modbus entity.
func (duo *DeviceUpdateOne) SetModbusConfig(m *Modbus) *DeviceUpdateOne {
	return duo.SetModbusConfigID(m.ID)
}

// SetUartConfigID sets the "uartConfig" edge to the Uart entity by ID.
func (duo *DeviceUpdateOne) SetUartConfigID(id string) *DeviceUpdateOne {
	duo.mutation.SetUartConfigID(id)
	return duo
}

// SetNillableUartConfigID sets the "uartConfig" edge to the Uart entity by ID if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableUartConfigID(id *string) *DeviceUpdateOne {
	if id != nil {
		duo = duo.SetUartConfigID(*id)
	}
	return duo
}

// SetUartConfig sets the "uartConfig" edge to the Uart entity.
func (duo *DeviceUpdateOne) SetUartConfig(u *Uart) *DeviceUpdateOne {
	return duo.SetUartConfigID(u.ID)
}

// SetUdpConfigID sets the "udpConfig" edge to the Udp entity by ID.
func (duo *DeviceUpdateOne) SetUdpConfigID(id string) *DeviceUpdateOne {
	duo.mutation.SetUdpConfigID(id)
	return duo
}

// SetNillableUdpConfigID sets the "udpConfig" edge to the Udp entity by ID if the given value is not nil.
func (duo *DeviceUpdateOne) SetNillableUdpConfigID(id *string) *DeviceUpdateOne {
	if id != nil {
		duo = duo.SetUdpConfigID(*id)
	}
	return duo
}

// SetUdpConfig sets the "udpConfig" edge to the Udp entity.
func (duo *DeviceUpdateOne) SetUdpConfig(u *Udp) *DeviceUpdateOne {
	return duo.SetUdpConfigID(u.ID)
}

// Mutation returns the DeviceMutation object of the builder.
func (duo *DeviceUpdateOne) Mutation() *DeviceMutation {
	return duo.mutation
}

// ClearModbusConfig clears the "modbusConfig" edge to the Modbus entity.
func (duo *DeviceUpdateOne) ClearModbusConfig() *DeviceUpdateOne {
	duo.mutation.ClearModbusConfig()
	return duo
}

// ClearUartConfig clears the "uartConfig" edge to the Uart entity.
func (duo *DeviceUpdateOne) ClearUartConfig() *DeviceUpdateOne {
	duo.mutation.ClearUartConfig()
	return duo
}

// ClearUdpConfig clears the "udpConfig" edge to the Udp entity.
func (duo *DeviceUpdateOne) ClearUdpConfig() *DeviceUpdateOne {
	duo.mutation.ClearUdpConfig()
	return duo
}

// Where appends a list predicates to the DeviceUpdate builder.
func (duo *DeviceUpdateOne) Where(ps ...predicate.Device) *DeviceUpdateOne {
	duo.mutation.Where(ps...)
	return duo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (duo *DeviceUpdateOne) Select(field string, fields ...string) *DeviceUpdateOne {
	duo.fields = append([]string{field}, fields...)
	return duo
}

// Save executes the query and returns the updated Device entity.
func (duo *DeviceUpdateOne) Save(ctx context.Context) (*Device, error) {
	duo.defaults()
	return withHooks(ctx, duo.sqlSave, duo.mutation, duo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duo *DeviceUpdateOne) SaveX(ctx context.Context) *Device {
	node, err := duo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (duo *DeviceUpdateOne) Exec(ctx context.Context) error {
	_, err := duo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duo *DeviceUpdateOne) ExecX(ctx context.Context) {
	if err := duo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (duo *DeviceUpdateOne) defaults() {
	if _, ok := duo.mutation.UpdateUnix(); !ok {
		v := device.UpdateDefaultUpdateUnix()
		duo.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (duo *DeviceUpdateOne) check() error {
	if v, ok := duo.mutation.IP(); ok {
		if err := device.IPValidator(v); err != nil {
			return &ValidationError{Name: "ip", err: fmt.Errorf(`ent: validator failed for field "Device.ip": %w`, err)}
		}
	}
	return nil
}

func (duo *DeviceUpdateOne) sqlSave(ctx context.Context) (_node *Device, err error) {
	if err := duo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(device.Table, device.Columns, sqlgraph.NewFieldSpec(device.FieldID, field.TypeString))
	id, ok := duo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Device.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := duo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, device.FieldID)
		for _, f := range fields {
			if !device.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != device.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := duo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := duo.mutation.Name(); ok {
		_spec.SetField(device.FieldName, field.TypeString, value)
	}
	if value, ok := duo.mutation.IP(); ok {
		_spec.SetField(device.FieldIP, field.TypeString, value)
	}
	if value, ok := duo.mutation.GetType(); ok {
		_spec.SetField(device.FieldType, field.TypeString, value)
	}
	if value, ok := duo.mutation.Os(); ok {
		_spec.SetField(device.FieldOs, field.TypeString, value)
	}
	if value, ok := duo.mutation.CPU(); ok {
		_spec.SetField(device.FieldCPU, field.TypeString, value)
	}
	if value, ok := duo.mutation.Gpu(); ok {
		_spec.SetField(device.FieldGpu, field.TypeString, value)
	}
	if value, ok := duo.mutation.Memory(); ok {
		_spec.SetField(device.FieldMemory, field.TypeString, value)
	}
	if value, ok := duo.mutation.Disk(); ok {
		_spec.SetField(device.FieldDisk, field.TypeString, value)
	}
	if value, ok := duo.mutation.Protocol(); ok {
		_spec.SetField(device.FieldProtocol, field.TypeString, value)
	}
	if value, ok := duo.mutation.Status(); ok {
		_spec.SetField(device.FieldStatus, field.TypeString, value)
	}
	if value, ok := duo.mutation.Healthtimestamp(); ok {
		_spec.SetField(device.FieldHealthtimestamp, field.TypeTime, value)
	}
	if value, ok := duo.mutation.Workmode(); ok {
		_spec.SetField(device.FieldWorkmode, field.TypeString, value)
	}
	if value, ok := duo.mutation.UpdateUnix(); ok {
		_spec.SetField(device.FieldUpdateUnix, field.TypeTime, value)
	}
	if duo.mutation.ModbusConfigCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.ModbusConfigTable,
			Columns: []string{device.ModbusConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.ModbusConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.ModbusConfigTable,
			Columns: []string{device.ModbusConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.UartConfigCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UartConfigTable,
			Columns: []string{device.UartConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.UartConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UartConfigTable,
			Columns: []string{device.UartConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if duo.mutation.UdpConfigCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UdpConfigTable,
			Columns: []string{device.UdpConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := duo.mutation.UdpConfigIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   device.UdpConfigTable,
			Columns: []string{device.UdpConfigColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Device{config: duo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, duo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{device.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	duo.mutation.done = true
	return _node, nil
}

// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// DataColumns holds the columns for the "data" table.
	DataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "index", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "type", Type: field.TypeString, Default: ""},
		{Name: "value", Type: field.TypeString, Default: ""},
		{Name: "update_unix", Type: field.TypeTime},
		{Name: "frame_id", Type: field.TypeString},
	}
	// DataTable holds the schema information for the "data" table.
	DataTable = &schema.Table{
		Name:       "data",
		Columns:    DataColumns,
		PrimaryKey: []*schema.Column{DataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "data_modbuses_data_points",
				Columns:    []*schema.Column{DataColumns[6]},
				RefColumns: []*schema.Column{ModbusesColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "data_uarts_data_points",
				Columns:    []*schema.Column{DataColumns[6]},
				RefColumns: []*schema.Column{UartsColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "data_udps_data_points",
				Columns:    []*schema.Column{DataColumns[6]},
				RefColumns: []*schema.Column{UdpsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// DevicesColumns holds the columns for the "devices" table.
	DevicesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "name", Type: field.TypeString, Default: ""},
		{Name: "ip", Type: field.TypeString},
		{Name: "type", Type: field.TypeString, Default: "lower"},
		{Name: "os", Type: field.TypeString, Default: ""},
		{Name: "cpu", Type: field.TypeString, Default: ""},
		{Name: "gpu", Type: field.TypeString, Default: ""},
		{Name: "memory", Type: field.TypeString, Default: ""},
		{Name: "disk", Type: field.TypeString, Default: ""},
		{Name: "protocol", Type: field.TypeString, Default: ""},
		{Name: "status", Type: field.TypeString, Default: "unready"},
		{Name: "healthtimestamp", Type: field.TypeTime},
		{Name: "workmode", Type: field.TypeString, Default: "centralized"},
		{Name: "create_unix", Type: field.TypeTime},
		{Name: "update_unix", Type: field.TypeTime},
	}
	// DevicesTable holds the schema information for the "devices" table.
	DevicesTable = &schema.Table{
		Name:       "devices",
		Columns:    DevicesColumns,
		PrimaryKey: []*schema.Column{DevicesColumns[0]},
	}
	// ModbusesColumns holds the columns for the "modbuses" table.
	ModbusesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "tid", Type: field.TypeString, Default: ""},
		{Name: "pid", Type: field.TypeString, Default: ""},
		{Name: "len", Type: field.TypeString, Default: ""},
		{Name: "uid", Type: field.TypeString, Default: ""},
		{Name: "fc", Type: field.TypeString, Default: ""},
		{Name: "create_unix", Type: field.TypeTime},
		{Name: "update_unix", Type: field.TypeTime},
		{Name: "device_id", Type: field.TypeString, Unique: true},
	}
	// ModbusesTable holds the schema information for the "modbuses" table.
	ModbusesTable = &schema.Table{
		Name:       "modbuses",
		Columns:    ModbusesColumns,
		PrimaryKey: []*schema.Column{ModbusesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "modbuses_devices_modbusConfig",
				Columns:    []*schema.Column{ModbusesColumns[8]},
				RefColumns: []*schema.Column{DevicesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// UartsColumns holds the columns for the "uarts" table.
	UartsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "header", Type: field.TypeString, Default: ""},
		{Name: "addr", Type: field.TypeString, Default: ""},
		{Name: "cmd", Type: field.TypeString, Default: ""},
		{Name: "tail", Type: field.TypeString, Default: ""},
		{Name: "create_unix", Type: field.TypeTime},
		{Name: "update_unix", Type: field.TypeTime},
		{Name: "device_id", Type: field.TypeString, Unique: true},
	}
	// UartsTable holds the schema information for the "uarts" table.
	UartsTable = &schema.Table{
		Name:       "uarts",
		Columns:    UartsColumns,
		PrimaryKey: []*schema.Column{UartsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "uarts_devices_uartConfig",
				Columns:    []*schema.Column{UartsColumns[7]},
				RefColumns: []*schema.Column{DevicesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// UdpsColumns holds the columns for the "udps" table.
	UdpsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "type", Type: field.TypeString, Default: ""},
		{Name: "header", Type: field.TypeString, Default: ""},
		{Name: "type_id", Type: field.TypeString, Default: ""},
		{Name: "create_unix", Type: field.TypeTime},
		{Name: "update_unix", Type: field.TypeTime},
		{Name: "device_id", Type: field.TypeString, Unique: true},
	}
	// UdpsTable holds the schema information for the "udps" table.
	UdpsTable = &schema.Table{
		Name:       "udps",
		Columns:    UdpsColumns,
		PrimaryKey: []*schema.Column{UdpsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "udps_devices_udpConfig",
				Columns:    []*schema.Column{UdpsColumns[6]},
				RefColumns: []*schema.Column{DevicesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		DataTable,
		DevicesTable,
		ModbusesTable,
		UartsTable,
		UdpsTable,
	}
)

func init() {
	DataTable.ForeignKeys[0].RefTable = ModbusesTable
	DataTable.ForeignKeys[1].RefTable = UartsTable
	DataTable.ForeignKeys[2].RefTable = UdpsTable
	ModbusesTable.ForeignKeys[0].RefTable = DevicesTable
	UartsTable.ForeignKeys[0].RefTable = DevicesTable
	UdpsTable.ForeignKeys[0].RefTable = DevicesTable
}

// Code generated by ent, DO NOT EDIT.

package modbus

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContainsFold(FieldID, id))
}

// DeviceID applies equality check predicate on the "device_id" field. It's identical to DeviceIDEQ.
func DeviceID(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldDeviceID, v))
}

// Tid applies equality check predicate on the "tid" field. It's identical to TidEQ.
func Tid(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldTid, v))
}

// Pid applies equality check predicate on the "pid" field. It's identical to PidEQ.
func Pid(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldPid, v))
}

// Len applies equality check predicate on the "len" field. It's identical to LenEQ.
func Len(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldLen, v))
}

// UID applies equality check predicate on the "uid" field. It's identical to UIDEQ.
func UID(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldUID, v))
}

// Fc applies equality check predicate on the "fc" field. It's identical to FcEQ.
func Fc(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldFc, v))
}

// CreateUnix applies equality check predicate on the "create_unix" field. It's identical to CreateUnixEQ.
func CreateUnix(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldCreateUnix, v))
}

// UpdateUnix applies equality check predicate on the "update_unix" field. It's identical to UpdateUnixEQ.
func UpdateUnix(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldUpdateUnix, v))
}

// DeviceIDEQ applies the EQ predicate on the "device_id" field.
func DeviceIDEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldDeviceID, v))
}

// DeviceIDNEQ applies the NEQ predicate on the "device_id" field.
func DeviceIDNEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldDeviceID, v))
}

// DeviceIDIn applies the In predicate on the "device_id" field.
func DeviceIDIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldDeviceID, vs...))
}

// DeviceIDNotIn applies the NotIn predicate on the "device_id" field.
func DeviceIDNotIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldDeviceID, vs...))
}

// DeviceIDGT applies the GT predicate on the "device_id" field.
func DeviceIDGT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldDeviceID, v))
}

// DeviceIDGTE applies the GTE predicate on the "device_id" field.
func DeviceIDGTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldDeviceID, v))
}

// DeviceIDLT applies the LT predicate on the "device_id" field.
func DeviceIDLT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldDeviceID, v))
}

// DeviceIDLTE applies the LTE predicate on the "device_id" field.
func DeviceIDLTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldDeviceID, v))
}

// DeviceIDContains applies the Contains predicate on the "device_id" field.
func DeviceIDContains(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContains(FieldDeviceID, v))
}

// DeviceIDHasPrefix applies the HasPrefix predicate on the "device_id" field.
func DeviceIDHasPrefix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasPrefix(FieldDeviceID, v))
}

// DeviceIDHasSuffix applies the HasSuffix predicate on the "device_id" field.
func DeviceIDHasSuffix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasSuffix(FieldDeviceID, v))
}

// DeviceIDEqualFold applies the EqualFold predicate on the "device_id" field.
func DeviceIDEqualFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEqualFold(FieldDeviceID, v))
}

// DeviceIDContainsFold applies the ContainsFold predicate on the "device_id" field.
func DeviceIDContainsFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContainsFold(FieldDeviceID, v))
}

// TidEQ applies the EQ predicate on the "tid" field.
func TidEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldTid, v))
}

// TidNEQ applies the NEQ predicate on the "tid" field.
func TidNEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldTid, v))
}

// TidIn applies the In predicate on the "tid" field.
func TidIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldTid, vs...))
}

// TidNotIn applies the NotIn predicate on the "tid" field.
func TidNotIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldTid, vs...))
}

// TidGT applies the GT predicate on the "tid" field.
func TidGT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldTid, v))
}

// TidGTE applies the GTE predicate on the "tid" field.
func TidGTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldTid, v))
}

// TidLT applies the LT predicate on the "tid" field.
func TidLT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldTid, v))
}

// TidLTE applies the LTE predicate on the "tid" field.
func TidLTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldTid, v))
}

// TidContains applies the Contains predicate on the "tid" field.
func TidContains(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContains(FieldTid, v))
}

// TidHasPrefix applies the HasPrefix predicate on the "tid" field.
func TidHasPrefix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasPrefix(FieldTid, v))
}

// TidHasSuffix applies the HasSuffix predicate on the "tid" field.
func TidHasSuffix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasSuffix(FieldTid, v))
}

// TidEqualFold applies the EqualFold predicate on the "tid" field.
func TidEqualFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEqualFold(FieldTid, v))
}

// TidContainsFold applies the ContainsFold predicate on the "tid" field.
func TidContainsFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContainsFold(FieldTid, v))
}

// PidEQ applies the EQ predicate on the "pid" field.
func PidEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldPid, v))
}

// PidNEQ applies the NEQ predicate on the "pid" field.
func PidNEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldPid, v))
}

// PidIn applies the In predicate on the "pid" field.
func PidIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldPid, vs...))
}

// PidNotIn applies the NotIn predicate on the "pid" field.
func PidNotIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldPid, vs...))
}

// PidGT applies the GT predicate on the "pid" field.
func PidGT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldPid, v))
}

// PidGTE applies the GTE predicate on the "pid" field.
func PidGTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldPid, v))
}

// PidLT applies the LT predicate on the "pid" field.
func PidLT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldPid, v))
}

// PidLTE applies the LTE predicate on the "pid" field.
func PidLTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldPid, v))
}

// PidContains applies the Contains predicate on the "pid" field.
func PidContains(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContains(FieldPid, v))
}

// PidHasPrefix applies the HasPrefix predicate on the "pid" field.
func PidHasPrefix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasPrefix(FieldPid, v))
}

// PidHasSuffix applies the HasSuffix predicate on the "pid" field.
func PidHasSuffix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasSuffix(FieldPid, v))
}

// PidEqualFold applies the EqualFold predicate on the "pid" field.
func PidEqualFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEqualFold(FieldPid, v))
}

// PidContainsFold applies the ContainsFold predicate on the "pid" field.
func PidContainsFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContainsFold(FieldPid, v))
}

// LenEQ applies the EQ predicate on the "len" field.
func LenEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldLen, v))
}

// LenNEQ applies the NEQ predicate on the "len" field.
func LenNEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldLen, v))
}

// LenIn applies the In predicate on the "len" field.
func LenIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldLen, vs...))
}

// LenNotIn applies the NotIn predicate on the "len" field.
func LenNotIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldLen, vs...))
}

// LenGT applies the GT predicate on the "len" field.
func LenGT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldLen, v))
}

// LenGTE applies the GTE predicate on the "len" field.
func LenGTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldLen, v))
}

// LenLT applies the LT predicate on the "len" field.
func LenLT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldLen, v))
}

// LenLTE applies the LTE predicate on the "len" field.
func LenLTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldLen, v))
}

// LenContains applies the Contains predicate on the "len" field.
func LenContains(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContains(FieldLen, v))
}

// LenHasPrefix applies the HasPrefix predicate on the "len" field.
func LenHasPrefix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasPrefix(FieldLen, v))
}

// LenHasSuffix applies the HasSuffix predicate on the "len" field.
func LenHasSuffix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasSuffix(FieldLen, v))
}

// LenEqualFold applies the EqualFold predicate on the "len" field.
func LenEqualFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEqualFold(FieldLen, v))
}

// LenContainsFold applies the ContainsFold predicate on the "len" field.
func LenContainsFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContainsFold(FieldLen, v))
}

// UIDEQ applies the EQ predicate on the "uid" field.
func UIDEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldUID, v))
}

// UIDNEQ applies the NEQ predicate on the "uid" field.
func UIDNEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldUID, v))
}

// UIDIn applies the In predicate on the "uid" field.
func UIDIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldUID, vs...))
}

// UIDNotIn applies the NotIn predicate on the "uid" field.
func UIDNotIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldUID, vs...))
}

// UIDGT applies the GT predicate on the "uid" field.
func UIDGT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldUID, v))
}

// UIDGTE applies the GTE predicate on the "uid" field.
func UIDGTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldUID, v))
}

// UIDLT applies the LT predicate on the "uid" field.
func UIDLT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldUID, v))
}

// UIDLTE applies the LTE predicate on the "uid" field.
func UIDLTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldUID, v))
}

// UIDContains applies the Contains predicate on the "uid" field.
func UIDContains(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContains(FieldUID, v))
}

// UIDHasPrefix applies the HasPrefix predicate on the "uid" field.
func UIDHasPrefix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasPrefix(FieldUID, v))
}

// UIDHasSuffix applies the HasSuffix predicate on the "uid" field.
func UIDHasSuffix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasSuffix(FieldUID, v))
}

// UIDEqualFold applies the EqualFold predicate on the "uid" field.
func UIDEqualFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEqualFold(FieldUID, v))
}

// UIDContainsFold applies the ContainsFold predicate on the "uid" field.
func UIDContainsFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContainsFold(FieldUID, v))
}

// FcEQ applies the EQ predicate on the "fc" field.
func FcEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldFc, v))
}

// FcNEQ applies the NEQ predicate on the "fc" field.
func FcNEQ(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldFc, v))
}

// FcIn applies the In predicate on the "fc" field.
func FcIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldFc, vs...))
}

// FcNotIn applies the NotIn predicate on the "fc" field.
func FcNotIn(vs ...string) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldFc, vs...))
}

// FcGT applies the GT predicate on the "fc" field.
func FcGT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldFc, v))
}

// FcGTE applies the GTE predicate on the "fc" field.
func FcGTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldFc, v))
}

// FcLT applies the LT predicate on the "fc" field.
func FcLT(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldFc, v))
}

// FcLTE applies the LTE predicate on the "fc" field.
func FcLTE(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldFc, v))
}

// FcContains applies the Contains predicate on the "fc" field.
func FcContains(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContains(FieldFc, v))
}

// FcHasPrefix applies the HasPrefix predicate on the "fc" field.
func FcHasPrefix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasPrefix(FieldFc, v))
}

// FcHasSuffix applies the HasSuffix predicate on the "fc" field.
func FcHasSuffix(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldHasSuffix(FieldFc, v))
}

// FcEqualFold applies the EqualFold predicate on the "fc" field.
func FcEqualFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldEqualFold(FieldFc, v))
}

// FcContainsFold applies the ContainsFold predicate on the "fc" field.
func FcContainsFold(v string) predicate.Modbus {
	return predicate.Modbus(sql.FieldContainsFold(FieldFc, v))
}

// CreateUnixEQ applies the EQ predicate on the "create_unix" field.
func CreateUnixEQ(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldCreateUnix, v))
}

// CreateUnixNEQ applies the NEQ predicate on the "create_unix" field.
func CreateUnixNEQ(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldCreateUnix, v))
}

// CreateUnixIn applies the In predicate on the "create_unix" field.
func CreateUnixIn(vs ...time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldCreateUnix, vs...))
}

// CreateUnixNotIn applies the NotIn predicate on the "create_unix" field.
func CreateUnixNotIn(vs ...time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldCreateUnix, vs...))
}

// CreateUnixGT applies the GT predicate on the "create_unix" field.
func CreateUnixGT(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldCreateUnix, v))
}

// CreateUnixGTE applies the GTE predicate on the "create_unix" field.
func CreateUnixGTE(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldCreateUnix, v))
}

// CreateUnixLT applies the LT predicate on the "create_unix" field.
func CreateUnixLT(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldCreateUnix, v))
}

// CreateUnixLTE applies the LTE predicate on the "create_unix" field.
func CreateUnixLTE(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldCreateUnix, v))
}

// UpdateUnixEQ applies the EQ predicate on the "update_unix" field.
func UpdateUnixEQ(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldEQ(FieldUpdateUnix, v))
}

// UpdateUnixNEQ applies the NEQ predicate on the "update_unix" field.
func UpdateUnixNEQ(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldNEQ(FieldUpdateUnix, v))
}

// UpdateUnixIn applies the In predicate on the "update_unix" field.
func UpdateUnixIn(vs ...time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldIn(FieldUpdateUnix, vs...))
}

// UpdateUnixNotIn applies the NotIn predicate on the "update_unix" field.
func UpdateUnixNotIn(vs ...time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldNotIn(FieldUpdateUnix, vs...))
}

// UpdateUnixGT applies the GT predicate on the "update_unix" field.
func UpdateUnixGT(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldGT(FieldUpdateUnix, v))
}

// UpdateUnixGTE applies the GTE predicate on the "update_unix" field.
func UpdateUnixGTE(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldGTE(FieldUpdateUnix, v))
}

// UpdateUnixLT applies the LT predicate on the "update_unix" field.
func UpdateUnixLT(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldLT(FieldUpdateUnix, v))
}

// UpdateUnixLTE applies the LTE predicate on the "update_unix" field.
func UpdateUnixLTE(v time.Time) predicate.Modbus {
	return predicate.Modbus(sql.FieldLTE(FieldUpdateUnix, v))
}

// HasDevice applies the HasEdge predicate on the "device" edge.
func HasDevice() predicate.Modbus {
	return predicate.Modbus(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, DeviceTable, DeviceColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDeviceWith applies the HasEdge predicate on the "device" edge with a given conditions (other predicates).
func HasDeviceWith(preds ...predicate.Device) predicate.Modbus {
	return predicate.Modbus(func(s *sql.Selector) {
		step := newDeviceStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDataPoints applies the HasEdge predicate on the "data_points" edge.
func HasDataPoints() predicate.Modbus {
	return predicate.Modbus(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, DataPointsTable, DataPointsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDataPointsWith applies the HasEdge predicate on the "data_points" edge with a given conditions (other predicates).
func HasDataPointsWith(preds ...predicate.Data) predicate.Modbus {
	return predicate.Modbus(func(s *sql.Selector) {
		step := newDataPointsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Modbus) predicate.Modbus {
	return predicate.Modbus(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Modbus) predicate.Modbus {
	return predicate.Modbus(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Modbus) predicate.Modbus {
	return predicate.Modbus(sql.NotPredicates(p))
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ModbusCreate is the builder for creating a Modbus entity.
type ModbusCreate struct {
	config
	mutation *ModbusMutation
	hooks    []Hook
}

// SetDeviceID sets the "device_id" field.
func (mc *ModbusCreate) SetDeviceID(s string) *ModbusCreate {
	mc.mutation.SetDeviceID(s)
	return mc
}

// SetTid sets the "tid" field.
func (mc *ModbusCreate) SetTid(s string) *ModbusCreate {
	mc.mutation.SetTid(s)
	return mc
}

// SetNillableTid sets the "tid" field if the given value is not nil.
func (mc *ModbusCreate) SetNillableTid(s *string) *ModbusCreate {
	if s != nil {
		mc.SetTid(*s)
	}
	return mc
}

// SetPid sets the "pid" field.
func (mc *ModbusCreate) SetPid(s string) *ModbusCreate {
	mc.mutation.SetPid(s)
	return mc
}

// SetNillablePid sets the "pid" field if the given value is not nil.
func (mc *ModbusCreate) SetNillablePid(s *string) *ModbusCreate {
	if s != nil {
		mc.SetPid(*s)
	}
	return mc
}

// SetLen sets the "len" field.
func (mc *ModbusCreate) SetLen(s string) *ModbusCreate {
	mc.mutation.SetLen(s)
	return mc
}

// SetNillableLen sets the "len" field if the given value is not nil.
func (mc *ModbusCreate) SetNillableLen(s *string) *ModbusCreate {
	if s != nil {
		mc.SetLen(*s)
	}
	return mc
}

// SetUID sets the "uid" field.
func (mc *ModbusCreate) SetUID(s string) *ModbusCreate {
	mc.mutation.SetUID(s)
	return mc
}

// SetNillableUID sets the "uid" field if the given value is not nil.
func (mc *ModbusCreate) SetNillableUID(s *string) *ModbusCreate {
	if s != nil {
		mc.SetUID(*s)
	}
	return mc
}

// SetFc sets the "fc" field.
func (mc *ModbusCreate) SetFc(s string) *ModbusCreate {
	mc.mutation.SetFc(s)
	return mc
}

// SetNillableFc sets the "fc" field if the given value is not nil.
func (mc *ModbusCreate) SetNillableFc(s *string) *ModbusCreate {
	if s != nil {
		mc.SetFc(*s)
	}
	return mc
}

// SetCreateUnix sets the "create_unix" field.
func (mc *ModbusCreate) SetCreateUnix(t time.Time) *ModbusCreate {
	mc.mutation.SetCreateUnix(t)
	return mc
}

// SetNillableCreateUnix sets the "create_unix" field if the given value is not nil.
func (mc *ModbusCreate) SetNillableCreateUnix(t *time.Time) *ModbusCreate {
	if t != nil {
		mc.SetCreateUnix(*t)
	}
	return mc
}

// SetUpdateUnix sets the "update_unix" field.
func (mc *ModbusCreate) SetUpdateUnix(t time.Time) *ModbusCreate {
	mc.mutation.SetUpdateUnix(t)
	return mc
}

// SetNillableUpdateUnix sets the "update_unix" field if the given value is not nil.
func (mc *ModbusCreate) SetNillableUpdateUnix(t *time.Time) *ModbusCreate {
	if t != nil {
		mc.SetUpdateUnix(*t)
	}
	return mc
}

// SetID sets the "id" field.
func (mc *ModbusCreate) SetID(s string) *ModbusCreate {
	mc.mutation.SetID(s)
	return mc
}

// SetDevice sets the "device" edge to the Device entity.
func (mc *ModbusCreate) SetDevice(d *Device) *ModbusCreate {
	return mc.SetDeviceID(d.ID)
}

// AddDataPointIDs adds the "data_points" edge to the Data entity by IDs.
func (mc *ModbusCreate) AddDataPointIDs(ids ...int) *ModbusCreate {
	mc.mutation.AddDataPointIDs(ids...)
	return mc
}

// AddDataPoints adds the "data_points" edges to the Data entity.
func (mc *ModbusCreate) AddDataPoints(d ...*Data) *ModbusCreate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return mc.AddDataPointIDs(ids...)
}

// Mutation returns the ModbusMutation object of the builder.
func (mc *ModbusCreate) Mutation() *ModbusMutation {
	return mc.mutation
}

// Save creates the Modbus in the database.
func (mc *ModbusCreate) Save(ctx context.Context) (*Modbus, error) {
	mc.defaults()
	return withHooks(ctx, mc.sqlSave, mc.mutation, mc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (mc *ModbusCreate) SaveX(ctx context.Context) *Modbus {
	v, err := mc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mc *ModbusCreate) Exec(ctx context.Context) error {
	_, err := mc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mc *ModbusCreate) ExecX(ctx context.Context) {
	if err := mc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mc *ModbusCreate) defaults() {
	if _, ok := mc.mutation.Tid(); !ok {
		v := modbus.DefaultTid
		mc.mutation.SetTid(v)
	}
	if _, ok := mc.mutation.Pid(); !ok {
		v := modbus.DefaultPid
		mc.mutation.SetPid(v)
	}
	if _, ok := mc.mutation.Len(); !ok {
		v := modbus.DefaultLen
		mc.mutation.SetLen(v)
	}
	if _, ok := mc.mutation.UID(); !ok {
		v := modbus.DefaultUID
		mc.mutation.SetUID(v)
	}
	if _, ok := mc.mutation.Fc(); !ok {
		v := modbus.DefaultFc
		mc.mutation.SetFc(v)
	}
	if _, ok := mc.mutation.CreateUnix(); !ok {
		v := modbus.DefaultCreateUnix()
		mc.mutation.SetCreateUnix(v)
	}
	if _, ok := mc.mutation.UpdateUnix(); !ok {
		v := modbus.DefaultUpdateUnix()
		mc.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mc *ModbusCreate) check() error {
	if _, ok := mc.mutation.DeviceID(); !ok {
		return &ValidationError{Name: "device_id", err: errors.New(`ent: missing required field "Modbus.device_id"`)}
	}
	if v, ok := mc.mutation.DeviceID(); ok {
		if err := modbus.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Modbus.device_id": %w`, err)}
		}
	}
	if _, ok := mc.mutation.Tid(); !ok {
		return &ValidationError{Name: "tid", err: errors.New(`ent: missing required field "Modbus.tid"`)}
	}
	if _, ok := mc.mutation.Pid(); !ok {
		return &ValidationError{Name: "pid", err: errors.New(`ent: missing required field "Modbus.pid"`)}
	}
	if _, ok := mc.mutation.Len(); !ok {
		return &ValidationError{Name: "len", err: errors.New(`ent: missing required field "Modbus.len"`)}
	}
	if _, ok := mc.mutation.UID(); !ok {
		return &ValidationError{Name: "uid", err: errors.New(`ent: missing required field "Modbus.uid"`)}
	}
	if _, ok := mc.mutation.Fc(); !ok {
		return &ValidationError{Name: "fc", err: errors.New(`ent: missing required field "Modbus.fc"`)}
	}
	if _, ok := mc.mutation.CreateUnix(); !ok {
		return &ValidationError{Name: "create_unix", err: errors.New(`ent: missing required field "Modbus.create_unix"`)}
	}
	if _, ok := mc.mutation.UpdateUnix(); !ok {
		return &ValidationError{Name: "update_unix", err: errors.New(`ent: missing required field "Modbus.update_unix"`)}
	}
	if v, ok := mc.mutation.ID(); ok {
		if err := modbus.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Modbus.id": %w`, err)}
		}
	}
	if len(mc.mutation.DeviceIDs()) == 0 {
		return &ValidationError{Name: "device", err: errors.New(`ent: missing required edge "Modbus.device"`)}
	}
	return nil
}

func (mc *ModbusCreate) sqlSave(ctx context.Context) (*Modbus, error) {
	if err := mc.check(); err != nil {
		return nil, err
	}
	_node, _spec := mc.createSpec()
	if err := sqlgraph.CreateNode(ctx, mc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Modbus.ID type: %T", _spec.ID.Value)
		}
	}
	mc.mutation.id = &_node.ID
	mc.mutation.done = true
	return _node, nil
}

func (mc *ModbusCreate) createSpec() (*Modbus, *sqlgraph.CreateSpec) {
	var (
		_node = &Modbus{config: mc.config}
		_spec = sqlgraph.NewCreateSpec(modbus.Table, sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString))
	)
	if id, ok := mc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := mc.mutation.Tid(); ok {
		_spec.SetField(modbus.FieldTid, field.TypeString, value)
		_node.Tid = value
	}
	if value, ok := mc.mutation.Pid(); ok {
		_spec.SetField(modbus.FieldPid, field.TypeString, value)
		_node.Pid = value
	}
	if value, ok := mc.mutation.Len(); ok {
		_spec.SetField(modbus.FieldLen, field.TypeString, value)
		_node.Len = value
	}
	if value, ok := mc.mutation.UID(); ok {
		_spec.SetField(modbus.FieldUID, field.TypeString, value)
		_node.UID = value
	}
	if value, ok := mc.mutation.Fc(); ok {
		_spec.SetField(modbus.FieldFc, field.TypeString, value)
		_node.Fc = value
	}
	if value, ok := mc.mutation.CreateUnix(); ok {
		_spec.SetField(modbus.FieldCreateUnix, field.TypeTime, value)
		_node.CreateUnix = value
	}
	if value, ok := mc.mutation.UpdateUnix(); ok {
		_spec.SetField(modbus.FieldUpdateUnix, field.TypeTime, value)
		_node.UpdateUnix = value
	}
	if nodes := mc.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   modbus.DeviceTable,
			Columns: []string{modbus.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.DeviceID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := mc.mutation.DataPointsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   modbus.DataPointsTable,
			Columns: []string{modbus.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// ModbusCreateBulk is the builder for creating many Modbus entities in bulk.
type ModbusCreateBulk struct {
	config
	err      error
	builders []*ModbusCreate
}

// Save creates the Modbus entities in the database.
func (mcb *ModbusCreateBulk) Save(ctx context.Context) ([]*Modbus, error) {
	if mcb.err != nil {
		return nil, mcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(mcb.builders))
	nodes := make([]*Modbus, len(mcb.builders))
	mutators := make([]Mutator, len(mcb.builders))
	for i := range mcb.builders {
		func(i int, root context.Context) {
			builder := mcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ModbusMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, mcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, mcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, mcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (mcb *ModbusCreateBulk) SaveX(ctx context.Context) []*Modbus {
	v, err := mcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mcb *ModbusCreateBulk) Exec(ctx context.Context) error {
	_, err := mcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mcb *ModbusCreateBulk) ExecX(ctx context.Context) {
	if err := mcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/predicate"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ModbusUpdate is the builder for updating Modbus entities.
type ModbusUpdate struct {
	config
	hooks    []Hook
	mutation *ModbusMutation
}

// Where appends a list predicates to the ModbusUpdate builder.
func (mu *ModbusUpdate) Where(ps ...predicate.Modbus) *ModbusUpdate {
	mu.mutation.Where(ps...)
	return mu
}

// SetDeviceID sets the "device_id" field.
func (mu *ModbusUpdate) SetDeviceID(s string) *ModbusUpdate {
	mu.mutation.SetDeviceID(s)
	return mu
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (mu *ModbusUpdate) SetNillableDeviceID(s *string) *ModbusUpdate {
	if s != nil {
		mu.SetDeviceID(*s)
	}
	return mu
}

// SetTid sets the "tid" field.
func (mu *ModbusUpdate) SetTid(s string) *ModbusUpdate {
	mu.mutation.SetTid(s)
	return mu
}

// SetNillableTid sets the "tid" field if the given value is not nil.
func (mu *ModbusUpdate) SetNillableTid(s *string) *ModbusUpdate {
	if s != nil {
		mu.SetTid(*s)
	}
	return mu
}

// SetPid sets the "pid" field.
func (mu *ModbusUpdate) SetPid(s string) *ModbusUpdate {
	mu.mutation.SetPid(s)
	return mu
}

// SetNillablePid sets the "pid" field if the given value is not nil.
func (mu *ModbusUpdate) SetNillablePid(s *string) *ModbusUpdate {
	if s != nil {
		mu.SetPid(*s)
	}
	return mu
}

// SetLen sets the "len" field.
func (mu *ModbusUpdate) SetLen(s string) *ModbusUpdate {
	mu.mutation.SetLen(s)
	return mu
}

// SetNillableLen sets the "len" field if the given value is not nil.
func (mu *ModbusUpdate) SetNillableLen(s *string) *ModbusUpdate {
	if s != nil {
		mu.SetLen(*s)
	}
	return mu
}

// SetUID sets the "uid" field.
func (mu *ModbusUpdate) SetUID(s string) *ModbusUpdate {
	mu.mutation.SetUID(s)
	return mu
}

// SetNillableUID sets the "uid" field if the given value is not nil.
func (mu *ModbusUpdate) SetNillableUID(s *string) *ModbusUpdate {
	if s != nil {
		mu.SetUID(*s)
	}
	return mu
}

// SetFc sets the "fc" field.
func (mu *ModbusUpdate) SetFc(s string) *ModbusUpdate {
	mu.mutation.SetFc(s)
	return mu
}

// SetNillableFc sets the "fc" field if the given value is not nil.
func (mu *ModbusUpdate) SetNillableFc(s *string) *ModbusUpdate {
	if s != nil {
		mu.SetFc(*s)
	}
	return mu
}

// SetUpdateUnix sets the "update_unix" field.
func (mu *ModbusUpdate) SetUpdateUnix(t time.Time) *ModbusUpdate {
	mu.mutation.SetUpdateUnix(t)
	return mu
}

// SetDevice sets the "device" edge to the Device entity.
func (mu *ModbusUpdate) SetDevice(d *Device) *ModbusUpdate {
	return mu.SetDeviceID(d.ID)
}

// AddDataPointIDs adds the "data_points" edge to the Data entity by IDs.
func (mu *ModbusUpdate) AddDataPointIDs(ids ...int) *ModbusUpdate {
	mu.mutation.AddDataPointIDs(ids...)
	return mu
}

// AddDataPoints adds the "data_points" edges to the Data entity.
func (mu *ModbusUpdate) AddDataPoints(d ...*Data) *ModbusUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return mu.AddDataPointIDs(ids...)
}

// Mutation returns the ModbusMutation object of the builder.
func (mu *ModbusUpdate) Mutation() *ModbusMutation {
	return mu.mutation
}

// ClearDevice clears the "device" edge to the Device entity.
func (mu *ModbusUpdate) ClearDevice() *ModbusUpdate {
	mu.mutation.ClearDevice()
	return mu
}

// ClearDataPoints clears all "data_points" edges to the Data entity.
func (mu *ModbusUpdate) ClearDataPoints() *ModbusUpdate {
	mu.mutation.ClearDataPoints()
	return mu
}

// RemoveDataPointIDs removes the "data_points" edge to Data entities by IDs.
func (mu *ModbusUpdate) RemoveDataPointIDs(ids ...int) *ModbusUpdate {
	mu.mutation.RemoveDataPointIDs(ids...)
	return mu
}

// RemoveDataPoints removes "data_points" edges to Data entities.
func (mu *ModbusUpdate) RemoveDataPoints(d ...*Data) *ModbusUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return mu.RemoveDataPointIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (mu *ModbusUpdate) Save(ctx context.Context) (int, error) {
	mu.defaults()
	return withHooks(ctx, mu.sqlSave, mu.mutation, mu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (mu *ModbusUpdate) SaveX(ctx context.Context) int {
	affected, err := mu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (mu *ModbusUpdate) Exec(ctx context.Context) error {
	_, err := mu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mu *ModbusUpdate) ExecX(ctx context.Context) {
	if err := mu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mu *ModbusUpdate) defaults() {
	if _, ok := mu.mutation.UpdateUnix(); !ok {
		v := modbus.UpdateDefaultUpdateUnix()
		mu.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mu *ModbusUpdate) check() error {
	if v, ok := mu.mutation.DeviceID(); ok {
		if err := modbus.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Modbus.device_id": %w`, err)}
		}
	}
	if mu.mutation.DeviceCleared() && len(mu.mutation.DeviceIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Modbus.device"`)
	}
	return nil
}

func (mu *ModbusUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := mu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(modbus.Table, modbus.Columns, sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString))
	if ps := mu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := mu.mutation.Tid(); ok {
		_spec.SetField(modbus.FieldTid, field.TypeString, value)
	}
	if value, ok := mu.mutation.Pid(); ok {
		_spec.SetField(modbus.FieldPid, field.TypeString, value)
	}
	if value, ok := mu.mutation.Len(); ok {
		_spec.SetField(modbus.FieldLen, field.TypeString, value)
	}
	if value, ok := mu.mutation.UID(); ok {
		_spec.SetField(modbus.FieldUID, field.TypeString, value)
	}
	if value, ok := mu.mutation.Fc(); ok {
		_spec.SetField(modbus.FieldFc, field.TypeString, value)
	}
	if value, ok := mu.mutation.UpdateUnix(); ok {
		_spec.SetField(modbus.FieldUpdateUnix, field.TypeTime, value)
	}
	if mu.mutation.DeviceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   modbus.DeviceTable,
			Columns: []string{modbus.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   modbus.DeviceTable,
			Columns: []string{modbus.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mu.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   modbus.DataPointsTable,
			Columns: []string{modbus.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.RemovedDataPointsIDs(); len(nodes) > 0 && !mu.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   modbus.DataPointsTable,
			Columns: []string{modbus.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.DataPointsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   modbus.DataPointsTable,
			Columns: []string{modbus.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, mu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{modbus.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	mu.mutation.done = true
	return n, nil
}

// ModbusUpdateOne is the builder for updating a single Modbus entity.
type ModbusUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ModbusMutation
}

// SetDeviceID sets the "device_id" field.
func (muo *ModbusUpdateOne) SetDeviceID(s string) *ModbusUpdateOne {
	muo.mutation.SetDeviceID(s)
	return muo
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (muo *ModbusUpdateOne) SetNillableDeviceID(s *string) *ModbusUpdateOne {
	if s != nil {
		muo.SetDeviceID(*s)
	}
	return muo
}

// SetTid sets the "tid" field.
func (muo *ModbusUpdateOne) SetTid(s string) *ModbusUpdateOne {
	muo.mutation.SetTid(s)
	return muo
}

// SetNillableTid sets the "tid" field if the given value is not nil.
func (muo *ModbusUpdateOne) SetNillableTid(s *string) *ModbusUpdateOne {
	if s != nil {
		muo.SetTid(*s)
	}
	return muo
}

// SetPid sets the "pid" field.
func (muo *ModbusUpdateOne) SetPid(s string) *ModbusUpdateOne {
	muo.mutation.SetPid(s)
	return muo
}

// SetNillablePid sets the "pid" field if the given value is not nil.
func (muo *ModbusUpdateOne) SetNillablePid(s *string) *ModbusUpdateOne {
	if s != nil {
		muo.SetPid(*s)
	}
	return muo
}

// SetLen sets the "len" field.
func (muo *ModbusUpdateOne) SetLen(s string) *ModbusUpdateOne {
	muo.mutation.SetLen(s)
	return muo
}

// SetNillableLen sets the "len" field if the given value is not nil.
func (muo *ModbusUpdateOne) SetNillableLen(s *string) *ModbusUpdateOne {
	if s != nil {
		muo.SetLen(*s)
	}
	return muo
}

// SetUID sets the "uid" field.
func (muo *ModbusUpdateOne) SetUID(s string) *ModbusUpdateOne {
	muo.mutation.SetUID(s)
	return muo
}

// SetNillableUID sets the "uid" field if the given value is not nil.
func (muo *ModbusUpdateOne) SetNillableUID(s *string) *ModbusUpdateOne {
	if s != nil {
		muo.SetUID(*s)
	}
	return muo
}

// SetFc sets the "fc" field.
func (muo *ModbusUpdateOne) SetFc(s string) *ModbusUpdateOne {
	muo.mutation.SetFc(s)
	return muo
}

// SetNillableFc sets the "fc" field if the given value is not nil.
func (muo *ModbusUpdateOne) SetNillableFc(s *string) *ModbusUpdateOne {
	if s != nil {
		muo.SetFc(*s)
	}
	return muo
}

// SetUpdateUnix sets the "update_unix" field.
func (muo *ModbusUpdateOne) SetUpdateUnix(t time.Time) *ModbusUpdateOne {
	muo.mutation.SetUpdateUnix(t)
	return muo
}

// SetDevice sets the "device" edge to the Device entity.
func (muo *ModbusUpdateOne) SetDevice(d *Device) *ModbusUpdateOne {
	return muo.SetDeviceID(d.ID)
}

// AddDataPointIDs adds the "data_points" edge to the Data entity by IDs.
func (muo *ModbusUpdateOne) AddDataPointIDs(ids ...int) *ModbusUpdateOne {
	muo.mutation.AddDataPointIDs(ids...)
	return muo
}

// AddDataPoints adds the "data_points" edges to the Data entity.
func (muo *ModbusUpdateOne) AddDataPoints(d ...*Data) *ModbusUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return muo.AddDataPointIDs(ids...)
}

// Mutation returns the ModbusMutation object of the builder.
func (muo *ModbusUpdateOne) Mutation() *ModbusMutation {
	return muo.mutation
}

// ClearDevice clears the "device" edge to the Device entity.
func (muo *ModbusUpdateOne) ClearDevice() *ModbusUpdateOne {
	muo.mutation.ClearDevice()
	return muo
}

// ClearDataPoints clears all "data_points" edges to the Data entity.
func (muo *ModbusUpdateOne) ClearDataPoints() *ModbusUpdateOne {
	muo.mutation.ClearDataPoints()
	return muo
}

// RemoveDataPointIDs removes the "data_points" edge to Data entities by IDs.
func (muo *ModbusUpdateOne) RemoveDataPointIDs(ids ...int) *ModbusUpdateOne {
	muo.mutation.RemoveDataPointIDs(ids...)
	return muo
}

// RemoveDataPoints removes "data_points" edges to Data entities.
func (muo *ModbusUpdateOne) RemoveDataPoints(d ...*Data) *ModbusUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return muo.RemoveDataPointIDs(ids...)
}

// Where appends a list predicates to the ModbusUpdate builder.
func (muo *ModbusUpdateOne) Where(ps ...predicate.Modbus) *ModbusUpdateOne {
	muo.mutation.Where(ps...)
	return muo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (muo *ModbusUpdateOne) Select(field string, fields ...string) *ModbusUpdateOne {
	muo.fields = append([]string{field}, fields...)
	return muo
}

// Save executes the query and returns the updated Modbus entity.
func (muo *ModbusUpdateOne) Save(ctx context.Context) (*Modbus, error) {
	muo.defaults()
	return withHooks(ctx, muo.sqlSave, muo.mutation, muo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (muo *ModbusUpdateOne) SaveX(ctx context.Context) *Modbus {
	node, err := muo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (muo *ModbusUpdateOne) Exec(ctx context.Context) error {
	_, err := muo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (muo *ModbusUpdateOne) ExecX(ctx context.Context) {
	if err := muo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (muo *ModbusUpdateOne) defaults() {
	if _, ok := muo.mutation.UpdateUnix(); !ok {
		v := modbus.UpdateDefaultUpdateUnix()
		muo.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (muo *ModbusUpdateOne) check() error {
	if v, ok := muo.mutation.DeviceID(); ok {
		if err := modbus.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Modbus.device_id": %w`, err)}
		}
	}
	if muo.mutation.DeviceCleared() && len(muo.mutation.DeviceIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Modbus.device"`)
	}
	return nil
}

func (muo *ModbusUpdateOne) sqlSave(ctx context.Context) (_node *Modbus, err error) {
	if err := muo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(modbus.Table, modbus.Columns, sqlgraph.NewFieldSpec(modbus.FieldID, field.TypeString))
	id, ok := muo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Modbus.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := muo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, modbus.FieldID)
		for _, f := range fields {
			if !modbus.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != modbus.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := muo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := muo.mutation.Tid(); ok {
		_spec.SetField(modbus.FieldTid, field.TypeString, value)
	}
	if value, ok := muo.mutation.Pid(); ok {
		_spec.SetField(modbus.FieldPid, field.TypeString, value)
	}
	if value, ok := muo.mutation.Len(); ok {
		_spec.SetField(modbus.FieldLen, field.TypeString, value)
	}
	if value, ok := muo.mutation.UID(); ok {
		_spec.SetField(modbus.FieldUID, field.TypeString, value)
	}
	if value, ok := muo.mutation.Fc(); ok {
		_spec.SetField(modbus.FieldFc, field.TypeString, value)
	}
	if value, ok := muo.mutation.UpdateUnix(); ok {
		_spec.SetField(modbus.FieldUpdateUnix, field.TypeTime, value)
	}
	if muo.mutation.DeviceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   modbus.DeviceTable,
			Columns: []string{modbus.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   modbus.DeviceTable,
			Columns: []string{modbus.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if muo.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   modbus.DataPointsTable,
			Columns: []string{modbus.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.RemovedDataPointsIDs(); len(nodes) > 0 && !muo.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   modbus.DataPointsTable,
			Columns: []string{modbus.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.DataPointsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   modbus.DataPointsTable,
			Columns: []string{modbus.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Modbus{config: muo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, muo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{modbus.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	muo.mutation.done = true
	return _node, nil
}

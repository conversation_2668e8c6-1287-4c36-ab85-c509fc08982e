// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/schema"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	dataFields := schema.Data{}.Fields()
	_ = dataFields
	// dataDescFrameID is the schema descriptor for frame_id field.
	dataDescFrameID := dataFields[0].Descriptor()
	// data.FrameIDValidator is a validator for the "frame_id" field. It is called by the builders before save.
	data.FrameIDValidator = dataDescFrameID.Validators[0].(func(string) error)
	// dataDescIndex is the schema descriptor for index field.
	dataDescIndex := dataFields[1].Descriptor()
	// data.IndexValidator is a validator for the "index" field. It is called by the builders before save.
	data.IndexValidator = dataDescIndex.Validators[0].(func(string) error)
	// dataDescName is the schema descriptor for name field.
	dataDescName := dataFields[2].Descriptor()
	// data.NameValidator is a validator for the "name" field. It is called by the builders before save.
	data.NameValidator = dataDescName.Validators[0].(func(string) error)
	// dataDescType is the schema descriptor for type field.
	dataDescType := dataFields[3].Descriptor()
	// data.DefaultType holds the default value on creation for the type field.
	data.DefaultType = dataDescType.Default.(string)
	// dataDescValue is the schema descriptor for value field.
	dataDescValue := dataFields[4].Descriptor()
	// data.DefaultValue holds the default value on creation for the value field.
	data.DefaultValue = dataDescValue.Default.(string)
	// dataDescUpdateUnix is the schema descriptor for update_unix field.
	dataDescUpdateUnix := dataFields[5].Descriptor()
	// data.DefaultUpdateUnix holds the default value on creation for the update_unix field.
	data.DefaultUpdateUnix = dataDescUpdateUnix.Default.(func() time.Time)
	// data.UpdateDefaultUpdateUnix holds the default value on update for the update_unix field.
	data.UpdateDefaultUpdateUnix = dataDescUpdateUnix.UpdateDefault.(func() time.Time)
	deviceFields := schema.Device{}.Fields()
	_ = deviceFields
	// deviceDescName is the schema descriptor for name field.
	deviceDescName := deviceFields[1].Descriptor()
	// device.DefaultName holds the default value on creation for the name field.
	device.DefaultName = deviceDescName.Default.(string)
	// deviceDescIP is the schema descriptor for ip field.
	deviceDescIP := deviceFields[2].Descriptor()
	// device.IPValidator is a validator for the "ip" field. It is called by the builders before save.
	device.IPValidator = deviceDescIP.Validators[0].(func(string) error)
	// deviceDescType is the schema descriptor for type field.
	deviceDescType := deviceFields[3].Descriptor()
	// device.DefaultType holds the default value on creation for the type field.
	device.DefaultType = deviceDescType.Default.(string)
	// deviceDescOs is the schema descriptor for os field.
	deviceDescOs := deviceFields[4].Descriptor()
	// device.DefaultOs holds the default value on creation for the os field.
	device.DefaultOs = deviceDescOs.Default.(string)
	// deviceDescCPU is the schema descriptor for cpu field.
	deviceDescCPU := deviceFields[5].Descriptor()
	// device.DefaultCPU holds the default value on creation for the cpu field.
	device.DefaultCPU = deviceDescCPU.Default.(string)
	// deviceDescGpu is the schema descriptor for gpu field.
	deviceDescGpu := deviceFields[6].Descriptor()
	// device.DefaultGpu holds the default value on creation for the gpu field.
	device.DefaultGpu = deviceDescGpu.Default.(string)
	// deviceDescMemory is the schema descriptor for memory field.
	deviceDescMemory := deviceFields[7].Descriptor()
	// device.DefaultMemory holds the default value on creation for the memory field.
	device.DefaultMemory = deviceDescMemory.Default.(string)
	// deviceDescDisk is the schema descriptor for disk field.
	deviceDescDisk := deviceFields[8].Descriptor()
	// device.DefaultDisk holds the default value on creation for the disk field.
	device.DefaultDisk = deviceDescDisk.Default.(string)
	// deviceDescProtocol is the schema descriptor for protocol field.
	deviceDescProtocol := deviceFields[9].Descriptor()
	// device.DefaultProtocol holds the default value on creation for the protocol field.
	device.DefaultProtocol = deviceDescProtocol.Default.(string)
	// deviceDescStatus is the schema descriptor for status field.
	deviceDescStatus := deviceFields[10].Descriptor()
	// device.DefaultStatus holds the default value on creation for the status field.
	device.DefaultStatus = deviceDescStatus.Default.(string)
	// deviceDescHealthtimestamp is the schema descriptor for healthtimestamp field.
	deviceDescHealthtimestamp := deviceFields[11].Descriptor()
	// device.DefaultHealthtimestamp holds the default value on creation for the healthtimestamp field.
	device.DefaultHealthtimestamp = deviceDescHealthtimestamp.Default.(time.Time)
	// deviceDescWorkmode is the schema descriptor for workmode field.
	deviceDescWorkmode := deviceFields[12].Descriptor()
	// device.DefaultWorkmode holds the default value on creation for the workmode field.
	device.DefaultWorkmode = deviceDescWorkmode.Default.(string)
	// deviceDescCreateUnix is the schema descriptor for create_unix field.
	deviceDescCreateUnix := deviceFields[13].Descriptor()
	// device.DefaultCreateUnix holds the default value on creation for the create_unix field.
	device.DefaultCreateUnix = deviceDescCreateUnix.Default.(func() time.Time)
	// deviceDescUpdateUnix is the schema descriptor for update_unix field.
	deviceDescUpdateUnix := deviceFields[14].Descriptor()
	// device.DefaultUpdateUnix holds the default value on creation for the update_unix field.
	device.DefaultUpdateUnix = deviceDescUpdateUnix.Default.(func() time.Time)
	// device.UpdateDefaultUpdateUnix holds the default value on update for the update_unix field.
	device.UpdateDefaultUpdateUnix = deviceDescUpdateUnix.UpdateDefault.(func() time.Time)
	// deviceDescID is the schema descriptor for id field.
	deviceDescID := deviceFields[0].Descriptor()
	// device.IDValidator is a validator for the "id" field. It is called by the builders before save.
	device.IDValidator = deviceDescID.Validators[0].(func(string) error)
	modbusFields := schema.Modbus{}.Fields()
	_ = modbusFields
	// modbusDescDeviceID is the schema descriptor for device_id field.
	modbusDescDeviceID := modbusFields[0].Descriptor()
	// modbus.DeviceIDValidator is a validator for the "device_id" field. It is called by the builders before save.
	modbus.DeviceIDValidator = modbusDescDeviceID.Validators[0].(func(string) error)
	// modbusDescTid is the schema descriptor for tid field.
	modbusDescTid := modbusFields[2].Descriptor()
	// modbus.DefaultTid holds the default value on creation for the tid field.
	modbus.DefaultTid = modbusDescTid.Default.(string)
	// modbusDescPid is the schema descriptor for pid field.
	modbusDescPid := modbusFields[3].Descriptor()
	// modbus.DefaultPid holds the default value on creation for the pid field.
	modbus.DefaultPid = modbusDescPid.Default.(string)
	// modbusDescLen is the schema descriptor for len field.
	modbusDescLen := modbusFields[4].Descriptor()
	// modbus.DefaultLen holds the default value on creation for the len field.
	modbus.DefaultLen = modbusDescLen.Default.(string)
	// modbusDescUID is the schema descriptor for uid field.
	modbusDescUID := modbusFields[5].Descriptor()
	// modbus.DefaultUID holds the default value on creation for the uid field.
	modbus.DefaultUID = modbusDescUID.Default.(string)
	// modbusDescFc is the schema descriptor for fc field.
	modbusDescFc := modbusFields[6].Descriptor()
	// modbus.DefaultFc holds the default value on creation for the fc field.
	modbus.DefaultFc = modbusDescFc.Default.(string)
	// modbusDescCreateUnix is the schema descriptor for create_unix field.
	modbusDescCreateUnix := modbusFields[7].Descriptor()
	// modbus.DefaultCreateUnix holds the default value on creation for the create_unix field.
	modbus.DefaultCreateUnix = modbusDescCreateUnix.Default.(func() time.Time)
	// modbusDescUpdateUnix is the schema descriptor for update_unix field.
	modbusDescUpdateUnix := modbusFields[8].Descriptor()
	// modbus.DefaultUpdateUnix holds the default value on creation for the update_unix field.
	modbus.DefaultUpdateUnix = modbusDescUpdateUnix.Default.(func() time.Time)
	// modbus.UpdateDefaultUpdateUnix holds the default value on update for the update_unix field.
	modbus.UpdateDefaultUpdateUnix = modbusDescUpdateUnix.UpdateDefault.(func() time.Time)
	// modbusDescID is the schema descriptor for id field.
	modbusDescID := modbusFields[1].Descriptor()
	// modbus.IDValidator is a validator for the "id" field. It is called by the builders before save.
	modbus.IDValidator = modbusDescID.Validators[0].(func(string) error)
	uartFields := schema.Uart{}.Fields()
	_ = uartFields
	// uartDescDeviceID is the schema descriptor for device_id field.
	uartDescDeviceID := uartFields[0].Descriptor()
	// uart.DeviceIDValidator is a validator for the "device_id" field. It is called by the builders before save.
	uart.DeviceIDValidator = uartDescDeviceID.Validators[0].(func(string) error)
	// uartDescHeader is the schema descriptor for header field.
	uartDescHeader := uartFields[2].Descriptor()
	// uart.DefaultHeader holds the default value on creation for the header field.
	uart.DefaultHeader = uartDescHeader.Default.(string)
	// uartDescAddr is the schema descriptor for addr field.
	uartDescAddr := uartFields[3].Descriptor()
	// uart.DefaultAddr holds the default value on creation for the addr field.
	uart.DefaultAddr = uartDescAddr.Default.(string)
	// uartDescCmd is the schema descriptor for cmd field.
	uartDescCmd := uartFields[4].Descriptor()
	// uart.DefaultCmd holds the default value on creation for the cmd field.
	uart.DefaultCmd = uartDescCmd.Default.(string)
	// uartDescTail is the schema descriptor for tail field.
	uartDescTail := uartFields[5].Descriptor()
	// uart.DefaultTail holds the default value on creation for the tail field.
	uart.DefaultTail = uartDescTail.Default.(string)
	// uartDescCreateUnix is the schema descriptor for create_unix field.
	uartDescCreateUnix := uartFields[6].Descriptor()
	// uart.DefaultCreateUnix holds the default value on creation for the create_unix field.
	uart.DefaultCreateUnix = uartDescCreateUnix.Default.(func() time.Time)
	// uartDescUpdateUnix is the schema descriptor for update_unix field.
	uartDescUpdateUnix := uartFields[7].Descriptor()
	// uart.DefaultUpdateUnix holds the default value on creation for the update_unix field.
	uart.DefaultUpdateUnix = uartDescUpdateUnix.Default.(func() time.Time)
	// uart.UpdateDefaultUpdateUnix holds the default value on update for the update_unix field.
	uart.UpdateDefaultUpdateUnix = uartDescUpdateUnix.UpdateDefault.(func() time.Time)
	// uartDescID is the schema descriptor for id field.
	uartDescID := uartFields[1].Descriptor()
	// uart.IDValidator is a validator for the "id" field. It is called by the builders before save.
	uart.IDValidator = uartDescID.Validators[0].(func(string) error)
	udpFields := schema.Udp{}.Fields()
	_ = udpFields
	// udpDescDeviceID is the schema descriptor for device_id field.
	udpDescDeviceID := udpFields[0].Descriptor()
	// udp.DeviceIDValidator is a validator for the "device_id" field. It is called by the builders before save.
	udp.DeviceIDValidator = udpDescDeviceID.Validators[0].(func(string) error)
	// udpDescType is the schema descriptor for type field.
	udpDescType := udpFields[2].Descriptor()
	// udp.DefaultType holds the default value on creation for the type field.
	udp.DefaultType = udpDescType.Default.(string)
	// udpDescHeader is the schema descriptor for header field.
	udpDescHeader := udpFields[3].Descriptor()
	// udp.DefaultHeader holds the default value on creation for the header field.
	udp.DefaultHeader = udpDescHeader.Default.(string)
	// udpDescTypeID is the schema descriptor for type_id field.
	udpDescTypeID := udpFields[4].Descriptor()
	// udp.DefaultTypeID holds the default value on creation for the type_id field.
	udp.DefaultTypeID = udpDescTypeID.Default.(string)
	// udpDescCreateUnix is the schema descriptor for create_unix field.
	udpDescCreateUnix := udpFields[5].Descriptor()
	// udp.DefaultCreateUnix holds the default value on creation for the create_unix field.
	udp.DefaultCreateUnix = udpDescCreateUnix.Default.(func() time.Time)
	// udpDescUpdateUnix is the schema descriptor for update_unix field.
	udpDescUpdateUnix := udpFields[6].Descriptor()
	// udp.DefaultUpdateUnix holds the default value on creation for the update_unix field.
	udp.DefaultUpdateUnix = udpDescUpdateUnix.Default.(func() time.Time)
	// udp.UpdateDefaultUpdateUnix holds the default value on update for the update_unix field.
	udp.UpdateDefaultUpdateUnix = udpDescUpdateUnix.UpdateDefault.(func() time.Time)
	// udpDescID is the schema descriptor for id field.
	udpDescID := udpFields[1].Descriptor()
	// udp.IDValidator is a validator for the "id" field. It is called by the builders before save.
	udp.IDValidator = udpDescID.Validators[0].(func(string) error)
}

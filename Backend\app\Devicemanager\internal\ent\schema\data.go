package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Data holds the schema definition for the Data entity.
type Data struct {
	ent.Schema
}

// Fields of the Data.
func (Data) Fields() []ent.Field {
	return []ent.Field{
		field.String("frame_id").
			Comment("Foreign key to frame").
			NotEmpty(),
		field.String("index").
			Comment("Index of the data").
			NotEmpty(),
		field.String("name").
			Comment("name of the index data").
			NotEmpty(),
		field.String("type").
			Comment("type of the index data").
			Default(""),
		field.String("value").
			Comment("value of the index data").
			Default(""),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("timestamp of the index data"),
	}
}

// Edges of the Data.
func (Data) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("modbus_frame", Modbus.Type).
			Ref("data_points").
			Field("frame_id").
			Unique().
			Required(),
		edge.From("uart_frame", Uart.Type).
			Ref("data_points").
			Field("frame_id").
			Unique().
			Required(),
		edge.From("udp_frame", Udp.Type).
			Ref("data_points").
			Field("frame_id").
			Unique().
			Required(),
	}
}

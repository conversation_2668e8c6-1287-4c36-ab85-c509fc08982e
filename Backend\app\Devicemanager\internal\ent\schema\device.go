package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Device holds the schema definition for the Device entity.
type Device struct {
	ent.Schema
}

// Fields of the Device.
func (Device) Fields() []ent.Field {
	return []ent.Field{
		//设备metadata
		field.String("id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("设备UID"),
		field.String("name").
			Comment("name of the machine").
			Default(""),

		//设备资源info
		field.String("ip").
			Comment("IP address of the machine").
			NotEmpty(),
		field.String("type").
			Comment("Type of the machine").
			Default("lower"),
		field.String("os").
			Comment("OS of the machine").
			Default(""),
		field.String("cpu").
			Comment("CPU model of the machine").
			Default(""),
		field.String("gpu").
			Comment("GPU model of the machine").
			Default(""),
		field.String("memory").
			Comment("Memory size of the machine").
			Default(""),
		field.String("disk").
			Comment("Disk size of the machine").
			Default(""),
		field.String("protocol").
			Comment("protocol of the communication").
			Default(""),

		//设备状态info
		field.String("status").
			Default("unready"),
		field.Time("healthtimestamp").
			Comment("Health check timestamp").
			Default(time.Unix(0, 0)),
		field.String("workmode").
			Default("centralized"),
		field.Time("create_unix").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("Update timestamp"),
	}
}

// Edges of the Device.
func (Device) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("modbusConfig", Modbus.Type).Unique(),
		edge.To("uartConfig", Uart.Type).Unique(),
		edge.To("udpConfig", Udp.Type).Unique(),
	}
}

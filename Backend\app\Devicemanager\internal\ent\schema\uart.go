package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Uart holds the schema definition for the Uart entity.
type Uart struct {
	ent.Schema
}

// Fields of the Uart.
func (Uart) Fields() []ent.Field {
	return []ent.Field{
		field.String("device_id").
			Comment("Foreign key to Device").
			NotEmpty(),
		field.String("id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("Frame ID"),
		field.String("header").
			Comment("Header of the frame").
			Default(""),
		field.String("addr").
			Comment("Address of the frame").
			Default(""),
		field.String("cmd").
			Comment("Command of the frame").
			Default(""),
		field.String("tail").
			Comment("Tail of the frame").
			Default(""),
		field.Time("create_unix").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("Update timestamp"),
	}
}

// Edges of the Uart.
func (Uart) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("device", Device.Type).Ref("uartConfig").Field("device_id").Unique().Required(),
		edge.To("data_points", Data.Type),
	}
}

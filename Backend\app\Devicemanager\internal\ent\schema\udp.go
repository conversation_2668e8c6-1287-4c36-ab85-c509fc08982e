package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Udp holds the schema definition for the Udp entity.
type Udp struct {
	ent.Schema
}

// Fields of the Udp.
func (Udp) Fields() []ent.Field {
	return []ent.Field{
		field.String("device_id").
			Comment("Foreign key to Device").
			NotEmpty(),
		field.String("id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("Frame ID"),
		field.String("type").
			Comment("Frame Type").
			Default(""),
		field.String("header").
			Comment("Frame Header").
			Default(""),
		field.String("type_id").
			Comment("Frame Type ID").
			Default(""),
		field.Time("create_unix").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("Update timestamp"),
	}
}

// Edges of the Udp.
func (Udp) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("device", Device.Type).Ref("udpConfig").Field("device_id").Unique().Required(),
		edge.To("data_points", Data.Type),
	}
}

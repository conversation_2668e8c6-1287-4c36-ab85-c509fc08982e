// Code generated by ent, DO NOT EDIT.

package uart

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the uart type in the database.
	Label = "uart"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeviceID holds the string denoting the device_id field in the database.
	FieldDeviceID = "device_id"
	// FieldHeader holds the string denoting the header field in the database.
	FieldHeader = "header"
	// FieldAddr holds the string denoting the addr field in the database.
	FieldAddr = "addr"
	// FieldCmd holds the string denoting the cmd field in the database.
	FieldCmd = "cmd"
	// FieldTail holds the string denoting the tail field in the database.
	FieldTail = "tail"
	// FieldCreateUnix holds the string denoting the create_unix field in the database.
	FieldCreateUnix = "create_unix"
	// FieldUpdateUnix holds the string denoting the update_unix field in the database.
	FieldUpdateUnix = "update_unix"
	// EdgeDevice holds the string denoting the device edge name in mutations.
	EdgeDevice = "device"
	// EdgeDataPoints holds the string denoting the data_points edge name in mutations.
	EdgeDataPoints = "data_points"
	// Table holds the table name of the uart in the database.
	Table = "uarts"
	// DeviceTable is the table that holds the device relation/edge.
	DeviceTable = "uarts"
	// DeviceInverseTable is the table name for the Device entity.
	// It exists in this package in order to avoid circular dependency with the "device" package.
	DeviceInverseTable = "devices"
	// DeviceColumn is the table column denoting the device relation/edge.
	DeviceColumn = "device_id"
	// DataPointsTable is the table that holds the data_points relation/edge.
	DataPointsTable = "data"
	// DataPointsInverseTable is the table name for the Data entity.
	// It exists in this package in order to avoid circular dependency with the "data" package.
	DataPointsInverseTable = "data"
	// DataPointsColumn is the table column denoting the data_points relation/edge.
	DataPointsColumn = "frame_id"
)

// Columns holds all SQL columns for uart fields.
var Columns = []string{
	FieldID,
	FieldDeviceID,
	FieldHeader,
	FieldAddr,
	FieldCmd,
	FieldTail,
	FieldCreateUnix,
	FieldUpdateUnix,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DeviceIDValidator is a validator for the "device_id" field. It is called by the builders before save.
	DeviceIDValidator func(string) error
	// DefaultHeader holds the default value on creation for the "header" field.
	DefaultHeader string
	// DefaultAddr holds the default value on creation for the "addr" field.
	DefaultAddr string
	// DefaultCmd holds the default value on creation for the "cmd" field.
	DefaultCmd string
	// DefaultTail holds the default value on creation for the "tail" field.
	DefaultTail string
	// DefaultCreateUnix holds the default value on creation for the "create_unix" field.
	DefaultCreateUnix func() time.Time
	// DefaultUpdateUnix holds the default value on creation for the "update_unix" field.
	DefaultUpdateUnix func() time.Time
	// UpdateDefaultUpdateUnix holds the default value on update for the "update_unix" field.
	UpdateDefaultUpdateUnix func() time.Time
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Uart queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeviceID orders the results by the device_id field.
func ByDeviceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeviceID, opts...).ToFunc()
}

// ByHeader orders the results by the header field.
func ByHeader(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHeader, opts...).ToFunc()
}

// ByAddr orders the results by the addr field.
func ByAddr(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAddr, opts...).ToFunc()
}

// ByCmd orders the results by the cmd field.
func ByCmd(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCmd, opts...).ToFunc()
}

// ByTail orders the results by the tail field.
func ByTail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTail, opts...).ToFunc()
}

// ByCreateUnix orders the results by the create_unix field.
func ByCreateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUnix, opts...).ToFunc()
}

// ByUpdateUnix orders the results by the update_unix field.
func ByUpdateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateUnix, opts...).ToFunc()
}

// ByDeviceField orders the results by device field.
func ByDeviceField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDeviceStep(), sql.OrderByField(field, opts...))
	}
}

// ByDataPointsCount orders the results by data_points count.
func ByDataPointsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newDataPointsStep(), opts...)
	}
}

// ByDataPoints orders the results by data_points terms.
func ByDataPoints(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newDataPointsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newDeviceStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DeviceInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, true, DeviceTable, DeviceColumn),
	)
}
func newDataPointsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(DataPointsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, DataPointsTable, DataPointsColumn),
	)
}

// Code generated by ent, DO NOT EDIT.

package uart

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Uart {
	return predicate.Uart(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Uart {
	return predicate.Uart(sql.FieldContainsFold(FieldID, id))
}

// DeviceID applies equality check predicate on the "device_id" field. It's identical to DeviceIDEQ.
func DeviceID(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldDeviceID, v))
}

// Header applies equality check predicate on the "header" field. It's identical to HeaderEQ.
func Header(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldHeader, v))
}

// Addr applies equality check predicate on the "addr" field. It's identical to AddrEQ.
func Addr(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldAddr, v))
}

// Cmd applies equality check predicate on the "cmd" field. It's identical to CmdEQ.
func Cmd(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldCmd, v))
}

// Tail applies equality check predicate on the "tail" field. It's identical to TailEQ.
func Tail(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldTail, v))
}

// CreateUnix applies equality check predicate on the "create_unix" field. It's identical to CreateUnixEQ.
func CreateUnix(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldCreateUnix, v))
}

// UpdateUnix applies equality check predicate on the "update_unix" field. It's identical to UpdateUnixEQ.
func UpdateUnix(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldUpdateUnix, v))
}

// DeviceIDEQ applies the EQ predicate on the "device_id" field.
func DeviceIDEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldDeviceID, v))
}

// DeviceIDNEQ applies the NEQ predicate on the "device_id" field.
func DeviceIDNEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldDeviceID, v))
}

// DeviceIDIn applies the In predicate on the "device_id" field.
func DeviceIDIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldDeviceID, vs...))
}

// DeviceIDNotIn applies the NotIn predicate on the "device_id" field.
func DeviceIDNotIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldDeviceID, vs...))
}

// DeviceIDGT applies the GT predicate on the "device_id" field.
func DeviceIDGT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldDeviceID, v))
}

// DeviceIDGTE applies the GTE predicate on the "device_id" field.
func DeviceIDGTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldDeviceID, v))
}

// DeviceIDLT applies the LT predicate on the "device_id" field.
func DeviceIDLT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldDeviceID, v))
}

// DeviceIDLTE applies the LTE predicate on the "device_id" field.
func DeviceIDLTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldDeviceID, v))
}

// DeviceIDContains applies the Contains predicate on the "device_id" field.
func DeviceIDContains(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContains(FieldDeviceID, v))
}

// DeviceIDHasPrefix applies the HasPrefix predicate on the "device_id" field.
func DeviceIDHasPrefix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasPrefix(FieldDeviceID, v))
}

// DeviceIDHasSuffix applies the HasSuffix predicate on the "device_id" field.
func DeviceIDHasSuffix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasSuffix(FieldDeviceID, v))
}

// DeviceIDEqualFold applies the EqualFold predicate on the "device_id" field.
func DeviceIDEqualFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEqualFold(FieldDeviceID, v))
}

// DeviceIDContainsFold applies the ContainsFold predicate on the "device_id" field.
func DeviceIDContainsFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContainsFold(FieldDeviceID, v))
}

// HeaderEQ applies the EQ predicate on the "header" field.
func HeaderEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldHeader, v))
}

// HeaderNEQ applies the NEQ predicate on the "header" field.
func HeaderNEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldHeader, v))
}

// HeaderIn applies the In predicate on the "header" field.
func HeaderIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldHeader, vs...))
}

// HeaderNotIn applies the NotIn predicate on the "header" field.
func HeaderNotIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldHeader, vs...))
}

// HeaderGT applies the GT predicate on the "header" field.
func HeaderGT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldHeader, v))
}

// HeaderGTE applies the GTE predicate on the "header" field.
func HeaderGTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldHeader, v))
}

// HeaderLT applies the LT predicate on the "header" field.
func HeaderLT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldHeader, v))
}

// HeaderLTE applies the LTE predicate on the "header" field.
func HeaderLTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldHeader, v))
}

// HeaderContains applies the Contains predicate on the "header" field.
func HeaderContains(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContains(FieldHeader, v))
}

// HeaderHasPrefix applies the HasPrefix predicate on the "header" field.
func HeaderHasPrefix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasPrefix(FieldHeader, v))
}

// HeaderHasSuffix applies the HasSuffix predicate on the "header" field.
func HeaderHasSuffix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasSuffix(FieldHeader, v))
}

// HeaderEqualFold applies the EqualFold predicate on the "header" field.
func HeaderEqualFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEqualFold(FieldHeader, v))
}

// HeaderContainsFold applies the ContainsFold predicate on the "header" field.
func HeaderContainsFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContainsFold(FieldHeader, v))
}

// AddrEQ applies the EQ predicate on the "addr" field.
func AddrEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldAddr, v))
}

// AddrNEQ applies the NEQ predicate on the "addr" field.
func AddrNEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldAddr, v))
}

// AddrIn applies the In predicate on the "addr" field.
func AddrIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldAddr, vs...))
}

// AddrNotIn applies the NotIn predicate on the "addr" field.
func AddrNotIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldAddr, vs...))
}

// AddrGT applies the GT predicate on the "addr" field.
func AddrGT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldAddr, v))
}

// AddrGTE applies the GTE predicate on the "addr" field.
func AddrGTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldAddr, v))
}

// AddrLT applies the LT predicate on the "addr" field.
func AddrLT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldAddr, v))
}

// AddrLTE applies the LTE predicate on the "addr" field.
func AddrLTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldAddr, v))
}

// AddrContains applies the Contains predicate on the "addr" field.
func AddrContains(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContains(FieldAddr, v))
}

// AddrHasPrefix applies the HasPrefix predicate on the "addr" field.
func AddrHasPrefix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasPrefix(FieldAddr, v))
}

// AddrHasSuffix applies the HasSuffix predicate on the "addr" field.
func AddrHasSuffix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasSuffix(FieldAddr, v))
}

// AddrEqualFold applies the EqualFold predicate on the "addr" field.
func AddrEqualFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEqualFold(FieldAddr, v))
}

// AddrContainsFold applies the ContainsFold predicate on the "addr" field.
func AddrContainsFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContainsFold(FieldAddr, v))
}

// CmdEQ applies the EQ predicate on the "cmd" field.
func CmdEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldCmd, v))
}

// CmdNEQ applies the NEQ predicate on the "cmd" field.
func CmdNEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldCmd, v))
}

// CmdIn applies the In predicate on the "cmd" field.
func CmdIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldCmd, vs...))
}

// CmdNotIn applies the NotIn predicate on the "cmd" field.
func CmdNotIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldCmd, vs...))
}

// CmdGT applies the GT predicate on the "cmd" field.
func CmdGT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldCmd, v))
}

// CmdGTE applies the GTE predicate on the "cmd" field.
func CmdGTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldCmd, v))
}

// CmdLT applies the LT predicate on the "cmd" field.
func CmdLT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldCmd, v))
}

// CmdLTE applies the LTE predicate on the "cmd" field.
func CmdLTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldCmd, v))
}

// CmdContains applies the Contains predicate on the "cmd" field.
func CmdContains(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContains(FieldCmd, v))
}

// CmdHasPrefix applies the HasPrefix predicate on the "cmd" field.
func CmdHasPrefix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasPrefix(FieldCmd, v))
}

// CmdHasSuffix applies the HasSuffix predicate on the "cmd" field.
func CmdHasSuffix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasSuffix(FieldCmd, v))
}

// CmdEqualFold applies the EqualFold predicate on the "cmd" field.
func CmdEqualFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEqualFold(FieldCmd, v))
}

// CmdContainsFold applies the ContainsFold predicate on the "cmd" field.
func CmdContainsFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContainsFold(FieldCmd, v))
}

// TailEQ applies the EQ predicate on the "tail" field.
func TailEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldTail, v))
}

// TailNEQ applies the NEQ predicate on the "tail" field.
func TailNEQ(v string) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldTail, v))
}

// TailIn applies the In predicate on the "tail" field.
func TailIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldTail, vs...))
}

// TailNotIn applies the NotIn predicate on the "tail" field.
func TailNotIn(vs ...string) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldTail, vs...))
}

// TailGT applies the GT predicate on the "tail" field.
func TailGT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldTail, v))
}

// TailGTE applies the GTE predicate on the "tail" field.
func TailGTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldTail, v))
}

// TailLT applies the LT predicate on the "tail" field.
func TailLT(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldTail, v))
}

// TailLTE applies the LTE predicate on the "tail" field.
func TailLTE(v string) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldTail, v))
}

// TailContains applies the Contains predicate on the "tail" field.
func TailContains(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContains(FieldTail, v))
}

// TailHasPrefix applies the HasPrefix predicate on the "tail" field.
func TailHasPrefix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasPrefix(FieldTail, v))
}

// TailHasSuffix applies the HasSuffix predicate on the "tail" field.
func TailHasSuffix(v string) predicate.Uart {
	return predicate.Uart(sql.FieldHasSuffix(FieldTail, v))
}

// TailEqualFold applies the EqualFold predicate on the "tail" field.
func TailEqualFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldEqualFold(FieldTail, v))
}

// TailContainsFold applies the ContainsFold predicate on the "tail" field.
func TailContainsFold(v string) predicate.Uart {
	return predicate.Uart(sql.FieldContainsFold(FieldTail, v))
}

// CreateUnixEQ applies the EQ predicate on the "create_unix" field.
func CreateUnixEQ(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldCreateUnix, v))
}

// CreateUnixNEQ applies the NEQ predicate on the "create_unix" field.
func CreateUnixNEQ(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldCreateUnix, v))
}

// CreateUnixIn applies the In predicate on the "create_unix" field.
func CreateUnixIn(vs ...time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldCreateUnix, vs...))
}

// CreateUnixNotIn applies the NotIn predicate on the "create_unix" field.
func CreateUnixNotIn(vs ...time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldCreateUnix, vs...))
}

// CreateUnixGT applies the GT predicate on the "create_unix" field.
func CreateUnixGT(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldCreateUnix, v))
}

// CreateUnixGTE applies the GTE predicate on the "create_unix" field.
func CreateUnixGTE(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldCreateUnix, v))
}

// CreateUnixLT applies the LT predicate on the "create_unix" field.
func CreateUnixLT(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldCreateUnix, v))
}

// CreateUnixLTE applies the LTE predicate on the "create_unix" field.
func CreateUnixLTE(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldCreateUnix, v))
}

// UpdateUnixEQ applies the EQ predicate on the "update_unix" field.
func UpdateUnixEQ(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldEQ(FieldUpdateUnix, v))
}

// UpdateUnixNEQ applies the NEQ predicate on the "update_unix" field.
func UpdateUnixNEQ(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldNEQ(FieldUpdateUnix, v))
}

// UpdateUnixIn applies the In predicate on the "update_unix" field.
func UpdateUnixIn(vs ...time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldIn(FieldUpdateUnix, vs...))
}

// UpdateUnixNotIn applies the NotIn predicate on the "update_unix" field.
func UpdateUnixNotIn(vs ...time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldNotIn(FieldUpdateUnix, vs...))
}

// UpdateUnixGT applies the GT predicate on the "update_unix" field.
func UpdateUnixGT(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldGT(FieldUpdateUnix, v))
}

// UpdateUnixGTE applies the GTE predicate on the "update_unix" field.
func UpdateUnixGTE(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldGTE(FieldUpdateUnix, v))
}

// UpdateUnixLT applies the LT predicate on the "update_unix" field.
func UpdateUnixLT(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldLT(FieldUpdateUnix, v))
}

// UpdateUnixLTE applies the LTE predicate on the "update_unix" field.
func UpdateUnixLTE(v time.Time) predicate.Uart {
	return predicate.Uart(sql.FieldLTE(FieldUpdateUnix, v))
}

// HasDevice applies the HasEdge predicate on the "device" edge.
func HasDevice() predicate.Uart {
	return predicate.Uart(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, DeviceTable, DeviceColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDeviceWith applies the HasEdge predicate on the "device" edge with a given conditions (other predicates).
func HasDeviceWith(preds ...predicate.Device) predicate.Uart {
	return predicate.Uart(func(s *sql.Selector) {
		step := newDeviceStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDataPoints applies the HasEdge predicate on the "data_points" edge.
func HasDataPoints() predicate.Uart {
	return predicate.Uart(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, DataPointsTable, DataPointsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDataPointsWith applies the HasEdge predicate on the "data_points" edge with a given conditions (other predicates).
func HasDataPointsWith(preds ...predicate.Data) predicate.Uart {
	return predicate.Uart(func(s *sql.Selector) {
		step := newDataPointsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Uart) predicate.Uart {
	return predicate.Uart(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Uart) predicate.Uart {
	return predicate.Uart(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Uart) predicate.Uart {
	return predicate.Uart(sql.NotPredicates(p))
}

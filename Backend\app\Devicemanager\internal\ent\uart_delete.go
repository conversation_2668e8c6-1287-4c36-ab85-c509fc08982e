// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/uart"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UartDelete is the builder for deleting a Uart entity.
type UartDelete struct {
	config
	hooks    []Hook
	mutation *UartMutation
}

// Where appends a list predicates to the UartDelete builder.
func (ud *UartDelete) Where(ps ...predicate.Uart) *UartDelete {
	ud.mutation.Where(ps...)
	return ud
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ud *UartDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ud.sqlExec, ud.mutation, ud.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ud *UartDelete) ExecX(ctx context.Context) int {
	n, err := ud.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ud *UartDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(uart.Table, sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString))
	if ps := ud.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ud.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ud.mutation.done = true
	return affected, err
}

// UartDeleteOne is the builder for deleting a single Uart entity.
type UartDeleteOne struct {
	ud *UartDelete
}

// Where appends a list predicates to the UartDelete builder.
func (udo *UartDeleteOne) Where(ps ...predicate.Uart) *UartDeleteOne {
	udo.ud.mutation.Where(ps...)
	return udo
}

// Exec executes the deletion query.
func (udo *UartDeleteOne) Exec(ctx context.Context) error {
	n, err := udo.ud.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{uart.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (udo *UartDeleteOne) ExecX(ctx context.Context) {
	if err := udo.Exec(ctx); err != nil {
		panic(err)
	}
}

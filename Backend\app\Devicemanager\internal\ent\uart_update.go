// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/uart"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UartUpdate is the builder for updating Uart entities.
type UartUpdate struct {
	config
	hooks    []Hook
	mutation *UartMutation
}

// Where appends a list predicates to the UartUpdate builder.
func (uu *UartUpdate) Where(ps ...predicate.Uart) *UartUpdate {
	uu.mutation.Where(ps...)
	return uu
}

// SetDeviceID sets the "device_id" field.
func (uu *UartUpdate) SetDeviceID(s string) *UartUpdate {
	uu.mutation.SetDeviceID(s)
	return uu
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (uu *UartUpdate) SetNillableDeviceID(s *string) *UartUpdate {
	if s != nil {
		uu.SetDeviceID(*s)
	}
	return uu
}

// SetHeader sets the "header" field.
func (uu *UartUpdate) SetHeader(s string) *UartUpdate {
	uu.mutation.SetHeader(s)
	return uu
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (uu *UartUpdate) SetNillableHeader(s *string) *UartUpdate {
	if s != nil {
		uu.SetHeader(*s)
	}
	return uu
}

// SetAddr sets the "addr" field.
func (uu *UartUpdate) SetAddr(s string) *UartUpdate {
	uu.mutation.SetAddr(s)
	return uu
}

// SetNillableAddr sets the "addr" field if the given value is not nil.
func (uu *UartUpdate) SetNillableAddr(s *string) *UartUpdate {
	if s != nil {
		uu.SetAddr(*s)
	}
	return uu
}

// SetCmd sets the "cmd" field.
func (uu *UartUpdate) SetCmd(s string) *UartUpdate {
	uu.mutation.SetCmd(s)
	return uu
}

// SetNillableCmd sets the "cmd" field if the given value is not nil.
func (uu *UartUpdate) SetNillableCmd(s *string) *UartUpdate {
	if s != nil {
		uu.SetCmd(*s)
	}
	return uu
}

// SetTail sets the "tail" field.
func (uu *UartUpdate) SetTail(s string) *UartUpdate {
	uu.mutation.SetTail(s)
	return uu
}

// SetNillableTail sets the "tail" field if the given value is not nil.
func (uu *UartUpdate) SetNillableTail(s *string) *UartUpdate {
	if s != nil {
		uu.SetTail(*s)
	}
	return uu
}

// SetUpdateUnix sets the "update_unix" field.
func (uu *UartUpdate) SetUpdateUnix(t time.Time) *UartUpdate {
	uu.mutation.SetUpdateUnix(t)
	return uu
}

// SetDevice sets the "device" edge to the Device entity.
func (uu *UartUpdate) SetDevice(d *Device) *UartUpdate {
	return uu.SetDeviceID(d.ID)
}

// AddDataPointIDs adds the "data_points" edge to the Data entity by IDs.
func (uu *UartUpdate) AddDataPointIDs(ids ...int) *UartUpdate {
	uu.mutation.AddDataPointIDs(ids...)
	return uu
}

// AddDataPoints adds the "data_points" edges to the Data entity.
func (uu *UartUpdate) AddDataPoints(d ...*Data) *UartUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uu.AddDataPointIDs(ids...)
}

// Mutation returns the UartMutation object of the builder.
func (uu *UartUpdate) Mutation() *UartMutation {
	return uu.mutation
}

// ClearDevice clears the "device" edge to the Device entity.
func (uu *UartUpdate) ClearDevice() *UartUpdate {
	uu.mutation.ClearDevice()
	return uu
}

// ClearDataPoints clears all "data_points" edges to the Data entity.
func (uu *UartUpdate) ClearDataPoints() *UartUpdate {
	uu.mutation.ClearDataPoints()
	return uu
}

// RemoveDataPointIDs removes the "data_points" edge to Data entities by IDs.
func (uu *UartUpdate) RemoveDataPointIDs(ids ...int) *UartUpdate {
	uu.mutation.RemoveDataPointIDs(ids...)
	return uu
}

// RemoveDataPoints removes "data_points" edges to Data entities.
func (uu *UartUpdate) RemoveDataPoints(d ...*Data) *UartUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uu.RemoveDataPointIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uu *UartUpdate) Save(ctx context.Context) (int, error) {
	uu.defaults()
	return withHooks(ctx, uu.sqlSave, uu.mutation, uu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uu *UartUpdate) SaveX(ctx context.Context) int {
	affected, err := uu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uu *UartUpdate) Exec(ctx context.Context) error {
	_, err := uu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uu *UartUpdate) ExecX(ctx context.Context) {
	if err := uu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uu *UartUpdate) defaults() {
	if _, ok := uu.mutation.UpdateUnix(); !ok {
		v := uart.UpdateDefaultUpdateUnix()
		uu.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uu *UartUpdate) check() error {
	if v, ok := uu.mutation.DeviceID(); ok {
		if err := uart.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Uart.device_id": %w`, err)}
		}
	}
	if uu.mutation.DeviceCleared() && len(uu.mutation.DeviceIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Uart.device"`)
	}
	return nil
}

func (uu *UartUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(uart.Table, uart.Columns, sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString))
	if ps := uu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uu.mutation.Header(); ok {
		_spec.SetField(uart.FieldHeader, field.TypeString, value)
	}
	if value, ok := uu.mutation.Addr(); ok {
		_spec.SetField(uart.FieldAddr, field.TypeString, value)
	}
	if value, ok := uu.mutation.Cmd(); ok {
		_spec.SetField(uart.FieldCmd, field.TypeString, value)
	}
	if value, ok := uu.mutation.Tail(); ok {
		_spec.SetField(uart.FieldTail, field.TypeString, value)
	}
	if value, ok := uu.mutation.UpdateUnix(); ok {
		_spec.SetField(uart.FieldUpdateUnix, field.TypeTime, value)
	}
	if uu.mutation.DeviceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   uart.DeviceTable,
			Columns: []string{uart.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   uart.DeviceTable,
			Columns: []string{uart.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   uart.DataPointsTable,
			Columns: []string{uart.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedDataPointsIDs(); len(nodes) > 0 && !uu.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   uart.DataPointsTable,
			Columns: []string{uart.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.DataPointsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   uart.DataPointsTable,
			Columns: []string{uart.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, uu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{uart.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uu.mutation.done = true
	return n, nil
}

// UartUpdateOne is the builder for updating a single Uart entity.
type UartUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UartMutation
}

// SetDeviceID sets the "device_id" field.
func (uuo *UartUpdateOne) SetDeviceID(s string) *UartUpdateOne {
	uuo.mutation.SetDeviceID(s)
	return uuo
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (uuo *UartUpdateOne) SetNillableDeviceID(s *string) *UartUpdateOne {
	if s != nil {
		uuo.SetDeviceID(*s)
	}
	return uuo
}

// SetHeader sets the "header" field.
func (uuo *UartUpdateOne) SetHeader(s string) *UartUpdateOne {
	uuo.mutation.SetHeader(s)
	return uuo
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (uuo *UartUpdateOne) SetNillableHeader(s *string) *UartUpdateOne {
	if s != nil {
		uuo.SetHeader(*s)
	}
	return uuo
}

// SetAddr sets the "addr" field.
func (uuo *UartUpdateOne) SetAddr(s string) *UartUpdateOne {
	uuo.mutation.SetAddr(s)
	return uuo
}

// SetNillableAddr sets the "addr" field if the given value is not nil.
func (uuo *UartUpdateOne) SetNillableAddr(s *string) *UartUpdateOne {
	if s != nil {
		uuo.SetAddr(*s)
	}
	return uuo
}

// SetCmd sets the "cmd" field.
func (uuo *UartUpdateOne) SetCmd(s string) *UartUpdateOne {
	uuo.mutation.SetCmd(s)
	return uuo
}

// SetNillableCmd sets the "cmd" field if the given value is not nil.
func (uuo *UartUpdateOne) SetNillableCmd(s *string) *UartUpdateOne {
	if s != nil {
		uuo.SetCmd(*s)
	}
	return uuo
}

// SetTail sets the "tail" field.
func (uuo *UartUpdateOne) SetTail(s string) *UartUpdateOne {
	uuo.mutation.SetTail(s)
	return uuo
}

// SetNillableTail sets the "tail" field if the given value is not nil.
func (uuo *UartUpdateOne) SetNillableTail(s *string) *UartUpdateOne {
	if s != nil {
		uuo.SetTail(*s)
	}
	return uuo
}

// SetUpdateUnix sets the "update_unix" field.
func (uuo *UartUpdateOne) SetUpdateUnix(t time.Time) *UartUpdateOne {
	uuo.mutation.SetUpdateUnix(t)
	return uuo
}

// SetDevice sets the "device" edge to the Device entity.
func (uuo *UartUpdateOne) SetDevice(d *Device) *UartUpdateOne {
	return uuo.SetDeviceID(d.ID)
}

// AddDataPointIDs adds the "data_points" edge to the Data entity by IDs.
func (uuo *UartUpdateOne) AddDataPointIDs(ids ...int) *UartUpdateOne {
	uuo.mutation.AddDataPointIDs(ids...)
	return uuo
}

// AddDataPoints adds the "data_points" edges to the Data entity.
func (uuo *UartUpdateOne) AddDataPoints(d ...*Data) *UartUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uuo.AddDataPointIDs(ids...)
}

// Mutation returns the UartMutation object of the builder.
func (uuo *UartUpdateOne) Mutation() *UartMutation {
	return uuo.mutation
}

// ClearDevice clears the "device" edge to the Device entity.
func (uuo *UartUpdateOne) ClearDevice() *UartUpdateOne {
	uuo.mutation.ClearDevice()
	return uuo
}

// ClearDataPoints clears all "data_points" edges to the Data entity.
func (uuo *UartUpdateOne) ClearDataPoints() *UartUpdateOne {
	uuo.mutation.ClearDataPoints()
	return uuo
}

// RemoveDataPointIDs removes the "data_points" edge to Data entities by IDs.
func (uuo *UartUpdateOne) RemoveDataPointIDs(ids ...int) *UartUpdateOne {
	uuo.mutation.RemoveDataPointIDs(ids...)
	return uuo
}

// RemoveDataPoints removes "data_points" edges to Data entities.
func (uuo *UartUpdateOne) RemoveDataPoints(d ...*Data) *UartUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uuo.RemoveDataPointIDs(ids...)
}

// Where appends a list predicates to the UartUpdate builder.
func (uuo *UartUpdateOne) Where(ps ...predicate.Uart) *UartUpdateOne {
	uuo.mutation.Where(ps...)
	return uuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uuo *UartUpdateOne) Select(field string, fields ...string) *UartUpdateOne {
	uuo.fields = append([]string{field}, fields...)
	return uuo
}

// Save executes the query and returns the updated Uart entity.
func (uuo *UartUpdateOne) Save(ctx context.Context) (*Uart, error) {
	uuo.defaults()
	return withHooks(ctx, uuo.sqlSave, uuo.mutation, uuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uuo *UartUpdateOne) SaveX(ctx context.Context) *Uart {
	node, err := uuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uuo *UartUpdateOne) Exec(ctx context.Context) error {
	_, err := uuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uuo *UartUpdateOne) ExecX(ctx context.Context) {
	if err := uuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uuo *UartUpdateOne) defaults() {
	if _, ok := uuo.mutation.UpdateUnix(); !ok {
		v := uart.UpdateDefaultUpdateUnix()
		uuo.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uuo *UartUpdateOne) check() error {
	if v, ok := uuo.mutation.DeviceID(); ok {
		if err := uart.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Uart.device_id": %w`, err)}
		}
	}
	if uuo.mutation.DeviceCleared() && len(uuo.mutation.DeviceIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Uart.device"`)
	}
	return nil
}

func (uuo *UartUpdateOne) sqlSave(ctx context.Context) (_node *Uart, err error) {
	if err := uuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(uart.Table, uart.Columns, sqlgraph.NewFieldSpec(uart.FieldID, field.TypeString))
	id, ok := uuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Uart.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, uart.FieldID)
		for _, f := range fields {
			if !uart.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != uart.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uuo.mutation.Header(); ok {
		_spec.SetField(uart.FieldHeader, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Addr(); ok {
		_spec.SetField(uart.FieldAddr, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Cmd(); ok {
		_spec.SetField(uart.FieldCmd, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Tail(); ok {
		_spec.SetField(uart.FieldTail, field.TypeString, value)
	}
	if value, ok := uuo.mutation.UpdateUnix(); ok {
		_spec.SetField(uart.FieldUpdateUnix, field.TypeTime, value)
	}
	if uuo.mutation.DeviceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   uart.DeviceTable,
			Columns: []string{uart.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   uart.DeviceTable,
			Columns: []string{uart.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   uart.DataPointsTable,
			Columns: []string{uart.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedDataPointsIDs(); len(nodes) > 0 && !uuo.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   uart.DataPointsTable,
			Columns: []string{uart.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.DataPointsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   uart.DataPointsTable,
			Columns: []string{uart.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Uart{config: uuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{uart.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uuo.mutation.done = true
	return _node, nil
}

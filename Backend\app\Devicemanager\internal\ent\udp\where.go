// Code generated by ent, DO NOT EDIT.

package udp

import (
	"GCF/app/Devicemanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Udp {
	return predicate.Udp(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Udp {
	return predicate.Udp(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Udp {
	return predicate.Udp(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Udp {
	return predicate.Udp(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Udp {
	return predicate.Udp(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Udp {
	return predicate.Udp(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Udp {
	return predicate.Udp(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Udp {
	return predicate.Udp(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Udp {
	return predicate.Udp(sql.FieldContainsFold(FieldID, id))
}

// DeviceID applies equality check predicate on the "device_id" field. It's identical to DeviceIDEQ.
func DeviceID(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldDeviceID, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldType, v))
}

// Header applies equality check predicate on the "header" field. It's identical to HeaderEQ.
func Header(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldHeader, v))
}

// TypeID applies equality check predicate on the "type_id" field. It's identical to TypeIDEQ.
func TypeID(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldTypeID, v))
}

// CreateUnix applies equality check predicate on the "create_unix" field. It's identical to CreateUnixEQ.
func CreateUnix(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldCreateUnix, v))
}

// UpdateUnix applies equality check predicate on the "update_unix" field. It's identical to UpdateUnixEQ.
func UpdateUnix(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldUpdateUnix, v))
}

// DeviceIDEQ applies the EQ predicate on the "device_id" field.
func DeviceIDEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldDeviceID, v))
}

// DeviceIDNEQ applies the NEQ predicate on the "device_id" field.
func DeviceIDNEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldNEQ(FieldDeviceID, v))
}

// DeviceIDIn applies the In predicate on the "device_id" field.
func DeviceIDIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldIn(FieldDeviceID, vs...))
}

// DeviceIDNotIn applies the NotIn predicate on the "device_id" field.
func DeviceIDNotIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldNotIn(FieldDeviceID, vs...))
}

// DeviceIDGT applies the GT predicate on the "device_id" field.
func DeviceIDGT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGT(FieldDeviceID, v))
}

// DeviceIDGTE applies the GTE predicate on the "device_id" field.
func DeviceIDGTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGTE(FieldDeviceID, v))
}

// DeviceIDLT applies the LT predicate on the "device_id" field.
func DeviceIDLT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLT(FieldDeviceID, v))
}

// DeviceIDLTE applies the LTE predicate on the "device_id" field.
func DeviceIDLTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLTE(FieldDeviceID, v))
}

// DeviceIDContains applies the Contains predicate on the "device_id" field.
func DeviceIDContains(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContains(FieldDeviceID, v))
}

// DeviceIDHasPrefix applies the HasPrefix predicate on the "device_id" field.
func DeviceIDHasPrefix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasPrefix(FieldDeviceID, v))
}

// DeviceIDHasSuffix applies the HasSuffix predicate on the "device_id" field.
func DeviceIDHasSuffix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasSuffix(FieldDeviceID, v))
}

// DeviceIDEqualFold applies the EqualFold predicate on the "device_id" field.
func DeviceIDEqualFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEqualFold(FieldDeviceID, v))
}

// DeviceIDContainsFold applies the ContainsFold predicate on the "device_id" field.
func DeviceIDContainsFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContainsFold(FieldDeviceID, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContainsFold(FieldType, v))
}

// HeaderEQ applies the EQ predicate on the "header" field.
func HeaderEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldHeader, v))
}

// HeaderNEQ applies the NEQ predicate on the "header" field.
func HeaderNEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldNEQ(FieldHeader, v))
}

// HeaderIn applies the In predicate on the "header" field.
func HeaderIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldIn(FieldHeader, vs...))
}

// HeaderNotIn applies the NotIn predicate on the "header" field.
func HeaderNotIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldNotIn(FieldHeader, vs...))
}

// HeaderGT applies the GT predicate on the "header" field.
func HeaderGT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGT(FieldHeader, v))
}

// HeaderGTE applies the GTE predicate on the "header" field.
func HeaderGTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGTE(FieldHeader, v))
}

// HeaderLT applies the LT predicate on the "header" field.
func HeaderLT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLT(FieldHeader, v))
}

// HeaderLTE applies the LTE predicate on the "header" field.
func HeaderLTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLTE(FieldHeader, v))
}

// HeaderContains applies the Contains predicate on the "header" field.
func HeaderContains(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContains(FieldHeader, v))
}

// HeaderHasPrefix applies the HasPrefix predicate on the "header" field.
func HeaderHasPrefix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasPrefix(FieldHeader, v))
}

// HeaderHasSuffix applies the HasSuffix predicate on the "header" field.
func HeaderHasSuffix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasSuffix(FieldHeader, v))
}

// HeaderEqualFold applies the EqualFold predicate on the "header" field.
func HeaderEqualFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEqualFold(FieldHeader, v))
}

// HeaderContainsFold applies the ContainsFold predicate on the "header" field.
func HeaderContainsFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContainsFold(FieldHeader, v))
}

// TypeIDEQ applies the EQ predicate on the "type_id" field.
func TypeIDEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldTypeID, v))
}

// TypeIDNEQ applies the NEQ predicate on the "type_id" field.
func TypeIDNEQ(v string) predicate.Udp {
	return predicate.Udp(sql.FieldNEQ(FieldTypeID, v))
}

// TypeIDIn applies the In predicate on the "type_id" field.
func TypeIDIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldIn(FieldTypeID, vs...))
}

// TypeIDNotIn applies the NotIn predicate on the "type_id" field.
func TypeIDNotIn(vs ...string) predicate.Udp {
	return predicate.Udp(sql.FieldNotIn(FieldTypeID, vs...))
}

// TypeIDGT applies the GT predicate on the "type_id" field.
func TypeIDGT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGT(FieldTypeID, v))
}

// TypeIDGTE applies the GTE predicate on the "type_id" field.
func TypeIDGTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldGTE(FieldTypeID, v))
}

// TypeIDLT applies the LT predicate on the "type_id" field.
func TypeIDLT(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLT(FieldTypeID, v))
}

// TypeIDLTE applies the LTE predicate on the "type_id" field.
func TypeIDLTE(v string) predicate.Udp {
	return predicate.Udp(sql.FieldLTE(FieldTypeID, v))
}

// TypeIDContains applies the Contains predicate on the "type_id" field.
func TypeIDContains(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContains(FieldTypeID, v))
}

// TypeIDHasPrefix applies the HasPrefix predicate on the "type_id" field.
func TypeIDHasPrefix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasPrefix(FieldTypeID, v))
}

// TypeIDHasSuffix applies the HasSuffix predicate on the "type_id" field.
func TypeIDHasSuffix(v string) predicate.Udp {
	return predicate.Udp(sql.FieldHasSuffix(FieldTypeID, v))
}

// TypeIDEqualFold applies the EqualFold predicate on the "type_id" field.
func TypeIDEqualFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldEqualFold(FieldTypeID, v))
}

// TypeIDContainsFold applies the ContainsFold predicate on the "type_id" field.
func TypeIDContainsFold(v string) predicate.Udp {
	return predicate.Udp(sql.FieldContainsFold(FieldTypeID, v))
}

// CreateUnixEQ applies the EQ predicate on the "create_unix" field.
func CreateUnixEQ(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldCreateUnix, v))
}

// CreateUnixNEQ applies the NEQ predicate on the "create_unix" field.
func CreateUnixNEQ(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldNEQ(FieldCreateUnix, v))
}

// CreateUnixIn applies the In predicate on the "create_unix" field.
func CreateUnixIn(vs ...time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldIn(FieldCreateUnix, vs...))
}

// CreateUnixNotIn applies the NotIn predicate on the "create_unix" field.
func CreateUnixNotIn(vs ...time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldNotIn(FieldCreateUnix, vs...))
}

// CreateUnixGT applies the GT predicate on the "create_unix" field.
func CreateUnixGT(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldGT(FieldCreateUnix, v))
}

// CreateUnixGTE applies the GTE predicate on the "create_unix" field.
func CreateUnixGTE(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldGTE(FieldCreateUnix, v))
}

// CreateUnixLT applies the LT predicate on the "create_unix" field.
func CreateUnixLT(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldLT(FieldCreateUnix, v))
}

// CreateUnixLTE applies the LTE predicate on the "create_unix" field.
func CreateUnixLTE(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldLTE(FieldCreateUnix, v))
}

// UpdateUnixEQ applies the EQ predicate on the "update_unix" field.
func UpdateUnixEQ(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldEQ(FieldUpdateUnix, v))
}

// UpdateUnixNEQ applies the NEQ predicate on the "update_unix" field.
func UpdateUnixNEQ(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldNEQ(FieldUpdateUnix, v))
}

// UpdateUnixIn applies the In predicate on the "update_unix" field.
func UpdateUnixIn(vs ...time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldIn(FieldUpdateUnix, vs...))
}

// UpdateUnixNotIn applies the NotIn predicate on the "update_unix" field.
func UpdateUnixNotIn(vs ...time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldNotIn(FieldUpdateUnix, vs...))
}

// UpdateUnixGT applies the GT predicate on the "update_unix" field.
func UpdateUnixGT(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldGT(FieldUpdateUnix, v))
}

// UpdateUnixGTE applies the GTE predicate on the "update_unix" field.
func UpdateUnixGTE(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldGTE(FieldUpdateUnix, v))
}

// UpdateUnixLT applies the LT predicate on the "update_unix" field.
func UpdateUnixLT(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldLT(FieldUpdateUnix, v))
}

// UpdateUnixLTE applies the LTE predicate on the "update_unix" field.
func UpdateUnixLTE(v time.Time) predicate.Udp {
	return predicate.Udp(sql.FieldLTE(FieldUpdateUnix, v))
}

// HasDevice applies the HasEdge predicate on the "device" edge.
func HasDevice() predicate.Udp {
	return predicate.Udp(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, DeviceTable, DeviceColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDeviceWith applies the HasEdge predicate on the "device" edge with a given conditions (other predicates).
func HasDeviceWith(preds ...predicate.Device) predicate.Udp {
	return predicate.Udp(func(s *sql.Selector) {
		step := newDeviceStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasDataPoints applies the HasEdge predicate on the "data_points" edge.
func HasDataPoints() predicate.Udp {
	return predicate.Udp(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, DataPointsTable, DataPointsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDataPointsWith applies the HasEdge predicate on the "data_points" edge with a given conditions (other predicates).
func HasDataPointsWith(preds ...predicate.Data) predicate.Udp {
	return predicate.Udp(func(s *sql.Selector) {
		step := newDataPointsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Udp) predicate.Udp {
	return predicate.Udp(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Udp) predicate.Udp {
	return predicate.Udp(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Udp) predicate.Udp {
	return predicate.Udp(sql.NotPredicates(p))
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Devicemanager/internal/ent/data"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/predicate"
	"GCF/app/Devicemanager/internal/ent/udp"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UDPUpdate is the builder for updating Udp entities.
type UDPUpdate struct {
	config
	hooks    []Hook
	mutation *UDPMutation
}

// Where appends a list predicates to the UDPUpdate builder.
func (uu *UDPUpdate) Where(ps ...predicate.Udp) *UDPUpdate {
	uu.mutation.Where(ps...)
	return uu
}

// SetDeviceID sets the "device_id" field.
func (uu *UDPUpdate) SetDeviceID(s string) *UDPUpdate {
	uu.mutation.SetDeviceID(s)
	return uu
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (uu *UDPUpdate) SetNillableDeviceID(s *string) *UDPUpdate {
	if s != nil {
		uu.SetDeviceID(*s)
	}
	return uu
}

// SetType sets the "type" field.
func (uu *UDPUpdate) SetType(s string) *UDPUpdate {
	uu.mutation.SetType(s)
	return uu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (uu *UDPUpdate) SetNillableType(s *string) *UDPUpdate {
	if s != nil {
		uu.SetType(*s)
	}
	return uu
}

// SetHeader sets the "header" field.
func (uu *UDPUpdate) SetHeader(s string) *UDPUpdate {
	uu.mutation.SetHeader(s)
	return uu
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (uu *UDPUpdate) SetNillableHeader(s *string) *UDPUpdate {
	if s != nil {
		uu.SetHeader(*s)
	}
	return uu
}

// SetTypeID sets the "type_id" field.
func (uu *UDPUpdate) SetTypeID(s string) *UDPUpdate {
	uu.mutation.SetTypeID(s)
	return uu
}

// SetNillableTypeID sets the "type_id" field if the given value is not nil.
func (uu *UDPUpdate) SetNillableTypeID(s *string) *UDPUpdate {
	if s != nil {
		uu.SetTypeID(*s)
	}
	return uu
}

// SetUpdateUnix sets the "update_unix" field.
func (uu *UDPUpdate) SetUpdateUnix(t time.Time) *UDPUpdate {
	uu.mutation.SetUpdateUnix(t)
	return uu
}

// SetDevice sets the "device" edge to the Device entity.
func (uu *UDPUpdate) SetDevice(d *Device) *UDPUpdate {
	return uu.SetDeviceID(d.ID)
}

// AddDataPointIDs adds the "data_points" edge to the Data entity by IDs.
func (uu *UDPUpdate) AddDataPointIDs(ids ...int) *UDPUpdate {
	uu.mutation.AddDataPointIDs(ids...)
	return uu
}

// AddDataPoints adds the "data_points" edges to the Data entity.
func (uu *UDPUpdate) AddDataPoints(d ...*Data) *UDPUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uu.AddDataPointIDs(ids...)
}

// Mutation returns the UDPMutation object of the builder.
func (uu *UDPUpdate) Mutation() *UDPMutation {
	return uu.mutation
}

// ClearDevice clears the "device" edge to the Device entity.
func (uu *UDPUpdate) ClearDevice() *UDPUpdate {
	uu.mutation.ClearDevice()
	return uu
}

// ClearDataPoints clears all "data_points" edges to the Data entity.
func (uu *UDPUpdate) ClearDataPoints() *UDPUpdate {
	uu.mutation.ClearDataPoints()
	return uu
}

// RemoveDataPointIDs removes the "data_points" edge to Data entities by IDs.
func (uu *UDPUpdate) RemoveDataPointIDs(ids ...int) *UDPUpdate {
	uu.mutation.RemoveDataPointIDs(ids...)
	return uu
}

// RemoveDataPoints removes "data_points" edges to Data entities.
func (uu *UDPUpdate) RemoveDataPoints(d ...*Data) *UDPUpdate {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uu.RemoveDataPointIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uu *UDPUpdate) Save(ctx context.Context) (int, error) {
	uu.defaults()
	return withHooks(ctx, uu.sqlSave, uu.mutation, uu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uu *UDPUpdate) SaveX(ctx context.Context) int {
	affected, err := uu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uu *UDPUpdate) Exec(ctx context.Context) error {
	_, err := uu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uu *UDPUpdate) ExecX(ctx context.Context) {
	if err := uu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uu *UDPUpdate) defaults() {
	if _, ok := uu.mutation.UpdateUnix(); !ok {
		v := udp.UpdateDefaultUpdateUnix()
		uu.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uu *UDPUpdate) check() error {
	if v, ok := uu.mutation.DeviceID(); ok {
		if err := udp.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Udp.device_id": %w`, err)}
		}
	}
	if uu.mutation.DeviceCleared() && len(uu.mutation.DeviceIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Udp.device"`)
	}
	return nil
}

func (uu *UDPUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(udp.Table, udp.Columns, sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString))
	if ps := uu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uu.mutation.GetType(); ok {
		_spec.SetField(udp.FieldType, field.TypeString, value)
	}
	if value, ok := uu.mutation.Header(); ok {
		_spec.SetField(udp.FieldHeader, field.TypeString, value)
	}
	if value, ok := uu.mutation.TypeID(); ok {
		_spec.SetField(udp.FieldTypeID, field.TypeString, value)
	}
	if value, ok := uu.mutation.UpdateUnix(); ok {
		_spec.SetField(udp.FieldUpdateUnix, field.TypeTime, value)
	}
	if uu.mutation.DeviceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   udp.DeviceTable,
			Columns: []string{udp.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   udp.DeviceTable,
			Columns: []string{udp.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   udp.DataPointsTable,
			Columns: []string{udp.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedDataPointsIDs(); len(nodes) > 0 && !uu.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   udp.DataPointsTable,
			Columns: []string{udp.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.DataPointsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   udp.DataPointsTable,
			Columns: []string{udp.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, uu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{udp.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uu.mutation.done = true
	return n, nil
}

// UDPUpdateOne is the builder for updating a single Udp entity.
type UDPUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UDPMutation
}

// SetDeviceID sets the "device_id" field.
func (uuo *UDPUpdateOne) SetDeviceID(s string) *UDPUpdateOne {
	uuo.mutation.SetDeviceID(s)
	return uuo
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (uuo *UDPUpdateOne) SetNillableDeviceID(s *string) *UDPUpdateOne {
	if s != nil {
		uuo.SetDeviceID(*s)
	}
	return uuo
}

// SetType sets the "type" field.
func (uuo *UDPUpdateOne) SetType(s string) *UDPUpdateOne {
	uuo.mutation.SetType(s)
	return uuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (uuo *UDPUpdateOne) SetNillableType(s *string) *UDPUpdateOne {
	if s != nil {
		uuo.SetType(*s)
	}
	return uuo
}

// SetHeader sets the "header" field.
func (uuo *UDPUpdateOne) SetHeader(s string) *UDPUpdateOne {
	uuo.mutation.SetHeader(s)
	return uuo
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (uuo *UDPUpdateOne) SetNillableHeader(s *string) *UDPUpdateOne {
	if s != nil {
		uuo.SetHeader(*s)
	}
	return uuo
}

// SetTypeID sets the "type_id" field.
func (uuo *UDPUpdateOne) SetTypeID(s string) *UDPUpdateOne {
	uuo.mutation.SetTypeID(s)
	return uuo
}

// SetNillableTypeID sets the "type_id" field if the given value is not nil.
func (uuo *UDPUpdateOne) SetNillableTypeID(s *string) *UDPUpdateOne {
	if s != nil {
		uuo.SetTypeID(*s)
	}
	return uuo
}

// SetUpdateUnix sets the "update_unix" field.
func (uuo *UDPUpdateOne) SetUpdateUnix(t time.Time) *UDPUpdateOne {
	uuo.mutation.SetUpdateUnix(t)
	return uuo
}

// SetDevice sets the "device" edge to the Device entity.
func (uuo *UDPUpdateOne) SetDevice(d *Device) *UDPUpdateOne {
	return uuo.SetDeviceID(d.ID)
}

// AddDataPointIDs adds the "data_points" edge to the Data entity by IDs.
func (uuo *UDPUpdateOne) AddDataPointIDs(ids ...int) *UDPUpdateOne {
	uuo.mutation.AddDataPointIDs(ids...)
	return uuo
}

// AddDataPoints adds the "data_points" edges to the Data entity.
func (uuo *UDPUpdateOne) AddDataPoints(d ...*Data) *UDPUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uuo.AddDataPointIDs(ids...)
}

// Mutation returns the UDPMutation object of the builder.
func (uuo *UDPUpdateOne) Mutation() *UDPMutation {
	return uuo.mutation
}

// ClearDevice clears the "device" edge to the Device entity.
func (uuo *UDPUpdateOne) ClearDevice() *UDPUpdateOne {
	uuo.mutation.ClearDevice()
	return uuo
}

// ClearDataPoints clears all "data_points" edges to the Data entity.
func (uuo *UDPUpdateOne) ClearDataPoints() *UDPUpdateOne {
	uuo.mutation.ClearDataPoints()
	return uuo
}

// RemoveDataPointIDs removes the "data_points" edge to Data entities by IDs.
func (uuo *UDPUpdateOne) RemoveDataPointIDs(ids ...int) *UDPUpdateOne {
	uuo.mutation.RemoveDataPointIDs(ids...)
	return uuo
}

// RemoveDataPoints removes "data_points" edges to Data entities.
func (uuo *UDPUpdateOne) RemoveDataPoints(d ...*Data) *UDPUpdateOne {
	ids := make([]int, len(d))
	for i := range d {
		ids[i] = d[i].ID
	}
	return uuo.RemoveDataPointIDs(ids...)
}

// Where appends a list predicates to the UDPUpdate builder.
func (uuo *UDPUpdateOne) Where(ps ...predicate.Udp) *UDPUpdateOne {
	uuo.mutation.Where(ps...)
	return uuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uuo *UDPUpdateOne) Select(field string, fields ...string) *UDPUpdateOne {
	uuo.fields = append([]string{field}, fields...)
	return uuo
}

// Save executes the query and returns the updated Udp entity.
func (uuo *UDPUpdateOne) Save(ctx context.Context) (*Udp, error) {
	uuo.defaults()
	return withHooks(ctx, uuo.sqlSave, uuo.mutation, uuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uuo *UDPUpdateOne) SaveX(ctx context.Context) *Udp {
	node, err := uuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uuo *UDPUpdateOne) Exec(ctx context.Context) error {
	_, err := uuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uuo *UDPUpdateOne) ExecX(ctx context.Context) {
	if err := uuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uuo *UDPUpdateOne) defaults() {
	if _, ok := uuo.mutation.UpdateUnix(); !ok {
		v := udp.UpdateDefaultUpdateUnix()
		uuo.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uuo *UDPUpdateOne) check() error {
	if v, ok := uuo.mutation.DeviceID(); ok {
		if err := udp.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Udp.device_id": %w`, err)}
		}
	}
	if uuo.mutation.DeviceCleared() && len(uuo.mutation.DeviceIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Udp.device"`)
	}
	return nil
}

func (uuo *UDPUpdateOne) sqlSave(ctx context.Context) (_node *Udp, err error) {
	if err := uuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(udp.Table, udp.Columns, sqlgraph.NewFieldSpec(udp.FieldID, field.TypeString))
	id, ok := uuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Udp.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, udp.FieldID)
		for _, f := range fields {
			if !udp.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != udp.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uuo.mutation.GetType(); ok {
		_spec.SetField(udp.FieldType, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Header(); ok {
		_spec.SetField(udp.FieldHeader, field.TypeString, value)
	}
	if value, ok := uuo.mutation.TypeID(); ok {
		_spec.SetField(udp.FieldTypeID, field.TypeString, value)
	}
	if value, ok := uuo.mutation.UpdateUnix(); ok {
		_spec.SetField(udp.FieldUpdateUnix, field.TypeTime, value)
	}
	if uuo.mutation.DeviceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   udp.DeviceTable,
			Columns: []string{udp.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.DeviceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: true,
			Table:   udp.DeviceTable,
			Columns: []string{udp.DeviceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(device.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   udp.DataPointsTable,
			Columns: []string{udp.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedDataPointsIDs(); len(nodes) > 0 && !uuo.mutation.DataPointsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   udp.DataPointsTable,
			Columns: []string{udp.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.DataPointsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   udp.DataPointsTable,
			Columns: []string{udp.DataPointsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Udp{config: uuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{udp.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uuo.mutation.done = true
	return _node, nil
}

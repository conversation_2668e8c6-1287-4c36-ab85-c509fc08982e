package handler

import (
	"net/http"

	"GCF/app/Devicemanager/internal/logic"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func DeleteDeviceFrameHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DeleteDeviceFrameRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewDeleteDeviceFrameLogic(r.Context(), svcCtx)
		resp, err := l.DeleteDeviceFrame(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

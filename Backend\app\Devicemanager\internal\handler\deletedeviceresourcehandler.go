package handler

import (
	"net/http"

	"GCF/app/Devicemanager/internal/logic"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func DeleteDeviceResourceHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DeleteDeviceResourceRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewDeleteDeviceResourceLogic(r.Context(), svcCtx)
		resp, err := l.DeleteDeviceResource(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

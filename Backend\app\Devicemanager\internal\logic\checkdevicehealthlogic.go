package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CheckDeviceHealthLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCheckDeviceHealthLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckDeviceHealthLogic {
	return &CheckDeviceHealthLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CheckDeviceHealthLogic) CheckDeviceHealth(in *devicemanager.CheckDeviceHealthRequest) (*devicemanager.CheckDeviceHealthResponse, error) {
	//TODO: 可以修改为检查缓存
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(in.DeviceId)).First(l.ctx)
	if err != nil {
		return nil, err
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	return &devicemanager.CheckDeviceHealthResponse{
		DeviceWorkStatus: &devicemanager.DeviceWorkStatus{
			HealthStatus: DeviceWorkStatus.HealthStatus,
			Timestamp:    DeviceWorkStatus.Timestamp,
			WorkMode:     DeviceWorkStatus.WorkMode,
		},
	}, nil
}

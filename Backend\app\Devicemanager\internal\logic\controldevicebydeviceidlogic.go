package logic

import (
	"context" // 添加二进制编码包
	"fmt"
	"net/http"

	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/pkg/errx"

	"github.com/zeromicro/go-zero/core/logx"
)

type ControlDeviceByDeviceIDLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewControlDeviceByDeviceIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ControlDeviceByDeviceIDLogic {
	return &ControlDeviceByDeviceIDLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ControlDeviceByDeviceIDLogic) ControlDeviceByDeviceID(in *devicemanager.ControlDeviceByDeviceIDRequest) (*devicemanager.ControlDeviceByDeviceIDResponse, error) {
	// // todo: add your logic here and delete this line
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(in.DeviceId)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("failed to get queryDevice %s: %v", in.DeviceId, err)
		return nil, err
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}

	frameType := in.FrameInfo.FrameMeta.FrameType
	respFrameInfo := &devicemanager.FrameInfo{}
	if frameType == "udp" {
		_, respFrameInfo, err = utils.UdpControl(nil, respFrameInfo, "rpc", queryDevice.IP)
		if err != nil {
			return nil, errx.New(http.StatusForbidden, fmt.Sprintf("device %s control err: %s ", queryDevice.ID, err))
		}
	}
	return &devicemanager.ControlDeviceByDeviceIDResponse{
		DeviceWorkStatus: &devicemanager.DeviceWorkStatus{
			HealthStatus: DeviceWorkStatus.HealthStatus,
			Timestamp:    DeviceWorkStatus.Timestamp,
			WorkMode:     DeviceWorkStatus.WorkMode,
		},
		FrameInfos: respFrameInfo,
	}, nil
}

package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ControlDeviceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewControlDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ControlDeviceLogic {
	return &ControlDeviceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ControlDeviceLogic) ControlDevice(req *types.ControlDeviceRequest) (resp *types.ControlDeviceResponse, err error) {
	// 根据DeviceUID查询设备
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("failed to get queryDevice %s: %v", req.DeviceUID, err)
		return nil, err
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}

	frameType := req.FrameInfo.FrameMeta.FrameType
	respFrameInfo := &types.FrameInfo{}
	if frameType == "udp" {
		respFrameInfo, _ , err = utils.UdpControl(&req.FrameInfo, nil, "api", queryDevice.IP)
		if err != nil {
			return nil, err
		}
	}

	// 构造最终响应 errx.New(http.StatusForbidden, fmt.Sprintf("device %s control err: %s ", queryDevice.ID, err))
	resp = &types.ControlDeviceResponse{
		DeviceWorkStatus: *DeviceWorkStatus,
	}
	resp.FrameInfos = append(resp.FrameInfos, *respFrameInfo)
	return resp, nil
}

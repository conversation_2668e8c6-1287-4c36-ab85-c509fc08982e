package logic

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDeviceFrameLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDeviceFrameLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDeviceFrameLogic {
	return &CreateDeviceFrameLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDeviceFrameLogic) CreateDeviceFrame(req *types.CreateDeviceFrameRequest) (resp *types.CreateDeviceFrameResponse, err error) {
	// 根据DeviceUID查询设备
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("failed to get queryDevice %s: %v", req.DeviceUID, err)
		return nil, err
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	//开启事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()
	// 获取设备元数据
	DeviceMeta := types.DeviceMeta{
		DeviceUID: queryDevice.ID,
	}
	Protocols := strings.Split(queryDevice.Protocol, ",")
	//添加Frame记录
	for _, frameInfo := range req.FrameInfos {
		protocolIndex := utils.FindProtocol(Protocols, frameInfo.FrameMeta.FrameType)
		FrameMeta := &types.FrameMeta{
			FrameType: frameInfo.FrameMeta.FrameType,
		}
		//如果协议不存在，则添加协议新建frameID；存在就查询frameID
		if protocolIndex == -1 {
			//set
			Protocols = utils.AddProtocol(Protocols, frameInfo.FrameMeta.FrameType)
			_, err = tx.Device.Update().
				SetProtocol(strings.Join(Protocols, ",")).
				Save(l.ctx)
			if err != nil {
				return nil, fmt.Errorf("update Device failed: %s", err.Error())
			}
			FrameUID := uuid.New().String()
			FrameMeta.FrameUID = FrameUID
		} else {
			//update
			queryModbus, err := l.svcCtx.Db.Modbus.Query().Where(modbus.DeviceIDEQ(queryDevice.ID)).First(l.ctx)
			if err != nil {
				return nil, fmt.Errorf("invalid data fields: %s", err.Error())
			}
			FrameMeta.FrameUID = queryModbus.ID
		}
		DeviceMeta.FrameMetas = append(DeviceMeta.FrameMetas, *FrameMeta)
		//根据协议修改或添加记录
		switch frameInfo.FrameMeta.FrameType {
		case utils.FrameTypeModbus:
			if protocolIndex == -1 {
				_, err = tx.Modbus.Create().
					SetID(FrameMeta.FrameUID).
					SetTid(frameInfo.FrameLibs.ModbusInfo.TID).
					SetPid(frameInfo.FrameLibs.ModbusInfo.PID).
					SetLen(frameInfo.FrameLibs.ModbusInfo.Len).
					SetUID(frameInfo.FrameLibs.ModbusInfo.UID).
					SetFc(frameInfo.FrameLibs.ModbusInfo.FC).
					SetDeviceID(queryDevice.ID).
					Save(l.ctx)
				if err != nil {
					err_rollback := tx.Rollback()
					if err_rollback != nil {
						return nil, fmt.Errorf("rollback Modbus failed: %s", err_rollback.Error())
					}
					return nil, fmt.Errorf("update Modbus failed: %s", err.Error())
				}
			} else {
				_, err = tx.Modbus.Update().
					Where(modbus.IDEQ(FrameMeta.FrameUID)).
					SetTid(frameInfo.FrameLibs.ModbusInfo.TID).
					SetPid(frameInfo.FrameLibs.ModbusInfo.PID).
					SetLen(frameInfo.FrameLibs.ModbusInfo.Len).
					SetUID(frameInfo.FrameLibs.ModbusInfo.UID).
					SetFc(frameInfo.FrameLibs.ModbusInfo.FC).
					Save(l.ctx)
				if err != nil {
					err_rollback := tx.Rollback()
					if err_rollback != nil {
						return nil, fmt.Errorf("rollback Modbus failed: %s", err_rollback.Error())
					}
					return nil, fmt.Errorf("update Modbus failed: %s", err.Error())
				}
			}
		case utils.FrameTypeUart:
			if protocolIndex == -1 {
				_, err = tx.Uart.Create().
					SetID(FrameMeta.FrameUID).
					SetHeader(frameInfo.FrameLibs.UartInfo.Header).
					SetAddr(frameInfo.FrameLibs.UartInfo.Addr).
					SetCmd(frameInfo.FrameLibs.UartInfo.Cmd).
					SetTail(frameInfo.FrameLibs.UartInfo.Tail).
					SetDeviceID(queryDevice.ID).
					Save(l.ctx)
				if err != nil {
					err_rollback := tx.Rollback()
					if err_rollback != nil {
						return nil, fmt.Errorf("rollback Uart failed: %s", err_rollback.Error())
					}
					return nil, fmt.Errorf("update Uart failed: %s", err.Error())
				}
			} else {
				_, err = tx.Uart.Update().
					Where(uart.IDEQ(FrameMeta.FrameUID)).
					SetHeader(frameInfo.FrameLibs.UartInfo.Header).
					SetAddr(frameInfo.FrameLibs.UartInfo.Addr).
					SetCmd(frameInfo.FrameLibs.UartInfo.Cmd).
					SetTail(frameInfo.FrameLibs.UartInfo.Tail).
					Save(l.ctx)
				if err != nil {
					err_rollback := tx.Rollback()
					if err_rollback != nil {
						return nil, fmt.Errorf("rollback Uart failed: %s", err_rollback.Error())
					}
					return nil, fmt.Errorf("update Uart failed: %s", err.Error())
				}
			}
		case utils.FrameTypeUdp:
			if protocolIndex == -1 {
				_, err = tx.Udp.Create().
					SetID(FrameMeta.FrameUID).
					SetType(frameInfo.FrameLibs.UdpInfo.Type).
					SetHeader(frameInfo.FrameLibs.UdpInfo.Header).
					SetTypeID(frameInfo.FrameLibs.UdpInfo.TypeID).
					SetDeviceID(queryDevice.ID).
					Save(l.ctx)
				if err != nil {
					err_rollback := tx.Rollback()
					if err_rollback != nil {
						return nil, fmt.Errorf("rollback Udp failed: %s", err_rollback.Error())
					}
					return nil, fmt.Errorf("update Udp failed: %s", err.Error())
				}
			} else {
				_, err = tx.Udp.Update().
					Where(udp.IDEQ(FrameMeta.FrameUID)).
					SetType(frameInfo.FrameLibs.UdpInfo.Type).
					SetHeader(frameInfo.FrameLibs.UdpInfo.Header).
					SetTypeID(frameInfo.FrameLibs.UdpInfo.TypeID).
					Save(l.ctx)
				if err != nil {
					err_rollback := tx.Rollback()
					if err_rollback != nil {
						return nil, fmt.Errorf("rollback Udp failed: %s", err_rollback.Error())
					}
					return nil, fmt.Errorf("update Udp failed: %s", err.Error())
				}
			}
		}
	}
	//提交事务
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("commit transaction failed: %s", err.Error())
	}
	//返回响应
	return &types.CreateDeviceFrameResponse{
		DeviceMeta:       DeviceMeta,
		DeviceWorkStatus: *DeviceWorkStatus,
	}, nil
}

func data2String(datas []types.DataDef) (string, string, string, error) {
	if len(datas) == 0 {
		return "", "", "", errors.New("empty input data")
	}

	var dataIndexs string
	var dataNames string
	var dataTypes string

	for i, data := range datas {
		if data.Index == "" || data.Name == "" || data.Type == "" {
			return "", "", "", errors.New("invalid data fields")
		}

		if i == len(datas)-1 {
			dataIndexs += data.Index
			dataNames += data.Name
			dataTypes += data.Type
		} else {
			dataIndexs += data.Index + ","
			dataNames += data.Name + ","
			dataTypes += data.Type + ","
		}
	}

	return dataIndexs, dataNames, dataTypes, nil
}

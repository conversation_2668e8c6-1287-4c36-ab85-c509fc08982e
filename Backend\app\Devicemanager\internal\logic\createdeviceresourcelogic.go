package logic

import (
	"context"
	"fmt"

	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"GCF/app/Devicemanager/internal/logic/utils"
	"strings"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDeviceResourceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDeviceResourceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDeviceResourceLogic {
	return &CreateDeviceResourceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDeviceResourceLogic) CreateDeviceResource(req *types.CreateDeviceResourceRequest) (resp *types.CreateDeviceResourceResponse, err error) {
	//生成UID
	uid := uuid.New().String()
	//开启事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()

	//生成DeviceMeta
	DeviceMeta := &types.DeviceMeta{
		DeviceUID: uid,
	}
	//生成DeviceWorkStatus
	//TODO: 根据设备类型设置健康状态
	HealthStatus := utils.HealthStatusReady
	WorkMode := utils.WorkModeCentralized
	DeviceWorkStatus := &types.DeviceWorkStatus{
		HealthStatus: HealthStatus,
		WorkMode:     WorkMode,
	}

	cpuResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.CPU)
	if err != nil {
		return nil, err
	}
	gpuResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.GPU)
	if err != nil {
		return nil, err
	}
	diskResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.Disk)
	if err != nil {
		return nil, err
	}
	memResource, err := utils.ResourceDefToString(&req.DeviceResourceInfo.Mem)
	if err != nil {
		return nil, err
	}

	//插入数据库
	_, err = tx.Device.Create().
		SetID(DeviceMeta.DeviceUID).
		SetName(req.DeviceResourceInfo.Hostname).
		SetIP(req.DeviceResourceInfo.IP).
		SetOs(req.DeviceResourceInfo.OS).
		SetType(req.DeviceResourceInfo.Type).
		SetCPU(cpuResource).
		SetGpu(gpuResource).
		SetMemory(memResource).
		SetDisk(diskResource).
		SetProtocol(strings.Join(req.DeviceResourceInfo.Protocol, ",")).
		SetWorkmode(DeviceWorkStatus.WorkMode).
		SetStatus(DeviceWorkStatus.HealthStatus).
		Save(l.ctx)
	if err != nil {
		err_rollback := tx.Rollback()
		if err_rollback != nil {
			return nil, fmt.Errorf("rollback Device failed: %s", err_rollback.Error())
		}
		return nil, fmt.Errorf("update Device failed: %s", err.Error())
	}

	//添加Frame记录
	for _, protocal := range req.DeviceResourceInfo.Protocol {
		frameUID := uuid.New().String()
		FrameMeta := &types.FrameMeta{
			FrameUID:  frameUID,
			FrameType: protocal,
		}
		DeviceMeta.FrameMetas = append(DeviceMeta.FrameMetas, *FrameMeta)
		switch protocal {
		case utils.FrameTypeModbus:
			_, err = tx.Modbus.Create().
				SetID(FrameMeta.FrameUID).
				SetDeviceID(DeviceMeta.DeviceUID).
				Save(l.ctx)
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					return nil, fmt.Errorf("rollback Modbus failed: %s", err_rollback.Error())
				}
				return nil, fmt.Errorf("update Modbus failed: %s", err.Error())
			}
		case utils.FrameTypeUart:
			_, err = tx.Uart.Create().
				SetID(FrameMeta.FrameUID).
				SetDeviceID(DeviceMeta.DeviceUID).
				Save(l.ctx)
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					return nil, fmt.Errorf("rollback Uart failed: %s", err_rollback.Error())
				}
				return nil, fmt.Errorf("update Uart failed: %s", err.Error())
			}
		case utils.FrameTypeUdp:
			_, err = tx.Udp.Create().
				SetID(FrameMeta.FrameUID).
				SetDeviceID(DeviceMeta.DeviceUID).
				Save(l.ctx)
			if err!= nil {
				err_rollback := tx.Rollback()
				if err_rollback!= nil {
					return nil, fmt.Errorf("rollback Udp failed: %s", err_rollback.Error())		
				}
				return nil, fmt.Errorf("update Udp failed: %s", err.Error())	
			}
		}
	}

	tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("commit transaction failed: %s", err.Error())
	}
	return &types.CreateDeviceResourceResponse{
		DeviceMeta:       *DeviceMeta,
		DeviceWorkStatus: *DeviceWorkStatus,
	}, nil

}

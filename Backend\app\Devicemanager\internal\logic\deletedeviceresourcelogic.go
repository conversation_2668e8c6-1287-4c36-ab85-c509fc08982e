package logic

import (
	"context"
	"fmt"
	"strings"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDeviceResourceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDeviceResourceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDeviceResourceLogic {
	return &DeleteDeviceResourceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDeviceResourceLogic) DeleteDeviceResource(req *types.DeleteDeviceResourceRequest) (resp *types.DeleteDeviceResourceResponse, err error) {
	// 根据DeviceUID查询设备
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("failed to get queryDevice %s: %v", req.DeviceUID, err)
		return nil, err
	}

	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}

	//TODO: 根据状态决定是否可以删除
	if DeviceWorkStatus.HealthStatus == utils.HealthStatusRunning {
		return nil, fmt.Errorf("device is running, please stop it first")
	}

	DeviceResourceInfo, err := utils.GetDeviceResourceInfo(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}

	//获取帧info数据和deviceMeta数据
	Protocols := strings.Split(queryDevice.Protocol, ",")
	DeviceMeta, _, err := utils.GetFrameInfosAndDeviceMeta(l.ctx, l.svcCtx, Protocols, queryDevice.ID)
	if err != nil {
		return nil, err
	}

	//开启事务
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		// 根据事务的结果提交或回滚。
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()

	//删除Frame记录
	for _, frameMeta := range DeviceMeta.FrameMetas {
		switch frameMeta.FrameType {
		case utils.FrameTypeModbus:
			_, err = tx.Modbus.Delete().Where(modbus.DeviceIDEQ(req.DeviceUID)).Exec(l.ctx)
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					return nil, fmt.Errorf("rollback Modbus failed: %s", err_rollback.Error())
				}
				return nil, fmt.Errorf("delete Modbus failed: %s", err.Error())
			}
		case utils.FrameTypeUart:
			_, err = tx.Uart.Delete().Where(uart.DeviceIDEQ(req.DeviceUID)).Exec(l.ctx)
			if err != nil {
				err_rollback := tx.Rollback()
				if err_rollback != nil {
					return nil, fmt.Errorf("rollback Uart failed: %s", err_rollback.Error())
				}
				return nil, fmt.Errorf("delete Uart failed: %s", err.Error())
			}
		case utils.FrameTypeUdp:
			_, err = tx.Udp.Delete().Where(udp.DeviceIDEQ(req.DeviceUID)).Exec(l.ctx)
			if err!= nil {
				err_rollback := tx.Rollback()
				if err_rollback!= nil {
					return nil, fmt.Errorf("rollback Udp failed: %s", err_rollback.Error())
				}
				return nil, fmt.Errorf("delete Udp failed: %s", err.Error())
			}
		}
	}

	//删除设备数据库
	_, err = tx.Device.Delete().Where(device.IDEQ(req.DeviceUID)).Exec(l.ctx)
	if err != nil {
		err_rollback := tx.Rollback()
		if err_rollback != nil {
			return nil, fmt.Errorf("rollback device failed: %s", err_rollback.Error())
		}
		return nil, fmt.Errorf("update device failed: %s", err.Error())
	}

	//提交事务
	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("commit transaction failed: %s", err.Error())
	}

	//返回响应
	return &types.DeleteDeviceResourceResponse{
		DeviceMeta:         *DeviceMeta,
		DeviceWorkStatus:   *DeviceWorkStatus,
		DeviceResourceInfo: *DeviceResourceInfo,
	}, nil
}

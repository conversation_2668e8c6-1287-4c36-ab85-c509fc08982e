package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type FetchDeviceDataLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFetchDeviceDataLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FetchDeviceDataLogic {
	return &FetchDeviceDataLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FetchDeviceDataLogic) FetchDeviceData(req *types.FetchDeviceDataRequest) (resp *types.FetchDeviceDataResponse, err error) {
	// 查询设备信息
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(req.DeviceUID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("failed to get queryDevice %s: %v", req.DeviceUID, err)
		return nil, err
	}
	DeviceWorkStatus, err := utils.GetDeviceWorkStatus(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	frameInfos, err := utils.FetchDeviceData() 
	if err!= nil {
		return nil, err
	}
	resp = &types.FetchDeviceDataResponse{
		DeviceWorkStatus: *DeviceWorkStatus,
		FrameInfos:       frameInfos,
	}
	return resp, nil
}

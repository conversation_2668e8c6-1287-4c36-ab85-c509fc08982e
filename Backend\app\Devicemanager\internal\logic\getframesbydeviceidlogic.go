package logic

import (
	"context"

	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFramesByDeviceIDLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetFramesByDeviceIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFramesByDeviceIDLogic {
	return &GetFramesByDeviceIDLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetFramesByDeviceIDLogic) GetFramesByDeviceID(in *devicemanager.GetFramesByDeviceIDRequest) (*devicemanager.GetFramesByDeviceIDResponse, error) {
	//TODO: 可以修改为检查缓存
	queryDevice, err := l.svcCtx.Db.Device.Query().Where(device.IDEQ(in.DeviceId)).First(l.ctx)
	if err != nil {
		return nil, err
	}
	DeviceResourceInfo, err := utils.GetDeviceResourceInfo(l.ctx, l.svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	Protocols := DeviceResourceInfo.Protocol
	frameMap, err := utils.GetFrameInfos(l.ctx, l.svcCtx, Protocols, in.DeviceId)
	if err != nil {
		return nil, err
	}
	frameInfos := make([]*devicemanager.FrameInfo, 0, len(frameMap))
	for protocol, frameInfo := range frameMap {
		if protocol == utils.FrameTypeModbus {
			frameInfos = append(frameInfos, &devicemanager.FrameInfo{
				FrameMeta: &devicemanager.FrameMeta{
					FrameUid:  frameInfo.FrameMeta.FrameUID,
					FrameType: frameInfo.FrameMeta.FrameType,
				},
				FrameLib: &devicemanager.FrameLibs{
					ModbusInfo: &devicemanager.ModbusInfo{
						Tid: frameInfo.FrameLibs.ModbusInfo.TID,
						Pid: frameInfo.FrameLibs.ModbusInfo.PID,
						Len: frameInfo.FrameLibs.ModbusInfo.Len,
						Uid: frameInfo.FrameLibs.ModbusInfo.UID,
						Fc:  frameInfo.FrameLibs.ModbusInfo.FC,
						// Datas: frameInfo.FrameLibs.ModbusInfo.Datas,
					},
				},
			})
		} else if protocol == utils.FrameTypeUart {
			frameInfos = append(frameInfos, &devicemanager.FrameInfo{
				FrameMeta: &devicemanager.FrameMeta{
					FrameUid:  frameInfo.FrameMeta.FrameUID,
					FrameType: frameInfo.FrameMeta.FrameType,
				},
				FrameLib: &devicemanager.FrameLibs{
					UartInfo: &devicemanager.UartInfo{
						Header: frameInfo.FrameLibs.UartInfo.Header,
						Addr:   frameInfo.FrameLibs.UartInfo.Addr,
						Cmd:    frameInfo.FrameLibs.UartInfo.Cmd,
						Tail:   frameInfo.FrameLibs.UartInfo.Tail,
						// Datas:  frameInfo.FrameLibs.UartInfo.Datas,
					},
				},
			})
		}	else if protocol == utils.FrameTypeUdp {
			frameInfos = append(frameInfos, &devicemanager.FrameInfo{
				FrameMeta: &devicemanager.FrameMeta{
					FrameUid:  frameInfo.FrameMeta.FrameUID,
					FrameType: frameInfo.FrameMeta.FrameType,
				},
				FrameLib: &devicemanager.FrameLibs{
					UdpInfo: &devicemanager.UdpInfo{
						Type:   frameInfo.FrameLibs.UdpInfo.Type,
						Header: frameInfo.FrameLibs.UdpInfo.Header,
						TypeId: frameInfo.FrameLibs.UdpInfo.TypeID,	
					},	
				},	
			})	
		}
	}
	return &devicemanager.GetFramesByDeviceIDResponse{
		Frames: frameInfos,
	}, nil
}

package logic

import (
	"context"
	"errors"
	"strings"

	"GCF/app/Devicemanager/internal/ent/device"
	"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDeviceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDeviceLogic {
	return &ListDeviceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDeviceLogic) ListDevice(req *types.ListDeviceRequest) (resp *types.ListDeviceResponse, err error) {
	//查询设备
	queryDevice := l.svcCtx.Db.Device.Query()
	// 根据请求参数添加查询条件
	if req != nil {
		// 根据DeviceUID添加条件
		if req.DeviceUID != "" {
			queryDevice = queryDevice.Where(device.IDEQ(req.DeviceUID))
		}
		// 根据FrameType添加条件 (modbus|uart)

		//TODO:可能有问题
		if req.FrameMetas != nil {
			for _, frameMeta := range req.FrameMetas {
				queryDevice = queryDevice.Where(device.ProtocolContains(frameMeta.FrameType))
			}
		}
		// 根据HealthStatus添加条件 (init|ready|running|pending|error)
		if req.HealthStatus != "" {
			queryDevice = queryDevice.Where(device.StatusEQ(req.HealthStatus))
		}
	}
	devices, err := queryDevice.All(l.ctx)
	if err != nil {
		return nil, err
	}

	resp = &types.ListDeviceResponse{
		Devices: make([]types.ListDevice, 0, len(devices)), // 使用切片初始化
	}

	for _, device := range devices {
		listDevice, err := utils.GetDevicesInfo(l.ctx, l.svcCtx, device)
		if err != nil {
			return nil, err
		}
		resp.Devices = append(resp.Devices, *listDevice)
	}
	return resp, nil
}

func string2Data(index, name, dataType string) ([]types.DataDef, error) {
	var datas []types.DataDef
	if index == "" || name == "" || dataType == "" {
		return nil, errors.New("empty input parameters")
	}
	dataindex := strings.Split(index, ",")
	dataname := strings.Split(name, ",")
	datatypes := strings.Split(dataType, ",")
	for i, _ := range dataindex {
		datas = append(datas, types.DataDef{Index: dataindex[i], Name: dataname[i], Type: datatypes[i]})
	}
	return datas, nil
}

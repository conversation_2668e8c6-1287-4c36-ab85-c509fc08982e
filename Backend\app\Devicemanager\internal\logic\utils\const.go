package utils

// 设备类型常量
const (
	DeviceTypeFan     = "fan"
	DeviceTypeMotor   = "motor"
	DeviceTypeVirtual = "virtual"
	DeviceTypePC      = "pc"
	DeviceTypeEdge    = "edge"
)

// 健康状态常量
const (
	HealthStatusInit    = "init"
	HealthStatusReady   = "ready"
	HealthStatusRunning = "running"
	HealthStatusPending = "pending"
	HealthStatusError   = "error"
)

// 工作模式常量
const (
	WorkModeDistributed = "distributed"
	WorkModeCentralized = "centralized"
)

// 帧类型常量
const (
	FrameTypeModbus = "modbus"
	FrameTypeUart   = "uart"
	FrameTypeUdp    = "udp"
)

package utils

import (
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	//"GCF/app/Devicemanager/internal/logic/utils"
	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/types"
	"GCF/pkg/errx"

	"github.com/google/uuid"
)

func UdpControl(reqFrame *types.FrameInfo, inFrame *devicemanager.FrameInfo, funType string, ip string) (respFrame *types.FrameInfo, outFrame *devicemanager.FrameInfo, err error) {
	if funType == "api" {
		frameType := reqFrame.FrameMeta.FrameType
		udpInfoOfControl := reqFrame.FrameLibs.UdpInfo
		if udpInfoOfControl.Type == "" || udpInfoOfControl.Header == "" || udpInfoOfControl.TypeID == "" {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("lack of udp headers %v", err))
		}
		typeHex := strings.TrimPrefix(udpInfoOfControl.Type, "0x")
		if typeHex != "0000" {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("type format error %v", err))
		}
		// 解析SEQ/ACK字段 (控制帧为0x0000)
		typeBytes, err := hex.DecodeString(typeHex)
		if err != nil {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("type format error %v", err))
		}
		headerHex := strings.TrimPrefix(udpInfoOfControl.Header, "0x")
		// 解析HEADER字段
		headerBytes, err := hex.DecodeString(headerHex)
		if err != nil {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("header format error %v", err))
		}
		headerBytes = ReverseBytes(headerBytes)
		// 解析SEQ/ACK ID字段
		typeIDBytes, err := hex.DecodeString(strings.TrimPrefix(udpInfoOfControl.TypeID, "0x"))
		if err != nil {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("typeid format error %v", err))
		}
		typeIDBytes = ReverseBytes(typeIDBytes)
		// 处理ARG字段，根据HEADER类型处理不同的参数
		argBytes := make([]byte, 0)
		switch headerHex {
		case "0000": // 获取设备状态机当前状态
			if udpInfoOfControl.Datas != nil && len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0000, Datas must be empty %v", err))
			}
		case "0001": // 状态机控制指令
			if udpInfoOfControl.Datas == nil || len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0001, Datas must have other data %v", err))
			}
			ctrlBytes := make([]byte, 0)
			// 查找CTRL参数
			for _, data := range udpInfoOfControl.Datas {
				if data.Name == "CTRL" {
					ctrlBytes, err = String2bytes(data.Value, 32, 1, data.Type)
					if err != nil {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("ctrl format error %v", err))
					}
				} else {
					return nil, nil, fmt.Errorf("do not support other command %v", err)
				}
			}
			argBytes = append(argBytes, ctrlBytes...)
		case "0002": // CURRENT_LOOP控制参数获取
			if udpInfoOfControl.Datas != nil && len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0002, Datas must be empty %v", err))
			}
		case "0003": // CURRENT_LOOP控制参数设置
			if udpInfoOfControl.Datas == nil || len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0003, Datas must have other data %v", err))
			}
			// 处理MASK, Kp_d, Ki_d, Kp_q, Ki_q, integral_bound参数
			maskBytes, kpdBytes, kidBytes, kpqBytes, kiqBytes := make([]byte, 0), make([]byte, 0), make([]byte, 0), make([]byte, 0), make([]byte, 0)
			integralBytes := make([]byte, 0)

			for _, data := range udpInfoOfControl.Datas {
				switch data.Name {
				case "MASK":
					if data.Type == "int32" {
						maskBytes, err = String2bytes(data.Value, 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("MASK parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("MASK parameter type must be int32: %v", err))
					}
				case "Kp_d":
					if data.Type == "float" || data.Type == "SQ8.8" {
						// 将浮点数转换为SQ8.8定点数格式
						kpdBytes, err = String2bytes(data.Value, 16, 1, data.Type)
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp_d parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp_d parameter type must be float or SQ8.8: %v", err))
					}
				case "Ki_d":
					if data.Type == "float" || data.Type == "SQ8.8" {
						kidBytes, err = String2bytes(data.Value, 16, 1, data.Type)
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki_d parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki_d parameter type must be float or SQ8.8: %v", err))
					}
				case "Kp_q":
					if data.Type == "float" || data.Type == "SQ8.8" {
						kpqBytes, err = String2bytes(data.Value, 16, 1, data.Type)
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp_q parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp_q parameter type must be float or SQ8.8: %v", err))
					}
				case "Ki_q":
					if data.Type == "float" || data.Type == "SQ8.8" {
						kiqBytes, err = String2bytes(data.Value, 16, 1, data.Type)
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki_q parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki_q parameter type must be float or SQ8.8: %v", err))
					}
				case "integral_bound":
					if data.Type == "int32" {
						integralBytes, err = String2bytes(data.Value, 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("integral_bound parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("integral_bound parameter type must be int32: %v", err))
					}
				default:
					return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Unsupported parameter name: %s", data.Name))
				}
			}

			argBytes = append(argBytes, maskBytes...)
			argBytes = append(argBytes, kpdBytes...)
			argBytes = append(argBytes, kidBytes...)
			argBytes = append(argBytes, kpqBytes...)
			argBytes = append(argBytes, kiqBytes...)
			argBytes = append(argBytes, integralBytes...)
		case "0004": // CURRENT_LOOP设定值获取
			if udpInfoOfControl.Datas != nil && len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0004, Datas must be empty %v", err))
			}
		case "0005": // CURRENT_LOOP设定值设置
			if udpInfoOfControl.Datas == nil || len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0005, Datas must have other data %v", err))
			}
			// 处理Id_ref, Iq_ref参数
			idRefBytes := make([]byte, 0)
			iqRefBytes := make([]byte, 0)

			for _, data := range udpInfoOfControl.Datas {
				switch data.Name {
				case "id_ref":
					if data.Type == "int16" {
						idRefBytes, err = String2bytes(data.Value, 16, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("id_ref parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("id_ref parameter type must be int16: %v", err))
					}
				case "iq_ref":
					if data.Type == "int16" {
						iqRefBytes, err = String2bytes(data.Value, 16, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("iq_ref parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("iq_ref parameter type must be int16: %v", err))
					}
				default:
					return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Unsupported parameter name: %s", data.Name))
				}
			}

			argBytes = append(argBytes, idRefBytes...)
			argBytes = append(argBytes, iqRefBytes...)
		case "0006": // SPEED_LOOP控制参数获取
			if udpInfoOfControl.Datas != nil && len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0006, Datas must be empty %v", err))
			}
		case "0007": // SPEED_LOOP控制参数设置
			if udpInfoOfControl.Datas == nil || len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0007, Datas must have other data %v", err))
			}
			// 处理MASK, Kp, Ki, Kd, integral_bound参数
			maskBytes, kpBytes, kiBytes, kdBytes, integralBytes := make([]byte, 0), make([]byte, 0), make([]byte, 0), make([]byte, 0), make([]byte, 0)

			for _, data := range udpInfoOfControl.Datas {
				switch data.Name {
				case "MASK":
					if data.Type == "int32" {
						maskBytes, err = String2bytes(data.Value, 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("MASK parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("MASK parameter type must be int32: %v", err))
					}
				case "Kp":
					if data.Type == "float" || data.Type == "SQ8.8" {
						kpBytes, err = String2bytes(data.Value, 16, 1, data.Type)
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp parameter type must be float or SQ8.8: %v", err))
					}
				case "Ki":
					if data.Type == "float" || data.Type == "SQ8.8" {
						kiBytes, err = String2bytes(data.Value, 16, 1, data.Type)
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki parameter type must be float or SQ8.8: %v", err))
					}
				case "Kd":
					if data.Type == "float" || data.Type == "SQ8.8" {
						kdBytes, err = String2bytes(data.Value, 16, 1, data.Type)
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kd parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kd parameter type must be float or SQ8.8: %v", err))
					}
				case "integral_bound":
					if data.Type == "int32" {
						integralBytes, err = String2bytes(data.Value, 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("integral_bound parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("integral_bound parameter type must be int32: %v", err))
					}
				default:
					return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Unsupported parameter name: %s", data.Name))
				}
			}

			argBytes = append(argBytes, maskBytes...)
			argBytes = append(argBytes, kpBytes...)
			argBytes = append(argBytes, kiBytes...)
			argBytes = append(argBytes, kdBytes...)
			argBytes = append(argBytes, integralBytes...)
		case "0008": // SPEED_LOOP设定值获取
			if udpInfoOfControl.Datas != nil && len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0008, Datas must be empty %v", err))
			}
		case "0009": // SPEED_LOOP设定值设置
			if udpInfoOfControl.Datas == nil || len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0009, Datas must have other data %v", err))
			}
			// 处理speed_ref参数
			speedRefBytes := make([]byte, 0)

			for _, data := range udpInfoOfControl.Datas {
				switch data.Name {
				case "speed_ref":
					if data.Type == "int16" {
						speedRefBytes, err = String2bytes(data.Value, 16, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("speed_ref parameter format error: %v", err))
						}
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("speed_ref parameter type must be int16: %v", err))
					}
				default:
					return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Unsupported parameter name: %s", data.Name))
				}
			}

			argBytes = append(argBytes, speedRefBytes...)
		case "000a", "000A": // 数据协议状态获取
			if udpInfoOfControl.Datas != nil && len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 000A, Datas must be empty %v", err))
			}
		case "000b", "000B": // 数据协议状态设置
			if udpInfoOfControl.Datas == nil || len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 000B, Datas must have other data %v", err))
			}
			// 处理TRANSFER_CTRL参数
			transferCtrlBytes := make([]byte, 0)

			for _, data := range udpInfoOfControl.Datas {
				switch data.Name {
				case "TRANSFER_CTRL":
					var intValue uint32
					if data.Type == "binary" {
						// 处理二进制格式
						ctrlBin := strings.TrimPrefix(data.Value, "0b")
						tmpValue, err1 := strconv.ParseUint(ctrlBin, 2, 32)
						if err1 != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("TRANSFER_CTRL binary parameter format error: %v", err1))
						}
						intValue = uint32(tmpValue)
						// 检查值是否有效
						if intValue != 1 && intValue != 2 && intValue != 4 {
							return nil, nil, errx.New(http.StatusBadRequest, "TRANSFER_CTRL parameter value invalid, valid values are 1(ALL), 2(STOP) or 4(DEBUG)")
						}
						tmpBytes := make([]byte, 4)
						binary.LittleEndian.PutUint32(tmpBytes, intValue)
						transferCtrlBytes = tmpBytes
					} else {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("TRANSFER_CTRL parameter type must be binary: %v", err))
					}
				default:
					return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Unsupported parameter name: %s", data.Name))
				}
			}

			argBytes = append(argBytes, transferCtrlBytes...)
		default:
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Unsupported HEADER type: %s", headerHex))
		}
		udpData := make([]byte, 0)
		udpData = append(udpData, typeBytes...)
		udpData = append(udpData, headerBytes...)
		udpData = append(udpData, typeIDBytes...)
		udpData = append(udpData, argBytes...)
		// 确保IP地址格式正确
		serverAddr := ip
		if !strings.Contains(serverAddr, ":") {
			serverAddr = serverAddr + ":8"
		}
		// 发送UDP请求并接收响应
		response, err := CreateNetConnect(frameType, serverAddr, udpData)
		if err != nil {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Failed to send UDP request: %v", err))
		}
		// 解析响应包
		if len(response) < 6 {
			return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
		}
		// 将二进制响应转换为十六进制字符串
		respType := "0x" + hex.EncodeToString(response[:2])
		respHeader := "0x" + hex.EncodeToString(ReverseBytes(response[2:4]))
		respTypeId := "0x" + hex.EncodeToString(ReverseBytes(response[4:6]))

		if respType != "0x0001" {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Response type error, should be 0x0001, actual: %s", respType))
		}
		if respTypeId != udpInfoOfControl.TypeID {
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Response TypeID error, should be same as request frame TypeID %s, actual: %s", udpInfoOfControl.Type, respTypeId))
		}

		// 构造响应的FrameInfo
		respFrame := &types.FrameInfo{
			FrameMeta: types.FrameMeta{
				FrameUID:  uuid.New().String(),
				FrameType: frameType,
			},
			FrameLibs: types.FrameLibs{
				UdpInfo: types.UdpInfo{
					Type:   respType,
					Header: respHeader,
					TypeID: respTypeId,
				},
			},
		}

		// 根据不同的HEADER类型，解析不同的响应数据
		if len(response) > 6 {
			respDatas := make([]types.DataDef, 0)
			headerHex := strings.TrimPrefix(respHeader, "0x")
			switch headerHex {
			case "0000": // 获取设备状态机当前状态
				if len(response) >= 10 {
					stateHex := hex.EncodeToString(ReverseBytes(response[6:10]))
					// 将十六进制转换为整数
					stateInt, err := strconv.ParseUint(stateHex, 16, 32)
					if err != nil {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Failed to parse STATE value: %v", err))
					}
					// 将整数转换为二进制字符串，并保持32位格式
					stateBin := fmt.Sprintf("0b%032b", stateInt)
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "STATE",
						Type:  "binary",
						Value: stateBin,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0001": // 状态机控制指令
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(response[6:10])
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "ERROR",
						Type:  "hex",
						Value: errorValue,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0002": // CURRENT_LOOP控制参数获取
				if len(response) >= 18 {
					// 解析2字节的SQ8.8参数
					kpdHex := hex.EncodeToString(ReverseBytes(response[6:8]))
					kidHex := hex.EncodeToString(ReverseBytes(response[8:10]))
					kpqHex := hex.EncodeToString(ReverseBytes(response[10:12]))
					kiqHex := hex.EncodeToString(ReverseBytes(response[12:14]))
					// 解析4字节的INT32参数
					integralBoundHex := hex.EncodeToString(ReverseBytes(response[14:18]))

					// 转换为SQ8.8和INT32值
					kpdInt, _ := strconv.ParseInt(kpdHex, 16, 16)
					kidInt, _ := strconv.ParseInt(kidHex, 16, 16)
					kpqInt, _ := strconv.ParseInt(kpqHex, 16, 16)
					kiqInt, _ := strconv.ParseInt(kiqHex, 16, 16)
					integralBoundInt, _ := strconv.ParseInt(integralBoundHex, 16, 32)

					// 将SQ8.8格式转换为浮点数
					kpdFloat := float64(kpdInt) / 256.0
					kidFloat := float64(kidInt) / 256.0
					kpqFloat := float64(kpqInt) / 256.0
					kiqFloat := float64(kiqInt) / 256.0

					// 格式化为字符串
					kpd := fmt.Sprintf("%.4f", kpdFloat)
					kid := fmt.Sprintf("%.4f", kidFloat)
					kpq := fmt.Sprintf("%.4f", kpqFloat)
					kiq := fmt.Sprintf("%.4f", kiqFloat)
					integralBound := fmt.Sprintf("%d", integralBoundInt)

					respDatas = append(respDatas,
						types.DataDef{Index: "0", Name: "Kp_d", Type: "SQ8.8", Value: kpd},
						types.DataDef{Index: "1", Name: "Ki_d", Type: "SQ8.8", Value: kid},
						types.DataDef{Index: "2", Name: "Kp_q", Type: "SQ8.8", Value: kpq},
						types.DataDef{Index: "3", Name: "Ki_q", Type: "SQ8.8", Value: kiq},
						types.DataDef{Index: "4", Name: "integral_bound", Type: "int32", Value: integralBound},
					)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0003": // CURRENT_LOOP控制参数设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(ReverseBytes(response[6:10]))
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "ERROR",
						Type:  "hex",
						Value: errorValue,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0004": // CURRENT_LOOP设定值获取
				if len(response) >= 10 {
					// 解析2字节的INT16参数
					idRefHex := hex.EncodeToString(ReverseBytes(response[6:8]))
					iqRefHex := hex.EncodeToString(ReverseBytes(response[8:10]))

					// 转换为INT16值
					idRefInt, _ := strconv.ParseInt(idRefHex, 16, 16)
					iqRefInt, _ := strconv.ParseInt(iqRefHex, 16, 16)

					// 格式化为字符串
					idRef := fmt.Sprintf("%d", idRefInt)
					iqRef := fmt.Sprintf("%d", iqRefInt)

					respDatas = append(respDatas,
						types.DataDef{Index: "0", Name: "Id_ref", Type: "int16", Value: idRef},
						types.DataDef{Index: "1", Name: "Iq_ref", Type: "int16", Value: iqRef},
					)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0005": // CURRENT_LOOP设定值设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(ReverseBytes(response[6:10]))
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "ERROR",
						Type:  "hex",
						Value: errorValue,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0006": // SPEED_LOOP控制参数获取
				if len(response) >= 16 {
					// 解析2字节的SQ8.8参数
					kpHex := hex.EncodeToString(ReverseBytes(response[6:8]))
					kiHex := hex.EncodeToString(ReverseBytes(response[8:10]))
					kdHex := hex.EncodeToString(ReverseBytes(response[10:12]))
					// 解析4字节的INT32参数
					integralBoundHex := hex.EncodeToString(ReverseBytes(response[12:16]))

					// 转换为SQ8.8和INT32值
					kpInt, _ := strconv.ParseInt(kpHex, 16, 16)
					kiInt, _ := strconv.ParseInt(kiHex, 16, 16)
					kdInt, _ := strconv.ParseInt(kdHex, 16, 16)
					integralBoundInt, _ := strconv.ParseInt(integralBoundHex, 16, 32)

					// 将SQ8.8格式转换为浮点数
					kpFloat := float64(kpInt) / 256.0
					kiFloat := float64(kiInt) / 256.0
					kdFloat := float64(kdInt) / 256.0

					// 格式化为字符串
					kp := fmt.Sprintf("%.4f", kpFloat)
					ki := fmt.Sprintf("%.4f", kiFloat)
					kd := fmt.Sprintf("%.4f", kdFloat)
					integralBound := fmt.Sprintf("%d", integralBoundInt)

					respDatas = append(respDatas,
						types.DataDef{Index: "0", Name: "Kp", Type: "SQ8.8", Value: kp},
						types.DataDef{Index: "1", Name: "Ki", Type: "SQ8.8", Value: ki},
						types.DataDef{Index: "2", Name: "Kd", Type: "SQ8.8", Value: kd},
						types.DataDef{Index: "3", Name: "integral_bound", Type: "int32", Value: integralBound},
					)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0007": // SPEED_LOOP控制参数设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(ReverseBytes(response[6:10]))
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "ERROR",
						Type:  "hex",
						Value: errorValue,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0008": // SPEED_LOOP设定值获取
				if len(response) >= 8 {
					// 解析2字节的INT16参数
					speedRefHex := hex.EncodeToString(ReverseBytes(response[6:8]))

					// 转换为INT16值
					speedRefInt, _ := strconv.ParseInt(speedRefHex, 16, 16)

					// 格式化为字符串
					speedRef := fmt.Sprintf("%d", speedRefInt)

					respDatas = append(respDatas,
						types.DataDef{Index: "0", Name: "speed_ref", Type: "int16", Value: speedRef},
					)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0009": // SPEED_LOOP设定值设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(ReverseBytes(response[6:10]))
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "ERROR",
						Type:  "hex",
						Value: errorValue,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "000a", "000A": // 数据协议状态获取
				if len(response) >= 10 {
					transferStateHex := hex.EncodeToString(ReverseBytes(response[6:10]))

					// 转换为整数
					transferStateInt, _ := strconv.ParseUint(transferStateHex, 16, 32)

					// 将整数转换为二进制字符串
					transferStateBin := fmt.Sprintf("0b%08b", transferStateInt)
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "TRANSFER_STATE",
						Type:  "binary",
						Value: transferStateBin,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "000b", "000B": // 数据协议状态设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(ReverseBytes(response[6:10]))
					respDatas = append(respDatas, types.DataDef{
						Index: "0",
						Name:  "ERROR",
						Type:  "hex",
						Value: errorValue,
					})
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			}

			// 将解析的数据添加到响应中
			if len(respDatas) > 0 {
				respFrame.FrameLibs.UdpInfo.Datas = respDatas
			}
		}
		return respFrame, nil, nil
	} else {
		udpInfoOfControl := inFrame.FrameLib.UdpInfo
		frameType := inFrame.FrameMeta.FrameType
		if udpInfoOfControl == nil {
			return nil, nil, fmt.Errorf("udpInfoOfControl is nil")
		}
		typeHex := strings.TrimPrefix(udpInfoOfControl.Type, "0x")
		if typeHex != "0000" {
			return nil, nil, fmt.Errorf("type只能为请求帧0x0000: %v", err)
		}
		typeBytes, err := hex.DecodeString(typeHex)
		if err != nil {
			return nil, nil, fmt.Errorf("type格式错误: %v", err)
		}
		headerHex := strings.TrimPrefix(udpInfoOfControl.Header, "0x")
		headerBytes, err := hex.DecodeString(headerHex)
		if err != nil {
			return nil, nil, fmt.Errorf("header格式错误: %v", err)
		}
		typeIDBytes, err := hex.DecodeString(strings.TrimPrefix(udpInfoOfControl.TypeId, "0x"))
		if err != nil {
			return nil, nil, fmt.Errorf("typeID格式错误: %v", err)
		}
		argBytes := make([]byte, 0)
		switch headerHex {
		case "0000": // 获取设备状态机当前状态
			if len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, fmt.Errorf("header为0000时无其他数据位 %v", err)
			}
		case "0001": // 状态机控制指令
			if len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, fmt.Errorf("header为0001时必须有其他数据位 %v", err)
			}
			ctrlBytes := make([]byte, 0)
			// 修改为处理二进制CTRL参数
			ctrlBytes, err = String2bytes(udpInfoOfControl.Datas, 32, 1, "binary")
			if err != nil {
				return nil, nil, fmt.Errorf("CTRL二进制参数格式错误: %v", err)
			}
			argBytes = append(argBytes, ctrlBytes...)
		case "0002": // CURRENT_LOOP控制参数获取
			if len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, fmt.Errorf("header为0002时无其他数据位 %v", err)
			}
		case "0003": // CURRENT_LOOP控制参数设置
			if len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0003, Datas must have other data %v", err))
			}
			// 处理MASK, Kp_d, Ki_d, Kp_q, Ki_q, integral_bound参数
			var dataParams []map[string]string
			err = json.Unmarshal([]byte(udpInfoOfControl.Datas), &dataParams)
			if err != nil {
				return nil, nil, fmt.Errorf("解析参数JSON失败: %v", err)
			}

			maskBytes := make([]byte, 0)
			kpdBytes := make([]byte, 0)
			kidBytes := make([]byte, 0)
			kpqBytes := make([]byte, 0)
			kiqBytes := make([]byte, 0)
			integralBytes := make([]byte, 0)

			for _, param := range dataParams {
				switch param["name"] {
				case "MASK":
					if param["type"] == "int32" {
						maskBytes, err = String2bytes(param["value"], 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("MASK parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("MASK参数类型必须为int32")
					}
				case "Kp_d":
					if param["type"] == "float" || param["type"] == "SQ8.8" {
						// 将浮点数转换为SQ8.8定点数格式
						kpdBytes, err = String2bytes(param["value"], 16, 1, "SQ8.8")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp_d parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("Kp_d参数类型必须为float或SQ8.8")
					}
				case "Ki_d":
					if param["type"] == "float" || param["type"] == "SQ8.8" {
						kidBytes, err = String2bytes(param["value"], 16, 1, "SQ8.8")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki_d parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("Ki_d参数类型必须为float或SQ8.8")
					}
				case "Kp_q":
					if param["type"] == "float" || param["type"] == "SQ8.8" {
						kpqBytes, err = String2bytes(param["value"], 16, 1, "SQ8.8")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp_q parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("Kp_q参数类型必须为float或SQ8.8")
					}
				case "Ki_q":
					if param["type"] == "float" || param["type"] == "SQ8.8" {
						kiqBytes, err = String2bytes(param["value"], 16, 1, "SQ8.8")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki_q parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("Ki_q参数类型必须为float或SQ8.8")
					}
				case "integral_bound":
					if param["type"] == "int32" {
						integralBytes, err = String2bytes(param["value"], 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("integral_bound parameter format error: %v", err))
						}
						// 检查是否为正数
						intValue, _ := strconv.ParseInt(param["value"], 10, 32)
						if intValue < 0 {
							return nil, nil, fmt.Errorf("integral_bound参数必须为正数")
						}
					} else {
						return nil, nil, fmt.Errorf("integral_bound参数类型必须为int32")
					}
				default:
					return nil, nil, fmt.Errorf("不支持的参数名称: %s", param["name"])
				}
			}

			argBytes = append(argBytes, maskBytes...)
			argBytes = append(argBytes, kpdBytes...)
			argBytes = append(argBytes, kidBytes...)
			argBytes = append(argBytes, kpqBytes...)
			argBytes = append(argBytes, kiqBytes...)
			argBytes = append(argBytes, integralBytes...)
		case "0004": // CURRENT_LOOP设定值获取
			if len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0004, Datas must be empty %v", err))
			}
		case "0005": // CURRENT_LOOP设定值设置
			if len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0005, Datas must have other data %v", err))
			}
			// 处理Id_ref, Iq_ref参数
			var dataParams []map[string]string
			err = json.Unmarshal([]byte(udpInfoOfControl.Datas), &dataParams)
			if err != nil {
				return nil, nil, fmt.Errorf("解析参数JSON失败: %v", err)
			}

			idRefBytes := make([]byte, 0)
			iqRefBytes := make([]byte, 0)

			for _, param := range dataParams {
				switch param["name"] {
				case "id_ref":
					if param["type"] == "int16" {
						idRefBytes, err = String2bytes(param["value"], 16, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("id_ref parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("id_ref参数类型必须为int16")
					}
				case "iq_ref":
					if param["type"] == "int16" {
						iqRefBytes, err = String2bytes(param["value"], 16, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("iq_ref parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("iq_ref参数类型必须为int16")
					}
				default:
					return nil, nil, fmt.Errorf("不支持的参数名称: %s", param["name"])
				}
			}

			argBytes = append(argBytes, idRefBytes...)
			argBytes = append(argBytes, iqRefBytes...)
		case "0006": // SPEED_LOOP控制参数获取
			if len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0006, Datas must be empty %v", err))
			}
		case "0007": // SPEED_LOOP控制参数设置
			if len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0007, Datas must have other data %v", err))
			}
			// 处理MASK, Kp, Ki, Kd, integral_bound参数
			var dataParams []map[string]string
			err = json.Unmarshal([]byte(udpInfoOfControl.Datas), &dataParams)
			if err != nil {
				return nil, nil, fmt.Errorf("解析参数JSON失败: %v", err)
			}

			maskBytes := make([]byte, 0)
			kpBytes := make([]byte, 0)
			kiBytes := make([]byte, 0)
			kdBytes := make([]byte, 0)
			integralBytes := make([]byte, 0)

			for _, param := range dataParams {
				switch param["name"] {
				case "MASK":
					if param["type"] == "int32" {
						maskBytes, err = String2bytes(param["value"], 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("MASK parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("MASK参数类型必须为int32")
					}
				case "Kp":
					if param["type"] == "float" || param["type"] == "SQ8.8" {
						kpBytes, err = String2bytes(param["value"], 16, 1, "SQ8.8")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kp parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("Kp参数类型必须为float或SQ8.8")
					}
				case "Ki":
					if param["type"] == "float" || param["type"] == "SQ8.8" {
						kiBytes, err = String2bytes(param["value"], 16, 1, "SQ8.8")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Ki parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("Ki参数类型必须为float或SQ8.8")
					}
				case "Kd":
					if param["type"] == "float" || param["type"] == "SQ8.8" {
						kdBytes, err = String2bytes(param["value"], 16, 1, "SQ8.8")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Kd parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("Kd参数类型必须为float或SQ8.8")
					}
				case "integral_bound":
					if param["type"] == "int32" {
						integralBytes, err = String2bytes(param["value"], 32, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("integral_bound parameter format error: %v", err))
						}
						// 检查是否为正数
						intValue, _ := strconv.ParseInt(param["value"], 10, 32)
						if intValue < 0 {
							return nil, nil, fmt.Errorf("integral_bound参数必须为正数")
						}
					} else {
						return nil, nil, fmt.Errorf("integral_bound参数类型必须为int32")
					}
				default:
					return nil, nil, fmt.Errorf("不支持的参数名称: %s", param["name"])
				}
			}

			argBytes = append(argBytes, maskBytes...)
			argBytes = append(argBytes, kpBytes...)
			argBytes = append(argBytes, kiBytes...)
			argBytes = append(argBytes, kdBytes...)
			argBytes = append(argBytes, integralBytes...)
		case "0008": // SPEED_LOOP设定值获取
			if len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0008, Datas must be empty %v", err))
			}
		case "0009": // SPEED_LOOP设定值设置
			if len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 0009, Datas must have other data %v", err))
			}
			// 处理speed_ref参数
			var dataParams []map[string]string
			err = json.Unmarshal([]byte(udpInfoOfControl.Datas), &dataParams)
			if err != nil {
				return nil, nil, fmt.Errorf("解析参数JSON失败: %v", err)
			}

			speedRefBytes := make([]byte, 0)

			for _, param := range dataParams {
				if param["name"] == "speed_ref" {
					if param["type"] == "int16" {
						speedRefBytes, err = String2bytes(param["value"], 16, 1, "int")
						if err != nil {
							return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("speed_ref parameter format error: %v", err))
						}
					} else {
						return nil, nil, fmt.Errorf("speed_ref参数类型必须为int16")
					}
				} else {
					return nil, nil, fmt.Errorf("不支持的参数名称: %s", param["name"])
				}
			}

			argBytes = append(argBytes, speedRefBytes...)
		case "000a", "000A": // 数据协议状态获取
			if len(udpInfoOfControl.Datas) > 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 000A, Datas must be empty %v", err))
			}
		case "000b", "000B": // 数据协议状态设置
			if len(udpInfoOfControl.Datas) == 0 {
				return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("When header is 000B, Datas must have other data %v", err))
			}
			// 处理TRANSFER_CTRL参数
			var dataParams []map[string]string
			err = json.Unmarshal([]byte(udpInfoOfControl.Datas), &dataParams)
			if err != nil {
				return nil, nil, fmt.Errorf("解析参数JSON失败: %v", err)
			}

			transferCtrlBytes := make([]byte, 0)

			for _, param := range dataParams {
				if param["name"] == "TRANSFER_CTRL" {
					if param["type"] == "binary" {
						// 使用工具函数处理二进制数据
						tempBytes := make([]byte, 4)
						tempBytes, err = String2bytes(param["value"], 32, 1, "binary")
						if err != nil {
							return nil, nil, fmt.Errorf("TRANSFER_CTRL二进制参数格式错误: %v", err)
						}

						// 检查值是否有效
						ctrlValue := uint8(tempBytes[0])
						if ctrlValue != 1 && ctrlValue != 2 && ctrlValue != 4 {
							return nil, nil, errx.New(http.StatusBadRequest, "TRANSFER_CTRL parameter value invalid, valid values are 1(ALL), 2(STOP) or 4(DEBUG)")
						}

						// 转换为4字节
						transferCtrlBytes = make([]byte, 4)
						transferCtrlBytes[3] = tempBytes[0] // 低位在右
					} else {
						return nil, nil, fmt.Errorf("TRANSFER_CTRL参数类型必须为binary")
					}
				} else {
					return nil, nil, fmt.Errorf("不支持的参数名称: %s", param["name"])
				}
			}

			argBytes = append(argBytes, transferCtrlBytes...)
		default:
			return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Unsupported HEADER type: %s", headerHex))
		}

		udpData := make([]byte, 0)
		udpData = append(udpData, typeBytes...)
		udpData = append(udpData, headerBytes...)
		udpData = append(udpData, typeIDBytes...)
		udpData = append(udpData, argBytes...)

		// 确保IP地址格式正确
		serverAddr := ip
		if !strings.Contains(serverAddr, ":") {
			serverAddr = serverAddr + ":8001"
		}

		response, err := CreateNetConnect(frameType, serverAddr, udpData)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to create net connect: %v", err)
		}

		// 解析响应包
		if len(response) < 6 {
			return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
		}

		respType := "0x" + hex.EncodeToString(response[:2])
		respHeader := "0x" + hex.EncodeToString(response[2:4])
		respTypeId := "0x" + hex.EncodeToString(response[4:6])

		outFrame = &devicemanager.FrameInfo{
			FrameMeta: &devicemanager.FrameMeta{
				FrameUid:  uuid.New().String(),
				FrameType: frameType,
			},
			FrameLib: &devicemanager.FrameLibs{
				UdpInfo: &devicemanager.UdpInfo{
					Type:   respType,
					Header: respHeader,
					TypeId: respTypeId,
				},
			},
		}

		// 根据不同的HEADER类型，解析不同的响应数据
		if len(response) > 6 {
			respDatas := ""
			headerHex := strings.TrimPrefix(respHeader, "0x")

			switch headerHex {
			case "0000": // 获取设备状态机当前状态
				if len(response) >= 10 {
					stateHex := hex.EncodeToString(response[6:10])
					// 将十六进制转换为整数
					stateInt, err := strconv.ParseUint(stateHex, 16, 32)
					if err != nil {
						return nil, nil, errx.New(http.StatusBadRequest, fmt.Sprintf("Failed to parse STATE value: %v", err))
					}
					// 将整数转换为二进制字符串，并保持32位格式
					stateBin := fmt.Sprintf("0b%032b", stateInt)
					respDatas = stateBin
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0001": // 状态机控制指令
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(response[6:10])
					respDatas = errorValue
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0002": // CURRENT_LOOP控制参数获取
				if len(response) >= 18 {
					// 解析2字节的SQ8.8参数
					kpdHex := hex.EncodeToString(response[6:8])
					kidHex := hex.EncodeToString(response[8:10])
					kpqHex := hex.EncodeToString(response[10:12])
					kiqHex := hex.EncodeToString(response[12:14])
					// 解析4字节的INT32参数
					integralBoundHex := hex.EncodeToString(response[14:18])

					// 转换为SQ8.8和INT32值
					kpdInt, _ := strconv.ParseInt(kpdHex, 16, 16)
					kidInt, _ := strconv.ParseInt(kidHex, 16, 16)
					kpqInt, _ := strconv.ParseInt(kpqHex, 16, 16)
					kiqInt, _ := strconv.ParseInt(kiqHex, 16, 16)
					integralBoundInt, _ := strconv.ParseInt(integralBoundHex, 16, 32)

					// 将SQ8.8格式转换为浮点数
					kpdFloat := float64(kpdInt) / 256.0
					kidFloat := float64(kidInt) / 256.0
					kpqFloat := float64(kpqInt) / 256.0
					kiqFloat := float64(kiqInt) / 256.0

					// 格式化为JSON字符串
					respDatas = fmt.Sprintf(`[{"index":"0","name":"Kp_d","type":"SQ8.8","value":"%.4f"},{"index":"1","name":"Ki_d","type":"SQ8.8","value":"%.4f"},{"index":"2","name":"Kp_q","type":"SQ8.8","value":"%.4f"},{"index":"3","name":"Ki_q","type":"SQ8.8","value":"%.4f"},{"index":"4","name":"integral_bound","type":"int32","value":"%d"}]`,
						kpdFloat, kidFloat, kpqFloat, kiqFloat, integralBoundInt)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0003": // CURRENT_LOOP控制参数设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(response[6:10])
					respDatas = fmt.Sprintf(`[{"index":"0","name":"ERROR","type":"hex","value":"%s"}]`, errorValue)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0004": // CURRENT_LOOP设定值获取
				if len(response) >= 10 {
					// 解析2字节的INT16参数
					idRefHex := hex.EncodeToString(response[6:8])
					iqRefHex := hex.EncodeToString(response[8:10])

					// 转换为INT16值
					idRefInt, _ := strconv.ParseInt(idRefHex, 16, 16)
					iqRefInt, _ := strconv.ParseInt(iqRefHex, 16, 16)

					// 格式化为JSON字符串
					respDatas = fmt.Sprintf(`[{"index":"0","name":"Id_ref","type":"int16","value":"%d"},{"index":"1","name":"Iq_ref","type":"int16","value":"%d"}]`,
						idRefInt, iqRefInt)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0005": // CURRENT_LOOP设定值设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(response[6:10])
					respDatas = fmt.Sprintf(`[{"index":"0","name":"ERROR","type":"hex","value":"%s"}]`, errorValue)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0006": // SPEED_LOOP控制参数获取
				if len(response) >= 16 {
					// 解析2字节的SQ8.8参数
					kpHex := hex.EncodeToString(response[6:8])
					kiHex := hex.EncodeToString(response[8:10])
					kdHex := hex.EncodeToString(response[10:12])
					// 解析4字节的INT32参数
					integralBoundHex := hex.EncodeToString(response[12:16])

					// 转换为SQ8.8和INT32值
					kpInt, _ := strconv.ParseInt(kpHex, 16, 16)
					kiInt, _ := strconv.ParseInt(kiHex, 16, 16)
					kdInt, _ := strconv.ParseInt(kdHex, 16, 16)
					integralBoundInt, _ := strconv.ParseInt(integralBoundHex, 16, 32)

					// 将SQ8.8格式转换为浮点数
					kpFloat := float64(kpInt) / 256.0
					kiFloat := float64(kiInt) / 256.0
					kdFloat := float64(kdInt) / 256.0

					// 格式化为JSON字符串
					respDatas = fmt.Sprintf(`[{"index":"0","name":"Kp","type":"SQ8.8","value":"%.4f"},{"index":"1","name":"Ki","type":"SQ8.8","value":"%.4f"},{"index":"2","name":"Kd","type":"SQ8.8","value":"%.4f"},{"index":"3","name":"integral_bound","type":"int32","value":"%d"}]`,
						kpFloat, kiFloat, kdFloat, integralBoundInt)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0007": // SPEED_LOOP控制参数设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(response[6:10])
					respDatas = fmt.Sprintf(`[{"index":"0","name":"ERROR","type":"hex","value":"%s"}]`, errorValue)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0008": // SPEED_LOOP设定值获取
				if len(response) >= 8 {
					// 解析2字节的INT16参数
					speedRefHex := hex.EncodeToString(response[6:8])

					// 转换为INT16值
					speedRefInt, _ := strconv.ParseInt(speedRefHex, 16, 16)

					// 格式化为JSON字符串
					respDatas = fmt.Sprintf(`[{"index":"0","name":"speed_ref","type":"int16","value":"%d"}]`, speedRefInt)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "0009": // SPEED_LOOP设定值设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(response[6:10])
					respDatas = fmt.Sprintf(`[{"index":"0","name":"ERROR","type":"hex","value":"%s"}]`, errorValue)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "000a", "000A": // 数据协议状态获取
				if len(response) >= 10 {
					transferStateHex := hex.EncodeToString(response[6:10])

					// 转换为整数
					transferStateInt, _ := strconv.ParseUint(transferStateHex, 16, 32)

					// 将整数转换为二进制字符串
					transferStateBin := fmt.Sprintf("0b%08b", transferStateInt)

					respDatas = fmt.Sprintf(`[{"index":"0","name":"TRANSFER_STATE","type":"binary","value":"%s"}]`,
						transferStateBin)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			case "000b", "000B": // 数据协议状态设置
				if len(response) >= 10 {
					errorValue := "0x" + hex.EncodeToString(response[6:10])
					respDatas = fmt.Sprintf(`[{"index":"0","name":"ERROR","type":"hex","value":"%s"}]`, errorValue)
				} else {
					return nil, nil, errx.New(http.StatusBadRequest, "Response data format error, insufficient length")
				}
			}

			// 将解析的数据添加到响应中
			if respDatas != "" {
				outFrame.FrameLib.UdpInfo.Datas = respDatas
			}
		}
		return nil, outFrame, nil
	}
}

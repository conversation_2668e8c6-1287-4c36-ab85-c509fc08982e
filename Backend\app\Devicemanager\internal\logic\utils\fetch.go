package utils
import (
	"fmt"
	"net"

	"GCF/app/Devicemanager/internal/types"

	"github.com/google/uuid"
)

func FetchDeviceData() (respFrame []types.FrameInfo, err error) {
	// 设置本地UDP监听地址
	localAddr, err := net.ResolveUDPAddr("udp", "0.0.0.0:6001")
	if err != nil {
		return nil, fmt.Errorf("解析本地地址失败: %v", err)
	}
	conn, err := net.ListenUDP("udp", localAddr)
	if err != nil {
		return nil, fmt.Errorf("连接设备失败: %v", err)
	}
	defer conn.Close()

	// 创建缓冲区接收数据
	buffer := make([]byte, 4096) // 足够大的缓冲区来接收数据
	n, _, err := conn.ReadFromUDP(buffer)
	if err != nil {
		return nil, fmt.Errorf("读取数据失败: %v", err)
	}

	// 解析接收到的数据
	if n > 0 {
		// 定义数据宽度常量
		const (
			ADC_ENCODER_COLLECT_DATA_WIDTH = 8 // 8字节
			CLARK_COLLECT_DATA_WIDTH       = 4 // 4字节
			PARK_COLLECT_DATA_WIDTH        = 4 // 4字节
			PI_OUTPUT_COLLECT_DATA_WIDTH   = 4 // 4字节
			PARK_INV_COLLECT_DATA_WIDTH    = 4 // 4字节
			CLARK_INV_COLLECT_DATA_WIDTH   = 6 // 6字节
			SVPWM_COLLECT_DATA_WIDTH       = 6 // 6字节
			INDEX_WIDTH                    = 4 // 4字节
		)

		// 单组数据长度
		singleGroupLength := ADC_ENCODER_COLLECT_DATA_WIDTH +
			CLARK_COLLECT_DATA_WIDTH +
			PARK_COLLECT_DATA_WIDTH +
			PI_OUTPUT_COLLECT_DATA_WIDTH +
			PARK_INV_COLLECT_DATA_WIDTH +
			CLARK_INV_COLLECT_DATA_WIDTH +
			SVPWM_COLLECT_DATA_WIDTH +
			INDEX_WIDTH

		// 计算可以解析的完整组数
		groupCount := n / singleGroupLength

		// 创建响应数据结构
		frameInfos := make([]types.FrameInfo, 0, groupCount)

		// 解析每一组数据
		for i := 0; i < groupCount; i++ {
			offset := i * singleGroupLength

			// 1. 解析索引
			index := int(buffer[offset]) | int(buffer[offset+1])<<8 | int(buffer[offset+2])<<16 | int(buffer[offset+3])<<24
			offset += INDEX_WIDTH

			// 2. 解析ADC和编码器数据 (Ia, Ib, Ic, raw_angle)
			ia := int16(buffer[offset]) | int16(buffer[offset+1])<<8
			ib := int16(buffer[offset+2]) | int16(buffer[offset+3])<<8
			ic := int16(buffer[offset+4]) | int16(buffer[offset+5])<<8
			rawAngleInt := int16(buffer[offset+6]) | int16(buffer[offset+7])<<8
			// 将 SQ2.14 格式转换为浮点数，除以 2^14
			rawAngle := float64(rawAngleInt) / 16384.0
			offset += ADC_ENCODER_COLLECT_DATA_WIDTH

			// 3. 解析Clark变换数据 (Ialpha, Ibeta)
			ialpha := int16(buffer[offset]) | int16(buffer[offset+1])<<8
			ibeta := int16(buffer[offset+2]) | int16(buffer[offset+3])<<8
			offset += CLARK_COLLECT_DATA_WIDTH

			// 4. 解析Park变换数据 (Id, Iq)
			id := int16(buffer[offset]) | int16(buffer[offset+1])<<8
			iq := int16(buffer[offset+2]) | int16(buffer[offset+3])<<8
			offset += PARK_COLLECT_DATA_WIDTH

			// 5. 解析PI输出数据 (Ud, Uq)
			ud := int16(buffer[offset]) | int16(buffer[offset+1])<<8
			uq := int16(buffer[offset+2]) | int16(buffer[offset+3])<<8
			offset += PI_OUTPUT_COLLECT_DATA_WIDTH

			// 6. 解析Park逆变换数据 (Ualpha, Ubeta)
			ualpha := int16(buffer[offset]) | int16(buffer[offset+1])<<8
			ubeta := int16(buffer[offset+2]) | int16(buffer[offset+3])<<8
			offset += PARK_INV_COLLECT_DATA_WIDTH

			// 7. 解析Clark逆变换数据 (Ua, Ub, Uc)
			ua := int16(buffer[offset]) | int16(buffer[offset+1])<<8
			ub := int16(buffer[offset+2]) | int16(buffer[offset+3])<<8
			uc := int16(buffer[offset+4]) | int16(buffer[offset+5])<<8
			offset += CLARK_INV_COLLECT_DATA_WIDTH

			// 8. 解析SVPWM数据 (Uan, Ubn, Ucn)
			uan := int16(buffer[offset]) | int16(buffer[offset+1])<<8
			ubn := int16(buffer[offset+2]) | int16(buffer[offset+3])<<8
			ucn := int16(buffer[offset+4]) | int16(buffer[offset+5])<<8

			// 创建数据点
			datas := []types.DataDef{
				{Index: "0", Name: "Index", Type: "int", Value: fmt.Sprintf("%d", index)},
				{Index: "1", Name: "Ia", Type: "int16", Value: fmt.Sprintf("%d", ia)},
				{Index: "2", Name: "Ib", Type: "int16", Value: fmt.Sprintf("%d", ib)},
				{Index: "3", Name: "Ic", Type: "int16", Value: fmt.Sprintf("%d", ic)},
				{Index: "4", Name: "RawAngle", Type: "SQ2.14", Value: fmt.Sprintf("%.6f", rawAngle)},
				{Index: "5", Name: "Ialpha", Type: "int16", Value: fmt.Sprintf("%d", ialpha)},
				{Index: "6", Name: "Ibeta", Type: "int16", Value: fmt.Sprintf("%d", ibeta)},
				{Index: "7", Name: "Id", Type: "int16", Value: fmt.Sprintf("%d", id)},
				{Index: "8", Name: "Iq", Type: "int16", Value: fmt.Sprintf("%d", iq)},
				{Index: "9", Name: "Ud", Type: "int16", Value: fmt.Sprintf("%d", ud)},
				{Index: "10", Name: "Uq", Type: "int16", Value: fmt.Sprintf("%d", uq)},
				{Index: "11", Name: "Ualpha", Type: "int16", Value: fmt.Sprintf("%d", ualpha)},
				{Index: "12", Name: "Ubeta", Type: "int16", Value: fmt.Sprintf("%d", ubeta)},
				{Index: "13", Name: "Ua", Type: "int16", Value: fmt.Sprintf("%d", ua)},
				{Index: "14", Name: "Ub", Type: "int16", Value: fmt.Sprintf("%d", ub)},
				{Index: "15", Name: "Uc", Type: "int16", Value: fmt.Sprintf("%d", uc)},
				{Index: "16", Name: "Uan", Type: "int16", Value: fmt.Sprintf("%d", uan)},
				{Index: "17", Name: "Ubn", Type: "int16", Value: fmt.Sprintf("%d", ubn)},
				{Index: "18", Name: "Ucn", Type: "int16", Value: fmt.Sprintf("%d", ucn)},
			}
			// 创建FrameInfo
			frameInfo := types.FrameInfo{
				FrameMeta: types.FrameMeta{
					FrameUID:  uuid.New().String(),
					FrameType: "udp",
				},
				FrameLibs: types.FrameLibs{
					UdpInfo: types.UdpInfo{
						Type:   "0x0002", // 数据帧类型
							Header: "0x000C", // 高频数据帧头
							TypeID: "0x0000", // 类型ID
							Datas:  datas,
						},
					},
			}
			frameInfos = append(frameInfos, frameInfo)
		}
		return frameInfos, nil
	}
	return nil, fmt.Errorf("未接收到数据")
}
package utils

import (
	"context"
	"encoding/binary"

	// "encoding/hex"
	"errors"
	"fmt"
	"net"
	"sort"
	"strconv"
	"strings"
	"time"

	"GCF/app/Devicemanager/internal/ent"
	"GCF/app/Devicemanager/internal/ent/modbus"
	"GCF/app/Devicemanager/internal/ent/uart"
	"GCF/app/Devicemanager/internal/ent/udp"
	"GCF/app/Devicemanager/internal/svc"
	"GCF/app/Devicemanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

var logger = logx.WithContext(context.Background())

// TODO:改成协程优化性能
func GetDevicesInfo(ctx context.Context, svcCtx *svc.ServiceContext, queryDevice *ent.Device) (*types.ListDevice, error) {
	DeviceResourceInfo, err := GetDeviceResourceInfo(ctx, svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	DeviceWorkStatus, err := GetDeviceWorkStatus(ctx, svcCtx, queryDevice)
	if err != nil {
		return nil, err
	}
	//获取帧info数据和deviceMeta数据
	DeviceMeta, FrameInfos, err := GetFrameInfosAndDeviceMeta(ctx, svcCtx, DeviceResourceInfo.Protocol, queryDevice.ID)
	if err != nil {
		return nil, err
	}
	return &types.ListDevice{
		DeviceMeta:         *DeviceMeta,
		DeviceResourceInfo: *DeviceResourceInfo,
		DeviceWorkStatus:   *DeviceWorkStatus,
		FrameInfos:         FrameInfos,
	}, nil
}
func GetFrameInfosAndDeviceMeta(ctx context.Context, svcCtx *svc.ServiceContext, Protocols []string, DeviceID string) (*types.DeviceMeta, []types.FrameInfo, error) {
	//获取帧info数据和deviceMeta数据
	FrameInfosMap, err := GetFrameInfos(ctx, svcCtx, Protocols, DeviceID)
	if err != nil {
		return nil, nil, err
	}
	FrameInfos := make([]types.FrameInfo, 0, len(FrameInfosMap))
	FrameMetas := make([]types.FrameMeta, 0, len(FrameInfosMap))
	for _, frameInfo := range FrameInfosMap {
		FrameInfos = append(FrameInfos, *frameInfo)
		FrameMetas = append(FrameMetas, frameInfo.FrameMeta)
	}
	DeviceMeta := &types.DeviceMeta{
		DeviceUID:  DeviceID,
		FrameMetas: FrameMetas,
	}
	return DeviceMeta, FrameInfos, nil
}

func GetDeviceResourceInfo(ctx context.Context, svcCtx *svc.ServiceContext, queryDevice *ent.Device) (*types.DeviceResourceInfo, error) {
	//TODO:获取设备资源信息
	cpuResource, err := stringToResourceDef(queryDevice.CPU)
	if err != nil {
		return nil, err
	}

	gpuResource, err := stringToResourceDef(queryDevice.Gpu)
	if err != nil {
		return nil, err
	}

	diskResource, err := stringToResourceDef(queryDevice.Disk)
	if err != nil {
		return nil, err
	}

	memResource, err := stringToResourceDef(queryDevice.Memory)
	if err != nil {
		return nil, err
	}

	return &types.DeviceResourceInfo{
		IP:       queryDevice.IP,
		Hostname: queryDevice.Name,
		OS:       queryDevice.Os,
		Type:     queryDevice.Type,
		CPU:      *cpuResource,
		GPU:      *gpuResource,
		Disk:     *diskResource,
		Mem:      *memResource,
		Protocol: strings.Split(queryDevice.Protocol, ","),
	}, nil
}

func FindProtocol(protocols []string, targetFrame string) int {
	sort.Strings(protocols)
	index := sort.SearchStrings(protocols, targetFrame)
	if index < len(protocols) && protocols[index] == targetFrame {
		return index
	}
	return -1
}

func AddProtocol(protocols []string, targetFrame string) []string {
	protocols = append(protocols, targetFrame)
	sort.Strings(protocols)
	return protocols
}

func RemoveProtocol(protocols []string, targetFrame string) []string {
	index := FindProtocol(protocols, targetFrame)
	if index != -1 {
		protocols = append(protocols[:index], protocols[index+1:]...)
	}
	return protocols
}

func stringToResourceDef(resourceDef string) (*types.ResourceDef, error) {
	resourceDefs := strings.Split(resourceDef, ",")
	if len(resourceDefs) != 3 {
		return nil, errors.New("resourceDef format error")
	}
	amount, err := strconv.ParseInt(resourceDefs[1], 10, 64)
	if err != nil {
		return nil, err
	}
	return &types.ResourceDef{
		Type:   resourceDefs[0],
		Amount: amount,
		Unit:   resourceDefs[2],
	}, nil
}

// 将ResourceDef转换为字符串格式
func ResourceDefToString(resourceDef *types.ResourceDef) (string, error) {
	if resourceDef == nil {
		return "", errors.New("resourceDef is nil")
	}
	return fmt.Sprintf("%s,%d,%s", resourceDef.Type, resourceDef.Amount, resourceDef.Unit), nil
}

func GetDeviceWorkStatus(ctx context.Context, svcCtx *svc.ServiceContext, queryDevice *ent.Device) (*types.DeviceWorkStatus, error) {
	Timestamp := time.Now()
	HealthStatus := queryDevice.Status
	if HealthStatus != HealthStatusReady || queryDevice.Healthtimestamp.Before(Timestamp.Add(-time.Minute*10)) {
		//TODO: 检查健康状态,惰性检查
		HealthStatus = "ready"
		svcCtx.Db.Device.Update().SetStatus(HealthStatus).SetHealthtimestamp(Timestamp).Exec(ctx)
	}
	return &types.DeviceWorkStatus{
		HealthStatus: HealthStatus,
		Timestamp:    Timestamp.Unix(),
		WorkMode:     queryDevice.Workmode,
	}, nil
}

func GetFrameInfos(ctx context.Context, svcCtx *svc.ServiceContext, Protocols []string, DeviceID string) (frameMap map[string]*types.FrameInfo, err error) {
	frameMap = make(map[string]*types.FrameInfo, len(Protocols))
	for _, protocol := range Protocols {
		switch protocol {
		case FrameTypeModbus:
			modbus, err := svcCtx.Db.Modbus.Query().Where(modbus.DeviceIDEQ(DeviceID)).First(ctx)
			if err != nil {
				return nil, err
			}
			//TODO:Data处理
			frameMap[protocol] = &types.FrameInfo{
				FrameMeta: types.FrameMeta{
					FrameUID:  modbus.ID,
					FrameType: protocol,
				},
				FrameLibs: types.FrameLibs{
					ModbusInfo: types.ModbusInfo{
						TID: modbus.Tid,
						PID: modbus.Pid,
						Len: modbus.Len,
						UID: modbus.UID,
						FC:  modbus.Fc,
					},
				},
			}
		case FrameTypeUart:
			uart, err := svcCtx.Db.Uart.Query().Where(uart.DeviceIDEQ(DeviceID)).First(ctx)
			if err != nil {
				return nil, err
			}
			//TODO:Data处理
			frameMap[protocol] = &types.FrameInfo{
				FrameMeta: types.FrameMeta{
					FrameUID:  uart.ID,
					FrameType: protocol,
				},
				FrameLibs: types.FrameLibs{
					UartInfo: types.UartInfo{
						Header: uart.Header,
						Addr:   uart.Addr,
						Cmd:    uart.Cmd,
						Tail:   uart.Tail,
					},
				},
			}
		case FrameTypeUdp:
			udp, err := svcCtx.Db.Udp.Query().Where(udp.DeviceIDEQ(DeviceID)).First(ctx)
			if err != nil {
				return nil, err
			}
			//TODO:Data处理
			frameMap[protocol] = &types.FrameInfo{
				FrameMeta: types.FrameMeta{
					FrameUID:  udp.ID,
					FrameType: protocol,
				},
				FrameLibs: types.FrameLibs{
					UdpInfo: types.UdpInfo{
						Type:   udp.Type,
						Header: udp.Header,
						TypeID: udp.TypeID,
					},
				},
			}
		}
	}
	return frameMap, nil
}

func CreateNetConnect(protocol string, ip string, req []byte) ([]byte, error) {
	response := make([]byte, 1024)
	if protocol == FrameTypeUdp {
		// 解析目标地址
		addr, err := net.ResolveUDPAddr("udp", ip)
		if err != nil {
			return nil, fmt.Errorf("解析地址失败: %v", err)
		}

		// 创建本地地址，绑定到固定端口6005
		localAddr, err := net.ResolveUDPAddr("udp", "0.0.0.0:6005")
		if err != nil {
			return nil, fmt.Errorf("解析本地地址失败: %v", err)
		}

		// 使用指定的本地地址创建UDP连接
		conn, err := net.DialUDP("udp", localAddr, addr)
		if err != nil {
			return nil, fmt.Errorf("连接设备失败: %v", err)
		}
		defer conn.Close()

		// 发送请求包
		_, err = conn.Write(req)
		if err != nil {
			return nil, fmt.Errorf("发送请求包失败: %v", err)
		}

		// listen, err := net.ListenUDP("udp", localAddr)
		// if err!= nil {
		// 	return nil, fmt.Errorf("监听本地地址失败: %v", err)
		// }
		// defer listen.Close()
		// fmt.Printf("listen success: %v\n", localAddr)

		n, _, err := conn.ReadFromUDP(response)
		if err != nil {
			return nil, fmt.Errorf("接收响应包失败: %v", err)
		}
		response = response[:n]
		fmt.Printf("response success : %v\n", response)

	}
	return response, nil
}

// func GetDevicebyID(ctx context.Context, svcCtx *svc.ServiceContext, id string) (*types.DeviceMeta, error) {
// 	device, err := svcCtx.Db.Device.Get(ctx, id)
// 	if err != nil {
// 		return nil, err
// 	}
// 	deviceMeta := &types.DeviceMeta{
// 		DeviceUID: device.ID,
// 	}
// 	return deviceMeta, nil
// }

func String2bytes(value string, bitSize int, endian int, dataType string) ([]byte, error) {
	intValue := int64(0)
	switch dataType {
	case "SQ8.8":
		floatValue, err := strconv.ParseFloat(value, bitSize)
		if err != nil {
			return nil, err
		}
		intValue = int64(floatValue * 256)
	case "int":
		var err error
		intValue, err = strconv.ParseInt(value, 10, bitSize)
		if err != nil {
			return nil, err
		}
	case "hex":
		var err error
		intValue, err = strconv.ParseInt(strings.TrimPrefix(value, "0x"), 16, bitSize)
		if err != nil {
			return nil, err
		}
	case "binary":
		var err error
		intValue, err = strconv.ParseInt(strings.TrimPrefix(value, "0b"), 2, bitSize)
		if err != nil {
			return nil, err
		}
	default:
		return nil, fmt.Errorf("unsupported data type: %s", dataType)
	}
	tmpBytes := make([]byte, bitSize/8)
	switch bitSize {
	case 32:
		if endian == 1 {
			binary.LittleEndian.PutUint32(tmpBytes, uint32(intValue))
		} else {
			binary.BigEndian.PutUint32(tmpBytes, uint32(intValue))
		}
	case 16:
		if endian == 1 {
			binary.LittleEndian.PutUint16(tmpBytes, uint16(intValue))
		} else {
			binary.BigEndian.PutUint16(tmpBytes, uint16(intValue))
		}
	case 8:
		tmpBytes[0] = byte(intValue)
	default:
		return nil, fmt.Errorf("unsupported bit size: %d", bitSize)
	}
	return tmpBytes, nil
}

func ReverseBytes(data []byte) []byte {
	for i, j := 0, len(data)-1; i < j; i, j = i+1, j-1 {
		data[i], data[j] = data[j], data[i]
	}
	return data
}

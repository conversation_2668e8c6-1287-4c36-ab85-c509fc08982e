// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1
// Source: device.proto

package server

import (
	"context"

	"GCF/app/Devicemanager/internal/devicemanager"
	"GCF/app/Devicemanager/internal/logic"
	"GCF/app/Devicemanager/internal/svc"
)

type DeviceServer struct {
	svcCtx *svc.ServiceContext
	devicemanager.UnimplementedDeviceServer
}

func NewDeviceServer(svcCtx *svc.ServiceContext) *DeviceServer {
	return &DeviceServer{
		svcCtx: svcCtx,
	}
}

func (s *DeviceServer) GetFramesByDeviceID(ctx context.Context, in *devicemanager.GetFramesByDeviceIDRequest) (*devicemanager.GetFramesByDeviceIDResponse, error) {
	l := logic.NewGetFramesByDeviceIDLogic(ctx, s.svcCtx)
	return l.GetFramesByDeviceID(in)
}

func (s *DeviceServer) GetDeviceByID(ctx context.Context, in *devicemanager.GetDeviceByIDRequest) (*devicemanager.GetDeviceByIDResponse, error) {
	l := logic.NewGetDeviceByIDLogic(ctx, s.svcCtx)
	return l.GetDeviceByID(in)
}

func (s *DeviceServer) CheckDeviceHealth(ctx context.Context, in *devicemanager.CheckDeviceHealthRequest) (*devicemanager.CheckDeviceHealthResponse, error) {
	l := logic.NewCheckDeviceHealthLogic(ctx, s.svcCtx)
	return l.CheckDeviceHealth(in)
}

func (s *DeviceServer) ControlDeviceByDeviceID(ctx context.Context, in *devicemanager.ControlDeviceByDeviceIDRequest) (*devicemanager.ControlDeviceByDeviceIDResponse, error) {
	l := logic.NewControlDeviceByDeviceIDLogic(ctx, s.svcCtx)
	return l.ControlDeviceByDeviceID(in)
}

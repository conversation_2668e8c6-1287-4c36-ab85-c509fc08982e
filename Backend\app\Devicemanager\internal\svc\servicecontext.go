package svc

import (
	"GCF/app/Devicemanager/internal/config"
	"GCF/app/Devicemanager/internal/ent"
	"GCF/pkg/zetcd"
	"fmt"

	_ "github.com/lib/pq"
	"github.com/zeromicro/go-zero/core/logx"
)

type ServiceContext struct {
	Config      config.Config
	ZEtcdClient *zetcd.ZEtcdClient
	Db          *ent.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	entClient, err := ent.Open(c.Db.Driver, c.Db.MustGetSource())
	logx.Must(err)
	fmt.Printf("Connected to database, driver %s, source %s\n", c.Db.Driver, c.Db.MustGetSource())

	ZEtcdClient, err := zetcd.NewZEtcdClient(c.ZEtcdConf)
	logx.Must(err)
	fmt.Printf("Connected to etcd, endpoints %s\n", c.ZEtcdConf.Endpoints)

	return &ServiceContext{
		Config:      c,
		Db:          entClient,
		ZEtcdClient: ZEtcdClient,
	}
}

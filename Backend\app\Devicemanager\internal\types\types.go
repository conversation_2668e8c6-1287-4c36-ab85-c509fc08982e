// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.1

package types

type ControlDeviceRequest struct {
	DeviceUID string    `json:"deviceUID"`
	FrameInfo FrameInfo `json:"frameInfo"`
}

type ControlDeviceResponse struct {
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	FrameInfos       []FrameInfo      `json:"frameInfo"`
}

type CreateDeviceFrameRequest struct {
	DeviceUID  string      `json:"deviceUID"`
	FrameInfos []FrameInfo `json:"frameInfos"`
}

type CreateDeviceFrameResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}

type CreateDeviceResourceRequest struct {
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
}

type CreateDeviceResourceResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}

type DataDef struct {
	Index string `json:"index"`
	Name  string `json:"name"`
	Type  string `json:"type"`
	Value string `json:"value"`
}

type DeleteDeviceFrameRequest struct {
	DeviceUID  string      `json:"deviceUID"`
	FrameMetas []FrameMeta `json:"frameMetas"`
}

type DeleteDeviceFrameResponse struct {
	DeviceMeta       DeviceMeta       `json:"deviceMeta"`
	FrameInfos       []FrameInfo      `json:"frameInfos"`
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
}

type DeleteDeviceResourceRequest struct {
	DeviceUID string `json:"deviceUID"`
}

type DeleteDeviceResourceResponse struct {
	DeviceMeta         DeviceMeta         `json:"deviceMeta"`
	DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
}

type DeviceMeta struct {
	DeviceUID  string      `json:"deviceUID"`
	FrameMetas []FrameMeta `json:"frameMetas"`
	DeviceDesc string      `json:"deviceDesc,optional"`
}

type DeviceResourceInfo struct {
	IP       string      `json:"ip"`
	Hostname string      `json:"hostname,optional"`
	Type     string      `json:"type,options=fan|motor|virtual|pc|edge,optional"`
	OS       string      `json:"os,optional"`
	CPU      ResourceDef `json:"cpu,optional"`
	GPU      ResourceDef `json:"gpu,optional"`
	Disk     ResourceDef `json:"disk,optional"`
	Mem      ResourceDef `json:"mem,optional"`
	Protocol []string    `json:"protocol"`
}

type DeviceWorkStatus struct {
	HealthStatus string `json:"healthStatus,options=init|ready|running|pending|error"`
	Timestamp    int64  `json:"timestamp"`
	WorkMode     string `json:"workMode,options=distributed|centralized"`
}

type FetchDeviceDataRequest struct {
	DeviceUID string `json:"deviceUID"`
}

type FetchDeviceDataResponse struct {
	DeviceWorkStatus DeviceWorkStatus `json:"deviceWorkStatus"`
	FrameInfos       []FrameInfo      `json:"frameInfos"`
}

type FrameInfo struct {
	FrameMeta FrameMeta `json:"frameMeta"`
	FrameLibs FrameLibs `json:"frameLibs"`
}

type FrameLibs struct {
	ModbusInfo ModbusInfo `json:"modbusInfo,optional"`
	UartInfo   UartInfo   `json:"uartInfo,optional"`
	UdpInfo    UdpInfo    `json:"udpInfo,optional"`
}

type FrameMeta struct {
	FrameUID  string `json:"frameUID,optional"`
	FrameType string `json:"frameType,options=modbus|uart|udp"`
}

type ListDevice struct {
	DeviceMeta         DeviceMeta         `json:"deviceMeta"`
	DeviceWorkStatus   DeviceWorkStatus   `json:"deviceWorkStatus"`
	DeviceResourceInfo DeviceResourceInfo `json:"deviceResourceInfo"`
	FrameInfos         []FrameInfo        `json:"frameInfos"`
}

type ListDeviceRequest struct {
	DeviceUID    string      `json:"deviceUID,optional"`
	FrameMetas   []FrameMeta `json:"frameMetas,optional"`
	HealthStatus string      `json:"healthStatus,options=init|ready|running|pending|error,optional"`
}

type ListDeviceResponse struct {
	Devices []ListDevice `json:"devices"`
}

type ModbusInfo struct {
	TID   string    `json:"tid"`
	PID   string    `json:"pid"`
	Len   string    `json:"len"`
	UID   string    `json:"uid"`
	FC    string    `json:"fc"`
	Datas []DataDef `json:"datas,optional"`
}

type ResourceDef struct {
	Amount int64  `json:"amount"`
	Type   string `json:"type"`
	Unit   string `json:"unit"`
}

type UartInfo struct {
	Header string    `json:"header"`
	Addr   string    `json:"addr"`
	Cmd    string    `json:"cmd"`
	Tail   string    `json:"tail"`
	Datas  []DataDef `json:"datas,optional"`
}

type UdpInfo struct {
	Type   string    `json:"type"`
	Header string    `json:"header"`
	TypeID string    `json:"typeID"`
	Datas  []DataDef `json:"datas,optional"`
}

#!/usr/bin/env bash
# set -x             # for debug
set -euo pipefail # fail early
SCRIPT_DIR="$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"

########################################################################
# This script runs `go run service` with required environment variables.
#
# No building BUILD.bazel, or docker image.
# It's for quick and dirty manual tests.
########################################################################

cd "$SCRIPT_DIR"/..

export KUBECONFIG_PATH=$HOME/.kube/config

echo "## Start API service."
echo "## Press Ctrl-C to kill them."
echo "## Log goes to ./api.log , remember: rm *.log"

go run storage.go -f etc/storage.yaml

syntax = "v1"

info (
	title:  "control Service"
	desc:   "control service related api"
	author: ""
	email:  ""
)
//数据结构体
type (
    ValueDef {
		Index string `json:"index"`
		Name  string `json:"name"`
		Value  string `json:"value"`
	}
)
//响应-请求结构体
type (
	//启动控制请求
    StartControlRequest {
        DeviceUID  string `json:"deviceUID"`
    }
    StartControlResponse {
        Result string `json:"result,optional"`
    }
    //停止控制请求
    StopControlRequest {
        DeviceUID  string `json:"deviceUID"`
    }
    StopControlResponse {
        Result string `json:"result,optional"`
    }
    //设置控制请求
    SetValueRequest {
        DeviceUID  string `json:"deviceUID"`
        Values      []ValueDef `json:"values"`
    }
    SetValueResponse {
        Result string `json:"result,optional"`
    }
    //获取控制请求
    GetValueRequest {
        DeviceUID  string `json:"deviceUID"`
        Values      []ValueDef `json:"values,optional"`
    }
    GetValueResponse {
        Values      []ValueDef `json:"values"`
    }
    //健康检查
    HealthRequest {
        DeviceUID  string `json:"deviceUID"`
    }
    HealthResponse {
        Status      string `json:"status"`
    }

)
//api
service device-api {
	@handler StartControlHandler
    post /api/v1/control/startControl (StartControlRequest) returns (StartControlResponse)
    @handler StopControlHandler
    post /api/v1/control/stopControl (StopControlRequest) returns (StopControlResponse)
    @handler SetValueHandler
    post /api/v1/control/setValue (SetValueRequest) returns (SetValueResponse)
    @handler GetValueHandler
    post /api/v1/control/getValue (GetValueRequest) returns (GetValueResponse)
    @handler HealthHandler
    post /api/v1/health (HealthRequest) returns (HealthResponse)
}
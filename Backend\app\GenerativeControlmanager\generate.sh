#!/usr/bin/env bash
# set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# CHANGE THIS: If we move the script.
SERVICE_ROOT=$SCRIPT_DIR

cd "$SERVICE_ROOT"

# Generate API
echo "Generating API in $(pwd)"
goctl api go -api api/data.api -dir .

# Generate RPC
echo "Generating RPC in $(pwd)"
goctl rpc protoc rpc/data.proto \
  --go_out="$(pwd)/internal" \
  --go-grpc_out="$(pwd)/internal" \
  --zrpc_out="$(pwd)"

# Generate data mock
pushd data/ > /dev/null
  mkdir -p mock
  mockgen -destination mock/data_mock.go -package mock . data
popd > /dev/null

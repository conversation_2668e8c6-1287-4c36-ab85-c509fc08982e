syntax = "v1"

info (
	title:  "Virtual Device Manager Service"
	desc:   "Virtual device management and simulation control API"
	author: ""
	email:  ""
)

type (
	DeviceInfo {
		DeviceID   string `json:"deviceID"`
		DeviceName string `json:"deviceName"`
		DeviceType string `json:"deviceType"`
		Status     string `json:"status"`
		CreateTime string `json:"createTime"`
	}
	DeviceHealthStatus {
		DeviceID     string `json:"deviceID"`
		HealthStatus string `json:"healthStatus"`
	}
	DeviceStatus {
		DeviceID string `json:"deviceID"`
		Status   string `json:"status"`
	}
	LogEntry {
		Timestamp string `json:"timestamp"`
		Level     string `json:"level"`
		Message   string `json:"message"`
	}
	DeviceConfig {
		ParamName string `json:"paramName"`
		Value     string `json:"value"`
	}
	FrameInfo {
		FrameID   string `json:"frameID"`
		FrameTime string `json:"frametime"`
	}
	ControlParam {
		ParamName    string `json:"paramName"`
		BlockPath    string `json:"blockPath"`
		ParamType    string `json:"paramType"`
		DefaultValue string `json:"defaultValue"`
		Description  string `json:"description"`
		Writable     bool   `json:"writable"`
	}
	OutputVar {
		VarName     string `json:"varName"`
		MatlabVar   string `json:"matlabVar"`
		Description string `json:"description"`
		Readable    bool   `json:"readable"`
	}
	SimulateInfo {
		SimulateID    string         `json:"simulateID"`
		SimulateType  string         `json:"simulateType"`
		SimulateTime  string         `json:"simulateTime"`
		Status        string         `json:"status"`
		ControlParams []ControlParam `json:"controlParam"`
		OutputVars    []OutputVar    `json:"outputVar"`
	}
	ControlParamValue {
		Timestamp string `json:"timestamp"`
		ParamName string `json:"paramName"`
		Value     string `json:"value"`
	}
	OutputVarValue {
		VarName   string   `json:"varName"` // 变量名称
		Timestamp []string `json:"timestamp"` // 时间列表
		Value     []string `json:"value"` // 值列表
	}
	SimulateConfig {
		ParamName string `json:"paramName"`
		Value     string `json:"value"`
	}
	SimulinkRuntime {
		Progress string           `json:"progress"` // 进度 (0.0-1.0)
		Output   []OutputVarValue `json:"output"` // 输出变量
	}
	SimulinkInfoAndRuntime {
		Info    SimulateInfo    `json:"info"` // 模型info信息
		Runtime SimulinkRuntime `json:"runtime"` // 模型运行信息
	}
)

type (
	// 创建设备
	CreateDeviceRequest {
		DeviceName string         `json:"deviceName"`
		DeviceType string         `json:"deviceType"`
		Config     []DeviceConfig `json:"config"`
	}
	CreateDeviceResponse {
		Success bool       `json:"success"`
		Message string     `json:"message"`
		Info    DeviceInfo `json:"deviceinfo"`
		Frame   FrameInfo  `json:"frameinfo"`
	}
	// 删除设备
	DeleteDeviceRequest {
		DeviceID string `json:"deviceID"`
	}
	DeleteDeviceResponse {
		Success bool       `json:"success"`
		Message string     `json:"message"`
		Info    DeviceInfo `json:"deviceinfo"`
		Frame   FrameInfo  `json:"frameinfo"`
	}
	// 查询设备列表
	ListDeviceRequest {
		DeviceType string `json:"deviceType"`
	}
	ListDeviceResponse {
		Devices []DeviceInfo `json:"devices"`
		Total   int32        `json:"total"`
		Message string       `json:"message"`
	}
	// 查询设备健康状态
	GetDeviceHealthRequest {
		DeviceID string `json:"deviceID"`
	}
	GetDeviceHealthResponse {
		DeviceID     string `json:"deviceID"`
		HealthStatus string `json:"healthStatus"`
		Message      string `json:"message"`
	}
	// 查询设备运行状态
	GetDeviceStatusRequest {
		DeviceID string `json:"deviceID"`
	}
	GetDeviceStatusResponse {
		DeviceID string `json:"deviceID"`
		Status   string `json:"status"`
		Message  string `json:"message"`
	}
	// 创建仿真
	CreateSimulationRequest {
		DeviceID     string         `json:"deviceID"`
		SimulinkDir  string         `json:"simulinkDir"`
		SimulinkName string         `json:"simulinkName"`
		SimulateTime string         `json:"simulateTime"`
		InitScript   string         `json:"initScript"`
		Params       []ControlParam `json:"params"`
		OutVars      []OutputVar    `json:"outVars"`
	}
	CreateSimulationResponse {
		Message string       `json:"message"`
		Info    SimulateInfo `json:"simulateInfo"`
	}
	// 查询仿真输出结果
	GetSimoutRequest {
		SimulateID string   `json:"simulateID"`
		Vars       []string `json:"vars"`
	}
	GetSimoutResponse {
		Output []OutputVarValue `json:"output"`
	}
	// 停止仿真
	CloseSimulationRequest {
		SimulateID string `json:"simulateID"` // 模型ID
	}
	CloseSimulationResponse {
		Info SimulateInfo `json:"simulateInfo"` // 模型info信息
	}
	// 获取仿真列表
	ListAvailableSimulationsRequest {
		SimulateID   string `json:"simulateID"` // 模型id
		SimulateType string `json:"simulateType"` // 模型类型
		Status       string `json:"status"` // 仿真状态,枚举类型，如"initializing", "ready","running", "stopped", "error"
	}
	ListAvailableSimulationsResponse {
		Info []SimulinkInfoAndRuntime `json:"info"` // 模型info信息
	}
	// 控制仿真设备启停
	StartSimulationRequest {
		SimulateID string              `json:"SimulateID"`
		StartTime  string              `json:"startTime"`
		StopTime   string              `json:"stopTime"`
		Params     []ControlParamValue `json:"params"`
	}
	StartSimulationResponse {
		Info    SimulateInfo    `json:"simulateInfo"`
		Runtime SimulinkRuntime `json:"simulateRuntime"`
	}
	// 单步控制
	ControlStepRequest {
		SimulateID   string            `json:"simulateID"`
		SimulateTime string            `json:"simulateTime"`
		Params       ControlParamValue `json:"params"`
	}
	ControlStepResponse {
		Success bool            `json:"success"`
		Message string          `json:"message"`
		Info    SimulateInfo    `json:"simulateInfo"`
		Runtime SimulinkRuntime `json:"simulinkRuntime"`
	}
	// 多步控制
	ControlContinuousRequest {
		SimulateID   string              `json:"simulateID"`
		SimulateTime string              `json:"simulateTime"`
		Params       []ControlParamValue `json:"params"`
	}
	ControlContinuousResponse {
		Success bool            `json:"success"`
		Message string          `json:"message"`
		Info    SimulateInfo    `json:"simulateInfo"`
		Runtime SimulinkRuntime `json:"simulinkRuntime"`
	}
	// 保存仿真结果
	SaveSimResultRequest {
		SimulateID string `json:"simulateID"`
	}
	SaveSimResultResponse {
		SimulateID string           `json:"simulateID"`
		Output     []OutputVarValue `json:"output"`
		Info       SimulateInfo     `json:"simulateInfo"`
		Message    string           `json:"message"`
	}
	// 查询设备日志
	GetDeviceLogRequest {
		DeviceID string `json:"deviceID"`
		LogLevel int32  `json:"logLevel"`
		Limit    int32  `json:"limit"`
	}
	GetDeviceLogResponse {
		Logs []LogEntry `json:"logs"`
	}
	// 查询设备配置
	GetDeviceConfigRequest {
		DeviceID string `json:"deviceID"`
	}
	GetDeviceConfigResponse {
		DeviceID   string         `json:"deviceID"`
		UpdateTime string         `json:"updateTime"`
		Config     []DeviceConfig `json:"config"`
	}
	// 更新设备配置
	UpdateDeviceConfigRequest {
		DeviceID string         `json:"deviceID"`
		Config   []DeviceConfig `json:"config"`
	}
	UpdateDeviceConfigResponse {
		Success    bool           `json:"success"`
		UpdateTime string         `json:"updateTime"`
		Config     []DeviceConfig `json:"config"`
		Message    string         `json:"message"`
	}
	RegisterSimulationParamRequest {
		SimulateID string         `json:"simulateID"`
		Params     []ControlParam `json:"params"`
	}
	RegisterSimulationParamResponse {
		Info SimulateInfo `json:"simulateInfo"` // 模型info信息
	}
	RegisterSimulationVarRequest {
		SimulateID string      `json:"simulateID"` // 模型ID
		Output     []OutputVar `json:"output"` // 输出变量列表
	}
	RegisterSimulationVarResponse {
		Info SimulateInfo `json:"simulateInfo"` // 模型info信息
	}
	GetSimulationInfoRequest {
		SimulateID string `json:"simulateID"` // 模型ID
	}
	GetSimulationInfoResponse {
		Info SimulinkInfoAndRuntime `json:"info"` // 模型info信息
	}
)

// API 路径
service Virtual-api {
	@handler CreateDeviceHandler
	post /api/v1/virtualdevice/create (CreateDeviceRequest) returns (CreateDeviceResponse)

	@handler DeleteDeviceHandler
	post /api/v1/virtualdevice/delete (DeleteDeviceRequest) returns (DeleteDeviceResponse)

	@handler ListDeviceHandler
	post /api/v1/virtualdevice/list (ListDeviceRequest) returns (ListDeviceResponse)

	@handler GetDeviceHealthHandler
	post /api/v1/virtualdevice/health (GetDeviceHealthRequest) returns (GetDeviceHealthResponse)

	@handler GetDeviceStatusHandler
	post /api/v1/virtualdevice/status (GetDeviceStatusRequest) returns (GetDeviceStatusResponse)

	@handler CreateSimulationHandler
	post /api/v1/virtualdevice/simulation/create (CreateSimulationRequest) returns (CreateSimulationResponse)

	@handler GetSimoutHandler
	post /api/v1/virtualdevice/simulation/output (GetSimoutRequest) returns (GetSimoutResponse)

	@handler CloseSimulationHandler
	post /api/v1/virtualdevice/simulation/close (CloseSimulationRequest) returns (CloseSimulationResponse)

	@handler ListAvailableSimulationsHandler
	post /api/v1/virtualdevice/simulation/list (ListAvailableSimulationsRequest) returns (ListAvailableSimulationsResponse)

	@handler StartSimulationHandler
	post /api/v1/virtualdevice/simulation/start (StartSimulationRequest) returns (StartSimulationResponse)

	@handler ControlStepHandler
	post /api/v1/virtualdevice/simulation/step (ControlStepRequest) returns (ControlStepResponse)

	@handler ControlContinuousHandler
	post /api/v1/virtualdevice/simulation/continuous (ControlContinuousRequest) returns (ControlContinuousResponse)

	@handler SaveSimResultHandler
	post /api/v1/virtualdevice/simulation/save (SaveSimResultRequest) returns (SaveSimResultResponse)

	@handler GetDeviceLogHandler
	post /api/v1/virtualdevice/log (GetDeviceLogRequest) returns (GetDeviceLogResponse)

	@handler GetDeviceConfigHandler
	post /api/v1/virtualdevice/config/get (GetDeviceConfigRequest) returns (GetDeviceConfigResponse)

	@handler UpdateDeviceConfigHandler
	post /api/v1/virtualdevice/config/update (UpdateDeviceConfigRequest) returns (UpdateDeviceConfigResponse)

	@handler RegisterSimulationParamHandler
	post /api/v1/virtualdevice/simulation/register/control (RegisterSimulationParamRequest) returns (RegisterSimulationParamResponse)

	@handler RegisterSimulationVarHandler
	post /api/v1/virtualdevice/simulation/register/outvar (RegisterSimulationVarRequest) returns (RegisterSimulationVarResponse)

	@handler GetSimulationInfo
	post /api/v1/virtualdevice/simulation/getinfo (GetSimulationInfoRequest) returns (GetSimulationInfoResponse)
}


-- create "data" table
CREATE TABLE "data" ("id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY, "frame_id" character varying NOT NULL, "index" character varying NOT NULL, "name" character varying NOT NULL, "type" character varying NOT NULL DEFAULT '', "value" character varying NOT NULL DEFAULT '', PRIMARY KEY ("id"));
-- create "device_logs" table
CREATE TABLE "device_logs" ("id" character varying NOT NULL, "name" character varying NOT NULL, "healthlog" text NOT NULL DEFAULT '', "simulatelog" text NOT NULL DEFAULT '', PRIMARY KEY ("id"));
-- create "frames" table
CREATE TABLE "frames" ("id" character varying NOT NULL, "device_id" character varying NOT NULL, "header" character varying NOT NULL DEFAULT '', "content" character varying NOT NULL DEFAULT '', "tail" character varying NOT NULL DEFAULT '', "time" timestamptz NOT NULL, PRIMARY KEY ("id"));
-- create "tests" table
CREATE TABLE "tests" ("id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY, PRIMARY KEY ("id"));
-- create "test_creates" table
CREATE TABLE "test_creates" ("id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY, PRIMARY KEY ("id"));
-- create "virtual_devices" table
CREATE TABLE "virtual_devices" ("id" character varying NOT NULL, "name" character varying NOT NULL DEFAULT '', "type" character varying NOT NULL DEFAULT '', "protocol" character varying NOT NULL DEFAULT '', "config" jsonb NOT NULL, "status" character varying NOT NULL DEFAULT 'ready', "healthtimestamp" timestamptz NOT NULL, "create_unix" timestamptz NOT NULL, "update_unix" timestamptz NOT NULL, PRIMARY KEY ("id"));

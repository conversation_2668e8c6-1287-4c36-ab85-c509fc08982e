// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"GCF/app/Virtualmanager/internal/ent/migrate"

	"GCF/app/Virtualmanager/internal/ent/data"
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"GCF/app/Virtualmanager/internal/ent/frame"
	"GCF/app/Virtualmanager/internal/ent/test"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Data is the client for interacting with the Data builders.
	Data *DataClient
	// DeviceLog is the client for interacting with the DeviceLog builders.
	DeviceLog *DeviceLogClient
	// Frame is the client for interacting with the Frame builders.
	Frame *FrameClient
	// Test is the client for interacting with the Test builders.
	Test *TestClient
	// VirtualDevice is the client for interacting with the VirtualDevice builders.
	VirtualDevice *VirtualDeviceClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Data = NewDataClient(c.config)
	c.DeviceLog = NewDeviceLogClient(c.config)
	c.Frame = NewFrameClient(c.config)
	c.Test = NewTestClient(c.config)
	c.VirtualDevice = NewVirtualDeviceClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:           ctx,
		config:        cfg,
		Data:          NewDataClient(cfg),
		DeviceLog:     NewDeviceLogClient(cfg),
		Frame:         NewFrameClient(cfg),
		Test:          NewTestClient(cfg),
		VirtualDevice: NewVirtualDeviceClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:           ctx,
		config:        cfg,
		Data:          NewDataClient(cfg),
		DeviceLog:     NewDeviceLogClient(cfg),
		Frame:         NewFrameClient(cfg),
		Test:          NewTestClient(cfg),
		VirtualDevice: NewVirtualDeviceClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Data.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Data.Use(hooks...)
	c.DeviceLog.Use(hooks...)
	c.Frame.Use(hooks...)
	c.Test.Use(hooks...)
	c.VirtualDevice.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Data.Intercept(interceptors...)
	c.DeviceLog.Intercept(interceptors...)
	c.Frame.Intercept(interceptors...)
	c.Test.Intercept(interceptors...)
	c.VirtualDevice.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *DataMutation:
		return c.Data.mutate(ctx, m)
	case *DeviceLogMutation:
		return c.DeviceLog.mutate(ctx, m)
	case *FrameMutation:
		return c.Frame.mutate(ctx, m)
	case *TestMutation:
		return c.Test.mutate(ctx, m)
	case *VirtualDeviceMutation:
		return c.VirtualDevice.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// DataClient is a client for the Data schema.
type DataClient struct {
	config
}

// NewDataClient returns a client for the Data from the given config.
func NewDataClient(c config) *DataClient {
	return &DataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `data.Hooks(f(g(h())))`.
func (c *DataClient) Use(hooks ...Hook) {
	c.hooks.Data = append(c.hooks.Data, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `data.Intercept(f(g(h())))`.
func (c *DataClient) Intercept(interceptors ...Interceptor) {
	c.inters.Data = append(c.inters.Data, interceptors...)
}

// Create returns a builder for creating a Data entity.
func (c *DataClient) Create() *DataCreate {
	mutation := newDataMutation(c.config, OpCreate)
	return &DataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Data entities.
func (c *DataClient) CreateBulk(builders ...*DataCreate) *DataCreateBulk {
	return &DataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DataClient) MapCreateBulk(slice any, setFunc func(*DataCreate, int)) *DataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DataCreateBulk{err: fmt.Errorf("calling to DataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Data.
func (c *DataClient) Update() *DataUpdate {
	mutation := newDataMutation(c.config, OpUpdate)
	return &DataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DataClient) UpdateOne(d *Data) *DataUpdateOne {
	mutation := newDataMutation(c.config, OpUpdateOne, withData(d))
	return &DataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DataClient) UpdateOneID(id int) *DataUpdateOne {
	mutation := newDataMutation(c.config, OpUpdateOne, withDataID(id))
	return &DataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Data.
func (c *DataClient) Delete() *DataDelete {
	mutation := newDataMutation(c.config, OpDelete)
	return &DataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DataClient) DeleteOne(d *Data) *DataDeleteOne {
	return c.DeleteOneID(d.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DataClient) DeleteOneID(id int) *DataDeleteOne {
	builder := c.Delete().Where(data.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DataDeleteOne{builder}
}

// Query returns a query builder for Data.
func (c *DataClient) Query() *DataQuery {
	return &DataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeData},
		inters: c.Interceptors(),
	}
}

// Get returns a Data entity by its id.
func (c *DataClient) Get(ctx context.Context, id int) (*Data, error) {
	return c.Query().Where(data.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DataClient) GetX(ctx context.Context, id int) *Data {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DataClient) Hooks() []Hook {
	return c.hooks.Data
}

// Interceptors returns the client interceptors.
func (c *DataClient) Interceptors() []Interceptor {
	return c.inters.Data
}

func (c *DataClient) mutate(ctx context.Context, m *DataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Data mutation op: %q", m.Op())
	}
}

// DeviceLogClient is a client for the DeviceLog schema.
type DeviceLogClient struct {
	config
}

// NewDeviceLogClient returns a client for the DeviceLog from the given config.
func NewDeviceLogClient(c config) *DeviceLogClient {
	return &DeviceLogClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `devicelog.Hooks(f(g(h())))`.
func (c *DeviceLogClient) Use(hooks ...Hook) {
	c.hooks.DeviceLog = append(c.hooks.DeviceLog, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `devicelog.Intercept(f(g(h())))`.
func (c *DeviceLogClient) Intercept(interceptors ...Interceptor) {
	c.inters.DeviceLog = append(c.inters.DeviceLog, interceptors...)
}

// Create returns a builder for creating a DeviceLog entity.
func (c *DeviceLogClient) Create() *DeviceLogCreate {
	mutation := newDeviceLogMutation(c.config, OpCreate)
	return &DeviceLogCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DeviceLog entities.
func (c *DeviceLogClient) CreateBulk(builders ...*DeviceLogCreate) *DeviceLogCreateBulk {
	return &DeviceLogCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DeviceLogClient) MapCreateBulk(slice any, setFunc func(*DeviceLogCreate, int)) *DeviceLogCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DeviceLogCreateBulk{err: fmt.Errorf("calling to DeviceLogClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DeviceLogCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DeviceLogCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DeviceLog.
func (c *DeviceLogClient) Update() *DeviceLogUpdate {
	mutation := newDeviceLogMutation(c.config, OpUpdate)
	return &DeviceLogUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DeviceLogClient) UpdateOne(dl *DeviceLog) *DeviceLogUpdateOne {
	mutation := newDeviceLogMutation(c.config, OpUpdateOne, withDeviceLog(dl))
	return &DeviceLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DeviceLogClient) UpdateOneID(id string) *DeviceLogUpdateOne {
	mutation := newDeviceLogMutation(c.config, OpUpdateOne, withDeviceLogID(id))
	return &DeviceLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DeviceLog.
func (c *DeviceLogClient) Delete() *DeviceLogDelete {
	mutation := newDeviceLogMutation(c.config, OpDelete)
	return &DeviceLogDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DeviceLogClient) DeleteOne(dl *DeviceLog) *DeviceLogDeleteOne {
	return c.DeleteOneID(dl.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DeviceLogClient) DeleteOneID(id string) *DeviceLogDeleteOne {
	builder := c.Delete().Where(devicelog.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DeviceLogDeleteOne{builder}
}

// Query returns a query builder for DeviceLog.
func (c *DeviceLogClient) Query() *DeviceLogQuery {
	return &DeviceLogQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDeviceLog},
		inters: c.Interceptors(),
	}
}

// Get returns a DeviceLog entity by its id.
func (c *DeviceLogClient) Get(ctx context.Context, id string) (*DeviceLog, error) {
	return c.Query().Where(devicelog.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DeviceLogClient) GetX(ctx context.Context, id string) *DeviceLog {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DeviceLogClient) Hooks() []Hook {
	return c.hooks.DeviceLog
}

// Interceptors returns the client interceptors.
func (c *DeviceLogClient) Interceptors() []Interceptor {
	return c.inters.DeviceLog
}

func (c *DeviceLogClient) mutate(ctx context.Context, m *DeviceLogMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DeviceLogCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DeviceLogUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DeviceLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DeviceLogDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DeviceLog mutation op: %q", m.Op())
	}
}

// FrameClient is a client for the Frame schema.
type FrameClient struct {
	config
}

// NewFrameClient returns a client for the Frame from the given config.
func NewFrameClient(c config) *FrameClient {
	return &FrameClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `frame.Hooks(f(g(h())))`.
func (c *FrameClient) Use(hooks ...Hook) {
	c.hooks.Frame = append(c.hooks.Frame, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `frame.Intercept(f(g(h())))`.
func (c *FrameClient) Intercept(interceptors ...Interceptor) {
	c.inters.Frame = append(c.inters.Frame, interceptors...)
}

// Create returns a builder for creating a Frame entity.
func (c *FrameClient) Create() *FrameCreate {
	mutation := newFrameMutation(c.config, OpCreate)
	return &FrameCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Frame entities.
func (c *FrameClient) CreateBulk(builders ...*FrameCreate) *FrameCreateBulk {
	return &FrameCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FrameClient) MapCreateBulk(slice any, setFunc func(*FrameCreate, int)) *FrameCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FrameCreateBulk{err: fmt.Errorf("calling to FrameClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FrameCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FrameCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Frame.
func (c *FrameClient) Update() *FrameUpdate {
	mutation := newFrameMutation(c.config, OpUpdate)
	return &FrameUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FrameClient) UpdateOne(f *Frame) *FrameUpdateOne {
	mutation := newFrameMutation(c.config, OpUpdateOne, withFrame(f))
	return &FrameUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FrameClient) UpdateOneID(id string) *FrameUpdateOne {
	mutation := newFrameMutation(c.config, OpUpdateOne, withFrameID(id))
	return &FrameUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Frame.
func (c *FrameClient) Delete() *FrameDelete {
	mutation := newFrameMutation(c.config, OpDelete)
	return &FrameDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FrameClient) DeleteOne(f *Frame) *FrameDeleteOne {
	return c.DeleteOneID(f.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FrameClient) DeleteOneID(id string) *FrameDeleteOne {
	builder := c.Delete().Where(frame.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FrameDeleteOne{builder}
}

// Query returns a query builder for Frame.
func (c *FrameClient) Query() *FrameQuery {
	return &FrameQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFrame},
		inters: c.Interceptors(),
	}
}

// Get returns a Frame entity by its id.
func (c *FrameClient) Get(ctx context.Context, id string) (*Frame, error) {
	return c.Query().Where(frame.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FrameClient) GetX(ctx context.Context, id string) *Frame {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *FrameClient) Hooks() []Hook {
	return c.hooks.Frame
}

// Interceptors returns the client interceptors.
func (c *FrameClient) Interceptors() []Interceptor {
	return c.inters.Frame
}

func (c *FrameClient) mutate(ctx context.Context, m *FrameMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FrameCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FrameUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FrameUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FrameDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Frame mutation op: %q", m.Op())
	}
}

// TestClient is a client for the Test schema.
type TestClient struct {
	config
}

// NewTestClient returns a client for the Test from the given config.
func NewTestClient(c config) *TestClient {
	return &TestClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `test.Hooks(f(g(h())))`.
func (c *TestClient) Use(hooks ...Hook) {
	c.hooks.Test = append(c.hooks.Test, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `test.Intercept(f(g(h())))`.
func (c *TestClient) Intercept(interceptors ...Interceptor) {
	c.inters.Test = append(c.inters.Test, interceptors...)
}

// Create returns a builder for creating a Test entity.
func (c *TestClient) Create() *TestCreate {
	mutation := newTestMutation(c.config, OpCreate)
	return &TestCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Test entities.
func (c *TestClient) CreateBulk(builders ...*TestCreate) *TestCreateBulk {
	return &TestCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TestClient) MapCreateBulk(slice any, setFunc func(*TestCreate, int)) *TestCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TestCreateBulk{err: fmt.Errorf("calling to TestClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TestCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TestCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Test.
func (c *TestClient) Update() *TestUpdate {
	mutation := newTestMutation(c.config, OpUpdate)
	return &TestUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TestClient) UpdateOne(t *Test) *TestUpdateOne {
	mutation := newTestMutation(c.config, OpUpdateOne, withTest(t))
	return &TestUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TestClient) UpdateOneID(id int) *TestUpdateOne {
	mutation := newTestMutation(c.config, OpUpdateOne, withTestID(id))
	return &TestUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Test.
func (c *TestClient) Delete() *TestDelete {
	mutation := newTestMutation(c.config, OpDelete)
	return &TestDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TestClient) DeleteOne(t *Test) *TestDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TestClient) DeleteOneID(id int) *TestDeleteOne {
	builder := c.Delete().Where(test.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TestDeleteOne{builder}
}

// Query returns a query builder for Test.
func (c *TestClient) Query() *TestQuery {
	return &TestQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTest},
		inters: c.Interceptors(),
	}
}

// Get returns a Test entity by its id.
func (c *TestClient) Get(ctx context.Context, id int) (*Test, error) {
	return c.Query().Where(test.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TestClient) GetX(ctx context.Context, id int) *Test {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TestClient) Hooks() []Hook {
	return c.hooks.Test
}

// Interceptors returns the client interceptors.
func (c *TestClient) Interceptors() []Interceptor {
	return c.inters.Test
}

func (c *TestClient) mutate(ctx context.Context, m *TestMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TestCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TestUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TestUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TestDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Test mutation op: %q", m.Op())
	}
}

// VirtualDeviceClient is a client for the VirtualDevice schema.
type VirtualDeviceClient struct {
	config
}

// NewVirtualDeviceClient returns a client for the VirtualDevice from the given config.
func NewVirtualDeviceClient(c config) *VirtualDeviceClient {
	return &VirtualDeviceClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `virtualdevice.Hooks(f(g(h())))`.
func (c *VirtualDeviceClient) Use(hooks ...Hook) {
	c.hooks.VirtualDevice = append(c.hooks.VirtualDevice, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `virtualdevice.Intercept(f(g(h())))`.
func (c *VirtualDeviceClient) Intercept(interceptors ...Interceptor) {
	c.inters.VirtualDevice = append(c.inters.VirtualDevice, interceptors...)
}

// Create returns a builder for creating a VirtualDevice entity.
func (c *VirtualDeviceClient) Create() *VirtualDeviceCreate {
	mutation := newVirtualDeviceMutation(c.config, OpCreate)
	return &VirtualDeviceCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of VirtualDevice entities.
func (c *VirtualDeviceClient) CreateBulk(builders ...*VirtualDeviceCreate) *VirtualDeviceCreateBulk {
	return &VirtualDeviceCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *VirtualDeviceClient) MapCreateBulk(slice any, setFunc func(*VirtualDeviceCreate, int)) *VirtualDeviceCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &VirtualDeviceCreateBulk{err: fmt.Errorf("calling to VirtualDeviceClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*VirtualDeviceCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &VirtualDeviceCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for VirtualDevice.
func (c *VirtualDeviceClient) Update() *VirtualDeviceUpdate {
	mutation := newVirtualDeviceMutation(c.config, OpUpdate)
	return &VirtualDeviceUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *VirtualDeviceClient) UpdateOne(vd *VirtualDevice) *VirtualDeviceUpdateOne {
	mutation := newVirtualDeviceMutation(c.config, OpUpdateOne, withVirtualDevice(vd))
	return &VirtualDeviceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *VirtualDeviceClient) UpdateOneID(id string) *VirtualDeviceUpdateOne {
	mutation := newVirtualDeviceMutation(c.config, OpUpdateOne, withVirtualDeviceID(id))
	return &VirtualDeviceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for VirtualDevice.
func (c *VirtualDeviceClient) Delete() *VirtualDeviceDelete {
	mutation := newVirtualDeviceMutation(c.config, OpDelete)
	return &VirtualDeviceDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *VirtualDeviceClient) DeleteOne(vd *VirtualDevice) *VirtualDeviceDeleteOne {
	return c.DeleteOneID(vd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *VirtualDeviceClient) DeleteOneID(id string) *VirtualDeviceDeleteOne {
	builder := c.Delete().Where(virtualdevice.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &VirtualDeviceDeleteOne{builder}
}

// Query returns a query builder for VirtualDevice.
func (c *VirtualDeviceClient) Query() *VirtualDeviceQuery {
	return &VirtualDeviceQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeVirtualDevice},
		inters: c.Interceptors(),
	}
}

// Get returns a VirtualDevice entity by its id.
func (c *VirtualDeviceClient) Get(ctx context.Context, id string) (*VirtualDevice, error) {
	return c.Query().Where(virtualdevice.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *VirtualDeviceClient) GetX(ctx context.Context, id string) *VirtualDevice {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *VirtualDeviceClient) Hooks() []Hook {
	return c.hooks.VirtualDevice
}

// Interceptors returns the client interceptors.
func (c *VirtualDeviceClient) Interceptors() []Interceptor {
	return c.inters.VirtualDevice
}

func (c *VirtualDeviceClient) mutate(ctx context.Context, m *VirtualDeviceMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&VirtualDeviceCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&VirtualDeviceUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&VirtualDeviceUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&VirtualDeviceDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown VirtualDevice mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Data, DeviceLog, Frame, Test, VirtualDevice []ent.Hook
	}
	inters struct {
		Data, DeviceLog, Frame, Test, VirtualDevice []ent.Interceptor
	}
)

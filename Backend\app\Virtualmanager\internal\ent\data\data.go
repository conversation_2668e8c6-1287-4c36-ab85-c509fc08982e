// Code generated by ent, DO NOT EDIT.

package data

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the data type in the database.
	Label = "data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldFrameID holds the string denoting the frame_id field in the database.
	FieldFrameID = "frame_id"
	// FieldIndex holds the string denoting the index field in the database.
	FieldIndex = "index"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldValue holds the string denoting the value field in the database.
	FieldValue = "value"
	// Table holds the table name of the data in the database.
	Table = "data"
)

// Columns holds all SQL columns for data fields.
var Columns = []string{
	FieldID,
	FieldFrameID,
	FieldIndex,
	FieldName,
	FieldType,
	FieldValue,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// FrameIDValidator is a validator for the "frame_id" field. It is called by the builders before save.
	FrameIDValidator func(string) error
	// IndexValidator is a validator for the "index" field. It is called by the builders before save.
	IndexValidator func(string) error
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType string
	// DefaultValue holds the default value on creation for the "value" field.
	DefaultValue string
)

// OrderOption defines the ordering options for the Data queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByFrameID orders the results by the frame_id field.
func ByFrameID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFrameID, opts...).ToFunc()
}

// ByIndex orders the results by the index field.
func ByIndex(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIndex, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByValue orders the results by the value field.
func ByValue(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldValue, opts...).ToFunc()
}

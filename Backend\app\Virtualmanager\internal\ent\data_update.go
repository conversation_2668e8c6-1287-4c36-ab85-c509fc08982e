// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/data"
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DataUpdate is the builder for updating Data entities.
type DataUpdate struct {
	config
	hooks    []Hook
	mutation *DataMutation
}

// Where appends a list predicates to the DataUpdate builder.
func (du *DataUpdate) Where(ps ...predicate.Data) *DataUpdate {
	du.mutation.Where(ps...)
	return du
}

// SetFrameID sets the "frame_id" field.
func (du *DataUpdate) SetFrameID(s string) *DataUpdate {
	du.mutation.SetFrameID(s)
	return du
}

// SetNillableFrameID sets the "frame_id" field if the given value is not nil.
func (du *DataUpdate) SetNillableFrameID(s *string) *DataUpdate {
	if s != nil {
		du.SetFrameID(*s)
	}
	return du
}

// SetIndex sets the "index" field.
func (du *DataUpdate) SetIndex(s string) *DataUpdate {
	du.mutation.SetIndex(s)
	return du
}

// SetNillableIndex sets the "index" field if the given value is not nil.
func (du *DataUpdate) SetNillableIndex(s *string) *DataUpdate {
	if s != nil {
		du.SetIndex(*s)
	}
	return du
}

// SetName sets the "name" field.
func (du *DataUpdate) SetName(s string) *DataUpdate {
	du.mutation.SetName(s)
	return du
}

// SetNillableName sets the "name" field if the given value is not nil.
func (du *DataUpdate) SetNillableName(s *string) *DataUpdate {
	if s != nil {
		du.SetName(*s)
	}
	return du
}

// SetType sets the "type" field.
func (du *DataUpdate) SetType(s string) *DataUpdate {
	du.mutation.SetType(s)
	return du
}

// SetNillableType sets the "type" field if the given value is not nil.
func (du *DataUpdate) SetNillableType(s *string) *DataUpdate {
	if s != nil {
		du.SetType(*s)
	}
	return du
}

// SetValue sets the "value" field.
func (du *DataUpdate) SetValue(s string) *DataUpdate {
	du.mutation.SetValue(s)
	return du
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (du *DataUpdate) SetNillableValue(s *string) *DataUpdate {
	if s != nil {
		du.SetValue(*s)
	}
	return du
}

// Mutation returns the DataMutation object of the builder.
func (du *DataUpdate) Mutation() *DataMutation {
	return du.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (du *DataUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, du.sqlSave, du.mutation, du.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (du *DataUpdate) SaveX(ctx context.Context) int {
	affected, err := du.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (du *DataUpdate) Exec(ctx context.Context) error {
	_, err := du.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (du *DataUpdate) ExecX(ctx context.Context) {
	if err := du.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (du *DataUpdate) check() error {
	if v, ok := du.mutation.FrameID(); ok {
		if err := data.FrameIDValidator(v); err != nil {
			return &ValidationError{Name: "frame_id", err: fmt.Errorf(`ent: validator failed for field "Data.frame_id": %w`, err)}
		}
	}
	if v, ok := du.mutation.Index(); ok {
		if err := data.IndexValidator(v); err != nil {
			return &ValidationError{Name: "index", err: fmt.Errorf(`ent: validator failed for field "Data.index": %w`, err)}
		}
	}
	if v, ok := du.mutation.Name(); ok {
		if err := data.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Data.name": %w`, err)}
		}
	}
	return nil
}

func (du *DataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := du.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(data.Table, data.Columns, sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt))
	if ps := du.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := du.mutation.FrameID(); ok {
		_spec.SetField(data.FieldFrameID, field.TypeString, value)
	}
	if value, ok := du.mutation.Index(); ok {
		_spec.SetField(data.FieldIndex, field.TypeString, value)
	}
	if value, ok := du.mutation.Name(); ok {
		_spec.SetField(data.FieldName, field.TypeString, value)
	}
	if value, ok := du.mutation.GetType(); ok {
		_spec.SetField(data.FieldType, field.TypeString, value)
	}
	if value, ok := du.mutation.Value(); ok {
		_spec.SetField(data.FieldValue, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, du.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{data.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	du.mutation.done = true
	return n, nil
}

// DataUpdateOne is the builder for updating a single Data entity.
type DataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *DataMutation
}

// SetFrameID sets the "frame_id" field.
func (duo *DataUpdateOne) SetFrameID(s string) *DataUpdateOne {
	duo.mutation.SetFrameID(s)
	return duo
}

// SetNillableFrameID sets the "frame_id" field if the given value is not nil.
func (duo *DataUpdateOne) SetNillableFrameID(s *string) *DataUpdateOne {
	if s != nil {
		duo.SetFrameID(*s)
	}
	return duo
}

// SetIndex sets the "index" field.
func (duo *DataUpdateOne) SetIndex(s string) *DataUpdateOne {
	duo.mutation.SetIndex(s)
	return duo
}

// SetNillableIndex sets the "index" field if the given value is not nil.
func (duo *DataUpdateOne) SetNillableIndex(s *string) *DataUpdateOne {
	if s != nil {
		duo.SetIndex(*s)
	}
	return duo
}

// SetName sets the "name" field.
func (duo *DataUpdateOne) SetName(s string) *DataUpdateOne {
	duo.mutation.SetName(s)
	return duo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (duo *DataUpdateOne) SetNillableName(s *string) *DataUpdateOne {
	if s != nil {
		duo.SetName(*s)
	}
	return duo
}

// SetType sets the "type" field.
func (duo *DataUpdateOne) SetType(s string) *DataUpdateOne {
	duo.mutation.SetType(s)
	return duo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (duo *DataUpdateOne) SetNillableType(s *string) *DataUpdateOne {
	if s != nil {
		duo.SetType(*s)
	}
	return duo
}

// SetValue sets the "value" field.
func (duo *DataUpdateOne) SetValue(s string) *DataUpdateOne {
	duo.mutation.SetValue(s)
	return duo
}

// SetNillableValue sets the "value" field if the given value is not nil.
func (duo *DataUpdateOne) SetNillableValue(s *string) *DataUpdateOne {
	if s != nil {
		duo.SetValue(*s)
	}
	return duo
}

// Mutation returns the DataMutation object of the builder.
func (duo *DataUpdateOne) Mutation() *DataMutation {
	return duo.mutation
}

// Where appends a list predicates to the DataUpdate builder.
func (duo *DataUpdateOne) Where(ps ...predicate.Data) *DataUpdateOne {
	duo.mutation.Where(ps...)
	return duo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (duo *DataUpdateOne) Select(field string, fields ...string) *DataUpdateOne {
	duo.fields = append([]string{field}, fields...)
	return duo
}

// Save executes the query and returns the updated Data entity.
func (duo *DataUpdateOne) Save(ctx context.Context) (*Data, error) {
	return withHooks(ctx, duo.sqlSave, duo.mutation, duo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (duo *DataUpdateOne) SaveX(ctx context.Context) *Data {
	node, err := duo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (duo *DataUpdateOne) Exec(ctx context.Context) error {
	_, err := duo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (duo *DataUpdateOne) ExecX(ctx context.Context) {
	if err := duo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (duo *DataUpdateOne) check() error {
	if v, ok := duo.mutation.FrameID(); ok {
		if err := data.FrameIDValidator(v); err != nil {
			return &ValidationError{Name: "frame_id", err: fmt.Errorf(`ent: validator failed for field "Data.frame_id": %w`, err)}
		}
	}
	if v, ok := duo.mutation.Index(); ok {
		if err := data.IndexValidator(v); err != nil {
			return &ValidationError{Name: "index", err: fmt.Errorf(`ent: validator failed for field "Data.index": %w`, err)}
		}
	}
	if v, ok := duo.mutation.Name(); ok {
		if err := data.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Data.name": %w`, err)}
		}
	}
	return nil
}

func (duo *DataUpdateOne) sqlSave(ctx context.Context) (_node *Data, err error) {
	if err := duo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(data.Table, data.Columns, sqlgraph.NewFieldSpec(data.FieldID, field.TypeInt))
	id, ok := duo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Data.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := duo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, data.FieldID)
		for _, f := range fields {
			if !data.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != data.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := duo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := duo.mutation.FrameID(); ok {
		_spec.SetField(data.FieldFrameID, field.TypeString, value)
	}
	if value, ok := duo.mutation.Index(); ok {
		_spec.SetField(data.FieldIndex, field.TypeString, value)
	}
	if value, ok := duo.mutation.Name(); ok {
		_spec.SetField(data.FieldName, field.TypeString, value)
	}
	if value, ok := duo.mutation.GetType(); ok {
		_spec.SetField(data.FieldType, field.TypeString, value)
	}
	if value, ok := duo.mutation.Value(); ok {
		_spec.SetField(data.FieldValue, field.TypeString, value)
	}
	_node = &Data{config: duo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, duo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{data.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	duo.mutation.done = true
	return _node, nil
}

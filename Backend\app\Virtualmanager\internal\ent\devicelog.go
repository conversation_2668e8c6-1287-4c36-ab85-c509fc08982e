// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// DeviceLog is the model entity for the DeviceLog schema.
type DeviceLog struct {
	config `json:"-"`
	// ID of the ent.
	// Foreign key to device_id
	ID string `json:"id,omitempty"`
	// name of the device
	Name string `json:"name,omitempty"`
	// log of health check
	Healthlog string `json:"healthlog,omitempty"`
	// log of simulation
	Simulatelog  string `json:"simulatelog,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DeviceLog) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case devicelog.FieldID, devicelog.FieldName, devicelog.FieldHealthlog, devicelog.FieldSimulatelog:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DeviceLog fields.
func (dl *DeviceLog) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case devicelog.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				dl.ID = value.String
			}
		case devicelog.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				dl.Name = value.String
			}
		case devicelog.FieldHealthlog:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field healthlog", values[i])
			} else if value.Valid {
				dl.Healthlog = value.String
			}
		case devicelog.FieldSimulatelog:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field simulatelog", values[i])
			} else if value.Valid {
				dl.Simulatelog = value.String
			}
		default:
			dl.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DeviceLog.
// This includes values selected through modifiers, order, etc.
func (dl *DeviceLog) Value(name string) (ent.Value, error) {
	return dl.selectValues.Get(name)
}

// Update returns a builder for updating this DeviceLog.
// Note that you need to call DeviceLog.Unwrap() before calling this method if this DeviceLog
// was returned from a transaction, and the transaction was committed or rolled back.
func (dl *DeviceLog) Update() *DeviceLogUpdateOne {
	return NewDeviceLogClient(dl.config).UpdateOne(dl)
}

// Unwrap unwraps the DeviceLog entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (dl *DeviceLog) Unwrap() *DeviceLog {
	_tx, ok := dl.config.driver.(*txDriver)
	if !ok {
		panic("ent: DeviceLog is not a transactional entity")
	}
	dl.config.driver = _tx.drv
	return dl
}

// String implements the fmt.Stringer.
func (dl *DeviceLog) String() string {
	var builder strings.Builder
	builder.WriteString("DeviceLog(")
	builder.WriteString(fmt.Sprintf("id=%v, ", dl.ID))
	builder.WriteString("name=")
	builder.WriteString(dl.Name)
	builder.WriteString(", ")
	builder.WriteString("healthlog=")
	builder.WriteString(dl.Healthlog)
	builder.WriteString(", ")
	builder.WriteString("simulatelog=")
	builder.WriteString(dl.Simulatelog)
	builder.WriteByte(')')
	return builder.String()
}

// DeviceLogs is a parsable slice of DeviceLog.
type DeviceLogs []*DeviceLog

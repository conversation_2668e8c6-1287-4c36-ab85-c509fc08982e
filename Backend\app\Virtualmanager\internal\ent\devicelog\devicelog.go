// Code generated by ent, DO NOT EDIT.

package devicelog

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the devicelog type in the database.
	Label = "device_log"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldHealthlog holds the string denoting the healthlog field in the database.
	FieldHealthlog = "healthlog"
	// FieldSimulatelog holds the string denoting the simulatelog field in the database.
	FieldSimulatelog = "simulatelog"
	// Table holds the table name of the devicelog in the database.
	Table = "device_logs"
)

// Columns holds all SQL columns for devicelog fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldHealthlog,
	FieldSimulatelog,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// DefaultHealthlog holds the default value on creation for the "healthlog" field.
	DefaultHealthlog string
	// DefaultSimulatelog holds the default value on creation for the "simulatelog" field.
	DefaultSimulatelog string
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the DeviceLog queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByHealthlog orders the results by the healthlog field.
func ByHealthlog(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHealthlog, opts...).ToFunc()
}

// BySimulatelog orders the results by the simulatelog field.
func BySimulatelog(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSimulatelog, opts...).ToFunc()
}

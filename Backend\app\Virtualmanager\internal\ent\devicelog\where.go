// Code generated by ent, DO NOT EDIT.

package devicelog

import (
	"GCF/app/Virtualmanager/internal/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldContainsFold(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldName, v))
}

// Healthlog applies equality check predicate on the "healthlog" field. It's identical to HealthlogEQ.
func Healthlog(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldHealthlog, v))
}

// Simulatelog applies equality check predicate on the "simulatelog" field. It's identical to SimulatelogEQ.
func Simulatelog(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldSimulatelog, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldContainsFold(FieldName, v))
}

// HealthlogEQ applies the EQ predicate on the "healthlog" field.
func HealthlogEQ(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldHealthlog, v))
}

// HealthlogNEQ applies the NEQ predicate on the "healthlog" field.
func HealthlogNEQ(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNEQ(FieldHealthlog, v))
}

// HealthlogIn applies the In predicate on the "healthlog" field.
func HealthlogIn(vs ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldIn(FieldHealthlog, vs...))
}

// HealthlogNotIn applies the NotIn predicate on the "healthlog" field.
func HealthlogNotIn(vs ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNotIn(FieldHealthlog, vs...))
}

// HealthlogGT applies the GT predicate on the "healthlog" field.
func HealthlogGT(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGT(FieldHealthlog, v))
}

// HealthlogGTE applies the GTE predicate on the "healthlog" field.
func HealthlogGTE(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGTE(FieldHealthlog, v))
}

// HealthlogLT applies the LT predicate on the "healthlog" field.
func HealthlogLT(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLT(FieldHealthlog, v))
}

// HealthlogLTE applies the LTE predicate on the "healthlog" field.
func HealthlogLTE(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLTE(FieldHealthlog, v))
}

// HealthlogContains applies the Contains predicate on the "healthlog" field.
func HealthlogContains(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldContains(FieldHealthlog, v))
}

// HealthlogHasPrefix applies the HasPrefix predicate on the "healthlog" field.
func HealthlogHasPrefix(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldHasPrefix(FieldHealthlog, v))
}

// HealthlogHasSuffix applies the HasSuffix predicate on the "healthlog" field.
func HealthlogHasSuffix(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldHasSuffix(FieldHealthlog, v))
}

// HealthlogEqualFold applies the EqualFold predicate on the "healthlog" field.
func HealthlogEqualFold(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEqualFold(FieldHealthlog, v))
}

// HealthlogContainsFold applies the ContainsFold predicate on the "healthlog" field.
func HealthlogContainsFold(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldContainsFold(FieldHealthlog, v))
}

// SimulatelogEQ applies the EQ predicate on the "simulatelog" field.
func SimulatelogEQ(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEQ(FieldSimulatelog, v))
}

// SimulatelogNEQ applies the NEQ predicate on the "simulatelog" field.
func SimulatelogNEQ(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNEQ(FieldSimulatelog, v))
}

// SimulatelogIn applies the In predicate on the "simulatelog" field.
func SimulatelogIn(vs ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldIn(FieldSimulatelog, vs...))
}

// SimulatelogNotIn applies the NotIn predicate on the "simulatelog" field.
func SimulatelogNotIn(vs ...string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldNotIn(FieldSimulatelog, vs...))
}

// SimulatelogGT applies the GT predicate on the "simulatelog" field.
func SimulatelogGT(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGT(FieldSimulatelog, v))
}

// SimulatelogGTE applies the GTE predicate on the "simulatelog" field.
func SimulatelogGTE(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldGTE(FieldSimulatelog, v))
}

// SimulatelogLT applies the LT predicate on the "simulatelog" field.
func SimulatelogLT(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLT(FieldSimulatelog, v))
}

// SimulatelogLTE applies the LTE predicate on the "simulatelog" field.
func SimulatelogLTE(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldLTE(FieldSimulatelog, v))
}

// SimulatelogContains applies the Contains predicate on the "simulatelog" field.
func SimulatelogContains(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldContains(FieldSimulatelog, v))
}

// SimulatelogHasPrefix applies the HasPrefix predicate on the "simulatelog" field.
func SimulatelogHasPrefix(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldHasPrefix(FieldSimulatelog, v))
}

// SimulatelogHasSuffix applies the HasSuffix predicate on the "simulatelog" field.
func SimulatelogHasSuffix(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldHasSuffix(FieldSimulatelog, v))
}

// SimulatelogEqualFold applies the EqualFold predicate on the "simulatelog" field.
func SimulatelogEqualFold(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldEqualFold(FieldSimulatelog, v))
}

// SimulatelogContainsFold applies the ContainsFold predicate on the "simulatelog" field.
func SimulatelogContainsFold(v string) predicate.DeviceLog {
	return predicate.DeviceLog(sql.FieldContainsFold(FieldSimulatelog, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DeviceLog) predicate.DeviceLog {
	return predicate.DeviceLog(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DeviceLog) predicate.DeviceLog {
	return predicate.DeviceLog(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DeviceLog) predicate.DeviceLog {
	return predicate.DeviceLog(sql.NotPredicates(p))
}

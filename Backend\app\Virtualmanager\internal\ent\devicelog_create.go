// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DeviceLogCreate is the builder for creating a DeviceLog entity.
type DeviceLogCreate struct {
	config
	mutation *DeviceLogMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (dlc *DeviceLogCreate) SetName(s string) *DeviceLogCreate {
	dlc.mutation.SetName(s)
	return dlc
}

// SetHealthlog sets the "healthlog" field.
func (dlc *DeviceLogCreate) SetHealthlog(s string) *DeviceLogCreate {
	dlc.mutation.SetHealthlog(s)
	return dlc
}

// SetNillableHealthlog sets the "healthlog" field if the given value is not nil.
func (dlc *DeviceLogCreate) SetNillableHealthlog(s *string) *DeviceLogCreate {
	if s != nil {
		dlc.SetHealthlog(*s)
	}
	return dlc
}

// SetSimulatelog sets the "simulatelog" field.
func (dlc *DeviceLogCreate) SetSimulatelog(s string) *DeviceLogCreate {
	dlc.mutation.SetSimulatelog(s)
	return dlc
}

// SetNillableSimulatelog sets the "simulatelog" field if the given value is not nil.
func (dlc *DeviceLogCreate) SetNillableSimulatelog(s *string) *DeviceLogCreate {
	if s != nil {
		dlc.SetSimulatelog(*s)
	}
	return dlc
}

// SetID sets the "id" field.
func (dlc *DeviceLogCreate) SetID(s string) *DeviceLogCreate {
	dlc.mutation.SetID(s)
	return dlc
}

// Mutation returns the DeviceLogMutation object of the builder.
func (dlc *DeviceLogCreate) Mutation() *DeviceLogMutation {
	return dlc.mutation
}

// Save creates the DeviceLog in the database.
func (dlc *DeviceLogCreate) Save(ctx context.Context) (*DeviceLog, error) {
	dlc.defaults()
	return withHooks(ctx, dlc.sqlSave, dlc.mutation, dlc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dlc *DeviceLogCreate) SaveX(ctx context.Context) *DeviceLog {
	v, err := dlc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dlc *DeviceLogCreate) Exec(ctx context.Context) error {
	_, err := dlc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dlc *DeviceLogCreate) ExecX(ctx context.Context) {
	if err := dlc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dlc *DeviceLogCreate) defaults() {
	if _, ok := dlc.mutation.Healthlog(); !ok {
		v := devicelog.DefaultHealthlog
		dlc.mutation.SetHealthlog(v)
	}
	if _, ok := dlc.mutation.Simulatelog(); !ok {
		v := devicelog.DefaultSimulatelog
		dlc.mutation.SetSimulatelog(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dlc *DeviceLogCreate) check() error {
	if _, ok := dlc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "DeviceLog.name"`)}
	}
	if v, ok := dlc.mutation.Name(); ok {
		if err := devicelog.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "DeviceLog.name": %w`, err)}
		}
	}
	if _, ok := dlc.mutation.Healthlog(); !ok {
		return &ValidationError{Name: "healthlog", err: errors.New(`ent: missing required field "DeviceLog.healthlog"`)}
	}
	if _, ok := dlc.mutation.Simulatelog(); !ok {
		return &ValidationError{Name: "simulatelog", err: errors.New(`ent: missing required field "DeviceLog.simulatelog"`)}
	}
	if v, ok := dlc.mutation.ID(); ok {
		if err := devicelog.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "DeviceLog.id": %w`, err)}
		}
	}
	return nil
}

func (dlc *DeviceLogCreate) sqlSave(ctx context.Context) (*DeviceLog, error) {
	if err := dlc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dlc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dlc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected DeviceLog.ID type: %T", _spec.ID.Value)
		}
	}
	dlc.mutation.id = &_node.ID
	dlc.mutation.done = true
	return _node, nil
}

func (dlc *DeviceLogCreate) createSpec() (*DeviceLog, *sqlgraph.CreateSpec) {
	var (
		_node = &DeviceLog{config: dlc.config}
		_spec = sqlgraph.NewCreateSpec(devicelog.Table, sqlgraph.NewFieldSpec(devicelog.FieldID, field.TypeString))
	)
	if id, ok := dlc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := dlc.mutation.Name(); ok {
		_spec.SetField(devicelog.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := dlc.mutation.Healthlog(); ok {
		_spec.SetField(devicelog.FieldHealthlog, field.TypeString, value)
		_node.Healthlog = value
	}
	if value, ok := dlc.mutation.Simulatelog(); ok {
		_spec.SetField(devicelog.FieldSimulatelog, field.TypeString, value)
		_node.Simulatelog = value
	}
	return _node, _spec
}

// DeviceLogCreateBulk is the builder for creating many DeviceLog entities in bulk.
type DeviceLogCreateBulk struct {
	config
	err      error
	builders []*DeviceLogCreate
}

// Save creates the DeviceLog entities in the database.
func (dlcb *DeviceLogCreateBulk) Save(ctx context.Context) ([]*DeviceLog, error) {
	if dlcb.err != nil {
		return nil, dlcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dlcb.builders))
	nodes := make([]*DeviceLog, len(dlcb.builders))
	mutators := make([]Mutator, len(dlcb.builders))
	for i := range dlcb.builders {
		func(i int, root context.Context) {
			builder := dlcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DeviceLogMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dlcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dlcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dlcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dlcb *DeviceLogCreateBulk) SaveX(ctx context.Context) []*DeviceLog {
	v, err := dlcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dlcb *DeviceLogCreateBulk) Exec(ctx context.Context) error {
	_, err := dlcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dlcb *DeviceLogCreateBulk) ExecX(ctx context.Context) {
	if err := dlcb.Exec(ctx); err != nil {
		panic(err)
	}
}

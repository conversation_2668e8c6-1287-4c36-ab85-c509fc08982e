// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DeviceLogDelete is the builder for deleting a DeviceLog entity.
type DeviceLogDelete struct {
	config
	hooks    []Hook
	mutation *DeviceLogMutation
}

// Where appends a list predicates to the DeviceLogDelete builder.
func (dld *DeviceLogDelete) Where(ps ...predicate.DeviceLog) *DeviceLogDelete {
	dld.mutation.Where(ps...)
	return dld
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (dld *DeviceLogDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, dld.sqlExec, dld.mutation, dld.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (dld *DeviceLogDelete) ExecX(ctx context.Context) int {
	n, err := dld.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (dld *DeviceLogDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(devicelog.Table, sqlgraph.NewFieldSpec(devicelog.FieldID, field.TypeString))
	if ps := dld.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, dld.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	dld.mutation.done = true
	return affected, err
}

// DeviceLogDeleteOne is the builder for deleting a single DeviceLog entity.
type DeviceLogDeleteOne struct {
	dld *DeviceLogDelete
}

// Where appends a list predicates to the DeviceLogDelete builder.
func (dldo *DeviceLogDeleteOne) Where(ps ...predicate.DeviceLog) *DeviceLogDeleteOne {
	dldo.dld.mutation.Where(ps...)
	return dldo
}

// Exec executes the deletion query.
func (dldo *DeviceLogDeleteOne) Exec(ctx context.Context) error {
	n, err := dldo.dld.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{devicelog.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (dldo *DeviceLogDeleteOne) ExecX(ctx context.Context) {
	if err := dldo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DeviceLogQuery is the builder for querying DeviceLog entities.
type DeviceLogQuery struct {
	config
	ctx        *QueryContext
	order      []devicelog.OrderOption
	inters     []Interceptor
	predicates []predicate.DeviceLog
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DeviceLogQuery builder.
func (dlq *DeviceLogQuery) Where(ps ...predicate.DeviceLog) *DeviceLogQuery {
	dlq.predicates = append(dlq.predicates, ps...)
	return dlq
}

// Limit the number of records to be returned by this query.
func (dlq *DeviceLogQuery) Limit(limit int) *DeviceLogQuery {
	dlq.ctx.Limit = &limit
	return dlq
}

// Offset to start from.
func (dlq *DeviceLogQuery) Offset(offset int) *DeviceLogQuery {
	dlq.ctx.Offset = &offset
	return dlq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (dlq *DeviceLogQuery) Unique(unique bool) *DeviceLogQuery {
	dlq.ctx.Unique = &unique
	return dlq
}

// Order specifies how the records should be ordered.
func (dlq *DeviceLogQuery) Order(o ...devicelog.OrderOption) *DeviceLogQuery {
	dlq.order = append(dlq.order, o...)
	return dlq
}

// First returns the first DeviceLog entity from the query.
// Returns a *NotFoundError when no DeviceLog was found.
func (dlq *DeviceLogQuery) First(ctx context.Context) (*DeviceLog, error) {
	nodes, err := dlq.Limit(1).All(setContextOp(ctx, dlq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{devicelog.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (dlq *DeviceLogQuery) FirstX(ctx context.Context) *DeviceLog {
	node, err := dlq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first DeviceLog ID from the query.
// Returns a *NotFoundError when no DeviceLog ID was found.
func (dlq *DeviceLogQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = dlq.Limit(1).IDs(setContextOp(ctx, dlq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{devicelog.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (dlq *DeviceLogQuery) FirstIDX(ctx context.Context) string {
	id, err := dlq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single DeviceLog entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one DeviceLog entity is found.
// Returns a *NotFoundError when no DeviceLog entities are found.
func (dlq *DeviceLogQuery) Only(ctx context.Context) (*DeviceLog, error) {
	nodes, err := dlq.Limit(2).All(setContextOp(ctx, dlq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{devicelog.Label}
	default:
		return nil, &NotSingularError{devicelog.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (dlq *DeviceLogQuery) OnlyX(ctx context.Context) *DeviceLog {
	node, err := dlq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only DeviceLog ID in the query.
// Returns a *NotSingularError when more than one DeviceLog ID is found.
// Returns a *NotFoundError when no entities are found.
func (dlq *DeviceLogQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = dlq.Limit(2).IDs(setContextOp(ctx, dlq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{devicelog.Label}
	default:
		err = &NotSingularError{devicelog.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (dlq *DeviceLogQuery) OnlyIDX(ctx context.Context) string {
	id, err := dlq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of DeviceLogs.
func (dlq *DeviceLogQuery) All(ctx context.Context) ([]*DeviceLog, error) {
	ctx = setContextOp(ctx, dlq.ctx, ent.OpQueryAll)
	if err := dlq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*DeviceLog, *DeviceLogQuery]()
	return withInterceptors[[]*DeviceLog](ctx, dlq, qr, dlq.inters)
}

// AllX is like All, but panics if an error occurs.
func (dlq *DeviceLogQuery) AllX(ctx context.Context) []*DeviceLog {
	nodes, err := dlq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of DeviceLog IDs.
func (dlq *DeviceLogQuery) IDs(ctx context.Context) (ids []string, err error) {
	if dlq.ctx.Unique == nil && dlq.path != nil {
		dlq.Unique(true)
	}
	ctx = setContextOp(ctx, dlq.ctx, ent.OpQueryIDs)
	if err = dlq.Select(devicelog.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (dlq *DeviceLogQuery) IDsX(ctx context.Context) []string {
	ids, err := dlq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (dlq *DeviceLogQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, dlq.ctx, ent.OpQueryCount)
	if err := dlq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, dlq, querierCount[*DeviceLogQuery](), dlq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (dlq *DeviceLogQuery) CountX(ctx context.Context) int {
	count, err := dlq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (dlq *DeviceLogQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, dlq.ctx, ent.OpQueryExist)
	switch _, err := dlq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (dlq *DeviceLogQuery) ExistX(ctx context.Context) bool {
	exist, err := dlq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DeviceLogQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (dlq *DeviceLogQuery) Clone() *DeviceLogQuery {
	if dlq == nil {
		return nil
	}
	return &DeviceLogQuery{
		config:     dlq.config,
		ctx:        dlq.ctx.Clone(),
		order:      append([]devicelog.OrderOption{}, dlq.order...),
		inters:     append([]Interceptor{}, dlq.inters...),
		predicates: append([]predicate.DeviceLog{}, dlq.predicates...),
		// clone intermediate query.
		sql:  dlq.sql.Clone(),
		path: dlq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.DeviceLog.Query().
//		GroupBy(devicelog.FieldName).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (dlq *DeviceLogQuery) GroupBy(field string, fields ...string) *DeviceLogGroupBy {
	dlq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DeviceLogGroupBy{build: dlq}
	grbuild.flds = &dlq.ctx.Fields
	grbuild.label = devicelog.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//	}
//
//	client.DeviceLog.Query().
//		Select(devicelog.FieldName).
//		Scan(ctx, &v)
func (dlq *DeviceLogQuery) Select(fields ...string) *DeviceLogSelect {
	dlq.ctx.Fields = append(dlq.ctx.Fields, fields...)
	sbuild := &DeviceLogSelect{DeviceLogQuery: dlq}
	sbuild.label = devicelog.Label
	sbuild.flds, sbuild.scan = &dlq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DeviceLogSelect configured with the given aggregations.
func (dlq *DeviceLogQuery) Aggregate(fns ...AggregateFunc) *DeviceLogSelect {
	return dlq.Select().Aggregate(fns...)
}

func (dlq *DeviceLogQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range dlq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, dlq); err != nil {
				return err
			}
		}
	}
	for _, f := range dlq.ctx.Fields {
		if !devicelog.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if dlq.path != nil {
		prev, err := dlq.path(ctx)
		if err != nil {
			return err
		}
		dlq.sql = prev
	}
	return nil
}

func (dlq *DeviceLogQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*DeviceLog, error) {
	var (
		nodes = []*DeviceLog{}
		_spec = dlq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*DeviceLog).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &DeviceLog{config: dlq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, dlq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (dlq *DeviceLogQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := dlq.querySpec()
	_spec.Node.Columns = dlq.ctx.Fields
	if len(dlq.ctx.Fields) > 0 {
		_spec.Unique = dlq.ctx.Unique != nil && *dlq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, dlq.driver, _spec)
}

func (dlq *DeviceLogQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(devicelog.Table, devicelog.Columns, sqlgraph.NewFieldSpec(devicelog.FieldID, field.TypeString))
	_spec.From = dlq.sql
	if unique := dlq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if dlq.path != nil {
		_spec.Unique = true
	}
	if fields := dlq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, devicelog.FieldID)
		for i := range fields {
			if fields[i] != devicelog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := dlq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := dlq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := dlq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := dlq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (dlq *DeviceLogQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(dlq.driver.Dialect())
	t1 := builder.Table(devicelog.Table)
	columns := dlq.ctx.Fields
	if len(columns) == 0 {
		columns = devicelog.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if dlq.sql != nil {
		selector = dlq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if dlq.ctx.Unique != nil && *dlq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range dlq.predicates {
		p(selector)
	}
	for _, p := range dlq.order {
		p(selector)
	}
	if offset := dlq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := dlq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// DeviceLogGroupBy is the group-by builder for DeviceLog entities.
type DeviceLogGroupBy struct {
	selector
	build *DeviceLogQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (dlgb *DeviceLogGroupBy) Aggregate(fns ...AggregateFunc) *DeviceLogGroupBy {
	dlgb.fns = append(dlgb.fns, fns...)
	return dlgb
}

// Scan applies the selector query and scans the result into the given value.
func (dlgb *DeviceLogGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, dlgb.build.ctx, ent.OpQueryGroupBy)
	if err := dlgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DeviceLogQuery, *DeviceLogGroupBy](ctx, dlgb.build, dlgb, dlgb.build.inters, v)
}

func (dlgb *DeviceLogGroupBy) sqlScan(ctx context.Context, root *DeviceLogQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(dlgb.fns))
	for _, fn := range dlgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*dlgb.flds)+len(dlgb.fns))
		for _, f := range *dlgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*dlgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := dlgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DeviceLogSelect is the builder for selecting fields of DeviceLog entities.
type DeviceLogSelect struct {
	*DeviceLogQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (dls *DeviceLogSelect) Aggregate(fns ...AggregateFunc) *DeviceLogSelect {
	dls.fns = append(dls.fns, fns...)
	return dls
}

// Scan applies the selector query and scans the result into the given value.
func (dls *DeviceLogSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, dls.ctx, ent.OpQuerySelect)
	if err := dls.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DeviceLogQuery, *DeviceLogSelect](ctx, dls.DeviceLogQuery, dls, dls.inters, v)
}

func (dls *DeviceLogSelect) sqlScan(ctx context.Context, root *DeviceLogQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(dls.fns))
	for _, fn := range dls.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*dls.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := dls.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

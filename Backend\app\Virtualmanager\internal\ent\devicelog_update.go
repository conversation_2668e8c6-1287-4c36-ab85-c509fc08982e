// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DeviceLogUpdate is the builder for updating DeviceLog entities.
type DeviceLogUpdate struct {
	config
	hooks    []Hook
	mutation *DeviceLogMutation
}

// Where appends a list predicates to the DeviceLogUpdate builder.
func (dlu *DeviceLogUpdate) Where(ps ...predicate.DeviceLog) *DeviceLogUpdate {
	dlu.mutation.Where(ps...)
	return dlu
}

// SetName sets the "name" field.
func (dlu *DeviceLogUpdate) SetName(s string) *DeviceLogUpdate {
	dlu.mutation.SetName(s)
	return dlu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (dlu *DeviceLogUpdate) SetNillableName(s *string) *DeviceLogUpdate {
	if s != nil {
		dlu.SetName(*s)
	}
	return dlu
}

// SetHealthlog sets the "healthlog" field.
func (dlu *DeviceLogUpdate) SetHealthlog(s string) *DeviceLogUpdate {
	dlu.mutation.SetHealthlog(s)
	return dlu
}

// SetNillableHealthlog sets the "healthlog" field if the given value is not nil.
func (dlu *DeviceLogUpdate) SetNillableHealthlog(s *string) *DeviceLogUpdate {
	if s != nil {
		dlu.SetHealthlog(*s)
	}
	return dlu
}

// SetSimulatelog sets the "simulatelog" field.
func (dlu *DeviceLogUpdate) SetSimulatelog(s string) *DeviceLogUpdate {
	dlu.mutation.SetSimulatelog(s)
	return dlu
}

// SetNillableSimulatelog sets the "simulatelog" field if the given value is not nil.
func (dlu *DeviceLogUpdate) SetNillableSimulatelog(s *string) *DeviceLogUpdate {
	if s != nil {
		dlu.SetSimulatelog(*s)
	}
	return dlu
}

// Mutation returns the DeviceLogMutation object of the builder.
func (dlu *DeviceLogUpdate) Mutation() *DeviceLogMutation {
	return dlu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (dlu *DeviceLogUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, dlu.sqlSave, dlu.mutation, dlu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (dlu *DeviceLogUpdate) SaveX(ctx context.Context) int {
	affected, err := dlu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (dlu *DeviceLogUpdate) Exec(ctx context.Context) error {
	_, err := dlu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dlu *DeviceLogUpdate) ExecX(ctx context.Context) {
	if err := dlu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dlu *DeviceLogUpdate) check() error {
	if v, ok := dlu.mutation.Name(); ok {
		if err := devicelog.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "DeviceLog.name": %w`, err)}
		}
	}
	return nil
}

func (dlu *DeviceLogUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := dlu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(devicelog.Table, devicelog.Columns, sqlgraph.NewFieldSpec(devicelog.FieldID, field.TypeString))
	if ps := dlu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := dlu.mutation.Name(); ok {
		_spec.SetField(devicelog.FieldName, field.TypeString, value)
	}
	if value, ok := dlu.mutation.Healthlog(); ok {
		_spec.SetField(devicelog.FieldHealthlog, field.TypeString, value)
	}
	if value, ok := dlu.mutation.Simulatelog(); ok {
		_spec.SetField(devicelog.FieldSimulatelog, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, dlu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{devicelog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	dlu.mutation.done = true
	return n, nil
}

// DeviceLogUpdateOne is the builder for updating a single DeviceLog entity.
type DeviceLogUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *DeviceLogMutation
}

// SetName sets the "name" field.
func (dluo *DeviceLogUpdateOne) SetName(s string) *DeviceLogUpdateOne {
	dluo.mutation.SetName(s)
	return dluo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (dluo *DeviceLogUpdateOne) SetNillableName(s *string) *DeviceLogUpdateOne {
	if s != nil {
		dluo.SetName(*s)
	}
	return dluo
}

// SetHealthlog sets the "healthlog" field.
func (dluo *DeviceLogUpdateOne) SetHealthlog(s string) *DeviceLogUpdateOne {
	dluo.mutation.SetHealthlog(s)
	return dluo
}

// SetNillableHealthlog sets the "healthlog" field if the given value is not nil.
func (dluo *DeviceLogUpdateOne) SetNillableHealthlog(s *string) *DeviceLogUpdateOne {
	if s != nil {
		dluo.SetHealthlog(*s)
	}
	return dluo
}

// SetSimulatelog sets the "simulatelog" field.
func (dluo *DeviceLogUpdateOne) SetSimulatelog(s string) *DeviceLogUpdateOne {
	dluo.mutation.SetSimulatelog(s)
	return dluo
}

// SetNillableSimulatelog sets the "simulatelog" field if the given value is not nil.
func (dluo *DeviceLogUpdateOne) SetNillableSimulatelog(s *string) *DeviceLogUpdateOne {
	if s != nil {
		dluo.SetSimulatelog(*s)
	}
	return dluo
}

// Mutation returns the DeviceLogMutation object of the builder.
func (dluo *DeviceLogUpdateOne) Mutation() *DeviceLogMutation {
	return dluo.mutation
}

// Where appends a list predicates to the DeviceLogUpdate builder.
func (dluo *DeviceLogUpdateOne) Where(ps ...predicate.DeviceLog) *DeviceLogUpdateOne {
	dluo.mutation.Where(ps...)
	return dluo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (dluo *DeviceLogUpdateOne) Select(field string, fields ...string) *DeviceLogUpdateOne {
	dluo.fields = append([]string{field}, fields...)
	return dluo
}

// Save executes the query and returns the updated DeviceLog entity.
func (dluo *DeviceLogUpdateOne) Save(ctx context.Context) (*DeviceLog, error) {
	return withHooks(ctx, dluo.sqlSave, dluo.mutation, dluo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (dluo *DeviceLogUpdateOne) SaveX(ctx context.Context) *DeviceLog {
	node, err := dluo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (dluo *DeviceLogUpdateOne) Exec(ctx context.Context) error {
	_, err := dluo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dluo *DeviceLogUpdateOne) ExecX(ctx context.Context) {
	if err := dluo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (dluo *DeviceLogUpdateOne) check() error {
	if v, ok := dluo.mutation.Name(); ok {
		if err := devicelog.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "DeviceLog.name": %w`, err)}
		}
	}
	return nil
}

func (dluo *DeviceLogUpdateOne) sqlSave(ctx context.Context) (_node *DeviceLog, err error) {
	if err := dluo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(devicelog.Table, devicelog.Columns, sqlgraph.NewFieldSpec(devicelog.FieldID, field.TypeString))
	id, ok := dluo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DeviceLog.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := dluo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, devicelog.FieldID)
		for _, f := range fields {
			if !devicelog.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != devicelog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := dluo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := dluo.mutation.Name(); ok {
		_spec.SetField(devicelog.FieldName, field.TypeString, value)
	}
	if value, ok := dluo.mutation.Healthlog(); ok {
		_spec.SetField(devicelog.FieldHealthlog, field.TypeString, value)
	}
	if value, ok := dluo.mutation.Simulatelog(); ok {
		_spec.SetField(devicelog.FieldSimulatelog, field.TypeString, value)
	}
	_node = &DeviceLog{config: dluo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, dluo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{devicelog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	dluo.mutation.done = true
	return _node, nil
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/frame"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Frame is the model entity for the Frame schema.
type Frame struct {
	config `json:"-"`
	// ID of the ent.
	// Frame ID
	ID string `json:"id,omitempty"`
	// Foreign key to Device
	DeviceID string `json:"device_id,omitempty"`
	// Header of the frame
	Header string `json:"header,omitempty"`
	// Data content of the frame
	Content string `json:"content,omitempty"`
	// Tail of the frame
	Tail string `json:"tail,omitempty"`
	// Create timestamp
	Time         time.Time `json:"time,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Frame) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case frame.FieldID, frame.FieldDeviceID, frame.FieldHeader, frame.FieldContent, frame.FieldTail:
			values[i] = new(sql.NullString)
		case frame.FieldTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Frame fields.
func (f *Frame) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case frame.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				f.ID = value.String
			}
		case frame.FieldDeviceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device_id", values[i])
			} else if value.Valid {
				f.DeviceID = value.String
			}
		case frame.FieldHeader:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field header", values[i])
			} else if value.Valid {
				f.Header = value.String
			}
		case frame.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				f.Content = value.String
			}
		case frame.FieldTail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tail", values[i])
			} else if value.Valid {
				f.Tail = value.String
			}
		case frame.FieldTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field time", values[i])
			} else if value.Valid {
				f.Time = value.Time
			}
		default:
			f.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Frame.
// This includes values selected through modifiers, order, etc.
func (f *Frame) Value(name string) (ent.Value, error) {
	return f.selectValues.Get(name)
}

// Update returns a builder for updating this Frame.
// Note that you need to call Frame.Unwrap() before calling this method if this Frame
// was returned from a transaction, and the transaction was committed or rolled back.
func (f *Frame) Update() *FrameUpdateOne {
	return NewFrameClient(f.config).UpdateOne(f)
}

// Unwrap unwraps the Frame entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (f *Frame) Unwrap() *Frame {
	_tx, ok := f.config.driver.(*txDriver)
	if !ok {
		panic("ent: Frame is not a transactional entity")
	}
	f.config.driver = _tx.drv
	return f
}

// String implements the fmt.Stringer.
func (f *Frame) String() string {
	var builder strings.Builder
	builder.WriteString("Frame(")
	builder.WriteString(fmt.Sprintf("id=%v, ", f.ID))
	builder.WriteString("device_id=")
	builder.WriteString(f.DeviceID)
	builder.WriteString(", ")
	builder.WriteString("header=")
	builder.WriteString(f.Header)
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(f.Content)
	builder.WriteString(", ")
	builder.WriteString("tail=")
	builder.WriteString(f.Tail)
	builder.WriteString(", ")
	builder.WriteString("time=")
	builder.WriteString(f.Time.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Frames is a parsable slice of Frame.
type Frames []*Frame

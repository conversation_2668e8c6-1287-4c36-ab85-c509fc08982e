// Code generated by ent, DO NOT EDIT.

package frame

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the frame type in the database.
	Label = "frame"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldDeviceID holds the string denoting the device_id field in the database.
	FieldDeviceID = "device_id"
	// FieldHeader holds the string denoting the header field in the database.
	FieldHeader = "header"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// FieldTail holds the string denoting the tail field in the database.
	FieldTail = "tail"
	// FieldTime holds the string denoting the time field in the database.
	FieldTime = "time"
	// Table holds the table name of the frame in the database.
	Table = "frames"
)

// Columns holds all SQL columns for frame fields.
var Columns = []string{
	FieldID,
	FieldDeviceID,
	FieldHeader,
	FieldContent,
	FieldTail,
	FieldTime,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DeviceIDValidator is a validator for the "device_id" field. It is called by the builders before save.
	DeviceIDValidator func(string) error
	// DefaultHeader holds the default value on creation for the "header" field.
	DefaultHeader string
	// DefaultContent holds the default value on creation for the "content" field.
	DefaultContent string
	// DefaultTail holds the default value on creation for the "tail" field.
	DefaultTail string
	// DefaultTime holds the default value on creation for the "time" field.
	DefaultTime func() time.Time
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Frame queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByDeviceID orders the results by the device_id field.
func ByDeviceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeviceID, opts...).ToFunc()
}

// ByHeader orders the results by the header field.
func ByHeader(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHeader, opts...).ToFunc()
}

// ByContent orders the results by the content field.
func ByContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContent, opts...).ToFunc()
}

// ByTail orders the results by the tail field.
func ByTail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTail, opts...).ToFunc()
}

// ByTime orders the results by the time field.
func ByTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTime, opts...).ToFunc()
}

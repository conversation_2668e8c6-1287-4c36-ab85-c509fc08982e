// Code generated by ent, DO NOT EDIT.

package frame

import (
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Frame {
	return predicate.Frame(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Frame {
	return predicate.Frame(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Frame {
	return predicate.Frame(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Frame {
	return predicate.Frame(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Frame {
	return predicate.Frame(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Frame {
	return predicate.Frame(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Frame {
	return predicate.Frame(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Frame {
	return predicate.Frame(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Frame {
	return predicate.Frame(sql.FieldContainsFold(FieldID, id))
}

// DeviceID applies equality check predicate on the "device_id" field. It's identical to DeviceIDEQ.
func DeviceID(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldDeviceID, v))
}

// Header applies equality check predicate on the "header" field. It's identical to HeaderEQ.
func Header(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldHeader, v))
}

// Content applies equality check predicate on the "content" field. It's identical to ContentEQ.
func Content(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldContent, v))
}

// Tail applies equality check predicate on the "tail" field. It's identical to TailEQ.
func Tail(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldTail, v))
}

// Time applies equality check predicate on the "time" field. It's identical to TimeEQ.
func Time(v time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldTime, v))
}

// DeviceIDEQ applies the EQ predicate on the "device_id" field.
func DeviceIDEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldDeviceID, v))
}

// DeviceIDNEQ applies the NEQ predicate on the "device_id" field.
func DeviceIDNEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldNEQ(FieldDeviceID, v))
}

// DeviceIDIn applies the In predicate on the "device_id" field.
func DeviceIDIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldIn(FieldDeviceID, vs...))
}

// DeviceIDNotIn applies the NotIn predicate on the "device_id" field.
func DeviceIDNotIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldNotIn(FieldDeviceID, vs...))
}

// DeviceIDGT applies the GT predicate on the "device_id" field.
func DeviceIDGT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGT(FieldDeviceID, v))
}

// DeviceIDGTE applies the GTE predicate on the "device_id" field.
func DeviceIDGTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGTE(FieldDeviceID, v))
}

// DeviceIDLT applies the LT predicate on the "device_id" field.
func DeviceIDLT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLT(FieldDeviceID, v))
}

// DeviceIDLTE applies the LTE predicate on the "device_id" field.
func DeviceIDLTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLTE(FieldDeviceID, v))
}

// DeviceIDContains applies the Contains predicate on the "device_id" field.
func DeviceIDContains(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContains(FieldDeviceID, v))
}

// DeviceIDHasPrefix applies the HasPrefix predicate on the "device_id" field.
func DeviceIDHasPrefix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasPrefix(FieldDeviceID, v))
}

// DeviceIDHasSuffix applies the HasSuffix predicate on the "device_id" field.
func DeviceIDHasSuffix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasSuffix(FieldDeviceID, v))
}

// DeviceIDEqualFold applies the EqualFold predicate on the "device_id" field.
func DeviceIDEqualFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEqualFold(FieldDeviceID, v))
}

// DeviceIDContainsFold applies the ContainsFold predicate on the "device_id" field.
func DeviceIDContainsFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContainsFold(FieldDeviceID, v))
}

// HeaderEQ applies the EQ predicate on the "header" field.
func HeaderEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldHeader, v))
}

// HeaderNEQ applies the NEQ predicate on the "header" field.
func HeaderNEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldNEQ(FieldHeader, v))
}

// HeaderIn applies the In predicate on the "header" field.
func HeaderIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldIn(FieldHeader, vs...))
}

// HeaderNotIn applies the NotIn predicate on the "header" field.
func HeaderNotIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldNotIn(FieldHeader, vs...))
}

// HeaderGT applies the GT predicate on the "header" field.
func HeaderGT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGT(FieldHeader, v))
}

// HeaderGTE applies the GTE predicate on the "header" field.
func HeaderGTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGTE(FieldHeader, v))
}

// HeaderLT applies the LT predicate on the "header" field.
func HeaderLT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLT(FieldHeader, v))
}

// HeaderLTE applies the LTE predicate on the "header" field.
func HeaderLTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLTE(FieldHeader, v))
}

// HeaderContains applies the Contains predicate on the "header" field.
func HeaderContains(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContains(FieldHeader, v))
}

// HeaderHasPrefix applies the HasPrefix predicate on the "header" field.
func HeaderHasPrefix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasPrefix(FieldHeader, v))
}

// HeaderHasSuffix applies the HasSuffix predicate on the "header" field.
func HeaderHasSuffix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasSuffix(FieldHeader, v))
}

// HeaderEqualFold applies the EqualFold predicate on the "header" field.
func HeaderEqualFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEqualFold(FieldHeader, v))
}

// HeaderContainsFold applies the ContainsFold predicate on the "header" field.
func HeaderContainsFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContainsFold(FieldHeader, v))
}

// ContentEQ applies the EQ predicate on the "content" field.
func ContentEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldContent, v))
}

// ContentNEQ applies the NEQ predicate on the "content" field.
func ContentNEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldNEQ(FieldContent, v))
}

// ContentIn applies the In predicate on the "content" field.
func ContentIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldIn(FieldContent, vs...))
}

// ContentNotIn applies the NotIn predicate on the "content" field.
func ContentNotIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldNotIn(FieldContent, vs...))
}

// ContentGT applies the GT predicate on the "content" field.
func ContentGT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGT(FieldContent, v))
}

// ContentGTE applies the GTE predicate on the "content" field.
func ContentGTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGTE(FieldContent, v))
}

// ContentLT applies the LT predicate on the "content" field.
func ContentLT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLT(FieldContent, v))
}

// ContentLTE applies the LTE predicate on the "content" field.
func ContentLTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLTE(FieldContent, v))
}

// ContentContains applies the Contains predicate on the "content" field.
func ContentContains(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContains(FieldContent, v))
}

// ContentHasPrefix applies the HasPrefix predicate on the "content" field.
func ContentHasPrefix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasPrefix(FieldContent, v))
}

// ContentHasSuffix applies the HasSuffix predicate on the "content" field.
func ContentHasSuffix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasSuffix(FieldContent, v))
}

// ContentEqualFold applies the EqualFold predicate on the "content" field.
func ContentEqualFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEqualFold(FieldContent, v))
}

// ContentContainsFold applies the ContainsFold predicate on the "content" field.
func ContentContainsFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContainsFold(FieldContent, v))
}

// TailEQ applies the EQ predicate on the "tail" field.
func TailEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldTail, v))
}

// TailNEQ applies the NEQ predicate on the "tail" field.
func TailNEQ(v string) predicate.Frame {
	return predicate.Frame(sql.FieldNEQ(FieldTail, v))
}

// TailIn applies the In predicate on the "tail" field.
func TailIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldIn(FieldTail, vs...))
}

// TailNotIn applies the NotIn predicate on the "tail" field.
func TailNotIn(vs ...string) predicate.Frame {
	return predicate.Frame(sql.FieldNotIn(FieldTail, vs...))
}

// TailGT applies the GT predicate on the "tail" field.
func TailGT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGT(FieldTail, v))
}

// TailGTE applies the GTE predicate on the "tail" field.
func TailGTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldGTE(FieldTail, v))
}

// TailLT applies the LT predicate on the "tail" field.
func TailLT(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLT(FieldTail, v))
}

// TailLTE applies the LTE predicate on the "tail" field.
func TailLTE(v string) predicate.Frame {
	return predicate.Frame(sql.FieldLTE(FieldTail, v))
}

// TailContains applies the Contains predicate on the "tail" field.
func TailContains(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContains(FieldTail, v))
}

// TailHasPrefix applies the HasPrefix predicate on the "tail" field.
func TailHasPrefix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasPrefix(FieldTail, v))
}

// TailHasSuffix applies the HasSuffix predicate on the "tail" field.
func TailHasSuffix(v string) predicate.Frame {
	return predicate.Frame(sql.FieldHasSuffix(FieldTail, v))
}

// TailEqualFold applies the EqualFold predicate on the "tail" field.
func TailEqualFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldEqualFold(FieldTail, v))
}

// TailContainsFold applies the ContainsFold predicate on the "tail" field.
func TailContainsFold(v string) predicate.Frame {
	return predicate.Frame(sql.FieldContainsFold(FieldTail, v))
}

// TimeEQ applies the EQ predicate on the "time" field.
func TimeEQ(v time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldEQ(FieldTime, v))
}

// TimeNEQ applies the NEQ predicate on the "time" field.
func TimeNEQ(v time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldNEQ(FieldTime, v))
}

// TimeIn applies the In predicate on the "time" field.
func TimeIn(vs ...time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldIn(FieldTime, vs...))
}

// TimeNotIn applies the NotIn predicate on the "time" field.
func TimeNotIn(vs ...time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldNotIn(FieldTime, vs...))
}

// TimeGT applies the GT predicate on the "time" field.
func TimeGT(v time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldGT(FieldTime, v))
}

// TimeGTE applies the GTE predicate on the "time" field.
func TimeGTE(v time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldGTE(FieldTime, v))
}

// TimeLT applies the LT predicate on the "time" field.
func TimeLT(v time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldLT(FieldTime, v))
}

// TimeLTE applies the LTE predicate on the "time" field.
func TimeLTE(v time.Time) predicate.Frame {
	return predicate.Frame(sql.FieldLTE(FieldTime, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Frame) predicate.Frame {
	return predicate.Frame(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Frame) predicate.Frame {
	return predicate.Frame(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Frame) predicate.Frame {
	return predicate.Frame(sql.NotPredicates(p))
}

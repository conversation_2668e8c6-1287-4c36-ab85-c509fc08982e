// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/frame"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FrameCreate is the builder for creating a Frame entity.
type FrameCreate struct {
	config
	mutation *FrameMutation
	hooks    []Hook
}

// SetDeviceID sets the "device_id" field.
func (fc *FrameCreate) SetDeviceID(s string) *FrameCreate {
	fc.mutation.SetDeviceID(s)
	return fc
}

// SetHeader sets the "header" field.
func (fc *FrameCreate) SetHeader(s string) *FrameCreate {
	fc.mutation.SetHeader(s)
	return fc
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (fc *FrameCreate) SetNillableHeader(s *string) *FrameCreate {
	if s != nil {
		fc.SetHeader(*s)
	}
	return fc
}

// SetContent sets the "content" field.
func (fc *FrameCreate) SetContent(s string) *FrameCreate {
	fc.mutation.SetContent(s)
	return fc
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (fc *FrameCreate) SetNillableContent(s *string) *FrameCreate {
	if s != nil {
		fc.SetContent(*s)
	}
	return fc
}

// SetTail sets the "tail" field.
func (fc *FrameCreate) SetTail(s string) *FrameCreate {
	fc.mutation.SetTail(s)
	return fc
}

// SetNillableTail sets the "tail" field if the given value is not nil.
func (fc *FrameCreate) SetNillableTail(s *string) *FrameCreate {
	if s != nil {
		fc.SetTail(*s)
	}
	return fc
}

// SetTime sets the "time" field.
func (fc *FrameCreate) SetTime(t time.Time) *FrameCreate {
	fc.mutation.SetTime(t)
	return fc
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (fc *FrameCreate) SetNillableTime(t *time.Time) *FrameCreate {
	if t != nil {
		fc.SetTime(*t)
	}
	return fc
}

// SetID sets the "id" field.
func (fc *FrameCreate) SetID(s string) *FrameCreate {
	fc.mutation.SetID(s)
	return fc
}

// Mutation returns the FrameMutation object of the builder.
func (fc *FrameCreate) Mutation() *FrameMutation {
	return fc.mutation
}

// Save creates the Frame in the database.
func (fc *FrameCreate) Save(ctx context.Context) (*Frame, error) {
	fc.defaults()
	return withHooks(ctx, fc.sqlSave, fc.mutation, fc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (fc *FrameCreate) SaveX(ctx context.Context) *Frame {
	v, err := fc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fc *FrameCreate) Exec(ctx context.Context) error {
	_, err := fc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fc *FrameCreate) ExecX(ctx context.Context) {
	if err := fc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fc *FrameCreate) defaults() {
	if _, ok := fc.mutation.Header(); !ok {
		v := frame.DefaultHeader
		fc.mutation.SetHeader(v)
	}
	if _, ok := fc.mutation.Content(); !ok {
		v := frame.DefaultContent
		fc.mutation.SetContent(v)
	}
	if _, ok := fc.mutation.Tail(); !ok {
		v := frame.DefaultTail
		fc.mutation.SetTail(v)
	}
	if _, ok := fc.mutation.Time(); !ok {
		v := frame.DefaultTime()
		fc.mutation.SetTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fc *FrameCreate) check() error {
	if _, ok := fc.mutation.DeviceID(); !ok {
		return &ValidationError{Name: "device_id", err: errors.New(`ent: missing required field "Frame.device_id"`)}
	}
	if v, ok := fc.mutation.DeviceID(); ok {
		if err := frame.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Frame.device_id": %w`, err)}
		}
	}
	if _, ok := fc.mutation.Header(); !ok {
		return &ValidationError{Name: "header", err: errors.New(`ent: missing required field "Frame.header"`)}
	}
	if _, ok := fc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "Frame.content"`)}
	}
	if _, ok := fc.mutation.Tail(); !ok {
		return &ValidationError{Name: "tail", err: errors.New(`ent: missing required field "Frame.tail"`)}
	}
	if _, ok := fc.mutation.Time(); !ok {
		return &ValidationError{Name: "time", err: errors.New(`ent: missing required field "Frame.time"`)}
	}
	if v, ok := fc.mutation.ID(); ok {
		if err := frame.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Frame.id": %w`, err)}
		}
	}
	return nil
}

func (fc *FrameCreate) sqlSave(ctx context.Context) (*Frame, error) {
	if err := fc.check(); err != nil {
		return nil, err
	}
	_node, _spec := fc.createSpec()
	if err := sqlgraph.CreateNode(ctx, fc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Frame.ID type: %T", _spec.ID.Value)
		}
	}
	fc.mutation.id = &_node.ID
	fc.mutation.done = true
	return _node, nil
}

func (fc *FrameCreate) createSpec() (*Frame, *sqlgraph.CreateSpec) {
	var (
		_node = &Frame{config: fc.config}
		_spec = sqlgraph.NewCreateSpec(frame.Table, sqlgraph.NewFieldSpec(frame.FieldID, field.TypeString))
	)
	if id, ok := fc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := fc.mutation.DeviceID(); ok {
		_spec.SetField(frame.FieldDeviceID, field.TypeString, value)
		_node.DeviceID = value
	}
	if value, ok := fc.mutation.Header(); ok {
		_spec.SetField(frame.FieldHeader, field.TypeString, value)
		_node.Header = value
	}
	if value, ok := fc.mutation.Content(); ok {
		_spec.SetField(frame.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	if value, ok := fc.mutation.Tail(); ok {
		_spec.SetField(frame.FieldTail, field.TypeString, value)
		_node.Tail = value
	}
	if value, ok := fc.mutation.Time(); ok {
		_spec.SetField(frame.FieldTime, field.TypeTime, value)
		_node.Time = value
	}
	return _node, _spec
}

// FrameCreateBulk is the builder for creating many Frame entities in bulk.
type FrameCreateBulk struct {
	config
	err      error
	builders []*FrameCreate
}

// Save creates the Frame entities in the database.
func (fcb *FrameCreateBulk) Save(ctx context.Context) ([]*Frame, error) {
	if fcb.err != nil {
		return nil, fcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(fcb.builders))
	nodes := make([]*Frame, len(fcb.builders))
	mutators := make([]Mutator, len(fcb.builders))
	for i := range fcb.builders {
		func(i int, root context.Context) {
			builder := fcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FrameMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, fcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, fcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, fcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (fcb *FrameCreateBulk) SaveX(ctx context.Context) []*Frame {
	v, err := fcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fcb *FrameCreateBulk) Exec(ctx context.Context) error {
	_, err := fcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fcb *FrameCreateBulk) ExecX(ctx context.Context) {
	if err := fcb.Exec(ctx); err != nil {
		panic(err)
	}
}

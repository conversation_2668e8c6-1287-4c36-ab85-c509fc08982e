// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/frame"
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FrameUpdate is the builder for updating Frame entities.
type FrameUpdate struct {
	config
	hooks    []Hook
	mutation *FrameMutation
}

// Where appends a list predicates to the FrameUpdate builder.
func (fu *FrameUpdate) Where(ps ...predicate.Frame) *FrameUpdate {
	fu.mutation.Where(ps...)
	return fu
}

// SetDeviceID sets the "device_id" field.
func (fu *FrameUpdate) SetDeviceID(s string) *FrameUpdate {
	fu.mutation.SetDeviceID(s)
	return fu
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (fu *FrameUpdate) SetNillableDeviceID(s *string) *FrameUpdate {
	if s != nil {
		fu.SetDeviceID(*s)
	}
	return fu
}

// SetHeader sets the "header" field.
func (fu *FrameUpdate) SetHeader(s string) *FrameUpdate {
	fu.mutation.SetHeader(s)
	return fu
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (fu *FrameUpdate) SetNillableHeader(s *string) *FrameUpdate {
	if s != nil {
		fu.SetHeader(*s)
	}
	return fu
}

// SetContent sets the "content" field.
func (fu *FrameUpdate) SetContent(s string) *FrameUpdate {
	fu.mutation.SetContent(s)
	return fu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (fu *FrameUpdate) SetNillableContent(s *string) *FrameUpdate {
	if s != nil {
		fu.SetContent(*s)
	}
	return fu
}

// SetTail sets the "tail" field.
func (fu *FrameUpdate) SetTail(s string) *FrameUpdate {
	fu.mutation.SetTail(s)
	return fu
}

// SetNillableTail sets the "tail" field if the given value is not nil.
func (fu *FrameUpdate) SetNillableTail(s *string) *FrameUpdate {
	if s != nil {
		fu.SetTail(*s)
	}
	return fu
}

// Mutation returns the FrameMutation object of the builder.
func (fu *FrameUpdate) Mutation() *FrameMutation {
	return fu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (fu *FrameUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, fu.sqlSave, fu.mutation, fu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fu *FrameUpdate) SaveX(ctx context.Context) int {
	affected, err := fu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (fu *FrameUpdate) Exec(ctx context.Context) error {
	_, err := fu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fu *FrameUpdate) ExecX(ctx context.Context) {
	if err := fu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fu *FrameUpdate) check() error {
	if v, ok := fu.mutation.DeviceID(); ok {
		if err := frame.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Frame.device_id": %w`, err)}
		}
	}
	return nil
}

func (fu *FrameUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := fu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(frame.Table, frame.Columns, sqlgraph.NewFieldSpec(frame.FieldID, field.TypeString))
	if ps := fu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := fu.mutation.DeviceID(); ok {
		_spec.SetField(frame.FieldDeviceID, field.TypeString, value)
	}
	if value, ok := fu.mutation.Header(); ok {
		_spec.SetField(frame.FieldHeader, field.TypeString, value)
	}
	if value, ok := fu.mutation.Content(); ok {
		_spec.SetField(frame.FieldContent, field.TypeString, value)
	}
	if value, ok := fu.mutation.Tail(); ok {
		_spec.SetField(frame.FieldTail, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, fu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{frame.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	fu.mutation.done = true
	return n, nil
}

// FrameUpdateOne is the builder for updating a single Frame entity.
type FrameUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *FrameMutation
}

// SetDeviceID sets the "device_id" field.
func (fuo *FrameUpdateOne) SetDeviceID(s string) *FrameUpdateOne {
	fuo.mutation.SetDeviceID(s)
	return fuo
}

// SetNillableDeviceID sets the "device_id" field if the given value is not nil.
func (fuo *FrameUpdateOne) SetNillableDeviceID(s *string) *FrameUpdateOne {
	if s != nil {
		fuo.SetDeviceID(*s)
	}
	return fuo
}

// SetHeader sets the "header" field.
func (fuo *FrameUpdateOne) SetHeader(s string) *FrameUpdateOne {
	fuo.mutation.SetHeader(s)
	return fuo
}

// SetNillableHeader sets the "header" field if the given value is not nil.
func (fuo *FrameUpdateOne) SetNillableHeader(s *string) *FrameUpdateOne {
	if s != nil {
		fuo.SetHeader(*s)
	}
	return fuo
}

// SetContent sets the "content" field.
func (fuo *FrameUpdateOne) SetContent(s string) *FrameUpdateOne {
	fuo.mutation.SetContent(s)
	return fuo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (fuo *FrameUpdateOne) SetNillableContent(s *string) *FrameUpdateOne {
	if s != nil {
		fuo.SetContent(*s)
	}
	return fuo
}

// SetTail sets the "tail" field.
func (fuo *FrameUpdateOne) SetTail(s string) *FrameUpdateOne {
	fuo.mutation.SetTail(s)
	return fuo
}

// SetNillableTail sets the "tail" field if the given value is not nil.
func (fuo *FrameUpdateOne) SetNillableTail(s *string) *FrameUpdateOne {
	if s != nil {
		fuo.SetTail(*s)
	}
	return fuo
}

// Mutation returns the FrameMutation object of the builder.
func (fuo *FrameUpdateOne) Mutation() *FrameMutation {
	return fuo.mutation
}

// Where appends a list predicates to the FrameUpdate builder.
func (fuo *FrameUpdateOne) Where(ps ...predicate.Frame) *FrameUpdateOne {
	fuo.mutation.Where(ps...)
	return fuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (fuo *FrameUpdateOne) Select(field string, fields ...string) *FrameUpdateOne {
	fuo.fields = append([]string{field}, fields...)
	return fuo
}

// Save executes the query and returns the updated Frame entity.
func (fuo *FrameUpdateOne) Save(ctx context.Context) (*Frame, error) {
	return withHooks(ctx, fuo.sqlSave, fuo.mutation, fuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fuo *FrameUpdateOne) SaveX(ctx context.Context) *Frame {
	node, err := fuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (fuo *FrameUpdateOne) Exec(ctx context.Context) error {
	_, err := fuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fuo *FrameUpdateOne) ExecX(ctx context.Context) {
	if err := fuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (fuo *FrameUpdateOne) check() error {
	if v, ok := fuo.mutation.DeviceID(); ok {
		if err := frame.DeviceIDValidator(v); err != nil {
			return &ValidationError{Name: "device_id", err: fmt.Errorf(`ent: validator failed for field "Frame.device_id": %w`, err)}
		}
	}
	return nil
}

func (fuo *FrameUpdateOne) sqlSave(ctx context.Context) (_node *Frame, err error) {
	if err := fuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(frame.Table, frame.Columns, sqlgraph.NewFieldSpec(frame.FieldID, field.TypeString))
	id, ok := fuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Frame.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := fuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, frame.FieldID)
		for _, f := range fields {
			if !frame.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != frame.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := fuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := fuo.mutation.DeviceID(); ok {
		_spec.SetField(frame.FieldDeviceID, field.TypeString, value)
	}
	if value, ok := fuo.mutation.Header(); ok {
		_spec.SetField(frame.FieldHeader, field.TypeString, value)
	}
	if value, ok := fuo.mutation.Content(); ok {
		_spec.SetField(frame.FieldContent, field.TypeString, value)
	}
	if value, ok := fuo.mutation.Tail(); ok {
		_spec.SetField(frame.FieldTail, field.TypeString, value)
	}
	_node = &Frame{config: fuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, fuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{frame.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	fuo.mutation.done = true
	return _node, nil
}

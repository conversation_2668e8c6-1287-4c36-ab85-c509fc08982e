// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// DataColumns holds the columns for the "data" table.
	DataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "frame_id", Type: field.TypeString},
		{Name: "index", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "type", Type: field.TypeString, Default: ""},
		{Name: "value", Type: field.TypeString, Default: ""},
	}
	// DataTable holds the schema information for the "data" table.
	DataTable = &schema.Table{
		Name:       "data",
		Columns:    DataColumns,
		PrimaryKey: []*schema.Column{DataColumns[0]},
	}
	// DeviceLogsColumns holds the columns for the "device_logs" table.
	DeviceLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "healthlog", Type: field.TypeString, Size: **********, Default: ""},
		{Name: "simulatelog", Type: field.TypeString, Size: **********, Default: ""},
	}
	// DeviceLogsTable holds the schema information for the "device_logs" table.
	DeviceLogsTable = &schema.Table{
		Name:       "device_logs",
		Columns:    DeviceLogsColumns,
		PrimaryKey: []*schema.Column{DeviceLogsColumns[0]},
	}
	// FramesColumns holds the columns for the "frames" table.
	FramesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "device_id", Type: field.TypeString},
		{Name: "header", Type: field.TypeString, Default: ""},
		{Name: "content", Type: field.TypeString, Default: ""},
		{Name: "tail", Type: field.TypeString, Default: ""},
		{Name: "time", Type: field.TypeTime},
	}
	// FramesTable holds the schema information for the "frames" table.
	FramesTable = &schema.Table{
		Name:       "frames",
		Columns:    FramesColumns,
		PrimaryKey: []*schema.Column{FramesColumns[0]},
	}
	// TestsColumns holds the columns for the "tests" table.
	TestsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
	}
	// TestsTable holds the schema information for the "tests" table.
	TestsTable = &schema.Table{
		Name:       "tests",
		Columns:    TestsColumns,
		PrimaryKey: []*schema.Column{TestsColumns[0]},
	}
	// VirtualDevicesColumns holds the columns for the "virtual_devices" table.
	VirtualDevicesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "name", Type: field.TypeString, Default: ""},
		{Name: "type", Type: field.TypeString, Default: ""},
		{Name: "protocol", Type: field.TypeString, Default: ""},
		{Name: "config", Type: field.TypeJSON},
		{Name: "status", Type: field.TypeString, Default: "ready"},
		{Name: "healthtimestamp", Type: field.TypeTime},
		{Name: "create_unix", Type: field.TypeTime},
		{Name: "update_unix", Type: field.TypeTime},
	}
	// VirtualDevicesTable holds the schema information for the "virtual_devices" table.
	VirtualDevicesTable = &schema.Table{
		Name:       "virtual_devices",
		Columns:    VirtualDevicesColumns,
		PrimaryKey: []*schema.Column{VirtualDevicesColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		DataTable,
		DeviceLogsTable,
		FramesTable,
		TestsTable,
		VirtualDevicesTable,
	}
)

func init() {
}

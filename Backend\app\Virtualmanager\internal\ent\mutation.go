// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/data"
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"GCF/app/Virtualmanager/internal/ent/frame"
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"GCF/app/Virtualmanager/internal/ent/schema"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeData          = "Data"
	TypeDeviceLog     = "DeviceLog"
	TypeFrame         = "Frame"
	TypeTest          = "Test"
	TypeVirtualDevice = "VirtualDevice"
)

// DataMutation represents an operation that mutates the Data nodes in the graph.
type DataMutation struct {
	config
	op            Op
	typ           string
	id            *int
	frame_id      *string
	index         *string
	name          *string
	_type         *string
	value         *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Data, error)
	predicates    []predicate.Data
}

var _ ent.Mutation = (*DataMutation)(nil)

// dataOption allows management of the mutation configuration using functional options.
type dataOption func(*DataMutation)

// newDataMutation creates new mutation for the Data entity.
func newDataMutation(c config, op Op, opts ...dataOption) *DataMutation {
	m := &DataMutation{
		config:        c,
		op:            op,
		typ:           TypeData,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDataID sets the ID field of the mutation.
func withDataID(id int) dataOption {
	return func(m *DataMutation) {
		var (
			err   error
			once  sync.Once
			value *Data
		)
		m.oldValue = func(ctx context.Context) (*Data, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Data.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withData sets the old Data of the mutation.
func withData(node *Data) dataOption {
	return func(m *DataMutation) {
		m.oldValue = func(context.Context) (*Data, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DataMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DataMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DataMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DataMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Data.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetFrameID sets the "frame_id" field.
func (m *DataMutation) SetFrameID(s string) {
	m.frame_id = &s
}

// FrameID returns the value of the "frame_id" field in the mutation.
func (m *DataMutation) FrameID() (r string, exists bool) {
	v := m.frame_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFrameID returns the old "frame_id" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldFrameID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFrameID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFrameID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFrameID: %w", err)
	}
	return oldValue.FrameID, nil
}

// ResetFrameID resets all changes to the "frame_id" field.
func (m *DataMutation) ResetFrameID() {
	m.frame_id = nil
}

// SetIndex sets the "index" field.
func (m *DataMutation) SetIndex(s string) {
	m.index = &s
}

// Index returns the value of the "index" field in the mutation.
func (m *DataMutation) Index() (r string, exists bool) {
	v := m.index
	if v == nil {
		return
	}
	return *v, true
}

// OldIndex returns the old "index" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldIndex(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIndex is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIndex requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIndex: %w", err)
	}
	return oldValue.Index, nil
}

// ResetIndex resets all changes to the "index" field.
func (m *DataMutation) ResetIndex() {
	m.index = nil
}

// SetName sets the "name" field.
func (m *DataMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *DataMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *DataMutation) ResetName() {
	m.name = nil
}

// SetType sets the "type" field.
func (m *DataMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *DataMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *DataMutation) ResetType() {
	m._type = nil
}

// SetValue sets the "value" field.
func (m *DataMutation) SetValue(s string) {
	m.value = &s
}

// Value returns the value of the "value" field in the mutation.
func (m *DataMutation) Value() (r string, exists bool) {
	v := m.value
	if v == nil {
		return
	}
	return *v, true
}

// OldValue returns the old "value" field's value of the Data entity.
// If the Data object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DataMutation) OldValue(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldValue is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldValue requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldValue: %w", err)
	}
	return oldValue.Value, nil
}

// ResetValue resets all changes to the "value" field.
func (m *DataMutation) ResetValue() {
	m.value = nil
}

// Where appends a list predicates to the DataMutation builder.
func (m *DataMutation) Where(ps ...predicate.Data) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DataMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DataMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Data, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DataMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DataMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Data).
func (m *DataMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DataMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.frame_id != nil {
		fields = append(fields, data.FieldFrameID)
	}
	if m.index != nil {
		fields = append(fields, data.FieldIndex)
	}
	if m.name != nil {
		fields = append(fields, data.FieldName)
	}
	if m._type != nil {
		fields = append(fields, data.FieldType)
	}
	if m.value != nil {
		fields = append(fields, data.FieldValue)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DataMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case data.FieldFrameID:
		return m.FrameID()
	case data.FieldIndex:
		return m.Index()
	case data.FieldName:
		return m.Name()
	case data.FieldType:
		return m.GetType()
	case data.FieldValue:
		return m.Value()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DataMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case data.FieldFrameID:
		return m.OldFrameID(ctx)
	case data.FieldIndex:
		return m.OldIndex(ctx)
	case data.FieldName:
		return m.OldName(ctx)
	case data.FieldType:
		return m.OldType(ctx)
	case data.FieldValue:
		return m.OldValue(ctx)
	}
	return nil, fmt.Errorf("unknown Data field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DataMutation) SetField(name string, value ent.Value) error {
	switch name {
	case data.FieldFrameID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFrameID(v)
		return nil
	case data.FieldIndex:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIndex(v)
		return nil
	case data.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case data.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case data.FieldValue:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetValue(v)
		return nil
	}
	return fmt.Errorf("unknown Data field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DataMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DataMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DataMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Data numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DataMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DataMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DataMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Data nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DataMutation) ResetField(name string) error {
	switch name {
	case data.FieldFrameID:
		m.ResetFrameID()
		return nil
	case data.FieldIndex:
		m.ResetIndex()
		return nil
	case data.FieldName:
		m.ResetName()
		return nil
	case data.FieldType:
		m.ResetType()
		return nil
	case data.FieldValue:
		m.ResetValue()
		return nil
	}
	return fmt.Errorf("unknown Data field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DataMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DataMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DataMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DataMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DataMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DataMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DataMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Data unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DataMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Data edge %s", name)
}

// DeviceLogMutation represents an operation that mutates the DeviceLog nodes in the graph.
type DeviceLogMutation struct {
	config
	op            Op
	typ           string
	id            *string
	name          *string
	healthlog     *string
	simulatelog   *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*DeviceLog, error)
	predicates    []predicate.DeviceLog
}

var _ ent.Mutation = (*DeviceLogMutation)(nil)

// devicelogOption allows management of the mutation configuration using functional options.
type devicelogOption func(*DeviceLogMutation)

// newDeviceLogMutation creates new mutation for the DeviceLog entity.
func newDeviceLogMutation(c config, op Op, opts ...devicelogOption) *DeviceLogMutation {
	m := &DeviceLogMutation{
		config:        c,
		op:            op,
		typ:           TypeDeviceLog,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDeviceLogID sets the ID field of the mutation.
func withDeviceLogID(id string) devicelogOption {
	return func(m *DeviceLogMutation) {
		var (
			err   error
			once  sync.Once
			value *DeviceLog
		)
		m.oldValue = func(ctx context.Context) (*DeviceLog, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().DeviceLog.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDeviceLog sets the old DeviceLog of the mutation.
func withDeviceLog(node *DeviceLog) devicelogOption {
	return func(m *DeviceLogMutation) {
		m.oldValue = func(context.Context) (*DeviceLog, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DeviceLogMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DeviceLogMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of DeviceLog entities.
func (m *DeviceLogMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DeviceLogMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DeviceLogMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().DeviceLog.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *DeviceLogMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *DeviceLogMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the DeviceLog entity.
// If the DeviceLog object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceLogMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *DeviceLogMutation) ResetName() {
	m.name = nil
}

// SetHealthlog sets the "healthlog" field.
func (m *DeviceLogMutation) SetHealthlog(s string) {
	m.healthlog = &s
}

// Healthlog returns the value of the "healthlog" field in the mutation.
func (m *DeviceLogMutation) Healthlog() (r string, exists bool) {
	v := m.healthlog
	if v == nil {
		return
	}
	return *v, true
}

// OldHealthlog returns the old "healthlog" field's value of the DeviceLog entity.
// If the DeviceLog object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceLogMutation) OldHealthlog(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHealthlog is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHealthlog requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHealthlog: %w", err)
	}
	return oldValue.Healthlog, nil
}

// ResetHealthlog resets all changes to the "healthlog" field.
func (m *DeviceLogMutation) ResetHealthlog() {
	m.healthlog = nil
}

// SetSimulatelog sets the "simulatelog" field.
func (m *DeviceLogMutation) SetSimulatelog(s string) {
	m.simulatelog = &s
}

// Simulatelog returns the value of the "simulatelog" field in the mutation.
func (m *DeviceLogMutation) Simulatelog() (r string, exists bool) {
	v := m.simulatelog
	if v == nil {
		return
	}
	return *v, true
}

// OldSimulatelog returns the old "simulatelog" field's value of the DeviceLog entity.
// If the DeviceLog object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DeviceLogMutation) OldSimulatelog(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSimulatelog is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSimulatelog requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSimulatelog: %w", err)
	}
	return oldValue.Simulatelog, nil
}

// ResetSimulatelog resets all changes to the "simulatelog" field.
func (m *DeviceLogMutation) ResetSimulatelog() {
	m.simulatelog = nil
}

// Where appends a list predicates to the DeviceLogMutation builder.
func (m *DeviceLogMutation) Where(ps ...predicate.DeviceLog) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DeviceLogMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DeviceLogMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.DeviceLog, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DeviceLogMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DeviceLogMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (DeviceLog).
func (m *DeviceLogMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DeviceLogMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m.name != nil {
		fields = append(fields, devicelog.FieldName)
	}
	if m.healthlog != nil {
		fields = append(fields, devicelog.FieldHealthlog)
	}
	if m.simulatelog != nil {
		fields = append(fields, devicelog.FieldSimulatelog)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DeviceLogMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case devicelog.FieldName:
		return m.Name()
	case devicelog.FieldHealthlog:
		return m.Healthlog()
	case devicelog.FieldSimulatelog:
		return m.Simulatelog()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DeviceLogMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case devicelog.FieldName:
		return m.OldName(ctx)
	case devicelog.FieldHealthlog:
		return m.OldHealthlog(ctx)
	case devicelog.FieldSimulatelog:
		return m.OldSimulatelog(ctx)
	}
	return nil, fmt.Errorf("unknown DeviceLog field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DeviceLogMutation) SetField(name string, value ent.Value) error {
	switch name {
	case devicelog.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case devicelog.FieldHealthlog:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHealthlog(v)
		return nil
	case devicelog.FieldSimulatelog:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSimulatelog(v)
		return nil
	}
	return fmt.Errorf("unknown DeviceLog field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DeviceLogMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DeviceLogMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DeviceLogMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown DeviceLog numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DeviceLogMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DeviceLogMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DeviceLogMutation) ClearField(name string) error {
	return fmt.Errorf("unknown DeviceLog nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DeviceLogMutation) ResetField(name string) error {
	switch name {
	case devicelog.FieldName:
		m.ResetName()
		return nil
	case devicelog.FieldHealthlog:
		m.ResetHealthlog()
		return nil
	case devicelog.FieldSimulatelog:
		m.ResetSimulatelog()
		return nil
	}
	return fmt.Errorf("unknown DeviceLog field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DeviceLogMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DeviceLogMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DeviceLogMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DeviceLogMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DeviceLogMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DeviceLogMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DeviceLogMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown DeviceLog unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DeviceLogMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown DeviceLog edge %s", name)
}

// FrameMutation represents an operation that mutates the Frame nodes in the graph.
type FrameMutation struct {
	config
	op            Op
	typ           string
	id            *string
	device_id     *string
	header        *string
	content       *string
	tail          *string
	time          *time.Time
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Frame, error)
	predicates    []predicate.Frame
}

var _ ent.Mutation = (*FrameMutation)(nil)

// frameOption allows management of the mutation configuration using functional options.
type frameOption func(*FrameMutation)

// newFrameMutation creates new mutation for the Frame entity.
func newFrameMutation(c config, op Op, opts ...frameOption) *FrameMutation {
	m := &FrameMutation{
		config:        c,
		op:            op,
		typ:           TypeFrame,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withFrameID sets the ID field of the mutation.
func withFrameID(id string) frameOption {
	return func(m *FrameMutation) {
		var (
			err   error
			once  sync.Once
			value *Frame
		)
		m.oldValue = func(ctx context.Context) (*Frame, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Frame.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withFrame sets the old Frame of the mutation.
func withFrame(node *Frame) frameOption {
	return func(m *FrameMutation) {
		m.oldValue = func(context.Context) (*Frame, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m FrameMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m FrameMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Frame entities.
func (m *FrameMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *FrameMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *FrameMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Frame.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetDeviceID sets the "device_id" field.
func (m *FrameMutation) SetDeviceID(s string) {
	m.device_id = &s
}

// DeviceID returns the value of the "device_id" field in the mutation.
func (m *FrameMutation) DeviceID() (r string, exists bool) {
	v := m.device_id
	if v == nil {
		return
	}
	return *v, true
}

// OldDeviceID returns the old "device_id" field's value of the Frame entity.
// If the Frame object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrameMutation) OldDeviceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeviceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeviceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeviceID: %w", err)
	}
	return oldValue.DeviceID, nil
}

// ResetDeviceID resets all changes to the "device_id" field.
func (m *FrameMutation) ResetDeviceID() {
	m.device_id = nil
}

// SetHeader sets the "header" field.
func (m *FrameMutation) SetHeader(s string) {
	m.header = &s
}

// Header returns the value of the "header" field in the mutation.
func (m *FrameMutation) Header() (r string, exists bool) {
	v := m.header
	if v == nil {
		return
	}
	return *v, true
}

// OldHeader returns the old "header" field's value of the Frame entity.
// If the Frame object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrameMutation) OldHeader(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHeader is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHeader requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHeader: %w", err)
	}
	return oldValue.Header, nil
}

// ResetHeader resets all changes to the "header" field.
func (m *FrameMutation) ResetHeader() {
	m.header = nil
}

// SetContent sets the "content" field.
func (m *FrameMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *FrameMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the Frame entity.
// If the Frame object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrameMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *FrameMutation) ResetContent() {
	m.content = nil
}

// SetTail sets the "tail" field.
func (m *FrameMutation) SetTail(s string) {
	m.tail = &s
}

// Tail returns the value of the "tail" field in the mutation.
func (m *FrameMutation) Tail() (r string, exists bool) {
	v := m.tail
	if v == nil {
		return
	}
	return *v, true
}

// OldTail returns the old "tail" field's value of the Frame entity.
// If the Frame object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrameMutation) OldTail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTail: %w", err)
	}
	return oldValue.Tail, nil
}

// ResetTail resets all changes to the "tail" field.
func (m *FrameMutation) ResetTail() {
	m.tail = nil
}

// SetTime sets the "time" field.
func (m *FrameMutation) SetTime(t time.Time) {
	m.time = &t
}

// Time returns the value of the "time" field in the mutation.
func (m *FrameMutation) Time() (r time.Time, exists bool) {
	v := m.time
	if v == nil {
		return
	}
	return *v, true
}

// OldTime returns the old "time" field's value of the Frame entity.
// If the Frame object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrameMutation) OldTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTime: %w", err)
	}
	return oldValue.Time, nil
}

// ResetTime resets all changes to the "time" field.
func (m *FrameMutation) ResetTime() {
	m.time = nil
}

// Where appends a list predicates to the FrameMutation builder.
func (m *FrameMutation) Where(ps ...predicate.Frame) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the FrameMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *FrameMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Frame, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *FrameMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *FrameMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Frame).
func (m *FrameMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *FrameMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.device_id != nil {
		fields = append(fields, frame.FieldDeviceID)
	}
	if m.header != nil {
		fields = append(fields, frame.FieldHeader)
	}
	if m.content != nil {
		fields = append(fields, frame.FieldContent)
	}
	if m.tail != nil {
		fields = append(fields, frame.FieldTail)
	}
	if m.time != nil {
		fields = append(fields, frame.FieldTime)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *FrameMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case frame.FieldDeviceID:
		return m.DeviceID()
	case frame.FieldHeader:
		return m.Header()
	case frame.FieldContent:
		return m.Content()
	case frame.FieldTail:
		return m.Tail()
	case frame.FieldTime:
		return m.Time()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *FrameMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case frame.FieldDeviceID:
		return m.OldDeviceID(ctx)
	case frame.FieldHeader:
		return m.OldHeader(ctx)
	case frame.FieldContent:
		return m.OldContent(ctx)
	case frame.FieldTail:
		return m.OldTail(ctx)
	case frame.FieldTime:
		return m.OldTime(ctx)
	}
	return nil, fmt.Errorf("unknown Frame field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FrameMutation) SetField(name string, value ent.Value) error {
	switch name {
	case frame.FieldDeviceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeviceID(v)
		return nil
	case frame.FieldHeader:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHeader(v)
		return nil
	case frame.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	case frame.FieldTail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTail(v)
		return nil
	case frame.FieldTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTime(v)
		return nil
	}
	return fmt.Errorf("unknown Frame field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *FrameMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *FrameMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FrameMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Frame numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *FrameMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *FrameMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *FrameMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Frame nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *FrameMutation) ResetField(name string) error {
	switch name {
	case frame.FieldDeviceID:
		m.ResetDeviceID()
		return nil
	case frame.FieldHeader:
		m.ResetHeader()
		return nil
	case frame.FieldContent:
		m.ResetContent()
		return nil
	case frame.FieldTail:
		m.ResetTail()
		return nil
	case frame.FieldTime:
		m.ResetTime()
		return nil
	}
	return fmt.Errorf("unknown Frame field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *FrameMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *FrameMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *FrameMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *FrameMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *FrameMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *FrameMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *FrameMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Frame unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *FrameMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Frame edge %s", name)
}

// TestMutation represents an operation that mutates the Test nodes in the graph.
type TestMutation struct {
	config
	op            Op
	typ           string
	id            *int
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Test, error)
	predicates    []predicate.Test
}

var _ ent.Mutation = (*TestMutation)(nil)

// testOption allows management of the mutation configuration using functional options.
type testOption func(*TestMutation)

// newTestMutation creates new mutation for the Test entity.
func newTestMutation(c config, op Op, opts ...testOption) *TestMutation {
	m := &TestMutation{
		config:        c,
		op:            op,
		typ:           TypeTest,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTestID sets the ID field of the mutation.
func withTestID(id int) testOption {
	return func(m *TestMutation) {
		var (
			err   error
			once  sync.Once
			value *Test
		)
		m.oldValue = func(ctx context.Context) (*Test, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Test.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTest sets the old Test of the mutation.
func withTest(node *Test) testOption {
	return func(m *TestMutation) {
		m.oldValue = func(context.Context) (*Test, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TestMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TestMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TestMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TestMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Test.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the TestMutation builder.
func (m *TestMutation) Where(ps ...predicate.Test) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TestMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TestMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Test, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TestMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TestMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Test).
func (m *TestMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TestMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TestMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TestMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Test field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TestMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Test field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TestMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TestMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TestMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Test numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TestMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TestMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TestMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Test nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TestMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Test field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TestMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TestMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TestMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TestMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TestMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TestMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TestMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Test unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TestMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Test edge %s", name)
}

// VirtualDeviceMutation represents an operation that mutates the VirtualDevice nodes in the graph.
type VirtualDeviceMutation struct {
	config
	op              Op
	typ             string
	id              *string
	name            *string
	_type           *string
	protocol        *string
	_config         *[]schema.DeviceConfig
	append_config   []schema.DeviceConfig
	status          *string
	healthtimestamp *time.Time
	create_unix     *time.Time
	update_unix     *time.Time
	clearedFields   map[string]struct{}
	done            bool
	oldValue        func(context.Context) (*VirtualDevice, error)
	predicates      []predicate.VirtualDevice
}

var _ ent.Mutation = (*VirtualDeviceMutation)(nil)

// virtualdeviceOption allows management of the mutation configuration using functional options.
type virtualdeviceOption func(*VirtualDeviceMutation)

// newVirtualDeviceMutation creates new mutation for the VirtualDevice entity.
func newVirtualDeviceMutation(c config, op Op, opts ...virtualdeviceOption) *VirtualDeviceMutation {
	m := &VirtualDeviceMutation{
		config:        c,
		op:            op,
		typ:           TypeVirtualDevice,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withVirtualDeviceID sets the ID field of the mutation.
func withVirtualDeviceID(id string) virtualdeviceOption {
	return func(m *VirtualDeviceMutation) {
		var (
			err   error
			once  sync.Once
			value *VirtualDevice
		)
		m.oldValue = func(ctx context.Context) (*VirtualDevice, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().VirtualDevice.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withVirtualDevice sets the old VirtualDevice of the mutation.
func withVirtualDevice(node *VirtualDevice) virtualdeviceOption {
	return func(m *VirtualDeviceMutation) {
		m.oldValue = func(context.Context) (*VirtualDevice, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m VirtualDeviceMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m VirtualDeviceMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of VirtualDevice entities.
func (m *VirtualDeviceMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *VirtualDeviceMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *VirtualDeviceMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().VirtualDevice.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *VirtualDeviceMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *VirtualDeviceMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *VirtualDeviceMutation) ResetName() {
	m.name = nil
}

// SetType sets the "type" field.
func (m *VirtualDeviceMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *VirtualDeviceMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *VirtualDeviceMutation) ResetType() {
	m._type = nil
}

// SetProtocol sets the "protocol" field.
func (m *VirtualDeviceMutation) SetProtocol(s string) {
	m.protocol = &s
}

// Protocol returns the value of the "protocol" field in the mutation.
func (m *VirtualDeviceMutation) Protocol() (r string, exists bool) {
	v := m.protocol
	if v == nil {
		return
	}
	return *v, true
}

// OldProtocol returns the old "protocol" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldProtocol(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProtocol is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProtocol requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProtocol: %w", err)
	}
	return oldValue.Protocol, nil
}

// ResetProtocol resets all changes to the "protocol" field.
func (m *VirtualDeviceMutation) ResetProtocol() {
	m.protocol = nil
}

// SetConfig sets the "config" field.
func (m *VirtualDeviceMutation) SetConfig(sc []schema.DeviceConfig) {
	m._config = &sc
	m.append_config = nil
}

// Config returns the value of the "config" field in the mutation.
func (m *VirtualDeviceMutation) Config() (r []schema.DeviceConfig, exists bool) {
	v := m._config
	if v == nil {
		return
	}
	return *v, true
}

// OldConfig returns the old "config" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldConfig(ctx context.Context) (v []schema.DeviceConfig, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfig is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfig requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfig: %w", err)
	}
	return oldValue.Config, nil
}

// AppendConfig adds sc to the "config" field.
func (m *VirtualDeviceMutation) AppendConfig(sc []schema.DeviceConfig) {
	m.append_config = append(m.append_config, sc...)
}

// AppendedConfig returns the list of values that were appended to the "config" field in this mutation.
func (m *VirtualDeviceMutation) AppendedConfig() ([]schema.DeviceConfig, bool) {
	if len(m.append_config) == 0 {
		return nil, false
	}
	return m.append_config, true
}

// ResetConfig resets all changes to the "config" field.
func (m *VirtualDeviceMutation) ResetConfig() {
	m._config = nil
	m.append_config = nil
}

// SetStatus sets the "status" field.
func (m *VirtualDeviceMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *VirtualDeviceMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *VirtualDeviceMutation) ResetStatus() {
	m.status = nil
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (m *VirtualDeviceMutation) SetHealthtimestamp(t time.Time) {
	m.healthtimestamp = &t
}

// Healthtimestamp returns the value of the "healthtimestamp" field in the mutation.
func (m *VirtualDeviceMutation) Healthtimestamp() (r time.Time, exists bool) {
	v := m.healthtimestamp
	if v == nil {
		return
	}
	return *v, true
}

// OldHealthtimestamp returns the old "healthtimestamp" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldHealthtimestamp(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHealthtimestamp is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHealthtimestamp requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHealthtimestamp: %w", err)
	}
	return oldValue.Healthtimestamp, nil
}

// ResetHealthtimestamp resets all changes to the "healthtimestamp" field.
func (m *VirtualDeviceMutation) ResetHealthtimestamp() {
	m.healthtimestamp = nil
}

// SetCreateUnix sets the "create_unix" field.
func (m *VirtualDeviceMutation) SetCreateUnix(t time.Time) {
	m.create_unix = &t
}

// CreateUnix returns the value of the "create_unix" field in the mutation.
func (m *VirtualDeviceMutation) CreateUnix() (r time.Time, exists bool) {
	v := m.create_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateUnix returns the old "create_unix" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldCreateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateUnix: %w", err)
	}
	return oldValue.CreateUnix, nil
}

// ResetCreateUnix resets all changes to the "create_unix" field.
func (m *VirtualDeviceMutation) ResetCreateUnix() {
	m.create_unix = nil
}

// SetUpdateUnix sets the "update_unix" field.
func (m *VirtualDeviceMutation) SetUpdateUnix(t time.Time) {
	m.update_unix = &t
}

// UpdateUnix returns the value of the "update_unix" field in the mutation.
func (m *VirtualDeviceMutation) UpdateUnix() (r time.Time, exists bool) {
	v := m.update_unix
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateUnix returns the old "update_unix" field's value of the VirtualDevice entity.
// If the VirtualDevice object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *VirtualDeviceMutation) OldUpdateUnix(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateUnix is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateUnix requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateUnix: %w", err)
	}
	return oldValue.UpdateUnix, nil
}

// ResetUpdateUnix resets all changes to the "update_unix" field.
func (m *VirtualDeviceMutation) ResetUpdateUnix() {
	m.update_unix = nil
}

// Where appends a list predicates to the VirtualDeviceMutation builder.
func (m *VirtualDeviceMutation) Where(ps ...predicate.VirtualDevice) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the VirtualDeviceMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *VirtualDeviceMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.VirtualDevice, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *VirtualDeviceMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *VirtualDeviceMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (VirtualDevice).
func (m *VirtualDeviceMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *VirtualDeviceMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.name != nil {
		fields = append(fields, virtualdevice.FieldName)
	}
	if m._type != nil {
		fields = append(fields, virtualdevice.FieldType)
	}
	if m.protocol != nil {
		fields = append(fields, virtualdevice.FieldProtocol)
	}
	if m._config != nil {
		fields = append(fields, virtualdevice.FieldConfig)
	}
	if m.status != nil {
		fields = append(fields, virtualdevice.FieldStatus)
	}
	if m.healthtimestamp != nil {
		fields = append(fields, virtualdevice.FieldHealthtimestamp)
	}
	if m.create_unix != nil {
		fields = append(fields, virtualdevice.FieldCreateUnix)
	}
	if m.update_unix != nil {
		fields = append(fields, virtualdevice.FieldUpdateUnix)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *VirtualDeviceMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case virtualdevice.FieldName:
		return m.Name()
	case virtualdevice.FieldType:
		return m.GetType()
	case virtualdevice.FieldProtocol:
		return m.Protocol()
	case virtualdevice.FieldConfig:
		return m.Config()
	case virtualdevice.FieldStatus:
		return m.Status()
	case virtualdevice.FieldHealthtimestamp:
		return m.Healthtimestamp()
	case virtualdevice.FieldCreateUnix:
		return m.CreateUnix()
	case virtualdevice.FieldUpdateUnix:
		return m.UpdateUnix()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *VirtualDeviceMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case virtualdevice.FieldName:
		return m.OldName(ctx)
	case virtualdevice.FieldType:
		return m.OldType(ctx)
	case virtualdevice.FieldProtocol:
		return m.OldProtocol(ctx)
	case virtualdevice.FieldConfig:
		return m.OldConfig(ctx)
	case virtualdevice.FieldStatus:
		return m.OldStatus(ctx)
	case virtualdevice.FieldHealthtimestamp:
		return m.OldHealthtimestamp(ctx)
	case virtualdevice.FieldCreateUnix:
		return m.OldCreateUnix(ctx)
	case virtualdevice.FieldUpdateUnix:
		return m.OldUpdateUnix(ctx)
	}
	return nil, fmt.Errorf("unknown VirtualDevice field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *VirtualDeviceMutation) SetField(name string, value ent.Value) error {
	switch name {
	case virtualdevice.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case virtualdevice.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case virtualdevice.FieldProtocol:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProtocol(v)
		return nil
	case virtualdevice.FieldConfig:
		v, ok := value.([]schema.DeviceConfig)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfig(v)
		return nil
	case virtualdevice.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case virtualdevice.FieldHealthtimestamp:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHealthtimestamp(v)
		return nil
	case virtualdevice.FieldCreateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateUnix(v)
		return nil
	case virtualdevice.FieldUpdateUnix:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateUnix(v)
		return nil
	}
	return fmt.Errorf("unknown VirtualDevice field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *VirtualDeviceMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *VirtualDeviceMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *VirtualDeviceMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown VirtualDevice numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *VirtualDeviceMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *VirtualDeviceMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *VirtualDeviceMutation) ClearField(name string) error {
	return fmt.Errorf("unknown VirtualDevice nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *VirtualDeviceMutation) ResetField(name string) error {
	switch name {
	case virtualdevice.FieldName:
		m.ResetName()
		return nil
	case virtualdevice.FieldType:
		m.ResetType()
		return nil
	case virtualdevice.FieldProtocol:
		m.ResetProtocol()
		return nil
	case virtualdevice.FieldConfig:
		m.ResetConfig()
		return nil
	case virtualdevice.FieldStatus:
		m.ResetStatus()
		return nil
	case virtualdevice.FieldHealthtimestamp:
		m.ResetHealthtimestamp()
		return nil
	case virtualdevice.FieldCreateUnix:
		m.ResetCreateUnix()
		return nil
	case virtualdevice.FieldUpdateUnix:
		m.ResetUpdateUnix()
		return nil
	}
	return fmt.Errorf("unknown VirtualDevice field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *VirtualDeviceMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *VirtualDeviceMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *VirtualDeviceMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *VirtualDeviceMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *VirtualDeviceMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *VirtualDeviceMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *VirtualDeviceMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown VirtualDevice unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *VirtualDeviceMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown VirtualDevice edge %s", name)
}

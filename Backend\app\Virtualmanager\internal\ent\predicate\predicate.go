// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// Data is the predicate function for data builders.
type Data func(*sql.Selector)

// DeviceLog is the predicate function for devicelog builders.
type DeviceLog func(*sql.Selector)

// Frame is the predicate function for frame builders.
type Frame func(*sql.Selector)

// Test is the predicate function for test builders.
type Test func(*sql.Selector)

// VirtualDevice is the predicate function for virtualdevice builders.
type VirtualDevice func(*sql.Selector)

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/data"
	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"GCF/app/Virtualmanager/internal/ent/frame"
	"GCF/app/Virtualmanager/internal/ent/schema"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	dataFields := schema.Data{}.Fields()
	_ = dataFields
	// dataDescFrameID is the schema descriptor for frame_id field.
	dataDescFrameID := dataFields[0].Descriptor()
	// data.FrameIDValidator is a validator for the "frame_id" field. It is called by the builders before save.
	data.FrameIDValidator = dataDescFrameID.Validators[0].(func(string) error)
	// dataDescIndex is the schema descriptor for index field.
	dataDescIndex := dataFields[1].Descriptor()
	// data.IndexValidator is a validator for the "index" field. It is called by the builders before save.
	data.IndexValidator = dataDescIndex.Validators[0].(func(string) error)
	// dataDescName is the schema descriptor for name field.
	dataDescName := dataFields[2].Descriptor()
	// data.NameValidator is a validator for the "name" field. It is called by the builders before save.
	data.NameValidator = dataDescName.Validators[0].(func(string) error)
	// dataDescType is the schema descriptor for type field.
	dataDescType := dataFields[3].Descriptor()
	// data.DefaultType holds the default value on creation for the type field.
	data.DefaultType = dataDescType.Default.(string)
	// dataDescValue is the schema descriptor for value field.
	dataDescValue := dataFields[4].Descriptor()
	// data.DefaultValue holds the default value on creation for the value field.
	data.DefaultValue = dataDescValue.Default.(string)
	devicelogFields := schema.DeviceLog{}.Fields()
	_ = devicelogFields
	// devicelogDescName is the schema descriptor for name field.
	devicelogDescName := devicelogFields[1].Descriptor()
	// devicelog.NameValidator is a validator for the "name" field. It is called by the builders before save.
	devicelog.NameValidator = devicelogDescName.Validators[0].(func(string) error)
	// devicelogDescHealthlog is the schema descriptor for healthlog field.
	devicelogDescHealthlog := devicelogFields[2].Descriptor()
	// devicelog.DefaultHealthlog holds the default value on creation for the healthlog field.
	devicelog.DefaultHealthlog = devicelogDescHealthlog.Default.(string)
	// devicelogDescSimulatelog is the schema descriptor for simulatelog field.
	devicelogDescSimulatelog := devicelogFields[3].Descriptor()
	// devicelog.DefaultSimulatelog holds the default value on creation for the simulatelog field.
	devicelog.DefaultSimulatelog = devicelogDescSimulatelog.Default.(string)
	// devicelogDescID is the schema descriptor for id field.
	devicelogDescID := devicelogFields[0].Descriptor()
	// devicelog.IDValidator is a validator for the "id" field. It is called by the builders before save.
	devicelog.IDValidator = devicelogDescID.Validators[0].(func(string) error)
	frameFields := schema.Frame{}.Fields()
	_ = frameFields
	// frameDescDeviceID is the schema descriptor for device_id field.
	frameDescDeviceID := frameFields[0].Descriptor()
	// frame.DeviceIDValidator is a validator for the "device_id" field. It is called by the builders before save.
	frame.DeviceIDValidator = frameDescDeviceID.Validators[0].(func(string) error)
	// frameDescHeader is the schema descriptor for header field.
	frameDescHeader := frameFields[2].Descriptor()
	// frame.DefaultHeader holds the default value on creation for the header field.
	frame.DefaultHeader = frameDescHeader.Default.(string)
	// frameDescContent is the schema descriptor for content field.
	frameDescContent := frameFields[3].Descriptor()
	// frame.DefaultContent holds the default value on creation for the content field.
	frame.DefaultContent = frameDescContent.Default.(string)
	// frameDescTail is the schema descriptor for tail field.
	frameDescTail := frameFields[4].Descriptor()
	// frame.DefaultTail holds the default value on creation for the tail field.
	frame.DefaultTail = frameDescTail.Default.(string)
	// frameDescTime is the schema descriptor for time field.
	frameDescTime := frameFields[5].Descriptor()
	// frame.DefaultTime holds the default value on creation for the time field.
	frame.DefaultTime = frameDescTime.Default.(func() time.Time)
	// frameDescID is the schema descriptor for id field.
	frameDescID := frameFields[1].Descriptor()
	// frame.IDValidator is a validator for the "id" field. It is called by the builders before save.
	frame.IDValidator = frameDescID.Validators[0].(func(string) error)
	virtualdeviceFields := schema.VirtualDevice{}.Fields()
	_ = virtualdeviceFields
	// virtualdeviceDescName is the schema descriptor for name field.
	virtualdeviceDescName := virtualdeviceFields[1].Descriptor()
	// virtualdevice.DefaultName holds the default value on creation for the name field.
	virtualdevice.DefaultName = virtualdeviceDescName.Default.(string)
	// virtualdeviceDescType is the schema descriptor for type field.
	virtualdeviceDescType := virtualdeviceFields[2].Descriptor()
	// virtualdevice.DefaultType holds the default value on creation for the type field.
	virtualdevice.DefaultType = virtualdeviceDescType.Default.(string)
	// virtualdeviceDescProtocol is the schema descriptor for protocol field.
	virtualdeviceDescProtocol := virtualdeviceFields[3].Descriptor()
	// virtualdevice.DefaultProtocol holds the default value on creation for the protocol field.
	virtualdevice.DefaultProtocol = virtualdeviceDescProtocol.Default.(string)
	// virtualdeviceDescConfig is the schema descriptor for config field.
	virtualdeviceDescConfig := virtualdeviceFields[4].Descriptor()
	// virtualdevice.DefaultConfig holds the default value on creation for the config field.
	virtualdevice.DefaultConfig = virtualdeviceDescConfig.Default.([]schema.DeviceConfig)
	// virtualdeviceDescStatus is the schema descriptor for status field.
	virtualdeviceDescStatus := virtualdeviceFields[5].Descriptor()
	// virtualdevice.DefaultStatus holds the default value on creation for the status field.
	virtualdevice.DefaultStatus = virtualdeviceDescStatus.Default.(string)
	// virtualdeviceDescHealthtimestamp is the schema descriptor for healthtimestamp field.
	virtualdeviceDescHealthtimestamp := virtualdeviceFields[6].Descriptor()
	// virtualdevice.DefaultHealthtimestamp holds the default value on creation for the healthtimestamp field.
	virtualdevice.DefaultHealthtimestamp = virtualdeviceDescHealthtimestamp.Default.(time.Time)
	// virtualdeviceDescCreateUnix is the schema descriptor for create_unix field.
	virtualdeviceDescCreateUnix := virtualdeviceFields[7].Descriptor()
	// virtualdevice.DefaultCreateUnix holds the default value on creation for the create_unix field.
	virtualdevice.DefaultCreateUnix = virtualdeviceDescCreateUnix.Default.(func() time.Time)
	// virtualdeviceDescUpdateUnix is the schema descriptor for update_unix field.
	virtualdeviceDescUpdateUnix := virtualdeviceFields[8].Descriptor()
	// virtualdevice.DefaultUpdateUnix holds the default value on creation for the update_unix field.
	virtualdevice.DefaultUpdateUnix = virtualdeviceDescUpdateUnix.Default.(func() time.Time)
	// virtualdevice.UpdateDefaultUpdateUnix holds the default value on update for the update_unix field.
	virtualdevice.UpdateDefaultUpdateUnix = virtualdeviceDescUpdateUnix.UpdateDefault.(func() time.Time)
	// virtualdeviceDescID is the schema descriptor for id field.
	virtualdeviceDescID := virtualdeviceFields[0].Descriptor()
	// virtualdevice.IDValidator is a validator for the "id" field. It is called by the builders before save.
	virtualdevice.IDValidator = virtualdeviceDescID.Validators[0].(func(string) error)
}

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Data holds the schema definition for the Data entity.
type Data struct {
	ent.Schema
}

// Fields of the Data.
func (Data) Fields() []ent.Field {
	return []ent.Field{
		field.String("frame_id").
			Comment("Foreign key to frame").
			NotEmpty(),
		field.String("index").
			Comment("Index of the data").
			NotEmpty(),
		field.String("name").
			Comment("name of the index data").
			NotEmpty(),
		field.String("type").
			Comment("type of the index data").
			Default(""),
		field.String("value").
			Comment("value of the index data").
			Default(""),
	}
}

// Edges of the Data.
func (Data) Edges() []ent.Edge {
	return nil
}

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// DeviceLog holds the schema definition for the DeviceLog entity.
type DeviceLog struct {
	ent.Schema
}

// Fields of the DeviceLog.
func (DeviceLog) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			Comment("Foreign key to device_id").
			NotEmpty(),
		field.String("name").
			Comment("name of the device").
			NotEmpty(),
		field.Text("healthlog").
			Comment("log of health check").
			Default(""),
		field.Text("simulatelog").
			Comment("log of simulation").
			Default(""),
	}
}

// Edges of the DeviceLog.
func (DeviceLog) Edges() []ent.Edge {
	return nil
}

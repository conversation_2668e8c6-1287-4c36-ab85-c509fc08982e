package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Frame holds the schema definition for the Frame entity.
type Frame struct {
	ent.Schema
}

// Fields of the Frame.
func (Frame) Fields() []ent.Field {
	return []ent.Field{
		field.String("device_id").
			Comment("Foreign key to Devi<PERSON>").
			NotEmpty(),
		field.String("id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("Frame ID"),
		field.String("header").
			Comment("Header of the frame").
			Default(""),
		field.String("content").
			Comment("Data content of the frame").
			Default(""),
		field.String("tail").
			Comment("Tail of the frame").
			Default(""),
		field.Time("time").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
	}
}

// Edges of the Frame.
func (Frame) Edges() []ent.Edge {
	return nil
}

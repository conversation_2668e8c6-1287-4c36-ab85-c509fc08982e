package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// VirtualDevice holds the schema definition for the VirtualDevice entity.
type VirtualDevice struct {
	ent.Schema
}

// DeviceConfig 定义设备配置结构
type DeviceConfig struct {
	ParamName string `json:"paramName"`
	Value     string `json:"value"`
}

// Fields of the VirtualDevice.
func (VirtualDevice) Fields() []ent.Field {
	return []ent.Field{
		//设备metadata
		field.String("id").
			NotEmpty().
			Unique().
			Immutable().
			Comment("设备UID"),
		field.String("name").
			Comment("name of the machine").
			Default(""),

		//设备资源info
		field.String("type").
			Comment("Type of the machine").
			Default(""),
		field.String("protocol").
			Comment("protocol of the communication").
			Default(""),
		field.JSON("config", []DeviceConfig{}).
			Comment("config of device").
			Default([]DeviceConfig{}),

		//设备状态info
		field.String("status").
			Default("ready"),
		field.Time("healthtimestamp").
			Comment("Health check timestamp").
			Default(time.Unix(0, 0)),
		field.Time("create_unix").
			Immutable().
			Default(time.Now).
			Comment("Create timestamp"),
		field.Time("update_unix").
			Default(time.Now).
			UpdateDefault(time.Now).
			Comment("Update timestamp"),
	}
}

// Edges of the VirtualDevice.
func (VirtualDevice) Edges() []ent.Edge {
	return nil
}

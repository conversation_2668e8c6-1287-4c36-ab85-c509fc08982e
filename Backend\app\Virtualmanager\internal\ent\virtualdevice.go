// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/schema"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// VirtualDevice is the model entity for the VirtualDevice schema.
type VirtualDevice struct {
	config `json:"-"`
	// ID of the ent.
	// 设备UID
	ID string `json:"id,omitempty"`
	// name of the machine
	Name string `json:"name,omitempty"`
	// Type of the machine
	Type string `json:"type,omitempty"`
	// protocol of the communication
	Protocol string `json:"protocol,omitempty"`
	// config of device
	Config []schema.DeviceConfig `json:"config,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// Health check timestamp
	Healthtimestamp time.Time `json:"healthtimestamp,omitempty"`
	// Create timestamp
	CreateUnix time.Time `json:"create_unix,omitempty"`
	// Update timestamp
	UpdateUnix   time.Time `json:"update_unix,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*VirtualDevice) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case virtualdevice.FieldConfig:
			values[i] = new([]byte)
		case virtualdevice.FieldID, virtualdevice.FieldName, virtualdevice.FieldType, virtualdevice.FieldProtocol, virtualdevice.FieldStatus:
			values[i] = new(sql.NullString)
		case virtualdevice.FieldHealthtimestamp, virtualdevice.FieldCreateUnix, virtualdevice.FieldUpdateUnix:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the VirtualDevice fields.
func (vd *VirtualDevice) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case virtualdevice.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				vd.ID = value.String
			}
		case virtualdevice.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				vd.Name = value.String
			}
		case virtualdevice.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				vd.Type = value.String
			}
		case virtualdevice.FieldProtocol:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field protocol", values[i])
			} else if value.Valid {
				vd.Protocol = value.String
			}
		case virtualdevice.FieldConfig:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field config", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &vd.Config); err != nil {
					return fmt.Errorf("unmarshal field config: %w", err)
				}
			}
		case virtualdevice.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				vd.Status = value.String
			}
		case virtualdevice.FieldHealthtimestamp:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field healthtimestamp", values[i])
			} else if value.Valid {
				vd.Healthtimestamp = value.Time
			}
		case virtualdevice.FieldCreateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_unix", values[i])
			} else if value.Valid {
				vd.CreateUnix = value.Time
			}
		case virtualdevice.FieldUpdateUnix:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_unix", values[i])
			} else if value.Valid {
				vd.UpdateUnix = value.Time
			}
		default:
			vd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the VirtualDevice.
// This includes values selected through modifiers, order, etc.
func (vd *VirtualDevice) Value(name string) (ent.Value, error) {
	return vd.selectValues.Get(name)
}

// Update returns a builder for updating this VirtualDevice.
// Note that you need to call VirtualDevice.Unwrap() before calling this method if this VirtualDevice
// was returned from a transaction, and the transaction was committed or rolled back.
func (vd *VirtualDevice) Update() *VirtualDeviceUpdateOne {
	return NewVirtualDeviceClient(vd.config).UpdateOne(vd)
}

// Unwrap unwraps the VirtualDevice entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (vd *VirtualDevice) Unwrap() *VirtualDevice {
	_tx, ok := vd.config.driver.(*txDriver)
	if !ok {
		panic("ent: VirtualDevice is not a transactional entity")
	}
	vd.config.driver = _tx.drv
	return vd
}

// String implements the fmt.Stringer.
func (vd *VirtualDevice) String() string {
	var builder strings.Builder
	builder.WriteString("VirtualDevice(")
	builder.WriteString(fmt.Sprintf("id=%v, ", vd.ID))
	builder.WriteString("name=")
	builder.WriteString(vd.Name)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(vd.Type)
	builder.WriteString(", ")
	builder.WriteString("protocol=")
	builder.WriteString(vd.Protocol)
	builder.WriteString(", ")
	builder.WriteString("config=")
	builder.WriteString(fmt.Sprintf("%v", vd.Config))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(vd.Status)
	builder.WriteString(", ")
	builder.WriteString("healthtimestamp=")
	builder.WriteString(vd.Healthtimestamp.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("create_unix=")
	builder.WriteString(vd.CreateUnix.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_unix=")
	builder.WriteString(vd.UpdateUnix.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// VirtualDevices is a parsable slice of VirtualDevice.
type VirtualDevices []*VirtualDevice

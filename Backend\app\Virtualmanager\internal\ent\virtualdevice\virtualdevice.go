// Code generated by ent, DO NOT EDIT.

package virtualdevice

import (
	"GCF/app/Virtualmanager/internal/ent/schema"
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the virtualdevice type in the database.
	Label = "virtual_device"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldProtocol holds the string denoting the protocol field in the database.
	FieldProtocol = "protocol"
	// FieldConfig holds the string denoting the config field in the database.
	FieldConfig = "config"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldHealthtimestamp holds the string denoting the healthtimestamp field in the database.
	FieldHealthtimestamp = "healthtimestamp"
	// FieldCreateUnix holds the string denoting the create_unix field in the database.
	FieldCreateUnix = "create_unix"
	// FieldUpdateUnix holds the string denoting the update_unix field in the database.
	FieldUpdateUnix = "update_unix"
	// Table holds the table name of the virtualdevice in the database.
	Table = "virtual_devices"
)

// Columns holds all SQL columns for virtualdevice fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldType,
	FieldProtocol,
	FieldConfig,
	FieldStatus,
	FieldHealthtimestamp,
	FieldCreateUnix,
	FieldUpdateUnix,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultName holds the default value on creation for the "name" field.
	DefaultName string
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType string
	// DefaultProtocol holds the default value on creation for the "protocol" field.
	DefaultProtocol string
	// DefaultConfig holds the default value on creation for the "config" field.
	DefaultConfig []schema.DeviceConfig
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
	// DefaultHealthtimestamp holds the default value on creation for the "healthtimestamp" field.
	DefaultHealthtimestamp time.Time
	// DefaultCreateUnix holds the default value on creation for the "create_unix" field.
	DefaultCreateUnix func() time.Time
	// DefaultUpdateUnix holds the default value on creation for the "update_unix" field.
	DefaultUpdateUnix func() time.Time
	// UpdateDefaultUpdateUnix holds the default value on update for the "update_unix" field.
	UpdateDefaultUpdateUnix func() time.Time
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the VirtualDevice queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByProtocol orders the results by the protocol field.
func ByProtocol(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProtocol, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByHealthtimestamp orders the results by the healthtimestamp field.
func ByHealthtimestamp(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHealthtimestamp, opts...).ToFunc()
}

// ByCreateUnix orders the results by the create_unix field.
func ByCreateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUnix, opts...).ToFunc()
}

// ByUpdateUnix orders the results by the update_unix field.
func ByUpdateUnix(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateUnix, opts...).ToFunc()
}

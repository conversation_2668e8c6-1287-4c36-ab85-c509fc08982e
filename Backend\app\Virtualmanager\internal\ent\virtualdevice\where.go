// Code generated by ent, DO NOT EDIT.

package virtualdevice

import (
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContainsFold(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldName, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldType, v))
}

// Protocol applies equality check predicate on the "protocol" field. It's identical to ProtocolEQ.
func Protocol(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldProtocol, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldStatus, v))
}

// Healthtimestamp applies equality check predicate on the "healthtimestamp" field. It's identical to HealthtimestampEQ.
func Healthtimestamp(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldHealthtimestamp, v))
}

// CreateUnix applies equality check predicate on the "create_unix" field. It's identical to CreateUnixEQ.
func CreateUnix(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldCreateUnix, v))
}

// UpdateUnix applies equality check predicate on the "update_unix" field. It's identical to UpdateUnixEQ.
func UpdateUnix(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldUpdateUnix, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContainsFold(FieldName, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContainsFold(FieldType, v))
}

// ProtocolEQ applies the EQ predicate on the "protocol" field.
func ProtocolEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldProtocol, v))
}

// ProtocolNEQ applies the NEQ predicate on the "protocol" field.
func ProtocolNEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldProtocol, v))
}

// ProtocolIn applies the In predicate on the "protocol" field.
func ProtocolIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldProtocol, vs...))
}

// ProtocolNotIn applies the NotIn predicate on the "protocol" field.
func ProtocolNotIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldProtocol, vs...))
}

// ProtocolGT applies the GT predicate on the "protocol" field.
func ProtocolGT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldProtocol, v))
}

// ProtocolGTE applies the GTE predicate on the "protocol" field.
func ProtocolGTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldProtocol, v))
}

// ProtocolLT applies the LT predicate on the "protocol" field.
func ProtocolLT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldProtocol, v))
}

// ProtocolLTE applies the LTE predicate on the "protocol" field.
func ProtocolLTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldProtocol, v))
}

// ProtocolContains applies the Contains predicate on the "protocol" field.
func ProtocolContains(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContains(FieldProtocol, v))
}

// ProtocolHasPrefix applies the HasPrefix predicate on the "protocol" field.
func ProtocolHasPrefix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasPrefix(FieldProtocol, v))
}

// ProtocolHasSuffix applies the HasSuffix predicate on the "protocol" field.
func ProtocolHasSuffix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasSuffix(FieldProtocol, v))
}

// ProtocolEqualFold applies the EqualFold predicate on the "protocol" field.
func ProtocolEqualFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEqualFold(FieldProtocol, v))
}

// ProtocolContainsFold applies the ContainsFold predicate on the "protocol" field.
func ProtocolContainsFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContainsFold(FieldProtocol, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldContainsFold(FieldStatus, v))
}

// HealthtimestampEQ applies the EQ predicate on the "healthtimestamp" field.
func HealthtimestampEQ(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldHealthtimestamp, v))
}

// HealthtimestampNEQ applies the NEQ predicate on the "healthtimestamp" field.
func HealthtimestampNEQ(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldHealthtimestamp, v))
}

// HealthtimestampIn applies the In predicate on the "healthtimestamp" field.
func HealthtimestampIn(vs ...time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldHealthtimestamp, vs...))
}

// HealthtimestampNotIn applies the NotIn predicate on the "healthtimestamp" field.
func HealthtimestampNotIn(vs ...time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldHealthtimestamp, vs...))
}

// HealthtimestampGT applies the GT predicate on the "healthtimestamp" field.
func HealthtimestampGT(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldHealthtimestamp, v))
}

// HealthtimestampGTE applies the GTE predicate on the "healthtimestamp" field.
func HealthtimestampGTE(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldHealthtimestamp, v))
}

// HealthtimestampLT applies the LT predicate on the "healthtimestamp" field.
func HealthtimestampLT(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldHealthtimestamp, v))
}

// HealthtimestampLTE applies the LTE predicate on the "healthtimestamp" field.
func HealthtimestampLTE(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldHealthtimestamp, v))
}

// CreateUnixEQ applies the EQ predicate on the "create_unix" field.
func CreateUnixEQ(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldCreateUnix, v))
}

// CreateUnixNEQ applies the NEQ predicate on the "create_unix" field.
func CreateUnixNEQ(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldCreateUnix, v))
}

// CreateUnixIn applies the In predicate on the "create_unix" field.
func CreateUnixIn(vs ...time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldCreateUnix, vs...))
}

// CreateUnixNotIn applies the NotIn predicate on the "create_unix" field.
func CreateUnixNotIn(vs ...time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldCreateUnix, vs...))
}

// CreateUnixGT applies the GT predicate on the "create_unix" field.
func CreateUnixGT(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldCreateUnix, v))
}

// CreateUnixGTE applies the GTE predicate on the "create_unix" field.
func CreateUnixGTE(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldCreateUnix, v))
}

// CreateUnixLT applies the LT predicate on the "create_unix" field.
func CreateUnixLT(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldCreateUnix, v))
}

// CreateUnixLTE applies the LTE predicate on the "create_unix" field.
func CreateUnixLTE(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldCreateUnix, v))
}

// UpdateUnixEQ applies the EQ predicate on the "update_unix" field.
func UpdateUnixEQ(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldEQ(FieldUpdateUnix, v))
}

// UpdateUnixNEQ applies the NEQ predicate on the "update_unix" field.
func UpdateUnixNEQ(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNEQ(FieldUpdateUnix, v))
}

// UpdateUnixIn applies the In predicate on the "update_unix" field.
func UpdateUnixIn(vs ...time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldIn(FieldUpdateUnix, vs...))
}

// UpdateUnixNotIn applies the NotIn predicate on the "update_unix" field.
func UpdateUnixNotIn(vs ...time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldNotIn(FieldUpdateUnix, vs...))
}

// UpdateUnixGT applies the GT predicate on the "update_unix" field.
func UpdateUnixGT(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGT(FieldUpdateUnix, v))
}

// UpdateUnixGTE applies the GTE predicate on the "update_unix" field.
func UpdateUnixGTE(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldGTE(FieldUpdateUnix, v))
}

// UpdateUnixLT applies the LT predicate on the "update_unix" field.
func UpdateUnixLT(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLT(FieldUpdateUnix, v))
}

// UpdateUnixLTE applies the LTE predicate on the "update_unix" field.
func UpdateUnixLTE(v time.Time) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.FieldLTE(FieldUpdateUnix, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.VirtualDevice) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.VirtualDevice) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.VirtualDevice) predicate.VirtualDevice {
	return predicate.VirtualDevice(sql.NotPredicates(p))
}

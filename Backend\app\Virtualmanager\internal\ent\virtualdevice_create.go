// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/schema"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// VirtualDeviceCreate is the builder for creating a VirtualDevice entity.
type VirtualDeviceCreate struct {
	config
	mutation *VirtualDeviceMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (vdc *VirtualDeviceCreate) SetName(s string) *VirtualDeviceCreate {
	vdc.mutation.SetName(s)
	return vdc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (vdc *VirtualDeviceCreate) SetNillableName(s *string) *VirtualDeviceCreate {
	if s != nil {
		vdc.SetName(*s)
	}
	return vdc
}

// SetType sets the "type" field.
func (vdc *VirtualDeviceCreate) SetType(s string) *VirtualDeviceCreate {
	vdc.mutation.SetType(s)
	return vdc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (vdc *VirtualDeviceCreate) SetNillableType(s *string) *VirtualDeviceCreate {
	if s != nil {
		vdc.SetType(*s)
	}
	return vdc
}

// SetProtocol sets the "protocol" field.
func (vdc *VirtualDeviceCreate) SetProtocol(s string) *VirtualDeviceCreate {
	vdc.mutation.SetProtocol(s)
	return vdc
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (vdc *VirtualDeviceCreate) SetNillableProtocol(s *string) *VirtualDeviceCreate {
	if s != nil {
		vdc.SetProtocol(*s)
	}
	return vdc
}

// SetConfig sets the "config" field.
func (vdc *VirtualDeviceCreate) SetConfig(sc []schema.DeviceConfig) *VirtualDeviceCreate {
	vdc.mutation.SetConfig(sc)
	return vdc
}

// SetStatus sets the "status" field.
func (vdc *VirtualDeviceCreate) SetStatus(s string) *VirtualDeviceCreate {
	vdc.mutation.SetStatus(s)
	return vdc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (vdc *VirtualDeviceCreate) SetNillableStatus(s *string) *VirtualDeviceCreate {
	if s != nil {
		vdc.SetStatus(*s)
	}
	return vdc
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (vdc *VirtualDeviceCreate) SetHealthtimestamp(t time.Time) *VirtualDeviceCreate {
	vdc.mutation.SetHealthtimestamp(t)
	return vdc
}

// SetNillableHealthtimestamp sets the "healthtimestamp" field if the given value is not nil.
func (vdc *VirtualDeviceCreate) SetNillableHealthtimestamp(t *time.Time) *VirtualDeviceCreate {
	if t != nil {
		vdc.SetHealthtimestamp(*t)
	}
	return vdc
}

// SetCreateUnix sets the "create_unix" field.
func (vdc *VirtualDeviceCreate) SetCreateUnix(t time.Time) *VirtualDeviceCreate {
	vdc.mutation.SetCreateUnix(t)
	return vdc
}

// SetNillableCreateUnix sets the "create_unix" field if the given value is not nil.
func (vdc *VirtualDeviceCreate) SetNillableCreateUnix(t *time.Time) *VirtualDeviceCreate {
	if t != nil {
		vdc.SetCreateUnix(*t)
	}
	return vdc
}

// SetUpdateUnix sets the "update_unix" field.
func (vdc *VirtualDeviceCreate) SetUpdateUnix(t time.Time) *VirtualDeviceCreate {
	vdc.mutation.SetUpdateUnix(t)
	return vdc
}

// SetNillableUpdateUnix sets the "update_unix" field if the given value is not nil.
func (vdc *VirtualDeviceCreate) SetNillableUpdateUnix(t *time.Time) *VirtualDeviceCreate {
	if t != nil {
		vdc.SetUpdateUnix(*t)
	}
	return vdc
}

// SetID sets the "id" field.
func (vdc *VirtualDeviceCreate) SetID(s string) *VirtualDeviceCreate {
	vdc.mutation.SetID(s)
	return vdc
}

// Mutation returns the VirtualDeviceMutation object of the builder.
func (vdc *VirtualDeviceCreate) Mutation() *VirtualDeviceMutation {
	return vdc.mutation
}

// Save creates the VirtualDevice in the database.
func (vdc *VirtualDeviceCreate) Save(ctx context.Context) (*VirtualDevice, error) {
	vdc.defaults()
	return withHooks(ctx, vdc.sqlSave, vdc.mutation, vdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (vdc *VirtualDeviceCreate) SaveX(ctx context.Context) *VirtualDevice {
	v, err := vdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (vdc *VirtualDeviceCreate) Exec(ctx context.Context) error {
	_, err := vdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (vdc *VirtualDeviceCreate) ExecX(ctx context.Context) {
	if err := vdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (vdc *VirtualDeviceCreate) defaults() {
	if _, ok := vdc.mutation.Name(); !ok {
		v := virtualdevice.DefaultName
		vdc.mutation.SetName(v)
	}
	if _, ok := vdc.mutation.GetType(); !ok {
		v := virtualdevice.DefaultType
		vdc.mutation.SetType(v)
	}
	if _, ok := vdc.mutation.Protocol(); !ok {
		v := virtualdevice.DefaultProtocol
		vdc.mutation.SetProtocol(v)
	}
	if _, ok := vdc.mutation.Config(); !ok {
		v := virtualdevice.DefaultConfig
		vdc.mutation.SetConfig(v)
	}
	if _, ok := vdc.mutation.Status(); !ok {
		v := virtualdevice.DefaultStatus
		vdc.mutation.SetStatus(v)
	}
	if _, ok := vdc.mutation.Healthtimestamp(); !ok {
		v := virtualdevice.DefaultHealthtimestamp
		vdc.mutation.SetHealthtimestamp(v)
	}
	if _, ok := vdc.mutation.CreateUnix(); !ok {
		v := virtualdevice.DefaultCreateUnix()
		vdc.mutation.SetCreateUnix(v)
	}
	if _, ok := vdc.mutation.UpdateUnix(); !ok {
		v := virtualdevice.DefaultUpdateUnix()
		vdc.mutation.SetUpdateUnix(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (vdc *VirtualDeviceCreate) check() error {
	if _, ok := vdc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "VirtualDevice.name"`)}
	}
	if _, ok := vdc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "VirtualDevice.type"`)}
	}
	if _, ok := vdc.mutation.Protocol(); !ok {
		return &ValidationError{Name: "protocol", err: errors.New(`ent: missing required field "VirtualDevice.protocol"`)}
	}
	if _, ok := vdc.mutation.Config(); !ok {
		return &ValidationError{Name: "config", err: errors.New(`ent: missing required field "VirtualDevice.config"`)}
	}
	if _, ok := vdc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "VirtualDevice.status"`)}
	}
	if _, ok := vdc.mutation.Healthtimestamp(); !ok {
		return &ValidationError{Name: "healthtimestamp", err: errors.New(`ent: missing required field "VirtualDevice.healthtimestamp"`)}
	}
	if _, ok := vdc.mutation.CreateUnix(); !ok {
		return &ValidationError{Name: "create_unix", err: errors.New(`ent: missing required field "VirtualDevice.create_unix"`)}
	}
	if _, ok := vdc.mutation.UpdateUnix(); !ok {
		return &ValidationError{Name: "update_unix", err: errors.New(`ent: missing required field "VirtualDevice.update_unix"`)}
	}
	if v, ok := vdc.mutation.ID(); ok {
		if err := virtualdevice.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "VirtualDevice.id": %w`, err)}
		}
	}
	return nil
}

func (vdc *VirtualDeviceCreate) sqlSave(ctx context.Context) (*VirtualDevice, error) {
	if err := vdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := vdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, vdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected VirtualDevice.ID type: %T", _spec.ID.Value)
		}
	}
	vdc.mutation.id = &_node.ID
	vdc.mutation.done = true
	return _node, nil
}

func (vdc *VirtualDeviceCreate) createSpec() (*VirtualDevice, *sqlgraph.CreateSpec) {
	var (
		_node = &VirtualDevice{config: vdc.config}
		_spec = sqlgraph.NewCreateSpec(virtualdevice.Table, sqlgraph.NewFieldSpec(virtualdevice.FieldID, field.TypeString))
	)
	if id, ok := vdc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := vdc.mutation.Name(); ok {
		_spec.SetField(virtualdevice.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := vdc.mutation.GetType(); ok {
		_spec.SetField(virtualdevice.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := vdc.mutation.Protocol(); ok {
		_spec.SetField(virtualdevice.FieldProtocol, field.TypeString, value)
		_node.Protocol = value
	}
	if value, ok := vdc.mutation.Config(); ok {
		_spec.SetField(virtualdevice.FieldConfig, field.TypeJSON, value)
		_node.Config = value
	}
	if value, ok := vdc.mutation.Status(); ok {
		_spec.SetField(virtualdevice.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := vdc.mutation.Healthtimestamp(); ok {
		_spec.SetField(virtualdevice.FieldHealthtimestamp, field.TypeTime, value)
		_node.Healthtimestamp = value
	}
	if value, ok := vdc.mutation.CreateUnix(); ok {
		_spec.SetField(virtualdevice.FieldCreateUnix, field.TypeTime, value)
		_node.CreateUnix = value
	}
	if value, ok := vdc.mutation.UpdateUnix(); ok {
		_spec.SetField(virtualdevice.FieldUpdateUnix, field.TypeTime, value)
		_node.UpdateUnix = value
	}
	return _node, _spec
}

// VirtualDeviceCreateBulk is the builder for creating many VirtualDevice entities in bulk.
type VirtualDeviceCreateBulk struct {
	config
	err      error
	builders []*VirtualDeviceCreate
}

// Save creates the VirtualDevice entities in the database.
func (vdcb *VirtualDeviceCreateBulk) Save(ctx context.Context) ([]*VirtualDevice, error) {
	if vdcb.err != nil {
		return nil, vdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(vdcb.builders))
	nodes := make([]*VirtualDevice, len(vdcb.builders))
	mutators := make([]Mutator, len(vdcb.builders))
	for i := range vdcb.builders {
		func(i int, root context.Context) {
			builder := vdcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*VirtualDeviceMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, vdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, vdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, vdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (vdcb *VirtualDeviceCreateBulk) SaveX(ctx context.Context) []*VirtualDevice {
	v, err := vdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (vdcb *VirtualDeviceCreateBulk) Exec(ctx context.Context) error {
	_, err := vdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (vdcb *VirtualDeviceCreateBulk) ExecX(ctx context.Context) {
	if err := vdcb.Exec(ctx); err != nil {
		panic(err)
	}
}

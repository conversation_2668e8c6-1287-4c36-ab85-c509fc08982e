// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// VirtualDeviceDelete is the builder for deleting a VirtualDevice entity.
type VirtualDeviceDelete struct {
	config
	hooks    []Hook
	mutation *VirtualDeviceMutation
}

// Where appends a list predicates to the VirtualDeviceDelete builder.
func (vdd *VirtualDeviceDelete) Where(ps ...predicate.VirtualDevice) *VirtualDeviceDelete {
	vdd.mutation.Where(ps...)
	return vdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (vdd *VirtualDeviceDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, vdd.sqlExec, vdd.mutation, vdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (vdd *VirtualDeviceDelete) ExecX(ctx context.Context) int {
	n, err := vdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (vdd *VirtualDeviceDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(virtualdevice.Table, sqlgraph.NewFieldSpec(virtualdevice.FieldID, field.TypeString))
	if ps := vdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, vdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	vdd.mutation.done = true
	return affected, err
}

// VirtualDeviceDeleteOne is the builder for deleting a single VirtualDevice entity.
type VirtualDeviceDeleteOne struct {
	vdd *VirtualDeviceDelete
}

// Where appends a list predicates to the VirtualDeviceDelete builder.
func (vddo *VirtualDeviceDeleteOne) Where(ps ...predicate.VirtualDevice) *VirtualDeviceDeleteOne {
	vddo.vdd.mutation.Where(ps...)
	return vddo
}

// Exec executes the deletion query.
func (vddo *VirtualDeviceDeleteOne) Exec(ctx context.Context) error {
	n, err := vddo.vdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{virtualdevice.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (vddo *VirtualDeviceDeleteOne) ExecX(ctx context.Context) {
	if err := vddo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// VirtualDeviceQuery is the builder for querying VirtualDevice entities.
type VirtualDeviceQuery struct {
	config
	ctx        *QueryContext
	order      []virtualdevice.OrderOption
	inters     []Interceptor
	predicates []predicate.VirtualDevice
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the VirtualDeviceQuery builder.
func (vdq *VirtualDeviceQuery) Where(ps ...predicate.VirtualDevice) *VirtualDeviceQuery {
	vdq.predicates = append(vdq.predicates, ps...)
	return vdq
}

// Limit the number of records to be returned by this query.
func (vdq *VirtualDeviceQuery) Limit(limit int) *VirtualDeviceQuery {
	vdq.ctx.Limit = &limit
	return vdq
}

// Offset to start from.
func (vdq *VirtualDeviceQuery) Offset(offset int) *VirtualDeviceQuery {
	vdq.ctx.Offset = &offset
	return vdq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (vdq *VirtualDeviceQuery) Unique(unique bool) *VirtualDeviceQuery {
	vdq.ctx.Unique = &unique
	return vdq
}

// Order specifies how the records should be ordered.
func (vdq *VirtualDeviceQuery) Order(o ...virtualdevice.OrderOption) *VirtualDeviceQuery {
	vdq.order = append(vdq.order, o...)
	return vdq
}

// First returns the first VirtualDevice entity from the query.
// Returns a *NotFoundError when no VirtualDevice was found.
func (vdq *VirtualDeviceQuery) First(ctx context.Context) (*VirtualDevice, error) {
	nodes, err := vdq.Limit(1).All(setContextOp(ctx, vdq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{virtualdevice.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) FirstX(ctx context.Context) *VirtualDevice {
	node, err := vdq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first VirtualDevice ID from the query.
// Returns a *NotFoundError when no VirtualDevice ID was found.
func (vdq *VirtualDeviceQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = vdq.Limit(1).IDs(setContextOp(ctx, vdq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{virtualdevice.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) FirstIDX(ctx context.Context) string {
	id, err := vdq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single VirtualDevice entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one VirtualDevice entity is found.
// Returns a *NotFoundError when no VirtualDevice entities are found.
func (vdq *VirtualDeviceQuery) Only(ctx context.Context) (*VirtualDevice, error) {
	nodes, err := vdq.Limit(2).All(setContextOp(ctx, vdq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{virtualdevice.Label}
	default:
		return nil, &NotSingularError{virtualdevice.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) OnlyX(ctx context.Context) *VirtualDevice {
	node, err := vdq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only VirtualDevice ID in the query.
// Returns a *NotSingularError when more than one VirtualDevice ID is found.
// Returns a *NotFoundError when no entities are found.
func (vdq *VirtualDeviceQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = vdq.Limit(2).IDs(setContextOp(ctx, vdq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{virtualdevice.Label}
	default:
		err = &NotSingularError{virtualdevice.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) OnlyIDX(ctx context.Context) string {
	id, err := vdq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of VirtualDevices.
func (vdq *VirtualDeviceQuery) All(ctx context.Context) ([]*VirtualDevice, error) {
	ctx = setContextOp(ctx, vdq.ctx, ent.OpQueryAll)
	if err := vdq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*VirtualDevice, *VirtualDeviceQuery]()
	return withInterceptors[[]*VirtualDevice](ctx, vdq, qr, vdq.inters)
}

// AllX is like All, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) AllX(ctx context.Context) []*VirtualDevice {
	nodes, err := vdq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of VirtualDevice IDs.
func (vdq *VirtualDeviceQuery) IDs(ctx context.Context) (ids []string, err error) {
	if vdq.ctx.Unique == nil && vdq.path != nil {
		vdq.Unique(true)
	}
	ctx = setContextOp(ctx, vdq.ctx, ent.OpQueryIDs)
	if err = vdq.Select(virtualdevice.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) IDsX(ctx context.Context) []string {
	ids, err := vdq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (vdq *VirtualDeviceQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, vdq.ctx, ent.OpQueryCount)
	if err := vdq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, vdq, querierCount[*VirtualDeviceQuery](), vdq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) CountX(ctx context.Context) int {
	count, err := vdq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (vdq *VirtualDeviceQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, vdq.ctx, ent.OpQueryExist)
	switch _, err := vdq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (vdq *VirtualDeviceQuery) ExistX(ctx context.Context) bool {
	exist, err := vdq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the VirtualDeviceQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (vdq *VirtualDeviceQuery) Clone() *VirtualDeviceQuery {
	if vdq == nil {
		return nil
	}
	return &VirtualDeviceQuery{
		config:     vdq.config,
		ctx:        vdq.ctx.Clone(),
		order:      append([]virtualdevice.OrderOption{}, vdq.order...),
		inters:     append([]Interceptor{}, vdq.inters...),
		predicates: append([]predicate.VirtualDevice{}, vdq.predicates...),
		// clone intermediate query.
		sql:  vdq.sql.Clone(),
		path: vdq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.VirtualDevice.Query().
//		GroupBy(virtualdevice.FieldName).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (vdq *VirtualDeviceQuery) GroupBy(field string, fields ...string) *VirtualDeviceGroupBy {
	vdq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &VirtualDeviceGroupBy{build: vdq}
	grbuild.flds = &vdq.ctx.Fields
	grbuild.label = virtualdevice.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//	}
//
//	client.VirtualDevice.Query().
//		Select(virtualdevice.FieldName).
//		Scan(ctx, &v)
func (vdq *VirtualDeviceQuery) Select(fields ...string) *VirtualDeviceSelect {
	vdq.ctx.Fields = append(vdq.ctx.Fields, fields...)
	sbuild := &VirtualDeviceSelect{VirtualDeviceQuery: vdq}
	sbuild.label = virtualdevice.Label
	sbuild.flds, sbuild.scan = &vdq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a VirtualDeviceSelect configured with the given aggregations.
func (vdq *VirtualDeviceQuery) Aggregate(fns ...AggregateFunc) *VirtualDeviceSelect {
	return vdq.Select().Aggregate(fns...)
}

func (vdq *VirtualDeviceQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range vdq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, vdq); err != nil {
				return err
			}
		}
	}
	for _, f := range vdq.ctx.Fields {
		if !virtualdevice.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if vdq.path != nil {
		prev, err := vdq.path(ctx)
		if err != nil {
			return err
		}
		vdq.sql = prev
	}
	return nil
}

func (vdq *VirtualDeviceQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*VirtualDevice, error) {
	var (
		nodes = []*VirtualDevice{}
		_spec = vdq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*VirtualDevice).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &VirtualDevice{config: vdq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, vdq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (vdq *VirtualDeviceQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := vdq.querySpec()
	_spec.Node.Columns = vdq.ctx.Fields
	if len(vdq.ctx.Fields) > 0 {
		_spec.Unique = vdq.ctx.Unique != nil && *vdq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, vdq.driver, _spec)
}

func (vdq *VirtualDeviceQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(virtualdevice.Table, virtualdevice.Columns, sqlgraph.NewFieldSpec(virtualdevice.FieldID, field.TypeString))
	_spec.From = vdq.sql
	if unique := vdq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if vdq.path != nil {
		_spec.Unique = true
	}
	if fields := vdq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, virtualdevice.FieldID)
		for i := range fields {
			if fields[i] != virtualdevice.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := vdq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := vdq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := vdq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := vdq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (vdq *VirtualDeviceQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(vdq.driver.Dialect())
	t1 := builder.Table(virtualdevice.Table)
	columns := vdq.ctx.Fields
	if len(columns) == 0 {
		columns = virtualdevice.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if vdq.sql != nil {
		selector = vdq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if vdq.ctx.Unique != nil && *vdq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range vdq.predicates {
		p(selector)
	}
	for _, p := range vdq.order {
		p(selector)
	}
	if offset := vdq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := vdq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// VirtualDeviceGroupBy is the group-by builder for VirtualDevice entities.
type VirtualDeviceGroupBy struct {
	selector
	build *VirtualDeviceQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (vdgb *VirtualDeviceGroupBy) Aggregate(fns ...AggregateFunc) *VirtualDeviceGroupBy {
	vdgb.fns = append(vdgb.fns, fns...)
	return vdgb
}

// Scan applies the selector query and scans the result into the given value.
func (vdgb *VirtualDeviceGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, vdgb.build.ctx, ent.OpQueryGroupBy)
	if err := vdgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*VirtualDeviceQuery, *VirtualDeviceGroupBy](ctx, vdgb.build, vdgb, vdgb.build.inters, v)
}

func (vdgb *VirtualDeviceGroupBy) sqlScan(ctx context.Context, root *VirtualDeviceQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(vdgb.fns))
	for _, fn := range vdgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*vdgb.flds)+len(vdgb.fns))
		for _, f := range *vdgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*vdgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := vdgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// VirtualDeviceSelect is the builder for selecting fields of VirtualDevice entities.
type VirtualDeviceSelect struct {
	*VirtualDeviceQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (vds *VirtualDeviceSelect) Aggregate(fns ...AggregateFunc) *VirtualDeviceSelect {
	vds.fns = append(vds.fns, fns...)
	return vds
}

// Scan applies the selector query and scans the result into the given value.
func (vds *VirtualDeviceSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, vds.ctx, ent.OpQuerySelect)
	if err := vds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*VirtualDeviceQuery, *VirtualDeviceSelect](ctx, vds.VirtualDeviceQuery, vds, vds.inters, v)
}

func (vds *VirtualDeviceSelect) sqlScan(ctx context.Context, root *VirtualDeviceQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(vds.fns))
	for _, fn := range vds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*vds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := vds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

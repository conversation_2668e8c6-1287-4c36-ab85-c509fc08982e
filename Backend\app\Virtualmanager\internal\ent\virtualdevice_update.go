// Code generated by ent, DO NOT EDIT.

package ent

import (
	"GCF/app/Virtualmanager/internal/ent/predicate"
	"GCF/app/Virtualmanager/internal/ent/schema"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// VirtualDeviceUpdate is the builder for updating VirtualDevice entities.
type VirtualDeviceUpdate struct {
	config
	hooks    []Hook
	mutation *VirtualDeviceMutation
}

// Where appends a list predicates to the VirtualDeviceUpdate builder.
func (vdu *VirtualDeviceUpdate) Where(ps ...predicate.VirtualDevice) *VirtualDeviceUpdate {
	vdu.mutation.Where(ps...)
	return vdu
}

// SetName sets the "name" field.
func (vdu *VirtualDeviceUpdate) SetName(s string) *VirtualDeviceUpdate {
	vdu.mutation.SetName(s)
	return vdu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (vdu *VirtualDeviceUpdate) SetNillableName(s *string) *VirtualDeviceUpdate {
	if s != nil {
		vdu.SetName(*s)
	}
	return vdu
}

// SetType sets the "type" field.
func (vdu *VirtualDeviceUpdate) SetType(s string) *VirtualDeviceUpdate {
	vdu.mutation.SetType(s)
	return vdu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (vdu *VirtualDeviceUpdate) SetNillableType(s *string) *VirtualDeviceUpdate {
	if s != nil {
		vdu.SetType(*s)
	}
	return vdu
}

// SetProtocol sets the "protocol" field.
func (vdu *VirtualDeviceUpdate) SetProtocol(s string) *VirtualDeviceUpdate {
	vdu.mutation.SetProtocol(s)
	return vdu
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (vdu *VirtualDeviceUpdate) SetNillableProtocol(s *string) *VirtualDeviceUpdate {
	if s != nil {
		vdu.SetProtocol(*s)
	}
	return vdu
}

// SetConfig sets the "config" field.
func (vdu *VirtualDeviceUpdate) SetConfig(sc []schema.DeviceConfig) *VirtualDeviceUpdate {
	vdu.mutation.SetConfig(sc)
	return vdu
}

// AppendConfig appends sc to the "config" field.
func (vdu *VirtualDeviceUpdate) AppendConfig(sc []schema.DeviceConfig) *VirtualDeviceUpdate {
	vdu.mutation.AppendConfig(sc)
	return vdu
}

// SetStatus sets the "status" field.
func (vdu *VirtualDeviceUpdate) SetStatus(s string) *VirtualDeviceUpdate {
	vdu.mutation.SetStatus(s)
	return vdu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (vdu *VirtualDeviceUpdate) SetNillableStatus(s *string) *VirtualDeviceUpdate {
	if s != nil {
		vdu.SetStatus(*s)
	}
	return vdu
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (vdu *VirtualDeviceUpdate) SetHealthtimestamp(t time.Time) *VirtualDeviceUpdate {
	vdu.mutation.SetHealthtimestamp(t)
	return vdu
}

// SetNillableHealthtimestamp sets the "healthtimestamp" field if the given value is not nil.
func (vdu *VirtualDeviceUpdate) SetNillableHealthtimestamp(t *time.Time) *VirtualDeviceUpdate {
	if t != nil {
		vdu.SetHealthtimestamp(*t)
	}
	return vdu
}

// SetUpdateUnix sets the "update_unix" field.
func (vdu *VirtualDeviceUpdate) SetUpdateUnix(t time.Time) *VirtualDeviceUpdate {
	vdu.mutation.SetUpdateUnix(t)
	return vdu
}

// Mutation returns the VirtualDeviceMutation object of the builder.
func (vdu *VirtualDeviceUpdate) Mutation() *VirtualDeviceMutation {
	return vdu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (vdu *VirtualDeviceUpdate) Save(ctx context.Context) (int, error) {
	vdu.defaults()
	return withHooks(ctx, vdu.sqlSave, vdu.mutation, vdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (vdu *VirtualDeviceUpdate) SaveX(ctx context.Context) int {
	affected, err := vdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (vdu *VirtualDeviceUpdate) Exec(ctx context.Context) error {
	_, err := vdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (vdu *VirtualDeviceUpdate) ExecX(ctx context.Context) {
	if err := vdu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (vdu *VirtualDeviceUpdate) defaults() {
	if _, ok := vdu.mutation.UpdateUnix(); !ok {
		v := virtualdevice.UpdateDefaultUpdateUnix()
		vdu.mutation.SetUpdateUnix(v)
	}
}

func (vdu *VirtualDeviceUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(virtualdevice.Table, virtualdevice.Columns, sqlgraph.NewFieldSpec(virtualdevice.FieldID, field.TypeString))
	if ps := vdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := vdu.mutation.Name(); ok {
		_spec.SetField(virtualdevice.FieldName, field.TypeString, value)
	}
	if value, ok := vdu.mutation.GetType(); ok {
		_spec.SetField(virtualdevice.FieldType, field.TypeString, value)
	}
	if value, ok := vdu.mutation.Protocol(); ok {
		_spec.SetField(virtualdevice.FieldProtocol, field.TypeString, value)
	}
	if value, ok := vdu.mutation.Config(); ok {
		_spec.SetField(virtualdevice.FieldConfig, field.TypeJSON, value)
	}
	if value, ok := vdu.mutation.AppendedConfig(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, virtualdevice.FieldConfig, value)
		})
	}
	if value, ok := vdu.mutation.Status(); ok {
		_spec.SetField(virtualdevice.FieldStatus, field.TypeString, value)
	}
	if value, ok := vdu.mutation.Healthtimestamp(); ok {
		_spec.SetField(virtualdevice.FieldHealthtimestamp, field.TypeTime, value)
	}
	if value, ok := vdu.mutation.UpdateUnix(); ok {
		_spec.SetField(virtualdevice.FieldUpdateUnix, field.TypeTime, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, vdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{virtualdevice.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	vdu.mutation.done = true
	return n, nil
}

// VirtualDeviceUpdateOne is the builder for updating a single VirtualDevice entity.
type VirtualDeviceUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *VirtualDeviceMutation
}

// SetName sets the "name" field.
func (vduo *VirtualDeviceUpdateOne) SetName(s string) *VirtualDeviceUpdateOne {
	vduo.mutation.SetName(s)
	return vduo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (vduo *VirtualDeviceUpdateOne) SetNillableName(s *string) *VirtualDeviceUpdateOne {
	if s != nil {
		vduo.SetName(*s)
	}
	return vduo
}

// SetType sets the "type" field.
func (vduo *VirtualDeviceUpdateOne) SetType(s string) *VirtualDeviceUpdateOne {
	vduo.mutation.SetType(s)
	return vduo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (vduo *VirtualDeviceUpdateOne) SetNillableType(s *string) *VirtualDeviceUpdateOne {
	if s != nil {
		vduo.SetType(*s)
	}
	return vduo
}

// SetProtocol sets the "protocol" field.
func (vduo *VirtualDeviceUpdateOne) SetProtocol(s string) *VirtualDeviceUpdateOne {
	vduo.mutation.SetProtocol(s)
	return vduo
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (vduo *VirtualDeviceUpdateOne) SetNillableProtocol(s *string) *VirtualDeviceUpdateOne {
	if s != nil {
		vduo.SetProtocol(*s)
	}
	return vduo
}

// SetConfig sets the "config" field.
func (vduo *VirtualDeviceUpdateOne) SetConfig(sc []schema.DeviceConfig) *VirtualDeviceUpdateOne {
	vduo.mutation.SetConfig(sc)
	return vduo
}

// AppendConfig appends sc to the "config" field.
func (vduo *VirtualDeviceUpdateOne) AppendConfig(sc []schema.DeviceConfig) *VirtualDeviceUpdateOne {
	vduo.mutation.AppendConfig(sc)
	return vduo
}

// SetStatus sets the "status" field.
func (vduo *VirtualDeviceUpdateOne) SetStatus(s string) *VirtualDeviceUpdateOne {
	vduo.mutation.SetStatus(s)
	return vduo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (vduo *VirtualDeviceUpdateOne) SetNillableStatus(s *string) *VirtualDeviceUpdateOne {
	if s != nil {
		vduo.SetStatus(*s)
	}
	return vduo
}

// SetHealthtimestamp sets the "healthtimestamp" field.
func (vduo *VirtualDeviceUpdateOne) SetHealthtimestamp(t time.Time) *VirtualDeviceUpdateOne {
	vduo.mutation.SetHealthtimestamp(t)
	return vduo
}

// SetNillableHealthtimestamp sets the "healthtimestamp" field if the given value is not nil.
func (vduo *VirtualDeviceUpdateOne) SetNillableHealthtimestamp(t *time.Time) *VirtualDeviceUpdateOne {
	if t != nil {
		vduo.SetHealthtimestamp(*t)
	}
	return vduo
}

// SetUpdateUnix sets the "update_unix" field.
func (vduo *VirtualDeviceUpdateOne) SetUpdateUnix(t time.Time) *VirtualDeviceUpdateOne {
	vduo.mutation.SetUpdateUnix(t)
	return vduo
}

// Mutation returns the VirtualDeviceMutation object of the builder.
func (vduo *VirtualDeviceUpdateOne) Mutation() *VirtualDeviceMutation {
	return vduo.mutation
}

// Where appends a list predicates to the VirtualDeviceUpdate builder.
func (vduo *VirtualDeviceUpdateOne) Where(ps ...predicate.VirtualDevice) *VirtualDeviceUpdateOne {
	vduo.mutation.Where(ps...)
	return vduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (vduo *VirtualDeviceUpdateOne) Select(field string, fields ...string) *VirtualDeviceUpdateOne {
	vduo.fields = append([]string{field}, fields...)
	return vduo
}

// Save executes the query and returns the updated VirtualDevice entity.
func (vduo *VirtualDeviceUpdateOne) Save(ctx context.Context) (*VirtualDevice, error) {
	vduo.defaults()
	return withHooks(ctx, vduo.sqlSave, vduo.mutation, vduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (vduo *VirtualDeviceUpdateOne) SaveX(ctx context.Context) *VirtualDevice {
	node, err := vduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (vduo *VirtualDeviceUpdateOne) Exec(ctx context.Context) error {
	_, err := vduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (vduo *VirtualDeviceUpdateOne) ExecX(ctx context.Context) {
	if err := vduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (vduo *VirtualDeviceUpdateOne) defaults() {
	if _, ok := vduo.mutation.UpdateUnix(); !ok {
		v := virtualdevice.UpdateDefaultUpdateUnix()
		vduo.mutation.SetUpdateUnix(v)
	}
}

func (vduo *VirtualDeviceUpdateOne) sqlSave(ctx context.Context) (_node *VirtualDevice, err error) {
	_spec := sqlgraph.NewUpdateSpec(virtualdevice.Table, virtualdevice.Columns, sqlgraph.NewFieldSpec(virtualdevice.FieldID, field.TypeString))
	id, ok := vduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "VirtualDevice.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := vduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, virtualdevice.FieldID)
		for _, f := range fields {
			if !virtualdevice.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != virtualdevice.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := vduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := vduo.mutation.Name(); ok {
		_spec.SetField(virtualdevice.FieldName, field.TypeString, value)
	}
	if value, ok := vduo.mutation.GetType(); ok {
		_spec.SetField(virtualdevice.FieldType, field.TypeString, value)
	}
	if value, ok := vduo.mutation.Protocol(); ok {
		_spec.SetField(virtualdevice.FieldProtocol, field.TypeString, value)
	}
	if value, ok := vduo.mutation.Config(); ok {
		_spec.SetField(virtualdevice.FieldConfig, field.TypeJSON, value)
	}
	if value, ok := vduo.mutation.AppendedConfig(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, virtualdevice.FieldConfig, value)
		})
	}
	if value, ok := vduo.mutation.Status(); ok {
		_spec.SetField(virtualdevice.FieldStatus, field.TypeString, value)
	}
	if value, ok := vduo.mutation.Healthtimestamp(); ok {
		_spec.SetField(virtualdevice.FieldHealthtimestamp, field.TypeTime, value)
	}
	if value, ok := vduo.mutation.UpdateUnix(); ok {
		_spec.SetField(virtualdevice.FieldUpdateUnix, field.TypeTime, value)
	}
	_node = &VirtualDevice{config: vduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, vduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{virtualdevice.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	vduo.mutation.done = true
	return _node, nil
}

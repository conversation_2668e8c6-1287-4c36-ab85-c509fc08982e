package handler

import (
	"net/http"

	"GCF/app/Virtualmanager/internal/logic"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func ListAvailableSimulationsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ListAvailableSimulationsRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewListAvailableSimulationsLogic(r.Context(), svcCtx)
		resp, err := l.ListAvailableSimulations(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

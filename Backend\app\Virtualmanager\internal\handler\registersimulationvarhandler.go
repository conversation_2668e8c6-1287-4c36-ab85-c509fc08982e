package handler

import (
	"net/http"

	"GCF/app/Virtualmanager/internal/logic"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func RegisterSimulationVarHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RegisterSimulationVarRequest
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := logic.NewRegisterSimulationVarLogic(r.Context(), svcCtx)
		resp, err := l.RegisterSimulationVar(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}

// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.2

package handler

import (
	"net/http"

	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/config/get",
				Handler: GetDeviceConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/config/update",
				Handler: UpdateDeviceConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/create",
				Handler: CreateDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/delete",
				Handler: DeleteDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/health",
				Handler: GetDevice<PERSON>ealth<PERSON>and<PERSON>(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/list",
				Handler: ListDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/log",
				Handler: GetDeviceLogHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/close",
				Handler: CloseSimulationHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/continuous",
				Handler: ControlContinuousHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/create",
				Handler: CreateSimulationHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/getinfo",
				Handler: GetSimulationInfoHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/list",
				Handler: ListAvailableSimulationsHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/output",
				Handler: GetSimoutHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/register/control",
				Handler: RegisterSimulationParamHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/register/outvar",
				Handler: RegisterSimulationVarHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/save",
				Handler: SaveSimResultHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/start",
				Handler: StartSimulationHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/simulation/step",
				Handler: ControlStepHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/api/v1/virtualdevice/status",
				Handler: GetDeviceStatusHandler(serverCtx),
			},
		},
	)
}

package logic

import (
	"context"
	"fmt"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type CloseSimulationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCloseSimulationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CloseSimulationLogic {
	return &CloseSimulationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CloseSimulationLogic) CloseSimulation(req *types.CloseSimulationRequest) (resp *types.CloseSimulationResponse, err error) {
	simulink_info, err := l.svcCtx.SimulinkRpc.CloseSimulink(l.ctx, &virtualclient.CloseSimulinkRequest{
		SimulinkId: req.SimulateID,
	})
	if err != nil {
		return nil, err
	}
	// 存储控制参数列表
	var controlParams []types.ControlParam
	for _, param := range simulink_info.SimulinkInfo.ControlParams {
		controlParams = append(controlParams, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}

	// 存储输出变量列表
	var outputVars []types.OutputVar
	for _, output := range simulink_info.SimulinkInfo.OutputVars {
		outputVars = append(outputVars, types.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		})
	}
	return &types.CloseSimulationResponse{
		Info: types.SimulateInfo{
			SimulateID:    simulink_info.SimulinkInfo.SimulinkId,
			SimulateType:  simulink_info.SimulinkInfo.SimulinkType,
			SimulateTime:  fmt.Sprintf("%v", simulink_info.SimulinkInfo.SimulinkTime),
			Status:        simulink_info.SimulinkInfo.SimulinkStatus,
			ControlParams: controlParams,
			OutputVars:    outputVars,
		},
	}, nil
}

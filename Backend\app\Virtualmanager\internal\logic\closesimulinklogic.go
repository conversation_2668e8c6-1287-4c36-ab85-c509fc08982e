package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CloseSimulinkLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCloseSimulinkLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CloseSimulinkLogic {
	return &CloseSimulinkLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 关闭模型
func (l *CloseSimulinkLogic) CloseSimulink(in *simulinkmanager.CloseSimulinkRequest) (*simulinkmanager.CloseSimulinkResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.CloseSimulinkResponse{}, nil
}

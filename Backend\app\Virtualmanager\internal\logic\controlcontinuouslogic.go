package logic

import (
	"context"
	"fmt"
	"strconv"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type ControlContinuousLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewControlContinuousLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ControlContinuousLogic {
	return &ControlContinuousLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ControlContinuousLogic) ControlContinuous(req *types.ControlContinuousRequest) (resp *types.ControlContinuousResponse, err error) {
	simulateTime, err := strconv.ParseFloat(req.SimulateTime, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid simulate time: %v", err)
	}
	// 构建请求
	request := &virtualclient.RunSimulationRequest{
		SimulinkId: req.SimulateID,
		StartTime:  0,
		StopTime:   float32(simulateTime),
		Params:     []*virtualclient.ControlParamValue{},
	}
	for _, param := range req.Params {
		paramValue := &virtualclient.ControlParamValue{
			ParamName: param.ParamName,
			Timestamp: param.Timestamp,
			Value:     param.Value,
		}
		request.Params = append(request.Params, paramValue)
	}
	run_simu, err := l.svcCtx.SimulinkRpc.RunSimulation(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var controlParam []types.ControlParam
	for _, param := range run_simu.SimulinkInfo.ControlParams {
		controlParam = append(controlParam, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}

	// 存储输出变量列表
	var outputVars []types.OutputVar
	for _, output := range run_simu.SimulinkInfo.OutputVars {
		outputVars = append(outputVars, types.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		})
	}
	var OutputVarValues []types.OutputVarValue
	for _, output := range run_simu.SimulinkRuntime.OutputVars {
		OutputVarValues = append(OutputVarValues, types.OutputVarValue{
			VarName:   output.VarName,
			Timestamp: output.Timestamp,
			Value:     output.Value,
		})
	}
	// 创建返回结构体
	resp = &types.ControlContinuousResponse{
		Success: true,
		Message: "Control done",
		Info: types.SimulateInfo{
			SimulateID:    run_simu.SimulinkInfo.SimulinkId,
			SimulateType:  run_simu.SimulinkInfo.SimulinkType,
			SimulateTime:  fmt.Sprintf("%v", run_simu.SimulinkInfo.SimulinkTime),
			Status:        run_simu.SimulinkInfo.SimulinkStatus,
			ControlParams: controlParam,
			OutputVars:    outputVars,
		},
		Runtime: types.SimulinkRuntime{
			Progress: fmt.Sprintf("%v", run_simu.SimulinkRuntime.Progress),
			Output:   OutputVarValues,
		},
	}
	return resp, nil
}

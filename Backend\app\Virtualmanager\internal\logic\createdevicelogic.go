package logic

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"GCF/app/Virtualmanager/internal/ent/schema"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDeviceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDeviceLogic {
	return &CreateDeviceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDeviceLogic) CreateDevice(req *types.CreateDeviceRequest) (resp *types.CreateDeviceResponse, err error) {
	// todo: 调用python的grpc服务来创建并加载新的仿真模型，并将其存入数据库;根据m文件路径来初始化仿真器
	// 创建虚拟设备逻辑
	// 打印调试信息
	// logx.Info("开始创建设备处理流程")
	// logx.Infof("接收到创建设备请求，设备名称: %s, 设备类型: %s", req.DeviceName, req.DeviceType)

	// 检查是否存在同名设备
	exist, err := l.svcCtx.Db.VirtualDevice.Query().
		Where(virtualdevice.NameEQ(req.DeviceName)).
		Exist(l.ctx)
	if err != nil {
		return nil, fmt.Errorf("查询设备失败: %s", err.Error())
	}
	if exist {
		return &types.CreateDeviceResponse{
			Success: false,
			Message: "Name already exists, please change the name",
		}, nil
	}

	uid := uuid.New().String()
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		// 处理事务的提交和回滚逻辑
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()
	configJSON, _ := json.Marshal(req.Config)
	var configs []schema.DeviceConfig
	json.Unmarshal(configJSON, &configs)
	currentTime := time.Now()
	currentTimeStr := currentTime.Format("2006-01-02 15:04:05")
	_, err = tx.VirtualDevice.Create().
		SetID(uid).
		SetName(req.DeviceName).
		SetType(req.DeviceType).
		SetProtocol("").
		SetConfig(configs).
		SetStatus("ready").
		SetCreateUnix(currentTime).
		SetUpdateUnix(currentTime).
		Save(l.ctx)
	if err != nil {
		err_rollback := tx.Rollback()
		if err_rollback != nil {
			return nil, fmt.Errorf("rollback Device failed: %s", err_rollback.Error())
		}
		return nil, fmt.Errorf("update Device failed: %s", err.Error())
	}

	//创建Frame记录
	frameUid := uuid.New().String()
	_, err = tx.Frame.Create().
		SetID(frameUid).
		SetDeviceID(uid).
		SetTime(time.Now()).
		Save(l.ctx)
	if err != nil {
		err_rollback := tx.Rollback()
		if err_rollback != nil {
			return nil, fmt.Errorf("rollback Frame failed: %s", err_rollback.Error())
		}
		return nil, fmt.Errorf("create Frame failed: %s", err.Error())
	}

	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("commit transaction failed: %s", err.Error())
	}
	// device, err := l.svcCtx.Db.VirtualDevice.Get(l.ctx, uid)
	// logx.Infof("查询刚创建的设备: %+v", device)
	return &types.CreateDeviceResponse{
		Success: true,
		Message: "Device created successfully",
		Info: types.DeviceInfo{
			DeviceID:   uid,
			DeviceName: req.DeviceName,
			DeviceType: req.DeviceType,
			Status:     "ready",
			CreateTime: currentTimeStr,
		},
		Frame: types.FrameInfo{
			FrameID:   frameUid,
			FrameTime: currentTimeStr,
		},
	}, nil
}

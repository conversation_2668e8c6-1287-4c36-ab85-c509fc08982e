package logic

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateSimulationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateSimulationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateSimulationLogic {
	return &CreateSimulationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateSimulationLogic) CreateSimulation(req *types.CreateSimulationRequest) (resp *types.CreateSimulationResponse, err error) {
	// todo: add your logic here and delete this line
	device, err := l.svcCtx.Db.VirtualDevice.Get(l.ctx, req.DeviceID)
	if err != nil {
		return nil, fmt.Errorf("device not found: %v", err)
	}
	simulateTime, err := strconv.ParseFloat(req.SimulateTime, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid simulate time format: %v", err)
	}
	device.Type = strings.ToLower(device.Type)
	request := &virtualclient.InitializeSimulinkRequest{
		SimulinkType:  device.Type,
		SimulinkDir:   req.SimulinkDir,
		SimulinkName:  req.SimulinkName,
		InitScript:    req.InitScript,
		RunTime:       float32(simulateTime),
		ControlParams: []*virtualclient.ControlParam{},
		OutputVars:    []*virtualclient.OutputVar{},
	}
	for _, param := range req.Params {
		paramValue := &virtualclient.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		}
		request.ControlParams = append(request.ControlParams, paramValue)
	}
	for _, output := range req.OutVars {
		outputValue := &virtualclient.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		}
		request.OutputVars = append(request.OutputVars, outputValue)
	}
	// logx.Infof("Request JSON: %+v", request)
	create_simu, err := l.svcCtx.SimulinkRpc.InitializeSimulink(l.ctx, request)
	logx.Infof("create_simu: %+v", create_simu)
	/// logx.Infof("error: %v", err.Error())
	logx.Infof("Successfully created simulation")
	if err != nil {
		return nil, err
	}
	// 存储控制参数列表
	var controlParams []types.ControlParam
	for _, param := range create_simu.SimulinkInfo.ControlParams {
		controlParams = append(controlParams, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}

	// 存储输出变量列表
	var outputVars []types.OutputVar
	for _, output := range create_simu.SimulinkInfo.OutputVars {
		outputVars = append(outputVars, types.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		})
	}
	resp = &types.CreateSimulationResponse{
		Message: "Successfully create simuliation",
		Info: types.SimulateInfo{
			SimulateID:    create_simu.SimulinkInfo.SimulinkId,
			SimulateType:  create_simu.SimulinkInfo.SimulinkType,
			SimulateTime:  fmt.Sprintf("%v", create_simu.SimulinkInfo.SimulinkTime),
			Status:        create_simu.SimulinkInfo.SimulinkStatus,
			ControlParams: controlParams,
			OutputVars:    outputVars,
		},
	}
	return resp, nil
}

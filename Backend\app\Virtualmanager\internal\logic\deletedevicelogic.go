package logic

import (
	"context"
	"fmt"

	"GCF/app/Virtualmanager/internal/ent/frame"
	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDeviceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDeviceLogic {
	return &DeleteDeviceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDeviceLogic) DeleteDevice(req *types.DeleteDeviceRequest) (resp *types.DeleteDeviceResponse, err error) {
	// todo: 连接python端grpc服务器，控制删除仿真模型，并在数据库中删除
	queryDevice, err := l.svcCtx.Db.VirtualDevice.Query().Where(virtualdevice.IDEQ(req.DeviceID)).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("failed to get queryDevice %s: %v", req.DeviceID, err)
		return nil, err
	}

	if queryDevice.Status == "running" {
		return nil, fmt.Errorf("设备正在运行中，无法删除")
	}

	// 开启事务删除设备及相关数据
	tx, err := l.svcCtx.Db.Tx(l.ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		if v := recover(); v != nil {
			tx.Rollback()
			panic(v)
		}
	}()

	// 先删除frame表中的设备记录
	queryFrame, err := l.svcCtx.Db.Frame.Query().Where(frame.DeviceIDEQ(req.DeviceID)).First(l.ctx)
	_, err = tx.Frame.Delete().Where(frame.DeviceIDEQ(queryDevice.ID)).Exec(l.ctx)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("删除设备关联的frame记录失败: %v", err)
	}

	// 删除设备
	err = tx.VirtualDevice.DeleteOne(queryDevice).Exec(l.ctx)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("删除设备失败: %v", err)
	}

	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}
	resp = &types.DeleteDeviceResponse{
		Success: true,
		Message: "设备删除成功",
		Info: types.DeviceInfo{
			DeviceID:   queryDevice.ID,
			DeviceName: queryDevice.Name,
			DeviceType: queryDevice.Type,
			Status:     "",
			CreateTime: queryDevice.CreateUnix.String(),
		},
		Frame: types.FrameInfo{
			FrameID:   queryFrame.ID,
			FrameTime: queryFrame.Time.String(),
		},
	}
	return resp, nil
}

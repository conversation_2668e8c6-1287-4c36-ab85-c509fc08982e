package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeviceConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeviceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceConfigLogic {
	return &GetDeviceConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDeviceConfigLogic) GetDeviceConfig(req *types.GetDeviceConfigRequest) (resp *types.GetDeviceConfigResponse, err error) {
	queryDevice, err := l.svcCtx.Db.VirtualDevice.Query().Where(virtualdevice.IDEQ(req.DeviceID)).First(l.ctx)
	if err != nil {
		return nil, err
	}

	// 将schema.DeviceConfig转换为types.DeviceConfig
	configs := make([]types.DeviceConfig, len(queryDevice.Config))
	for i, config := range queryDevice.Config {
		configs[i] = types.DeviceConfig{
			ParamName: config.ParamName,
			Value:     config.Value,
		}
	}

	resp = &types.GetDeviceConfigResponse{
		DeviceID:   req.DeviceID,
		UpdateTime: queryDevice.UpdateUnix.String(),
		Config:     configs,
	}
	return resp, nil
}

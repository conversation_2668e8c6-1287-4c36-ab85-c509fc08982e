package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeviceHealthLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeviceHealthLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceHealthLogic {
	return &GetDeviceHealthLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDeviceHealthLogic) GetDeviceHealth(req *types.GetDeviceHealthRequest) (resp *types.GetDeviceHealthResponse, err error) {
	queryDevice, err := l.svcCtx.Db.VirtualDevice.Query().Where(virtualdevice.IDEQ(req.DeviceID)).First(l.ctx)
	if err != nil {
		return nil, err
	}
	resp = &types.GetDeviceHealthResponse{
		DeviceID:     req.DeviceID,
		HealthStatus: queryDevice.Status,
		Message:      queryDevice.Healthtimestamp.String(),
	}
	return resp, nil
}

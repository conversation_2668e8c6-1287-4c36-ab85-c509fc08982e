package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/ent/devicelog"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeviceLogLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeviceLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceLogLogic {
	return &GetDeviceLogLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDeviceLogLogic) GetDeviceLog(req *types.GetDeviceLogRequest) (resp *types.GetDeviceLogResponse, err error) {
	// 连接数据库，查询设备的日志信息
	// todo: 查询前置日志
	queryDevice, err := l.svcCtx.Db.DeviceLog.Query().Where(devicelog.IDEQ(req.DeviceID)).First(l.ctx)
	if err != nil {
		return nil, err
	}
	resp = &types.GetDeviceLogResponse{
		Logs: []types.LogEntry{
			{
				Message: queryDevice.Healthlog,
			},
			{
				Message: queryDevice.Simulatelog,
			},
		},
	}
	return resp, nil
}

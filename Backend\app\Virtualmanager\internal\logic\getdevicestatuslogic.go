package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeviceStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeviceStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceStatusLogic {
	return &GetDeviceStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDeviceStatusLogic) GetDeviceStatus(req *types.GetDeviceStatusRequest) (resp *types.GetDeviceStatusResponse, err error) {
	queryDevice, err := l.svcCtx.Db.VirtualDevice.Query().Where(virtualdevice.IDEQ(req.DeviceID)).First(l.ctx)
	if err != nil {
		return nil, err
	}
	resp = &types.GetDeviceStatusResponse{
		DeviceID: req.DeviceID,
		Status:   queryDevice.Status,
		Message:  "",
	}
	return resp, nil
}

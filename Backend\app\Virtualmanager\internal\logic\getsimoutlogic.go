package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSimoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetSimoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSimoutLogic {
	return &GetSimoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSimoutLogic) GetSimout(req *types.GetSimoutRequest) (resp *types.GetSimoutResponse, err error) {
	request := &virtualclient.GetSimulationResultsRequest{
		SimulinkId: req.SimulateID,
		VarNames:   req.Vars,
	}
	simout, err := l.svcCtx.SimulinkRpc.GetSimulationResults(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var outputVar []types.OutputVarValue
	for _, output := range simout.OutputVars {
		outputVar = append(outputVar, types.OutputVarValue{
			VarName:   output.VarName,
			Timestamp: output.Timestamp,
			Value:     output.Value,
		})
	}
	return &types.GetSimoutResponse{
		Output: outputVar,
	}, nil
}

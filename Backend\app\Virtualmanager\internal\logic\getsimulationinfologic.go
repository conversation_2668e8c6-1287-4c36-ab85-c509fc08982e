package logic

import (
	"context"
	"fmt"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSimulationInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetSimulationInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSimulationInfoLogic {
	return &GetSimulationInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSimulationInfoLogic) GetSimulationInfo(req *types.GetSimulationInfoRequest) (resp *types.GetSimulationInfoResponse, err error) {
	request := &virtualclient.GetSimulinkInfoRequest{
		SimulinkId: req.SimulateID,
	}
	info, err := l.svcCtx.SimulinkRpc.GetSimulinkInfo(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var controlParam []types.ControlParam
	for _, param := range info.SimulinkInfoAndRuntime.SimulinkInfo.ControlParams {
		controlParam = append(controlParam, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}
	var outputVar []types.OutputVar
	for _, vars := range info.SimulinkInfoAndRuntime.SimulinkInfo.OutputVars {
		outputVar = append(outputVar, types.OutputVar{
			VarName:     vars.VarName,
			MatlabVar:   vars.MatlabVar,
			Description: vars.Description,
			Readable:    vars.Readable,
		})
	}
	simulateinfo := &types.SimulateInfo{
		SimulateID:    info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkId,
		SimulateType:  info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkType,
		SimulateTime:  fmt.Sprintf("%.2f", info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkTime),
		Status:        info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkStatus,
		ControlParams: controlParam,
		OutputVars:    outputVar,
	}
	var OutputVarValues []types.OutputVarValue
	for _, output := range info.SimulinkInfoAndRuntime.SimulinkRuntime.OutputVars {
		OutputVarValues = append(OutputVarValues, types.OutputVarValue{
			VarName:   output.VarName,
			Timestamp: output.Timestamp,
			Value:     output.Value,
		})
	}
	simulateruntime := &types.SimulinkRuntime{
		Progress: fmt.Sprintf("%.2f", info.SimulinkInfoAndRuntime.SimulinkRuntime.Progress),
		Output:   OutputVarValues,
	}
	resp = &types.GetSimulationInfoResponse{
		Info: types.SimulinkInfoAndRuntime{
			Info:    *simulateinfo,
			Runtime: *simulateruntime,
		},
	}
	return resp, nil
}

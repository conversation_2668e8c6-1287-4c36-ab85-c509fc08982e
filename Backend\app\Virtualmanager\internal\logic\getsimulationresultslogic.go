package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSimulationResultsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetSimulationResultsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSimulationResultsLogic {
	return &GetSimulationResultsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取仿真结果
func (l *GetSimulationResultsLogic) GetSimulationResults(in *simulinkmanager.GetSimulationResultsRequest) (*simulinkmanager.GetSimulationResultsResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.GetSimulationResultsResponse{}, nil
}

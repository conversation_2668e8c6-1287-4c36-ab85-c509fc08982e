package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSimulinkInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetSimulinkInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSimulinkInfoLogic {
	return &GetSimulinkInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取模型信息
func (l *GetSimulinkInfoLogic) GetSimulinkInfo(in *simulinkmanager.GetSimulinkInfoRequest) (*simulinkmanager.GetSimulinkInfoResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.GetSimulinkInfoResponse{}, nil
}

package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type InitializeSimulinkLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewInitializeSimulinkLogic(ctx context.Context, svcCtx *svc.ServiceContext) *InitializeSimulinkLogic {
	return &InitializeSimulinkLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 初始化模型
func (l *InitializeSimulinkLogic) InitializeSimulink(in *simulinkmanager.InitializeSimulinkRequest) (*simulinkmanager.InitializeSimulinkResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.InitializeSimulinkResponse{}, nil
}

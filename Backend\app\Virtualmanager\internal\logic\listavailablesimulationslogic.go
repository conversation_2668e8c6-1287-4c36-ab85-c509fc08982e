package logic

import (
	"context"
	"strconv"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListAvailableSimulationsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListAvailableSimulationsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListAvailableSimulationsLogic {
	return &ListAvailableSimulationsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListAvailableSimulationsLogic) ListAvailableSimulations(req *types.ListAvailableSimulationsRequest) (resp *types.ListAvailableSimulationsResponse, err error) {
	// todo: add your logic here and delete this line
	request := &virtualclient.ListAvailableSimulinksRequest{
		SimulinkId:     req.SimulateID,
		SimulinkType:   req.SimulateType,
		SimulinkStatus: req.Status,
	}
	list_simu, err := l.svcCtx.SimulinkRpc.ListAvailableSimulinks(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var Simulinks []types.SimulinkInfoAndRuntime
	for _, res := range list_simu.SimulinkInfoAndRuntime {
		var controlParam []types.ControlParam
		for _, param := range res.SimulinkInfo.ControlParams {
			controlParam = append(controlParam, types.ControlParam{
				ParamName:    param.ParamName,
				BlockPath:    param.BlockPath,
				ParamType:    param.ParamType,
				DefaultValue: param.DefaultValue,
				Description:  param.Description,
				Writable:     param.Writable,
			})
		}
		var outputVars []types.OutputVar
		for _, output := range res.SimulinkInfo.OutputVars {
			outputVars = append(outputVars, types.OutputVar{
				VarName:     output.VarName,
				MatlabVar:   output.MatlabVar,
				Description: output.Description,
				Readable:    output.Readable,
			})
		}
		var OutputVarValues []types.OutputVarValue
		for _, output := range res.SimulinkRuntime.OutputVars {
			OutputVarValues = append(OutputVarValues, types.OutputVarValue{
				VarName:   output.VarName,
				Timestamp: output.Timestamp,
				Value:     output.Value,
			})
		}
		Simulinks = append(Simulinks, types.SimulinkInfoAndRuntime{
			Info: types.SimulateInfo{
				SimulateID:    res.SimulinkInfo.SimulinkId,
				SimulateType:  res.SimulinkInfo.SimulinkType,
				SimulateTime:  strconv.FormatFloat(float64(res.SimulinkInfo.SimulinkTime), 'f', -1, 32),
				Status:        res.SimulinkInfo.SimulinkStatus,
				ControlParams: controlParam,
				OutputVars:    outputVars,
			},
			Runtime: types.SimulinkRuntime{
				Progress: strconv.FormatFloat(float64(res.SimulinkRuntime.Progress), 'f', -1, 32),
				Output:   OutputVarValues,
			},
		})
	}
	return &types.ListAvailableSimulationsResponse{
		Info: Simulinks,
	}, nil
}

package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListAvailableSimulinksLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListAvailableSimulinksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListAvailableSimulinksLogic {
	return &ListAvailableSimulinksLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取可用模型列表
func (l *ListAvailableSimulinksLogic) ListAvailableSimulinks(in *simulinkmanager.ListAvailableSimulinksRequest) (*simulinkmanager.ListAvailableSimulinksResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.ListAvailableSimulinksResponse{}, nil
}

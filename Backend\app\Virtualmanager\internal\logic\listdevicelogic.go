package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDeviceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewListDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDeviceLogic {
	return &ListDeviceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDeviceLogic) ListDevice(req *types.ListDeviceRequest) (resp *types.ListDeviceResponse, err error) {
	resp = &types.ListDeviceResponse{}

	// 构建查询条件
	query := l.svcCtx.Db.VirtualDevice.Query()
	if req.DeviceType != "" {
		query = query.Where(virtualdevice.TypeEQ(req.DeviceType))
	}

	// 查询所有设备
	devices, err := query.All(l.ctx)
	if err != nil {
		return nil, err
	}
	// 获取设备总数
	total, err := query.Count(l.ctx)
	if err != nil {
		return nil, err
	}

	// 构建返回结果
	resp = &types.ListDeviceResponse{
		Devices: make([]types.DeviceInfo, 0),
		Total:   int32(total),
		Message: "success",
	}
	// 遍历设备列表并根据DeviceInfo结构构建返回结果
	deviceList := make([]types.DeviceInfo, 0)
	for _, device := range devices {
		deviceInfo := types.DeviceInfo{
			DeviceID:   device.ID,
			DeviceName: device.Name,
			DeviceType: device.Type,
			Status:     device.Status,
			CreateTime: device.CreateUnix.String(),
		}
		deviceList = append(deviceList, deviceInfo)
	}
	resp.Devices = deviceList
	return resp, nil

	// deviceList1 := make([]types.DeviceInfo, 0)
	// for _, device := range devices {
	// 	//只是一个示例，注意这个rpc逻辑是返回全部可以用的设备，实际的逻辑调用要修改
	// 	simulink_info_and_runtime, err := l.svcCtx.SimulinkRpc.ListAvailableSimulinks(l.ctx, &virtualclient.ListAvailableSimulinksRequest{
	// 		SimulinkId:     device.ID,
	// 		SimulinkType:   device.Type,
	// 		SimulinkStatus: device.Status,
	// 	})
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	deviceList1 = append(deviceList1, types.DeviceInfo{
	// 		DeviceID:   simulink_info_and_runtime.SimulinkInfoAndRuntime[0].SimulinkInfo.SimulinkId, //使用例子
	// 		DeviceName: device.Name,
	// 		DeviceType: device.Type,
	// 		Status:     device.Status,
	// 	})
	// }
	// return resp, nil
}

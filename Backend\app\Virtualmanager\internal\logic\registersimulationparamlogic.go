package logic

import (
	"context"
	"fmt"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterSimulationParamLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRegisterSimulationParamLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterSimulationParamLogic {
	return &RegisterSimulationParamLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RegisterSimulationParamLogic) RegisterSimulationParam(req *types.RegisterSimulationParamRequest) (resp *types.RegisterSimulationParamResponse, err error) {
	request := &virtualclient.ResgisterSimulinkParamRequest{
		SimulinkId:    req.SimulateID,
		ControlParams: []*virtualclient.ControlParam{},
	}
	for _, param := range req.Params {
		request.ControlParams = append(request.ControlParams, &virtualclient.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}
	register, err := l.svcCtx.SimulinkRpc.RegisterSimulinkParam(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var controlParam []types.ControlParam
	for _, param := range register.SimulinkInfo.ControlParams {
		controlParam = append(controlParam, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}
	var OutputVars []types.OutputVar
	for _, output := range register.SimulinkInfo.OutputVars {
		OutputVars = append(OutputVars, types.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		})
	}
	return &types.RegisterSimulationParamResponse{
		Info: types.SimulateInfo{
			SimulateID:    register.SimulinkInfo.SimulinkId,
			SimulateType:  register.SimulinkInfo.SimulinkType,
			SimulateTime:  fmt.Sprintf("%f", register.SimulinkInfo.SimulinkTime),
			Status:        register.SimulinkInfo.SimulinkStatus,
			ControlParams: controlParam,
			OutputVars:    OutputVars,
		},
	}, nil
}

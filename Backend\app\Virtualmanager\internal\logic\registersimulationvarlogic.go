package logic

import (
	"context"
	"fmt"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterSimulationVarLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRegisterSimulationVarLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterSimulationVarLogic {
	return &RegisterSimulationVarLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RegisterSimulationVarLogic) RegisterSimulationVar(req *types.RegisterSimulationVarRequest) (resp *types.RegisterSimulationVarResponse, err error) {
	request := &virtualclient.RegisterSimulinkVarRequest{
		SimulinkId: req.SimulateID,
		OutputVars: []*virtualclient.OutputVar{},
	}
	for _, param := range req.Output {
		request.OutputVars = append(request.OutputVars, &virtualclient.OutputVar{
			VarName:     param.VarName,
			MatlabVar:   param.MatlabVar,
			Description: param.Description,
			Readable:    param.Readable,
		})
	}
	register, err := l.svcCtx.SimulinkRpc.RegisterSimulinkVar(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var controlParam []types.ControlParam
	for _, param := range register.SimulinkInfo.ControlParams {
		controlParam = append(controlParam, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}
	var OutputVars []types.OutputVar
	for _, output := range register.SimulinkInfo.OutputVars {
		OutputVars = append(OutputVars, types.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		})
	}
	return &types.RegisterSimulationVarResponse{
		Info: types.SimulateInfo{
			SimulateID:    register.SimulinkInfo.SimulinkId,
			SimulateType:  register.SimulinkInfo.SimulinkType,
			SimulateTime:  fmt.Sprintf("%f", register.SimulinkInfo.SimulinkTime),
			Status:        register.SimulinkInfo.SimulinkStatus,
			ControlParams: controlParam,
			OutputVars:    OutputVars,
		},
	}, nil
}

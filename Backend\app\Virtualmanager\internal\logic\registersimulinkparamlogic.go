package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterSimulinkParamLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRegisterSimulinkParamLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterSimulinkParamLogic {
	return &RegisterSimulinkParamLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 注册控制参数
func (l *RegisterSimulinkParamLogic) RegisterSimulinkParam(in *simulinkmanager.ResgisterSimulinkParamRequest) (*simulinkmanager.RegisterSimulinkParamResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.RegisterSimulinkParamResponse{}, nil
}

package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterSimulinkVarLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRegisterSimulinkVarLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterSimulinkVarLogic {
	return &RegisterSimulinkVarLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 注册输出参数
func (l *RegisterSimulinkVarLogic) RegisterSimulinkVar(in *simulinkmanager.RegisterSimulinkVarRequest) (*simulinkmanager.RegisterSimulinkVarResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.RegisterSimulinkVarResponse{}, nil
}

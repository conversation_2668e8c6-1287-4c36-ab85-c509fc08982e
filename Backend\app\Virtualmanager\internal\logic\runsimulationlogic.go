package logic

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type RunSimulationLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRunSimulationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RunSimulationLogic {
	return &RunSimulationLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 运行仿真
func (l *RunSimulationLogic) RunSimulation(in *simulinkmanager.RunSimulationRequest) (*simulinkmanager.RunSimulationResponse, error) {
	// todo: add your logic here and delete this line

	return &simulinkmanager.RunSimulationResponse{}, nil
}

package logic

import (
	"context"
	"fmt"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveSimResultLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSaveSimResultLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveSimResultLogic {
	return &SaveSimResultLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SaveSimResultLogic) SaveSimResult(req *types.SaveSimResultRequest) (resp *types.SaveSimResultResponse, err error) {
	request := &virtualclient.GetSimulationResultsRequest{
		SimulinkId: req.SimulateID,
		VarNames:   nil,
	}
	simout, err := l.svcCtx.SimulinkRpc.GetSimulationResults(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var outputVar []types.OutputVarValue
	for _, output := range simout.OutputVars {
		outputVar = append(outputVar, types.OutputVarValue{
			VarName:   output.VarName,
			Timestamp: output.Timestamp,
			Value:     output.Value,
		})
	}
	// todo:将仿真信息保存到数据库？
	request1 := &virtualclient.GetSimulinkInfoRequest{
		SimulinkId: req.SimulateID,
	}
	info, err := l.svcCtx.SimulinkRpc.GetSimulinkInfo(l.ctx, request1)
	if err != nil {
		return nil, err
	}
	var controlParam []types.ControlParam
	for _, param := range info.SimulinkInfoAndRuntime.SimulinkInfo.ControlParams {
		controlParam = append(controlParam, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}
	var outputVars []types.OutputVar
	for _, output := range info.SimulinkInfoAndRuntime.SimulinkInfo.OutputVars {
		outputVars = append(outputVars, types.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		})
	}
	simulateinfo := &types.SimulateInfo{
		SimulateID:    info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkId,
		SimulateType:  info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkType,
		SimulateTime:  fmt.Sprintf("%.2f", info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkTime),
		Status:        info.SimulinkInfoAndRuntime.SimulinkInfo.SimulinkStatus,
		ControlParams: controlParam,
		OutputVars:    outputVars,
	}
	resp = &types.SaveSimResultResponse{
		SimulateID: req.SimulateID,
		Output:     outputVar,
		Message:    "Save simulation result done",
		Info:       *simulateinfo,
	}
	return resp, nil
}

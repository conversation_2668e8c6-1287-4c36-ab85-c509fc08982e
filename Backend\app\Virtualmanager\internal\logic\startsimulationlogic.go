package logic

import (
	"context"
	"fmt"
	"strconv"

	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"

	"github.com/zeromicro/go-zero/core/logx"
)

type StartSimulationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewStartSimulationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *StartSimulationLogic {
	return &StartSimulationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *StartSimulationLogic) StartSimulation(req *types.StartSimulationRequest) (resp *types.StartSimulationResponse, err error) {
	startTime, err := strconv.ParseFloat(req.StartTime, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid start time: %v", err)
	}
	stopTime, err := strconv.ParseFloat(req.StopTime, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid stop time: %v", err)
	}
	// 构建请求
	request := &virtualclient.RunSimulationRequest{
		SimulinkId: req.SimulateID,
		StartTime:  float32(startTime),
		StopTime:   float32(stopTime),
		Params:     []*virtualclient.ControlParamValue{},
	}
	for _, param := range req.Params {
		paramValue := &virtualclient.ControlParamValue{
			ParamName: param.ParamName,
			Timestamp: param.Timestamp,
			Value:     param.Value,
		}
		request.Params = append(request.Params, paramValue)
	}
	run_simu, err := l.svcCtx.SimulinkRpc.RunSimulation(l.ctx, request)
	if err != nil {
		return nil, err
	}
	var controlParam []types.ControlParam
	for _, param := range run_simu.SimulinkInfo.ControlParams {
		controlParam = append(controlParam, types.ControlParam{
			ParamName:    param.ParamName,
			BlockPath:    param.BlockPath,
			ParamType:    param.ParamType,
			DefaultValue: param.DefaultValue,
			Description:  param.Description,
			Writable:     param.Writable,
		})
	}

	// 存储输出变量列表
	var outputVars []types.OutputVar
	for _, output := range run_simu.SimulinkInfo.OutputVars {
		outputVars = append(outputVars, types.OutputVar{
			VarName:     output.VarName,
			MatlabVar:   output.MatlabVar,
			Description: output.Description,
			Readable:    output.Readable,
		})
	}
	var OutputVarValues []types.OutputVarValue
	for _, output := range run_simu.SimulinkRuntime.OutputVars {
		OutputVarValues = append(OutputVarValues, types.OutputVarValue{
			VarName:   output.VarName,
			Timestamp: output.Timestamp,
			Value:     output.Value,
		})
	}
	// 创建返回结构体
	resp = &types.StartSimulationResponse{
		Info: types.SimulateInfo{
			SimulateID:    run_simu.SimulinkInfo.SimulinkId,
			SimulateType:  run_simu.SimulinkInfo.SimulinkType,
			SimulateTime:  fmt.Sprintf("%v", run_simu.SimulinkInfo.SimulinkTime),
			Status:        run_simu.SimulinkInfo.SimulinkStatus,
			ControlParams: controlParam,
			OutputVars:    outputVars,
		},
		Runtime: types.SimulinkRuntime{
			Progress: fmt.Sprintf("%v", run_simu.SimulinkRuntime.Progress),
			Output:   OutputVarValues,
		},
	}
	return resp, nil
}

package logic

import (
	"context"
	"time"

	"GCF/app/Virtualmanager/internal/ent/virtualdevice"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/app/Virtualmanager/internal/types"
	"GCF/app/Virtualmanager/internal/ent/schema"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDeviceConfigLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDeviceConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDeviceConfigLogic {
	return &UpdateDeviceConfigLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDeviceConfigLogic) UpdateDeviceConfig(req *types.UpdateDeviceConfigRequest) (resp *types.UpdateDeviceConfigResponse, err error) {
	resp = &types.UpdateDeviceConfigResponse{}

	deviceConfig, err := l.svcCtx.Db.VirtualDevice.Query().Where(virtualdevice.IDEQ(req.DeviceID)).First(l.ctx)
	if err != nil {
		return nil, err
	}

	// 获取当前设备配置
	configMap := deviceConfig.Config
	// 遍历并更新配置项
	for i := range req.Config  {
		found := false
		for j := range configMap {
			if req.Config[i].ParamName == configMap[j].ParamName {
				configMap[j].Value = req.Config[i].Value
				found = true
				break
			}
		}
		if !found {
			newConfig := schema.DeviceConfig{
                ParamName: req.Config[i].ParamName,
                Value:     req.Config[i].Value,
            }
            configMap = append(configMap, newConfig)
		}
	}
	_, err = l.svcCtx.Db.VirtualDevice.UpdateOne(deviceConfig).SetConfig(configMap).Save(l.ctx)
	if err != nil {
		return nil, err
	}
	// 记录设备配置更新时间
	updateTime := time.Now()
	_, err = l.svcCtx.Db.VirtualDevice.UpdateOne(deviceConfig).SetUpdateUnix(updateTime).Save(l.ctx)
	if err != nil {
		return nil, err
	}

	resp.Success = true
	resp.Message = "设备配置更新成功"
	resp.UpdateTime = updateTime.Format("2006-01-02 15:04:05")
	// 初始化一个空的响应配置切片用于存储转换后的配置数据
	responseConfig := make([]types.DeviceConfig, len(configMap))
    for i, config := range configMap {
        responseConfig[i] = types.DeviceConfig{
            ParamName: config.ParamName,
            Value:     config.Value,
        }
    }
    resp.Config = responseConfig

	return resp, nil
}

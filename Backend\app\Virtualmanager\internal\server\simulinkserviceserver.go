// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.2
// Source: virtual.proto

package server

import (
	"context"

	"GCF/app/Virtualmanager/internal/logic"
	"GCF/app/Virtualmanager/internal/simulinkmanager"
	"GCF/app/Virtualmanager/internal/svc"
)

type SimulinkServiceServer struct {
	svcCtx *svc.ServiceContext
	simulinkmanager.UnimplementedSimulinkServiceServer
}

func NewSimulinkServiceServer(svcCtx *svc.ServiceContext) *SimulinkServiceServer {
	return &SimulinkServiceServer{
		svcCtx: svcCtx,
	}
}

// 初始化模型
func (s *SimulinkServiceServer) InitializeSimulink(ctx context.Context, in *simulinkmanager.InitializeSimulinkRequest) (*simulinkmanager.InitializeSimulinkResponse, error) {
	l := logic.NewInitializeSimulinkLogic(ctx, s.svcCtx)
	return l.InitializeSimulink(in)
}

// 注册控制参数
func (s *SimulinkServiceServer) RegisterSimulinkParam(ctx context.Context, in *simulinkmanager.ResgisterSimulinkParamRequest) (*simulinkmanager.RegisterSimulinkParamResponse, error) {
	l := logic.NewRegisterSimulinkParamLogic(ctx, s.svcCtx)
	return l.RegisterSimulinkParam(in)
}

// 注册输出参数
func (s *SimulinkServiceServer) RegisterSimulinkVar(ctx context.Context, in *simulinkmanager.RegisterSimulinkVarRequest) (*simulinkmanager.RegisterSimulinkVarResponse, error) {
	l := logic.NewRegisterSimulinkVarLogic(ctx, s.svcCtx)
	return l.RegisterSimulinkVar(in)
}

// 运行仿真
func (s *SimulinkServiceServer) RunSimulation(ctx context.Context, in *simulinkmanager.RunSimulationRequest) (*simulinkmanager.RunSimulationResponse, error) {
	l := logic.NewRunSimulationLogic(ctx, s.svcCtx)
	return l.RunSimulation(in)
}

// 获取模型信息
func (s *SimulinkServiceServer) GetSimulinkInfo(ctx context.Context, in *simulinkmanager.GetSimulinkInfoRequest) (*simulinkmanager.GetSimulinkInfoResponse, error) {
	l := logic.NewGetSimulinkInfoLogic(ctx, s.svcCtx)
	return l.GetSimulinkInfo(in)
}

// 获取仿真结果
func (s *SimulinkServiceServer) GetSimulationResults(ctx context.Context, in *simulinkmanager.GetSimulationResultsRequest) (*simulinkmanager.GetSimulationResultsResponse, error) {
	l := logic.NewGetSimulationResultsLogic(ctx, s.svcCtx)
	return l.GetSimulationResults(in)
}

// 关闭模型
func (s *SimulinkServiceServer) CloseSimulink(ctx context.Context, in *simulinkmanager.CloseSimulinkRequest) (*simulinkmanager.CloseSimulinkResponse, error) {
	l := logic.NewCloseSimulinkLogic(ctx, s.svcCtx)
	return l.CloseSimulink(in)
}

// 获取可用模型列表
func (s *SimulinkServiceServer) ListAvailableSimulinks(ctx context.Context, in *simulinkmanager.ListAvailableSimulinksRequest) (*simulinkmanager.ListAvailableSimulinksResponse, error) {
	l := logic.NewListAvailableSimulinksLogic(ctx, s.svcCtx)
	return l.ListAvailableSimulinks(in)
}

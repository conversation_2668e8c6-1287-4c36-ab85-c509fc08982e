// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.19.4
// source: rpc/virtual.proto

package simulinkmanager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SimulinkService_InitializeSimulink_FullMethodName     = "/simulinkmanager.SimulinkService/InitializeSimulink"
	SimulinkService_RegisterSimulinkParam_FullMethodName  = "/simulinkmanager.SimulinkService/RegisterSimulinkParam"
	SimulinkService_RegisterSimulinkVar_FullMethodName    = "/simulinkmanager.SimulinkService/RegisterSimulinkVar"
	SimulinkService_RunSimulation_FullMethodName          = "/simulinkmanager.SimulinkService/RunSimulation"
	SimulinkService_GetSimulinkInfo_FullMethodName        = "/simulinkmanager.SimulinkService/GetSimulinkInfo"
	SimulinkService_GetSimulationResults_FullMethodName   = "/simulinkmanager.SimulinkService/GetSimulationResults"
	SimulinkService_CloseSimulink_FullMethodName          = "/simulinkmanager.SimulinkService/CloseSimulink"
	SimulinkService_ListAvailableSimulinks_FullMethodName = "/simulinkmanager.SimulinkService/ListAvailableSimulinks"
)

// SimulinkServiceClient is the client API for SimulinkService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Simulink模型控制服务
type SimulinkServiceClient interface {
	// 初始化模型
	InitializeSimulink(ctx context.Context, in *InitializeSimulinkRequest, opts ...grpc.CallOption) (*InitializeSimulinkResponse, error)
	// 注册控制参数
	RegisterSimulinkParam(ctx context.Context, in *ResgisterSimulinkParamRequest, opts ...grpc.CallOption) (*RegisterSimulinkParamResponse, error)
	// 注册输出参数
	RegisterSimulinkVar(ctx context.Context, in *RegisterSimulinkVarRequest, opts ...grpc.CallOption) (*RegisterSimulinkVarResponse, error)
	// 运行仿真
	RunSimulation(ctx context.Context, in *RunSimulationRequest, opts ...grpc.CallOption) (*RunSimulationResponse, error)
	// 获取模型信息
	GetSimulinkInfo(ctx context.Context, in *GetSimulinkInfoRequest, opts ...grpc.CallOption) (*GetSimulinkInfoResponse, error)
	// 获取仿真结果
	GetSimulationResults(ctx context.Context, in *GetSimulationResultsRequest, opts ...grpc.CallOption) (*GetSimulationResultsResponse, error)
	// 关闭模型
	CloseSimulink(ctx context.Context, in *CloseSimulinkRequest, opts ...grpc.CallOption) (*CloseSimulinkResponse, error)
	// 获取可用模型列表
	ListAvailableSimulinks(ctx context.Context, in *ListAvailableSimulinksRequest, opts ...grpc.CallOption) (*ListAvailableSimulinksResponse, error)
}

type simulinkServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSimulinkServiceClient(cc grpc.ClientConnInterface) SimulinkServiceClient {
	return &simulinkServiceClient{cc}
}

func (c *simulinkServiceClient) InitializeSimulink(ctx context.Context, in *InitializeSimulinkRequest, opts ...grpc.CallOption) (*InitializeSimulinkResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InitializeSimulinkResponse)
	err := c.cc.Invoke(ctx, SimulinkService_InitializeSimulink_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulinkServiceClient) RegisterSimulinkParam(ctx context.Context, in *ResgisterSimulinkParamRequest, opts ...grpc.CallOption) (*RegisterSimulinkParamResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterSimulinkParamResponse)
	err := c.cc.Invoke(ctx, SimulinkService_RegisterSimulinkParam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulinkServiceClient) RegisterSimulinkVar(ctx context.Context, in *RegisterSimulinkVarRequest, opts ...grpc.CallOption) (*RegisterSimulinkVarResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterSimulinkVarResponse)
	err := c.cc.Invoke(ctx, SimulinkService_RegisterSimulinkVar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulinkServiceClient) RunSimulation(ctx context.Context, in *RunSimulationRequest, opts ...grpc.CallOption) (*RunSimulationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RunSimulationResponse)
	err := c.cc.Invoke(ctx, SimulinkService_RunSimulation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulinkServiceClient) GetSimulinkInfo(ctx context.Context, in *GetSimulinkInfoRequest, opts ...grpc.CallOption) (*GetSimulinkInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSimulinkInfoResponse)
	err := c.cc.Invoke(ctx, SimulinkService_GetSimulinkInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulinkServiceClient) GetSimulationResults(ctx context.Context, in *GetSimulationResultsRequest, opts ...grpc.CallOption) (*GetSimulationResultsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSimulationResultsResponse)
	err := c.cc.Invoke(ctx, SimulinkService_GetSimulationResults_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulinkServiceClient) CloseSimulink(ctx context.Context, in *CloseSimulinkRequest, opts ...grpc.CallOption) (*CloseSimulinkResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CloseSimulinkResponse)
	err := c.cc.Invoke(ctx, SimulinkService_CloseSimulink_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulinkServiceClient) ListAvailableSimulinks(ctx context.Context, in *ListAvailableSimulinksRequest, opts ...grpc.CallOption) (*ListAvailableSimulinksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAvailableSimulinksResponse)
	err := c.cc.Invoke(ctx, SimulinkService_ListAvailableSimulinks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SimulinkServiceServer is the server API for SimulinkService service.
// All implementations must embed UnimplementedSimulinkServiceServer
// for forward compatibility.
//
// Simulink模型控制服务
type SimulinkServiceServer interface {
	// 初始化模型
	InitializeSimulink(context.Context, *InitializeSimulinkRequest) (*InitializeSimulinkResponse, error)
	// 注册控制参数
	RegisterSimulinkParam(context.Context, *ResgisterSimulinkParamRequest) (*RegisterSimulinkParamResponse, error)
	// 注册输出参数
	RegisterSimulinkVar(context.Context, *RegisterSimulinkVarRequest) (*RegisterSimulinkVarResponse, error)
	// 运行仿真
	RunSimulation(context.Context, *RunSimulationRequest) (*RunSimulationResponse, error)
	// 获取模型信息
	GetSimulinkInfo(context.Context, *GetSimulinkInfoRequest) (*GetSimulinkInfoResponse, error)
	// 获取仿真结果
	GetSimulationResults(context.Context, *GetSimulationResultsRequest) (*GetSimulationResultsResponse, error)
	// 关闭模型
	CloseSimulink(context.Context, *CloseSimulinkRequest) (*CloseSimulinkResponse, error)
	// 获取可用模型列表
	ListAvailableSimulinks(context.Context, *ListAvailableSimulinksRequest) (*ListAvailableSimulinksResponse, error)
	mustEmbedUnimplementedSimulinkServiceServer()
}

// UnimplementedSimulinkServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSimulinkServiceServer struct{}

func (UnimplementedSimulinkServiceServer) InitializeSimulink(context.Context, *InitializeSimulinkRequest) (*InitializeSimulinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitializeSimulink not implemented")
}
func (UnimplementedSimulinkServiceServer) RegisterSimulinkParam(context.Context, *ResgisterSimulinkParamRequest) (*RegisterSimulinkParamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterSimulinkParam not implemented")
}
func (UnimplementedSimulinkServiceServer) RegisterSimulinkVar(context.Context, *RegisterSimulinkVarRequest) (*RegisterSimulinkVarResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterSimulinkVar not implemented")
}
func (UnimplementedSimulinkServiceServer) RunSimulation(context.Context, *RunSimulationRequest) (*RunSimulationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunSimulation not implemented")
}
func (UnimplementedSimulinkServiceServer) GetSimulinkInfo(context.Context, *GetSimulinkInfoRequest) (*GetSimulinkInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulinkInfo not implemented")
}
func (UnimplementedSimulinkServiceServer) GetSimulationResults(context.Context, *GetSimulationResultsRequest) (*GetSimulationResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulationResults not implemented")
}
func (UnimplementedSimulinkServiceServer) CloseSimulink(context.Context, *CloseSimulinkRequest) (*CloseSimulinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseSimulink not implemented")
}
func (UnimplementedSimulinkServiceServer) ListAvailableSimulinks(context.Context, *ListAvailableSimulinksRequest) (*ListAvailableSimulinksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailableSimulinks not implemented")
}
func (UnimplementedSimulinkServiceServer) mustEmbedUnimplementedSimulinkServiceServer() {}
func (UnimplementedSimulinkServiceServer) testEmbeddedByValue()                         {}

// UnsafeSimulinkServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SimulinkServiceServer will
// result in compilation errors.
type UnsafeSimulinkServiceServer interface {
	mustEmbedUnimplementedSimulinkServiceServer()
}

func RegisterSimulinkServiceServer(s grpc.ServiceRegistrar, srv SimulinkServiceServer) {
	// If the following call pancis, it indicates UnimplementedSimulinkServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SimulinkService_ServiceDesc, srv)
}

func _SimulinkService_InitializeSimulink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitializeSimulinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).InitializeSimulink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_InitializeSimulink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).InitializeSimulink(ctx, req.(*InitializeSimulinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulinkService_RegisterSimulinkParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResgisterSimulinkParamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).RegisterSimulinkParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_RegisterSimulinkParam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).RegisterSimulinkParam(ctx, req.(*ResgisterSimulinkParamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulinkService_RegisterSimulinkVar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterSimulinkVarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).RegisterSimulinkVar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_RegisterSimulinkVar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).RegisterSimulinkVar(ctx, req.(*RegisterSimulinkVarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulinkService_RunSimulation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunSimulationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).RunSimulation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_RunSimulation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).RunSimulation(ctx, req.(*RunSimulationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulinkService_GetSimulinkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimulinkInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).GetSimulinkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_GetSimulinkInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).GetSimulinkInfo(ctx, req.(*GetSimulinkInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulinkService_GetSimulationResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimulationResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).GetSimulationResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_GetSimulationResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).GetSimulationResults(ctx, req.(*GetSimulationResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulinkService_CloseSimulink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseSimulinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).CloseSimulink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_CloseSimulink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).CloseSimulink(ctx, req.(*CloseSimulinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SimulinkService_ListAvailableSimulinks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailableSimulinksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulinkServiceServer).ListAvailableSimulinks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SimulinkService_ListAvailableSimulinks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulinkServiceServer).ListAvailableSimulinks(ctx, req.(*ListAvailableSimulinksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SimulinkService_ServiceDesc is the grpc.ServiceDesc for SimulinkService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SimulinkService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "simulinkmanager.SimulinkService",
	HandlerType: (*SimulinkServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitializeSimulink",
			Handler:    _SimulinkService_InitializeSimulink_Handler,
		},
		{
			MethodName: "RegisterSimulinkParam",
			Handler:    _SimulinkService_RegisterSimulinkParam_Handler,
		},
		{
			MethodName: "RegisterSimulinkVar",
			Handler:    _SimulinkService_RegisterSimulinkVar_Handler,
		},
		{
			MethodName: "RunSimulation",
			Handler:    _SimulinkService_RunSimulation_Handler,
		},
		{
			MethodName: "GetSimulinkInfo",
			Handler:    _SimulinkService_GetSimulinkInfo_Handler,
		},
		{
			MethodName: "GetSimulationResults",
			Handler:    _SimulinkService_GetSimulationResults_Handler,
		},
		{
			MethodName: "CloseSimulink",
			Handler:    _SimulinkService_CloseSimulink_Handler,
		},
		{
			MethodName: "ListAvailableSimulinks",
			Handler:    _SimulinkService_ListAvailableSimulinks_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/virtual.proto",
}

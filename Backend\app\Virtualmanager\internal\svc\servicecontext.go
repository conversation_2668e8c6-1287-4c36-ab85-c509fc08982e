package svc

import (
	"GCF/app/Virtualmanager/internal/config"
	"GCF/app/Virtualmanager/internal/ent"
	virtualclient "GCF/app/Virtualmanager/simulinkservice"
	"fmt"

	_ "github.com/lib/pq"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config      config.Config
	Db          *ent.Client
	SimulinkRpc virtualclient.SimulinkService
}

func NewServiceContext(c config.Config) *ServiceContext {
	entClient, err := ent.Open(c.Db.Driver, c.Db.MustGetSource())
	logx.Must(err)
	fmt.Printf("Connected to database, driver %s, source %s\n", c.Db.Driver, c.Db.MustGetSource())

	return &ServiceContext{
		Config:      c,
		Db:          entClient,
		SimulinkRpc: virtualclient.NewSimulinkService(zrpc.MustNewClient(c.SimulinkRpcConf)),
	}
}

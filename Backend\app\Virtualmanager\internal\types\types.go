// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.2

package types

type CloseSimulationRequest struct {
	SimulateID string `json:"simulateID"` // 模型ID
}

type CloseSimulationResponse struct {
	Info SimulateInfo `json:"simulateInfo"` // 模型info信息
}

type ControlContinuousRequest struct {
	SimulateID   string              `json:"simulateID"`
	SimulateTime string              `json:"simulateTime"`
	Params       []ControlParamValue `json:"params"`
}

type ControlContinuousResponse struct {
	Success bool            `json:"success"`
	Message string          `json:"message"`
	Info    SimulateInfo    `json:"simulateInfo"`
	Runtime SimulinkRuntime `json:"simulinkRuntime"`
}

type ControlParam struct {
	ParamName    string `json:"paramName"`
	BlockPath    string `json:"blockPath"`
	ParamType    string `json:"paramType"`
	DefaultValue string `json:"defaultValue"`
	Description  string `json:"description"`
	Writable     bool   `json:"writable"`
}

type ControlParamValue struct {
	Timestamp string `json:"timestamp"`
	ParamName string `json:"paramName"`
	Value     string `json:"value"`
}

type ControlStepRequest struct {
	SimulateID   string            `json:"simulateID"`
	SimulateTime string            `json:"simulateTime"`
	Params       ControlParamValue `json:"params"`
}

type ControlStepResponse struct {
	Success bool            `json:"success"`
	Message string          `json:"message"`
	Info    SimulateInfo    `json:"simulateInfo"`
	Runtime SimulinkRuntime `json:"simulinkRuntime"`
}

type CreateDeviceRequest struct {
	DeviceName string         `json:"deviceName"`
	DeviceType string         `json:"deviceType"`
	Config     []DeviceConfig `json:"config"`
}

type CreateDeviceResponse struct {
	Success bool       `json:"success"`
	Message string     `json:"message"`
	Info    DeviceInfo `json:"deviceinfo"`
	Frame   FrameInfo  `json:"frameinfo"`
}

type CreateSimulationRequest struct {
	DeviceID     string         `json:"deviceID"`
	SimulinkDir  string         `json:"simulinkDir"`
	SimulinkName string         `json:"simulinkName"`
	SimulateTime string         `json:"simulateTime"`
	InitScript   string         `json:"initScript"`
	Params       []ControlParam `json:"params"`
	OutVars      []OutputVar    `json:"outVars"`
}

type CreateSimulationResponse struct {
	Message string       `json:"message"`
	Info    SimulateInfo `json:"simulateInfo"`
}

type DeleteDeviceRequest struct {
	DeviceID string `json:"deviceID"`
}

type DeleteDeviceResponse struct {
	Success bool       `json:"success"`
	Message string     `json:"message"`
	Info    DeviceInfo `json:"deviceinfo"`
	Frame   FrameInfo  `json:"frameinfo"`
}

type DeviceConfig struct {
	ParamName string `json:"paramName"`
	Value     string `json:"value"`
}

type DeviceHealthStatus struct {
	DeviceID     string `json:"deviceID"`
	HealthStatus string `json:"healthStatus"`
}

type DeviceInfo struct {
	DeviceID   string `json:"deviceID"`
	DeviceName string `json:"deviceName"`
	DeviceType string `json:"deviceType"`
	Status     string `json:"status"`
	CreateTime string `json:"createTime"`
}

type DeviceStatus struct {
	DeviceID string `json:"deviceID"`
	Status   string `json:"status"`
}

type FrameInfo struct {
	FrameID   string `json:"frameID"`
	FrameTime string `json:"frametime"`
}

type GetDeviceConfigRequest struct {
	DeviceID string `json:"deviceID"`
}

type GetDeviceConfigResponse struct {
	DeviceID   string         `json:"deviceID"`
	UpdateTime string         `json:"updateTime"`
	Config     []DeviceConfig `json:"config"`
}

type GetDeviceHealthRequest struct {
	DeviceID string `json:"deviceID"`
}

type GetDeviceHealthResponse struct {
	DeviceID     string `json:"deviceID"`
	HealthStatus string `json:"healthStatus"`
	Message      string `json:"message"`
}

type GetDeviceLogRequest struct {
	DeviceID string `json:"deviceID"`
	LogLevel int32  `json:"logLevel"`
	Limit    int32  `json:"limit"`
}

type GetDeviceLogResponse struct {
	Logs []LogEntry `json:"logs"`
}

type GetDeviceStatusRequest struct {
	DeviceID string `json:"deviceID"`
}

type GetDeviceStatusResponse struct {
	DeviceID string `json:"deviceID"`
	Status   string `json:"status"`
	Message  string `json:"message"`
}

type GetSimoutRequest struct {
	SimulateID string   `json:"simulateID"`
	Vars       []string `json:"vars"`
}

type GetSimoutResponse struct {
	Output []OutputVarValue `json:"output"`
}

type GetSimulationInfoRequest struct {
	SimulateID string `json:"simulateID"` // 模型ID
}

type GetSimulationInfoResponse struct {
	Info SimulinkInfoAndRuntime `json:"info"` // 模型info信息
}

type ListAvailableSimulationsRequest struct {
	SimulateID   string `json:"simulateID"`   // 模型id
	SimulateType string `json:"simulateType"` // 模型类型
	Status       string `json:"status"`       // 仿真状态,枚举类型，如"initializing", "ready","running", "stopped", "error"
}

type ListAvailableSimulationsResponse struct {
	Info []SimulinkInfoAndRuntime `json:"info"` // 模型info信息
}

type ListDeviceRequest struct {
	DeviceType string `json:"deviceType"`
}

type ListDeviceResponse struct {
	Devices []DeviceInfo `json:"devices"`
	Total   int32        `json:"total"`
	Message string       `json:"message"`
}

type LogEntry struct {
	Timestamp string `json:"timestamp"`
	Level     string `json:"level"`
	Message   string `json:"message"`
}

type OutputVar struct {
	VarName     string `json:"varName"`
	MatlabVar   string `json:"matlabVar"`
	Description string `json:"description"`
	Readable    bool   `json:"readable"`
}

type OutputVarValue struct {
	VarName   string   `json:"varName"`   // 变量名称
	Timestamp []string `json:"timestamp"` // 时间列表
	Value     []string `json:"value"`     // 值列表
}

type RegisterSimulationParamRequest struct {
	SimulateID string         `json:"simulateID"`
	Params     []ControlParam `json:"params"`
}

type RegisterSimulationParamResponse struct {
	Info SimulateInfo `json:"simulateInfo"` // 模型info信息
}

type RegisterSimulationVarRequest struct {
	SimulateID string      `json:"simulateID"` // 模型ID
	Output     []OutputVar `json:"output"`     // 输出变量列表
}

type RegisterSimulationVarResponse struct {
	Info SimulateInfo `json:"simulateInfo"` // 模型info信息
}

type SaveSimResultRequest struct {
	SimulateID string `json:"simulateID"`
}

type SaveSimResultResponse struct {
	SimulateID string           `json:"simulateID"`
	Output     []OutputVarValue `json:"output"`
	Info       SimulateInfo     `json:"simulateInfo"`
	Message    string           `json:"message"`
}

type SimulateConfig struct {
	ParamName string `json:"paramName"`
	Value     string `json:"value"`
}

type SimulateInfo struct {
	SimulateID    string         `json:"simulateID"`
	SimulateType  string         `json:"simulateType"`
	SimulateTime  string         `json:"simulateTime"`
	Status        string         `json:"status"`
	ControlParams []ControlParam `json:"controlParam"`
	OutputVars    []OutputVar    `json:"outputVar"`
}

type SimulinkInfoAndRuntime struct {
	Info    SimulateInfo    `json:"info"`    // 模型info信息
	Runtime SimulinkRuntime `json:"runtime"` // 模型运行信息
}

type SimulinkRuntime struct {
	Progress string           `json:"progress"` // 进度 (0.0-1.0)
	Output   []OutputVarValue `json:"output"`   // 输出变量
}

type StartSimulationRequest struct {
	SimulateID string              `json:"SimulateID"`
	StartTime  string              `json:"startTime"`
	StopTime   string              `json:"stopTime"`
	Params     []ControlParamValue `json:"params"`
}

type StartSimulationResponse struct {
	Info    SimulateInfo    `json:"simulateInfo"`
	Runtime SimulinkRuntime `json:"simulateRuntime"`
}

type UpdateDeviceConfigRequest struct {
	DeviceID string         `json:"deviceID"`
	Config   []DeviceConfig `json:"config"`
}

type UpdateDeviceConfigResponse struct {
	Success    bool           `json:"success"`
	UpdateTime string         `json:"updateTime"`
	Config     []DeviceConfig `json:"config"`
	Message    string         `json:"message"`
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.19.4
// source: rpc/virtual.proto

package virtualmanager

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 初始化模型请求
type InitializeSimulinkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkType  string                 `protobuf:"bytes,1,opt,name=simulink_type,json=simulinkType,proto3" json:"simulink_type,omitempty"`    // 模型类型 (e.g., "pressure", "motor")
	SimulinkDir   string                 `protobuf:"bytes,2,opt,name=simulink_dir,json=simulinkDir,proto3" json:"simulink_dir,omitempty"`       // 模型目录
	SimulinkName  string                 `protobuf:"bytes,3,opt,name=simulink_name,json=simulinkName,proto3" json:"simulink_name,omitempty"`    // 模型名称
	InitScript    string                 `protobuf:"bytes,4,opt,name=init_script,json=initScript,proto3" json:"init_script,omitempty"`          // 初始化脚本
	RunTime       float32                `protobuf:"fixed32,5,opt,name=run_time,json=runTime,proto3" json:"run_time,omitempty"`                 // 仿真时长
	ControlParams []*ControlParam        `protobuf:"bytes,6,rep,name=control_params,json=controlParams,proto3" json:"control_params,omitempty"` // 控制参数列表
	OutputVars    []*OutputVar           `protobuf:"bytes,7,rep,name=output_vars,json=outputVars,proto3" json:"output_vars,omitempty"`          // 输出变量列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitializeSimulinkRequest) Reset() {
	*x = InitializeSimulinkRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitializeSimulinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitializeSimulinkRequest) ProtoMessage() {}

func (x *InitializeSimulinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitializeSimulinkRequest.ProtoReflect.Descriptor instead.
func (*InitializeSimulinkRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{0}
}

func (x *InitializeSimulinkRequest) GetSimulinkType() string {
	if x != nil {
		return x.SimulinkType
	}
	return ""
}

func (x *InitializeSimulinkRequest) GetSimulinkDir() string {
	if x != nil {
		return x.SimulinkDir
	}
	return ""
}

func (x *InitializeSimulinkRequest) GetSimulinkName() string {
	if x != nil {
		return x.SimulinkName
	}
	return ""
}

func (x *InitializeSimulinkRequest) GetInitScript() string {
	if x != nil {
		return x.InitScript
	}
	return ""
}

func (x *InitializeSimulinkRequest) GetRunTime() float32 {
	if x != nil {
		return x.RunTime
	}
	return 0
}

func (x *InitializeSimulinkRequest) GetControlParams() []*ControlParam {
	if x != nil {
		return x.ControlParams
	}
	return nil
}

func (x *InitializeSimulinkRequest) GetOutputVars() []*OutputVar {
	if x != nil {
		return x.OutputVars
	}
	return nil
}

type InitializeSimulinkResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkInfo  *SimulinkInfo          `protobuf:"bytes,1,opt,name=simulink_info,json=simulinkInfo,proto3" json:"simulink_info,omitempty"` // 模型info信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitializeSimulinkResponse) Reset() {
	*x = InitializeSimulinkResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitializeSimulinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitializeSimulinkResponse) ProtoMessage() {}

func (x *InitializeSimulinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitializeSimulinkResponse.ProtoReflect.Descriptor instead.
func (*InitializeSimulinkResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{1}
}

func (x *InitializeSimulinkResponse) GetSimulinkInfo() *SimulinkInfo {
	if x != nil {
		return x.SimulinkInfo
	}
	return nil
}

type SimulinkInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId     string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"`             // 模型id
	SimulinkType   string                 `protobuf:"bytes,2,opt,name=simulink_type,json=simulinkType,proto3" json:"simulink_type,omitempty"`       // 模型类型
	SimulinkStatus string                 `protobuf:"bytes,3,opt,name=simulink_status,json=simulinkStatus,proto3" json:"simulink_status,omitempty"` // 仿真状态,枚举类型，如"initializing", "ready","running", "stopped", "error"
	SimulinkTime   float32                `protobuf:"fixed32,4,opt,name=simulink_time,json=simulinkTime,proto3" json:"simulink_time,omitempty"`     // 模型运行时间
	ControlParams  []*ControlParam        `protobuf:"bytes,5,rep,name=control_params,json=controlParams,proto3" json:"control_params,omitempty"`    // 控制参数列表
	OutputVars     []*OutputVar           `protobuf:"bytes,6,rep,name=output_vars,json=outputVars,proto3" json:"output_vars,omitempty"`             // 输出变量列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SimulinkInfo) Reset() {
	*x = SimulinkInfo{}
	mi := &file_rpc_virtual_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulinkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulinkInfo) ProtoMessage() {}

func (x *SimulinkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulinkInfo.ProtoReflect.Descriptor instead.
func (*SimulinkInfo) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{2}
}

func (x *SimulinkInfo) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

func (x *SimulinkInfo) GetSimulinkType() string {
	if x != nil {
		return x.SimulinkType
	}
	return ""
}

func (x *SimulinkInfo) GetSimulinkStatus() string {
	if x != nil {
		return x.SimulinkStatus
	}
	return ""
}

func (x *SimulinkInfo) GetSimulinkTime() float32 {
	if x != nil {
		return x.SimulinkTime
	}
	return 0
}

func (x *SimulinkInfo) GetControlParams() []*ControlParam {
	if x != nil {
		return x.ControlParams
	}
	return nil
}

func (x *SimulinkInfo) GetOutputVars() []*OutputVar {
	if x != nil {
		return x.OutputVars
	}
	return nil
}

// 控制参数
type ControlParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ParamName     string                 `protobuf:"bytes,1,opt,name=param_name,json=paramName,proto3" json:"param_name,omitempty"`          // 参数名称
	BlockPath     string                 `protobuf:"bytes,2,opt,name=block_path,json=blockPath,proto3" json:"block_path,omitempty"`          // MATLAB块路径
	ParamType     string                 `protobuf:"bytes,3,opt,name=param_type,json=paramType,proto3" json:"param_type,omitempty"`          // 参数类型
	DefaultValue  string                 `protobuf:"bytes,4,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"` // 默认值
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                       // 参数描述
	Writable      bool                   `protobuf:"varint,6,opt,name=writable,proto3" json:"writable,omitempty"`                            // 是否可写
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlParam) Reset() {
	*x = ControlParam{}
	mi := &file_rpc_virtual_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlParam) ProtoMessage() {}

func (x *ControlParam) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlParam.ProtoReflect.Descriptor instead.
func (*ControlParam) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{3}
}

func (x *ControlParam) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *ControlParam) GetBlockPath() string {
	if x != nil {
		return x.BlockPath
	}
	return ""
}

func (x *ControlParam) GetParamType() string {
	if x != nil {
		return x.ParamType
	}
	return ""
}

func (x *ControlParam) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *ControlParam) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ControlParam) GetWritable() bool {
	if x != nil {
		return x.Writable
	}
	return false
}

// 输出变量
type OutputVar struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VarName       string                 `protobuf:"bytes,1,opt,name=var_name,json=varName,proto3" json:"var_name,omitempty"`       // 变量名称
	MatlabVar     string                 `protobuf:"bytes,2,opt,name=matlab_var,json=matlabVar,proto3" json:"matlab_var,omitempty"` // MATLAB变量路径
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`              // 变量描述
	Readable      bool                   `protobuf:"varint,4,opt,name=readable,proto3" json:"readable,omitempty"`                   // 是否可读
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OutputVar) Reset() {
	*x = OutputVar{}
	mi := &file_rpc_virtual_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutputVar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputVar) ProtoMessage() {}

func (x *OutputVar) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputVar.ProtoReflect.Descriptor instead.
func (*OutputVar) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{4}
}

func (x *OutputVar) GetVarName() string {
	if x != nil {
		return x.VarName
	}
	return ""
}

func (x *OutputVar) GetMatlabVar() string {
	if x != nil {
		return x.MatlabVar
	}
	return ""
}

func (x *OutputVar) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OutputVar) GetReadable() bool {
	if x != nil {
		return x.Readable
	}
	return false
}

// 注册控制参数
type ResgisterSimulinkParamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId    string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"`          // 模型ID
	ControlParams []*ControlParam        `protobuf:"bytes,2,rep,name=control_params,json=controlParams,proto3" json:"control_params,omitempty"` // 控制参数列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResgisterSimulinkParamRequest) Reset() {
	*x = ResgisterSimulinkParamRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResgisterSimulinkParamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResgisterSimulinkParamRequest) ProtoMessage() {}

func (x *ResgisterSimulinkParamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResgisterSimulinkParamRequest.ProtoReflect.Descriptor instead.
func (*ResgisterSimulinkParamRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{5}
}

func (x *ResgisterSimulinkParamRequest) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

func (x *ResgisterSimulinkParamRequest) GetControlParams() []*ControlParam {
	if x != nil {
		return x.ControlParams
	}
	return nil
}

type RegisterSimulinkParamResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkInfo  *SimulinkInfo          `protobuf:"bytes,1,opt,name=simulink_info,json=simulinkInfo,proto3" json:"simulink_info,omitempty"` // 模型info信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterSimulinkParamResponse) Reset() {
	*x = RegisterSimulinkParamResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterSimulinkParamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterSimulinkParamResponse) ProtoMessage() {}

func (x *RegisterSimulinkParamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterSimulinkParamResponse.ProtoReflect.Descriptor instead.
func (*RegisterSimulinkParamResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{6}
}

func (x *RegisterSimulinkParamResponse) GetSimulinkInfo() *SimulinkInfo {
	if x != nil {
		return x.SimulinkInfo
	}
	return nil
}

// 注册输出变量
type RegisterSimulinkVarRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId    string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"` // 模型ID
	OutputVars    []*OutputVar           `protobuf:"bytes,2,rep,name=output_vars,json=outputVars,proto3" json:"output_vars,omitempty"` // 输出变量列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterSimulinkVarRequest) Reset() {
	*x = RegisterSimulinkVarRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterSimulinkVarRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterSimulinkVarRequest) ProtoMessage() {}

func (x *RegisterSimulinkVarRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterSimulinkVarRequest.ProtoReflect.Descriptor instead.
func (*RegisterSimulinkVarRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{7}
}

func (x *RegisterSimulinkVarRequest) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

func (x *RegisterSimulinkVarRequest) GetOutputVars() []*OutputVar {
	if x != nil {
		return x.OutputVars
	}
	return nil
}

type RegisterSimulinkVarResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkInfo  *SimulinkInfo          `protobuf:"bytes,1,opt,name=simulink_info,json=simulinkInfo,proto3" json:"simulink_info,omitempty"` // 模型info信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterSimulinkVarResponse) Reset() {
	*x = RegisterSimulinkVarResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterSimulinkVarResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterSimulinkVarResponse) ProtoMessage() {}

func (x *RegisterSimulinkVarResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterSimulinkVarResponse.ProtoReflect.Descriptor instead.
func (*RegisterSimulinkVarResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{8}
}

func (x *RegisterSimulinkVarResponse) GetSimulinkInfo() *SimulinkInfo {
	if x != nil {
		return x.SimulinkInfo
	}
	return nil
}

// 运行仿真请求
type ControlParamValue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     string                 `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ParamName     string                 `protobuf:"bytes,2,opt,name=param_name,json=paramName,proto3" json:"param_name,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlParamValue) Reset() {
	*x = ControlParamValue{}
	mi := &file_rpc_virtual_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlParamValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlParamValue) ProtoMessage() {}

func (x *ControlParamValue) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlParamValue.ProtoReflect.Descriptor instead.
func (*ControlParamValue) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{9}
}

func (x *ControlParamValue) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *ControlParamValue) GetParamName() string {
	if x != nil {
		return x.ParamName
	}
	return ""
}

func (x *ControlParamValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type OutputVarValue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VarName       string                 `protobuf:"bytes,1,opt,name=var_name,json=varName,proto3" json:"var_name,omitempty"` // 变量名称
	Timestamp     []string               `protobuf:"bytes,2,rep,name=timestamp,proto3" json:"timestamp,omitempty"`            // 时间列表
	Value         []string               `protobuf:"bytes,3,rep,name=value,proto3" json:"value,omitempty"`                    // 值列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OutputVarValue) Reset() {
	*x = OutputVarValue{}
	mi := &file_rpc_virtual_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutputVarValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputVarValue) ProtoMessage() {}

func (x *OutputVarValue) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputVarValue.ProtoReflect.Descriptor instead.
func (*OutputVarValue) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{10}
}

func (x *OutputVarValue) GetVarName() string {
	if x != nil {
		return x.VarName
	}
	return ""
}

func (x *OutputVarValue) GetTimestamp() []string {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *OutputVarValue) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

// 仿真运行状态和信息
type SimulinkRuntime struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Progress      float32                `protobuf:"fixed32,1,opt,name=progress,proto3" json:"progress,omitempty"`                     // 进度 (0.0-1.0)
	OutputVars    []*OutputVarValue      `protobuf:"bytes,2,rep,name=output_vars,json=outputVars,proto3" json:"output_vars,omitempty"` // 输出变量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimulinkRuntime) Reset() {
	*x = SimulinkRuntime{}
	mi := &file_rpc_virtual_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulinkRuntime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulinkRuntime) ProtoMessage() {}

func (x *SimulinkRuntime) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulinkRuntime.ProtoReflect.Descriptor instead.
func (*SimulinkRuntime) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{11}
}

func (x *SimulinkRuntime) GetProgress() float32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *SimulinkRuntime) GetOutputVars() []*OutputVarValue {
	if x != nil {
		return x.OutputVars
	}
	return nil
}

type RunSimulationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId    string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"` // 模型ID
	StartTime     float32                `protobuf:"fixed32,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`  // 开始时间
	StopTime      float32                `protobuf:"fixed32,3,opt,name=stop_time,json=stopTime,proto3" json:"stop_time,omitempty"`     // 结束时间
	Params        []*ControlParamValue   `protobuf:"bytes,4,rep,name=params,proto3" json:"params,omitempty"`                           // 仿真参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RunSimulationRequest) Reset() {
	*x = RunSimulationRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunSimulationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunSimulationRequest) ProtoMessage() {}

func (x *RunSimulationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunSimulationRequest.ProtoReflect.Descriptor instead.
func (*RunSimulationRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{12}
}

func (x *RunSimulationRequest) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

func (x *RunSimulationRequest) GetStartTime() float32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *RunSimulationRequest) GetStopTime() float32 {
	if x != nil {
		return x.StopTime
	}
	return 0
}

func (x *RunSimulationRequest) GetParams() []*ControlParamValue {
	if x != nil {
		return x.Params
	}
	return nil
}

type RunSimulationResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	SimulinkInfo    *SimulinkInfo          `protobuf:"bytes,1,opt,name=simulink_info,json=simulinkInfo,proto3" json:"simulink_info,omitempty"`          // 模型info信息
	SimulinkRuntime *SimulinkRuntime       `protobuf:"bytes,2,opt,name=simulink_runtime,json=simulinkRuntime,proto3" json:"simulink_runtime,omitempty"` // 模型运行信息
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RunSimulationResponse) Reset() {
	*x = RunSimulationResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunSimulationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunSimulationResponse) ProtoMessage() {}

func (x *RunSimulationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunSimulationResponse.ProtoReflect.Descriptor instead.
func (*RunSimulationResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{13}
}

func (x *RunSimulationResponse) GetSimulinkInfo() *SimulinkInfo {
	if x != nil {
		return x.SimulinkInfo
	}
	return nil
}

func (x *RunSimulationResponse) GetSimulinkRuntime() *SimulinkRuntime {
	if x != nil {
		return x.SimulinkRuntime
	}
	return nil
}

// 获取模型信息请求
type GetSimulinkInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId    string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"` // 模型ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSimulinkInfoRequest) Reset() {
	*x = GetSimulinkInfoRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSimulinkInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimulinkInfoRequest) ProtoMessage() {}

func (x *GetSimulinkInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimulinkInfoRequest.ProtoReflect.Descriptor instead.
func (*GetSimulinkInfoRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{14}
}

func (x *GetSimulinkInfoRequest) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

type GetSimulinkInfoResponse struct {
	state                  protoimpl.MessageState  `protogen:"open.v1"`
	SimulinkInfoAndRuntime *SimulinkInfoAndRuntime `protobuf:"bytes,1,opt,name=simulink_info_and_runtime,json=simulinkInfoAndRuntime,proto3" json:"simulink_info_and_runtime,omitempty"` // 模型info信息
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GetSimulinkInfoResponse) Reset() {
	*x = GetSimulinkInfoResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSimulinkInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimulinkInfoResponse) ProtoMessage() {}

func (x *GetSimulinkInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimulinkInfoResponse.ProtoReflect.Descriptor instead.
func (*GetSimulinkInfoResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{15}
}

func (x *GetSimulinkInfoResponse) GetSimulinkInfoAndRuntime() *SimulinkInfoAndRuntime {
	if x != nil {
		return x.SimulinkInfoAndRuntime
	}
	return nil
}

type SimulinkInfoAndRuntime struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	SimulinkInfo    *SimulinkInfo          `protobuf:"bytes,1,opt,name=simulink_info,json=simulinkInfo,proto3" json:"simulink_info,omitempty"`          // 模型info信息
	SimulinkRuntime *SimulinkRuntime       `protobuf:"bytes,2,opt,name=simulink_runtime,json=simulinkRuntime,proto3" json:"simulink_runtime,omitempty"` // 模型运行信息
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SimulinkInfoAndRuntime) Reset() {
	*x = SimulinkInfoAndRuntime{}
	mi := &file_rpc_virtual_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimulinkInfoAndRuntime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulinkInfoAndRuntime) ProtoMessage() {}

func (x *SimulinkInfoAndRuntime) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulinkInfoAndRuntime.ProtoReflect.Descriptor instead.
func (*SimulinkInfoAndRuntime) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{16}
}

func (x *SimulinkInfoAndRuntime) GetSimulinkInfo() *SimulinkInfo {
	if x != nil {
		return x.SimulinkInfo
	}
	return nil
}

func (x *SimulinkInfoAndRuntime) GetSimulinkRuntime() *SimulinkRuntime {
	if x != nil {
		return x.SimulinkRuntime
	}
	return nil
}

// 获取仿真结果请求
type GetSimulationResultsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId    string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"` // 模型ID
	VarNames      []string               `protobuf:"bytes,2,rep,name=var_names,json=varNames,proto3" json:"var_names,omitempty"`       // 变量名称列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSimulationResultsRequest) Reset() {
	*x = GetSimulationResultsRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSimulationResultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimulationResultsRequest) ProtoMessage() {}

func (x *GetSimulationResultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimulationResultsRequest.ProtoReflect.Descriptor instead.
func (*GetSimulationResultsRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{17}
}

func (x *GetSimulationResultsRequest) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

func (x *GetSimulationResultsRequest) GetVarNames() []string {
	if x != nil {
		return x.VarNames
	}
	return nil
}

type GetSimulationResultsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OutputVars    []*OutputVarValue      `protobuf:"bytes,1,rep,name=output_vars,json=outputVars,proto3" json:"output_vars,omitempty"` // 输出变量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSimulationResultsResponse) Reset() {
	*x = GetSimulationResultsResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSimulationResultsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSimulationResultsResponse) ProtoMessage() {}

func (x *GetSimulationResultsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSimulationResultsResponse.ProtoReflect.Descriptor instead.
func (*GetSimulationResultsResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{18}
}

func (x *GetSimulationResultsResponse) GetOutputVars() []*OutputVarValue {
	if x != nil {
		return x.OutputVars
	}
	return nil
}

// 关闭模型请求
type CloseSimulinkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId    string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"` // 模型ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CloseSimulinkRequest) Reset() {
	*x = CloseSimulinkRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloseSimulinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseSimulinkRequest) ProtoMessage() {}

func (x *CloseSimulinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseSimulinkRequest.ProtoReflect.Descriptor instead.
func (*CloseSimulinkRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{19}
}

func (x *CloseSimulinkRequest) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

type CloseSimulinkResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimulinkInfo  *SimulinkInfo          `protobuf:"bytes,1,opt,name=simulink_info,json=simulinkInfo,proto3" json:"simulink_info,omitempty"` // 模型info信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CloseSimulinkResponse) Reset() {
	*x = CloseSimulinkResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloseSimulinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseSimulinkResponse) ProtoMessage() {}

func (x *CloseSimulinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseSimulinkResponse.ProtoReflect.Descriptor instead.
func (*CloseSimulinkResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{20}
}

func (x *CloseSimulinkResponse) GetSimulinkInfo() *SimulinkInfo {
	if x != nil {
		return x.SimulinkInfo
	}
	return nil
}

// 获取可用模型列表请求
type ListAvailableSimulinksRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SimulinkId     string                 `protobuf:"bytes,1,opt,name=simulink_id,json=simulinkId,proto3" json:"simulink_id,omitempty"`             // 模型id
	SimulinkType   string                 `protobuf:"bytes,2,opt,name=simulink_type,json=simulinkType,proto3" json:"simulink_type,omitempty"`       // 模型类型
	SimulinkStatus string                 `protobuf:"bytes,3,opt,name=simulink_status,json=simulinkStatus,proto3" json:"simulink_status,omitempty"` // 仿真状态,枚举类型，如"initializing", "ready","running", "stopped", "error"
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListAvailableSimulinksRequest) Reset() {
	*x = ListAvailableSimulinksRequest{}
	mi := &file_rpc_virtual_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableSimulinksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableSimulinksRequest) ProtoMessage() {}

func (x *ListAvailableSimulinksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableSimulinksRequest.ProtoReflect.Descriptor instead.
func (*ListAvailableSimulinksRequest) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{21}
}

func (x *ListAvailableSimulinksRequest) GetSimulinkId() string {
	if x != nil {
		return x.SimulinkId
	}
	return ""
}

func (x *ListAvailableSimulinksRequest) GetSimulinkType() string {
	if x != nil {
		return x.SimulinkType
	}
	return ""
}

func (x *ListAvailableSimulinksRequest) GetSimulinkStatus() string {
	if x != nil {
		return x.SimulinkStatus
	}
	return ""
}

type ListAvailableSimulinksResponse struct {
	state                  protoimpl.MessageState    `protogen:"open.v1"`
	SimulinkInfoAndRuntime []*SimulinkInfoAndRuntime `protobuf:"bytes,1,rep,name=simulink_info_and_runtime,json=simulinkInfoAndRuntime,proto3" json:"simulink_info_and_runtime,omitempty"` // 模型info信息
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ListAvailableSimulinksResponse) Reset() {
	*x = ListAvailableSimulinksResponse{}
	mi := &file_rpc_virtual_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAvailableSimulinksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAvailableSimulinksResponse) ProtoMessage() {}

func (x *ListAvailableSimulinksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_virtual_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAvailableSimulinksResponse.ProtoReflect.Descriptor instead.
func (*ListAvailableSimulinksResponse) Descriptor() ([]byte, []int) {
	return file_rpc_virtual_proto_rawDescGZIP(), []int{22}
}

func (x *ListAvailableSimulinksResponse) GetSimulinkInfoAndRuntime() []*SimulinkInfoAndRuntime {
	if x != nil {
		return x.SimulinkInfoAndRuntime
	}
	return nil
}

var File_rpc_virtual_proto protoreflect.FileDescriptor

var file_rpc_virtual_proto_rawDesc = string([]byte{
	0x0a, 0x11, 0x72, 0x70, 0x63, 0x2f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x22, 0xc5, 0x02, 0x0a, 0x19, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69,
	0x7a, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69,
	0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x64, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x44, 0x69, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x69, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x72, 0x75, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x3a, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x76, 0x61, 0x72, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x61, 0x72, 0x52,
	0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x61, 0x72, 0x73, 0x22, 0x5f, 0x0a, 0x1a, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa3, 0x02, 0x0a,
	0x0c, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3a, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x5f, 0x76, 0x61, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69,
	0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x56, 0x61, 0x72, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x61,
	0x72, 0x73, 0x22, 0xce, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x72, 0x69, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x77, 0x72, 0x69, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x09, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x61,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x61, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x61, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x76, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x61, 0x74, 0x6c, 0x61, 0x62, 0x56, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x72, 0x65, 0x61, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x1d, 0x52, 0x65,
	0x73, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0e,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x62, 0x0a, 0x1d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c,
	0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x79, 0x0a, 0x1a, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x56, 0x61, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69,
	0x6e, 0x6b, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x76,
	0x61, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x56, 0x61, 0x72, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x61, 0x72, 0x73,
	0x22, 0x60, 0x0a, 0x1b, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x69, 0x6e, 0x6b, 0x56, 0x61, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x41, 0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x66, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5f, 0x0a, 0x0e, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x56, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x76, 0x61, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x6e, 0x0a, 0x0f, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x5f, 0x76, 0x61, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x61, 0x72, 0x73, 0x22, 0xae, 0x01, 0x0a, 0x14,
	0x52, 0x75, 0x6e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c,
	0x69, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x39, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xa6, 0x01, 0x0a,
	0x15, 0x52, 0x75, 0x6e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4a, 0x0a, 0x10, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x75,
	0x6e, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x39, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x64,
	0x22, 0x7c, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x19, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x61, 0x6e, 0x64,
	0x5f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x6e, 0x64, 0x52,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x16, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x41, 0x6e, 0x64, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xa7,
	0x01, 0x0a, 0x16, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x41,
	0x6e, 0x64, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4a, 0x0a, 0x10,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x5b, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x61, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x61, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x5f, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f,
	0x76, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x69, 0x72,
	0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x56, 0x61, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x56, 0x61, 0x72, 0x73, 0x22, 0x37, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x64, 0x22,
	0x5a, 0x0a, 0x15, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8e, 0x01, 0x0a, 0x1d,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x83, 0x01, 0x0a,
	0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x61, 0x0a, 0x19, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x41, 0x6e, 0x64, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x16, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x6e, 0x64, 0x52, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x32, 0xe9, 0x06, 0x0a, 0x07, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x12, 0x6b,
	0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x29, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x15, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x12, 0x2d, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x6e, 0x0a, 0x13, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x56, 0x61, 0x72, 0x12, 0x2a, 0x2e, 0x76, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x56, 0x61, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x56, 0x61, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0d, 0x52, 0x75, 0x6e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x24, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x76, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x62, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x26, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x76, 0x69,
	0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x2b, 0x2e, 0x76,
	0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x76, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0d, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x24, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75,
	0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x12,
	0x2d, 0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e,
	0x2e, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x12,
	0x5a, 0x10, 0x2e, 0x2f, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_rpc_virtual_proto_rawDescOnce sync.Once
	file_rpc_virtual_proto_rawDescData []byte
)

func file_rpc_virtual_proto_rawDescGZIP() []byte {
	file_rpc_virtual_proto_rawDescOnce.Do(func() {
		file_rpc_virtual_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_virtual_proto_rawDesc), len(file_rpc_virtual_proto_rawDesc)))
	})
	return file_rpc_virtual_proto_rawDescData
}

var file_rpc_virtual_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_rpc_virtual_proto_goTypes = []any{
	(*InitializeSimulinkRequest)(nil),      // 0: virtualmanager.InitializeSimulinkRequest
	(*InitializeSimulinkResponse)(nil),     // 1: virtualmanager.InitializeSimulinkResponse
	(*SimulinkInfo)(nil),                   // 2: virtualmanager.SimulinkInfo
	(*ControlParam)(nil),                   // 3: virtualmanager.ControlParam
	(*OutputVar)(nil),                      // 4: virtualmanager.OutputVar
	(*ResgisterSimulinkParamRequest)(nil),  // 5: virtualmanager.ResgisterSimulinkParamRequest
	(*RegisterSimulinkParamResponse)(nil),  // 6: virtualmanager.RegisterSimulinkParamResponse
	(*RegisterSimulinkVarRequest)(nil),     // 7: virtualmanager.RegisterSimulinkVarRequest
	(*RegisterSimulinkVarResponse)(nil),    // 8: virtualmanager.RegisterSimulinkVarResponse
	(*ControlParamValue)(nil),              // 9: virtualmanager.ControlParamValue
	(*OutputVarValue)(nil),                 // 10: virtualmanager.OutputVarValue
	(*SimulinkRuntime)(nil),                // 11: virtualmanager.SimulinkRuntime
	(*RunSimulationRequest)(nil),           // 12: virtualmanager.RunSimulationRequest
	(*RunSimulationResponse)(nil),          // 13: virtualmanager.RunSimulationResponse
	(*GetSimulinkInfoRequest)(nil),         // 14: virtualmanager.GetSimulinkInfoRequest
	(*GetSimulinkInfoResponse)(nil),        // 15: virtualmanager.GetSimulinkInfoResponse
	(*SimulinkInfoAndRuntime)(nil),         // 16: virtualmanager.SimulinkInfoAndRuntime
	(*GetSimulationResultsRequest)(nil),    // 17: virtualmanager.GetSimulationResultsRequest
	(*GetSimulationResultsResponse)(nil),   // 18: virtualmanager.GetSimulationResultsResponse
	(*CloseSimulinkRequest)(nil),           // 19: virtualmanager.CloseSimulinkRequest
	(*CloseSimulinkResponse)(nil),          // 20: virtualmanager.CloseSimulinkResponse
	(*ListAvailableSimulinksRequest)(nil),  // 21: virtualmanager.ListAvailableSimulinksRequest
	(*ListAvailableSimulinksResponse)(nil), // 22: virtualmanager.ListAvailableSimulinksResponse
}
var file_rpc_virtual_proto_depIdxs = []int32{
	3,  // 0: virtualmanager.InitializeSimulinkRequest.control_params:type_name -> virtualmanager.ControlParam
	4,  // 1: virtualmanager.InitializeSimulinkRequest.output_vars:type_name -> virtualmanager.OutputVar
	2,  // 2: virtualmanager.InitializeSimulinkResponse.simulink_info:type_name -> virtualmanager.SimulinkInfo
	3,  // 3: virtualmanager.SimulinkInfo.control_params:type_name -> virtualmanager.ControlParam
	4,  // 4: virtualmanager.SimulinkInfo.output_vars:type_name -> virtualmanager.OutputVar
	3,  // 5: virtualmanager.ResgisterSimulinkParamRequest.control_params:type_name -> virtualmanager.ControlParam
	2,  // 6: virtualmanager.RegisterSimulinkParamResponse.simulink_info:type_name -> virtualmanager.SimulinkInfo
	4,  // 7: virtualmanager.RegisterSimulinkVarRequest.output_vars:type_name -> virtualmanager.OutputVar
	2,  // 8: virtualmanager.RegisterSimulinkVarResponse.simulink_info:type_name -> virtualmanager.SimulinkInfo
	10, // 9: virtualmanager.SimulinkRuntime.output_vars:type_name -> virtualmanager.OutputVarValue
	9,  // 10: virtualmanager.RunSimulationRequest.params:type_name -> virtualmanager.ControlParamValue
	2,  // 11: virtualmanager.RunSimulationResponse.simulink_info:type_name -> virtualmanager.SimulinkInfo
	11, // 12: virtualmanager.RunSimulationResponse.simulink_runtime:type_name -> virtualmanager.SimulinkRuntime
	16, // 13: virtualmanager.GetSimulinkInfoResponse.simulink_info_and_runtime:type_name -> virtualmanager.SimulinkInfoAndRuntime
	2,  // 14: virtualmanager.SimulinkInfoAndRuntime.simulink_info:type_name -> virtualmanager.SimulinkInfo
	11, // 15: virtualmanager.SimulinkInfoAndRuntime.simulink_runtime:type_name -> virtualmanager.SimulinkRuntime
	10, // 16: virtualmanager.GetSimulationResultsResponse.output_vars:type_name -> virtualmanager.OutputVarValue
	2,  // 17: virtualmanager.CloseSimulinkResponse.simulink_info:type_name -> virtualmanager.SimulinkInfo
	16, // 18: virtualmanager.ListAvailableSimulinksResponse.simulink_info_and_runtime:type_name -> virtualmanager.SimulinkInfoAndRuntime
	0,  // 19: virtualmanager.Virtual.InitializeSimulink:input_type -> virtualmanager.InitializeSimulinkRequest
	5,  // 20: virtualmanager.Virtual.RegisterSimulinkParam:input_type -> virtualmanager.ResgisterSimulinkParamRequest
	7,  // 21: virtualmanager.Virtual.RegisterSimulinkVar:input_type -> virtualmanager.RegisterSimulinkVarRequest
	12, // 22: virtualmanager.Virtual.RunSimulation:input_type -> virtualmanager.RunSimulationRequest
	14, // 23: virtualmanager.Virtual.GetSimulinkInfo:input_type -> virtualmanager.GetSimulinkInfoRequest
	17, // 24: virtualmanager.Virtual.GetSimulationResults:input_type -> virtualmanager.GetSimulationResultsRequest
	19, // 25: virtualmanager.Virtual.CloseSimulink:input_type -> virtualmanager.CloseSimulinkRequest
	21, // 26: virtualmanager.Virtual.ListAvailableSimulinks:input_type -> virtualmanager.ListAvailableSimulinksRequest
	1,  // 27: virtualmanager.Virtual.InitializeSimulink:output_type -> virtualmanager.InitializeSimulinkResponse
	6,  // 28: virtualmanager.Virtual.RegisterSimulinkParam:output_type -> virtualmanager.RegisterSimulinkParamResponse
	8,  // 29: virtualmanager.Virtual.RegisterSimulinkVar:output_type -> virtualmanager.RegisterSimulinkVarResponse
	13, // 30: virtualmanager.Virtual.RunSimulation:output_type -> virtualmanager.RunSimulationResponse
	15, // 31: virtualmanager.Virtual.GetSimulinkInfo:output_type -> virtualmanager.GetSimulinkInfoResponse
	18, // 32: virtualmanager.Virtual.GetSimulationResults:output_type -> virtualmanager.GetSimulationResultsResponse
	20, // 33: virtualmanager.Virtual.CloseSimulink:output_type -> virtualmanager.CloseSimulinkResponse
	22, // 34: virtualmanager.Virtual.ListAvailableSimulinks:output_type -> virtualmanager.ListAvailableSimulinksResponse
	27, // [27:35] is the sub-list for method output_type
	19, // [19:27] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_rpc_virtual_proto_init() }
func file_rpc_virtual_proto_init() {
	if File_rpc_virtual_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_virtual_proto_rawDesc), len(file_rpc_virtual_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_virtual_proto_goTypes,
		DependencyIndexes: file_rpc_virtual_proto_depIdxs,
		MessageInfos:      file_rpc_virtual_proto_msgTypes,
	}.Build()
	File_rpc_virtual_proto = out.File
	file_rpc_virtual_proto_goTypes = nil
	file_rpc_virtual_proto_depIdxs = nil
}

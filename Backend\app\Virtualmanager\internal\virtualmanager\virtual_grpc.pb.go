// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.19.4
// source: rpc/virtual.proto

package virtualmanager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Virtual_InitializeSimulink_FullMethodName     = "/virtualmanager.Virtual/InitializeSimulink"
	Virtual_RegisterSimulinkParam_FullMethodName  = "/virtualmanager.Virtual/RegisterSimulinkParam"
	Virtual_RegisterSimulinkVar_FullMethodName    = "/virtualmanager.Virtual/RegisterSimulinkVar"
	Virtual_RunSimulation_FullMethodName          = "/virtualmanager.Virtual/RunSimulation"
	Virtual_GetSimulinkInfo_FullMethodName        = "/virtualmanager.Virtual/GetSimulinkInfo"
	Virtual_GetSimulationResults_FullMethodName   = "/virtualmanager.Virtual/GetSimulationResults"
	Virtual_CloseSimulink_FullMethodName          = "/virtualmanager.Virtual/CloseSimulink"
	Virtual_ListAvailableSimulinks_FullMethodName = "/virtualmanager.Virtual/ListAvailableSimulinks"
)

// VirtualClient is the client API for Virtual service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Simulink模型控制服务
type VirtualClient interface {
	// 初始化模型
	InitializeSimulink(ctx context.Context, in *InitializeSimulinkRequest, opts ...grpc.CallOption) (*InitializeSimulinkResponse, error)
	// 注册控制参数
	RegisterSimulinkParam(ctx context.Context, in *ResgisterSimulinkParamRequest, opts ...grpc.CallOption) (*RegisterSimulinkParamResponse, error)
	// 注册输出参数
	RegisterSimulinkVar(ctx context.Context, in *RegisterSimulinkVarRequest, opts ...grpc.CallOption) (*RegisterSimulinkVarResponse, error)
	// 运行仿真
	RunSimulation(ctx context.Context, in *RunSimulationRequest, opts ...grpc.CallOption) (*RunSimulationResponse, error)
	// 获取模型信息
	GetSimulinkInfo(ctx context.Context, in *GetSimulinkInfoRequest, opts ...grpc.CallOption) (*GetSimulinkInfoResponse, error)
	// 获取仿真结果
	GetSimulationResults(ctx context.Context, in *GetSimulationResultsRequest, opts ...grpc.CallOption) (*GetSimulationResultsResponse, error)
	// 关闭模型
	CloseSimulink(ctx context.Context, in *CloseSimulinkRequest, opts ...grpc.CallOption) (*CloseSimulinkResponse, error)
	// 获取可用模型列表
	ListAvailableSimulinks(ctx context.Context, in *ListAvailableSimulinksRequest, opts ...grpc.CallOption) (*ListAvailableSimulinksResponse, error)
}

type virtualClient struct {
	cc grpc.ClientConnInterface
}

func NewVirtualClient(cc grpc.ClientConnInterface) VirtualClient {
	return &virtualClient{cc}
}

func (c *virtualClient) InitializeSimulink(ctx context.Context, in *InitializeSimulinkRequest, opts ...grpc.CallOption) (*InitializeSimulinkResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InitializeSimulinkResponse)
	err := c.cc.Invoke(ctx, Virtual_InitializeSimulink_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualClient) RegisterSimulinkParam(ctx context.Context, in *ResgisterSimulinkParamRequest, opts ...grpc.CallOption) (*RegisterSimulinkParamResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterSimulinkParamResponse)
	err := c.cc.Invoke(ctx, Virtual_RegisterSimulinkParam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualClient) RegisterSimulinkVar(ctx context.Context, in *RegisterSimulinkVarRequest, opts ...grpc.CallOption) (*RegisterSimulinkVarResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterSimulinkVarResponse)
	err := c.cc.Invoke(ctx, Virtual_RegisterSimulinkVar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualClient) RunSimulation(ctx context.Context, in *RunSimulationRequest, opts ...grpc.CallOption) (*RunSimulationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RunSimulationResponse)
	err := c.cc.Invoke(ctx, Virtual_RunSimulation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualClient) GetSimulinkInfo(ctx context.Context, in *GetSimulinkInfoRequest, opts ...grpc.CallOption) (*GetSimulinkInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSimulinkInfoResponse)
	err := c.cc.Invoke(ctx, Virtual_GetSimulinkInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualClient) GetSimulationResults(ctx context.Context, in *GetSimulationResultsRequest, opts ...grpc.CallOption) (*GetSimulationResultsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSimulationResultsResponse)
	err := c.cc.Invoke(ctx, Virtual_GetSimulationResults_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualClient) CloseSimulink(ctx context.Context, in *CloseSimulinkRequest, opts ...grpc.CallOption) (*CloseSimulinkResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CloseSimulinkResponse)
	err := c.cc.Invoke(ctx, Virtual_CloseSimulink_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualClient) ListAvailableSimulinks(ctx context.Context, in *ListAvailableSimulinksRequest, opts ...grpc.CallOption) (*ListAvailableSimulinksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAvailableSimulinksResponse)
	err := c.cc.Invoke(ctx, Virtual_ListAvailableSimulinks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualServer is the server API for Virtual service.
// All implementations must embed UnimplementedVirtualServer
// for forward compatibility.
//
// Simulink模型控制服务
type VirtualServer interface {
	// 初始化模型
	InitializeSimulink(context.Context, *InitializeSimulinkRequest) (*InitializeSimulinkResponse, error)
	// 注册控制参数
	RegisterSimulinkParam(context.Context, *ResgisterSimulinkParamRequest) (*RegisterSimulinkParamResponse, error)
	// 注册输出参数
	RegisterSimulinkVar(context.Context, *RegisterSimulinkVarRequest) (*RegisterSimulinkVarResponse, error)
	// 运行仿真
	RunSimulation(context.Context, *RunSimulationRequest) (*RunSimulationResponse, error)
	// 获取模型信息
	GetSimulinkInfo(context.Context, *GetSimulinkInfoRequest) (*GetSimulinkInfoResponse, error)
	// 获取仿真结果
	GetSimulationResults(context.Context, *GetSimulationResultsRequest) (*GetSimulationResultsResponse, error)
	// 关闭模型
	CloseSimulink(context.Context, *CloseSimulinkRequest) (*CloseSimulinkResponse, error)
	// 获取可用模型列表
	ListAvailableSimulinks(context.Context, *ListAvailableSimulinksRequest) (*ListAvailableSimulinksResponse, error)
	mustEmbedUnimplementedVirtualServer()
}

// UnimplementedVirtualServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVirtualServer struct{}

func (UnimplementedVirtualServer) InitializeSimulink(context.Context, *InitializeSimulinkRequest) (*InitializeSimulinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitializeSimulink not implemented")
}
func (UnimplementedVirtualServer) RegisterSimulinkParam(context.Context, *ResgisterSimulinkParamRequest) (*RegisterSimulinkParamResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterSimulinkParam not implemented")
}
func (UnimplementedVirtualServer) RegisterSimulinkVar(context.Context, *RegisterSimulinkVarRequest) (*RegisterSimulinkVarResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterSimulinkVar not implemented")
}
func (UnimplementedVirtualServer) RunSimulation(context.Context, *RunSimulationRequest) (*RunSimulationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunSimulation not implemented")
}
func (UnimplementedVirtualServer) GetSimulinkInfo(context.Context, *GetSimulinkInfoRequest) (*GetSimulinkInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulinkInfo not implemented")
}
func (UnimplementedVirtualServer) GetSimulationResults(context.Context, *GetSimulationResultsRequest) (*GetSimulationResultsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulationResults not implemented")
}
func (UnimplementedVirtualServer) CloseSimulink(context.Context, *CloseSimulinkRequest) (*CloseSimulinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseSimulink not implemented")
}
func (UnimplementedVirtualServer) ListAvailableSimulinks(context.Context, *ListAvailableSimulinksRequest) (*ListAvailableSimulinksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAvailableSimulinks not implemented")
}
func (UnimplementedVirtualServer) mustEmbedUnimplementedVirtualServer() {}
func (UnimplementedVirtualServer) testEmbeddedByValue()                 {}

// UnsafeVirtualServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VirtualServer will
// result in compilation errors.
type UnsafeVirtualServer interface {
	mustEmbedUnimplementedVirtualServer()
}

func RegisterVirtualServer(s grpc.ServiceRegistrar, srv VirtualServer) {
	// If the following call pancis, it indicates UnimplementedVirtualServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Virtual_ServiceDesc, srv)
}

func _Virtual_InitializeSimulink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitializeSimulinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).InitializeSimulink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_InitializeSimulink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).InitializeSimulink(ctx, req.(*InitializeSimulinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Virtual_RegisterSimulinkParam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResgisterSimulinkParamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).RegisterSimulinkParam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_RegisterSimulinkParam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).RegisterSimulinkParam(ctx, req.(*ResgisterSimulinkParamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Virtual_RegisterSimulinkVar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterSimulinkVarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).RegisterSimulinkVar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_RegisterSimulinkVar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).RegisterSimulinkVar(ctx, req.(*RegisterSimulinkVarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Virtual_RunSimulation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunSimulationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).RunSimulation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_RunSimulation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).RunSimulation(ctx, req.(*RunSimulationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Virtual_GetSimulinkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimulinkInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).GetSimulinkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_GetSimulinkInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).GetSimulinkInfo(ctx, req.(*GetSimulinkInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Virtual_GetSimulationResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimulationResultsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).GetSimulationResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_GetSimulationResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).GetSimulationResults(ctx, req.(*GetSimulationResultsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Virtual_CloseSimulink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseSimulinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).CloseSimulink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_CloseSimulink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).CloseSimulink(ctx, req.(*CloseSimulinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Virtual_ListAvailableSimulinks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAvailableSimulinksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualServer).ListAvailableSimulinks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Virtual_ListAvailableSimulinks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualServer).ListAvailableSimulinks(ctx, req.(*ListAvailableSimulinksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Virtual_ServiceDesc is the grpc.ServiceDesc for Virtual service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Virtual_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "virtualmanager.Virtual",
	HandlerType: (*VirtualServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InitializeSimulink",
			Handler:    _Virtual_InitializeSimulink_Handler,
		},
		{
			MethodName: "RegisterSimulinkParam",
			Handler:    _Virtual_RegisterSimulinkParam_Handler,
		},
		{
			MethodName: "RegisterSimulinkVar",
			Handler:    _Virtual_RegisterSimulinkVar_Handler,
		},
		{
			MethodName: "RunSimulation",
			Handler:    _Virtual_RunSimulation_Handler,
		},
		{
			MethodName: "GetSimulinkInfo",
			Handler:    _Virtual_GetSimulinkInfo_Handler,
		},
		{
			MethodName: "GetSimulationResults",
			Handler:    _Virtual_GetSimulationResults_Handler,
		},
		{
			MethodName: "CloseSimulink",
			Handler:    _Virtual_CloseSimulink_Handler,
		},
		{
			MethodName: "ListAvailableSimulinks",
			Handler:    _Virtual_ListAvailableSimulinks_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc/virtual.proto",
}

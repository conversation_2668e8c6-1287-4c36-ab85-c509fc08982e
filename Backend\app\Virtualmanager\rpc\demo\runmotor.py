import matlab.engine       
import numpy as np    
import matplotlib.pyplot as plt  
import time
import os

eng = matlab.engine.start_matlab()

# 添加包含电机模型的目录到MATLAB路径
model_dir = r"C:\Users\<USER>\Desktop\Haier\ElectricalModel"
eng.addpath(model_dir, nargout=0)

# 运行初始化脚本
eng.Motor_script(nargout=0)

# 获取模型名称和文件路径
model_name = 'FOCsimulation'
simu_file = os.path.join(model_dir, model_name + '.slx')

# 尝试加载模型
try:
    eng.load_system(model_name, nargout=0)
    print("模型加载成功")
except Exception as e:
    print(f"模型加载失败: {str(e)}")
    eng.quit()
    exit(1)

# 使用模型名称设置参数
init_ref = 300
eng.set_param(model_name + '/Input/Constant', 'value', str(init_ref), nargout=0)


# 设置仿真时间
sim_time = 4
eng.set_param(model_name, 'StopTime', str(sim_time), nargout=0)
# 模拟传入控制信号
pause_times = [1, 2, 3]
ref_values = [600, 900, 1200]
speed = []
t = []
# 对每个时间点分段进行仿真
start_time = 0
for pause_time, ref_val in zip(pause_times, ref_values):
    # 设置当前段的仿真时间
    eng.set_param(model_name, 'StartTime', str(start_time), nargout=0)
    eng.set_param(model_name, 'StopTime', str(pause_time), nargout=0)
    
    # 运行仿真
    # eng.sim(model_name, nargout=0)
    eng.eval("simOut = sim('FOCsimulation');", nargout=0)
    cur_speed = np.array(eng.eval('simOut.speed.Data'))
    cur_t = np.array(eng.eval('simOut.tout'))
    speed.extend(cur_speed.tolist())
    t.extend(cur_t.tolist())
    
    # 修改 ref 参数
    eng.set_param(model_name + '/Input/Constant', 'value', str(ref_val), nargout=0)
    print(f"在 {pause_time}s 时刻, 参考值更新为 {ref_val}")
    
    # 更新开始时间
    start_time = pause_time

# 运行最后一段仿真
eng.set_param(model_name, 'StartTime', str(start_time), nargout=0)
eng.set_param(model_name, 'StopTime', str(sim_time), nargout=0)
eng.eval("simOut = sim('FOCsimulation');", nargout=0)
cur_speed = np.array(eng.eval('simOut.speed.Data'))
cur_t = np.array(eng.eval('simOut.tout'))
speed.extend(cur_speed.tolist())
t.extend(cur_t.tolist())

# 方法1：直接执行 whos
print("\n--- MATLAB whos 输出 ---")
eng.eval("whos", nargout=0)
eng.eval(f'''
    sim_status = get_param("{model_name}", "SimulationStatus");
    if strcmp(sim_status, "stopped")
        disp('仿真已暂停');
    end
    ''', nargout=0)
t, speed = np.array(t), np.array(speed)



# 获取仿真结果
try:
    # out = eng.workspace['simOut']  
    # speed = np.array(eng.eval('simOut.speed.Data'))
    # t = np.array(eng.eval('simOut.tout'))
    
    # 检查数据形状
    print(f"速度数据形状: {speed.shape}, 时间数据形状: {t.shape}")
    
    # 重采样时间，确保两个长度一样
    if t.shape[0] != speed.shape[0]:
        step = t.shape[0] // speed.shape[0]
        t = t[::step]
        min_len = min(speed.shape[0], t.shape[0])
        speed, t = speed[:min_len, :], t[:min_len, :]
        print(f"重采样后形状: 速度={speed.shape}, 时间={t.shape}")
    
    # 绘制结果
    fig = plt.figure(figsize=(10, 6))
    plt.plot(t, speed, 'b-', linewidth=2)
    plt.xlabel('时间 (s)')
    plt.ylabel('转速 (rpm)')
    plt.title('电机转速响应')
    plt.grid(True)
    
    # 标记参考值变化点
    for i, (pause_time, ref_val) in enumerate(zip(pause_times, ref_values)):
        plt.axvline(x=pause_time, color='r', linestyle='--')
        plt.text(pause_time+0.1, min(speed), f'参考值: {ref_val}', 
                 verticalalignment='bottom', horizontalalignment='left')
    
    # 保存图片到用户主目录
    save_path = os.path.expanduser('~/speed_response.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"图像已保存到: {save_path}")
    plt.show()
    
except Exception as e:
    print(f"处理仿真结果时出错: {str(e)}")

# 关闭MATLAB引擎
print("仿真完成，关闭MATLAB引擎")
eng.quit()


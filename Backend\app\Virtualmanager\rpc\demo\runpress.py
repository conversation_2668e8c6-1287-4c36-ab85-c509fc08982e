import matlab.engine
import numpy as np
import matplotlib.pyplot as plt
import time
import os
import math

eng = matlab.engine.start_matlab()

# 配置参数
# 参数名称    类型     默认值      参数含义
simulate_time = 4  # single   仿真时长
sin_amplitude = 3e5  # int      输入正弦波的幅值
sin_bias = 4e5  # int      输入正弦波的偏置
sin_frequency = 2 * math.pi * 0.5  # single   输入正弦波的频率

# 添加包含压机模型的目录到MATLAB路径
model_dir = r"C:\Users\<USER>\Desktop\Haier\PressureModel"

eng.addpath(model_dir, nargout=0)

# 运行初始化脚本
eng.Q(nargout=0)

# 获取模型名称和文件路径
model_name = 'Pneumatic_pressure_systems'
simu_file = os.path.join(model_dir, model_name + '.slx')

# 尝试加载模型
try:
    eng.load_system(model_name, nargout=0)
    print("模型加载成功")
except Exception as e:
    print(f"模型加载失败: {str(e)}")
    eng.quit()
    exit(1)

# 在加载模型后，添加以下代码来获取模型结构
# print("获取模型结构...")
# eng.eval(f"""
#     mdl = '{model_name}';
#     blocks = find_system(mdl, 'Type', 'Block');
#     disp('模型中的所有模块:');
#     for i = 1:length(blocks)
#         disp(blocks{{i}});
#     end
# """, nargout=0)

# 设置仿真时间 - 使用配置的参数
sim_time = simulate_time
eng.set_param(model_name, 'StopTime', str(sim_time), nargout=0)

# 设置正弦波参数
eng.workspace['sin_amplitude'] = float(sin_amplitude)
eng.workspace['sin_bias'] = float(sin_bias)
eng.workspace['sin_frequency'] = float(sin_frequency)
eng.eval(f"set_param('{model_name}/Sine Wave2', 'Amplitude', 'sin_amplitude')", nargout=0)
eng.eval(f"set_param('{model_name}/Sine Wave2', 'Bias', 'sin_bias')", nargout=0)
eng.eval(f"set_param('{model_name}/Sine Wave2', 'Frequency', 'sin_frequency')", nargout=0)

press = []
pressR = []
t = []

start_time = 0
eng.set_param(model_name, 'StartTime', str(start_time), nargout=0)
eng.eval("simOut = sim('Pneumatic_pressure_systems');", nargout=0)
cur_pressR = np.array(eng.eval('simOut.Pr.Data'))
cur_press = np.array(eng.eval('simOut.P.Data'))    
cur_t = np.array(eng.eval('simOut.tout'))
pressR.extend(cur_pressR.tolist())
press.extend(cur_press.tolist())
t.extend(cur_t.tolist())

# 方法1：直接执行 whos
print("\n--- MATLAB whos 输出 ---")
eng.eval("whos", nargout=0)
eng.eval(f'''
    sim_status = get_param("{model_name}", "SimulationStatus");
    if strcmp(sim_status, "stopped")
        disp('仿真已暂停');
    end
    ''', nargout=0)
t, press,pressR = np.array(t), np.array(press), np.array(pressR)

# 获取仿真结果
try:

    # 检查数据形状
    print(f"压力数据形状: {press.shape}, 压力参考数据形状: {pressR.shape}, 时间数据形状: {t.shape}")

    # 重采样时间，确保所有数据长度一致
    if t.shape[0] != press.shape[0] or t.shape[0] != len(pressR):
        step = t.shape[0] // min(press.shape[0], len(pressR))
        t = t[::step]
        min_len = min(press.shape[0], t.shape[0], len(pressR))
        press = press[:min_len]
        pressR = pressR[:min_len]
        t = t[:min_len]
        print(f"重采样后形状: 压力={press.shape}, 参考压力={len(pressR)}, 时间={t.shape}")

    # 绘制结果
    fig = plt.figure(figsize=(12, 8))
    plt.plot(t, press, 'b-', linewidth=2, label='P')
    plt.plot(t, pressR, 'r--', linewidth=2, label='Pr')
    plt.xlabel('Time(s)')
    plt.ylabel('Pressure(MPa)')
    plt.title('Pressure Response')
    plt.grid(True)
    plt.legend()

    plt.tight_layout()

    # 保存图片到用户主目录
    save_path = os.path.expanduser('~/pressure_response.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"图像已保存到: {save_path}")
    plt.show()

except Exception as e:
    print(f"处理仿真结果时出错: {str(e)}")

# 关闭MATLAB引擎
print("仿真完成，关闭MATLAB引擎")
eng.quit()


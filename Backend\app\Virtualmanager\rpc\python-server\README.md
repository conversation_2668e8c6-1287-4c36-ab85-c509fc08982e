# Simulink模型控制系统 - 详细设计文档

## 项目概述
本项目实现了一个基于Python的Simulink模型控制系统，包含基类和特定模型的子类实现，以及基于gRPC的通信接口。系统提供了一套完整的API用于控制Simulink模型的初始化、参数设置、仿真运行和结果获取。

## 架构设计
系统采用分层架构设计：
1. **基础层(SimulinkBase)**: 提供与MATLAB/Simulink交互的基础功能
2. **模型实现层**: 继承自SimulinkBase，实现特定模型的控制逻辑
3. **服务层**: 基于gRPC提供远程调用接口
4. **客户端层**: 通过gRPC与服务层交互

## 项目结构

```
├── simulink_base.py       # Simulink模型控制基类
├── pressure_model.py      # 压力模型控制类
├── motor_model.py         # 电机模型控制类
├── simulink_service.proto # gRPC服务定义
├── simulink_server.py     # gRPC服务器实现
└── README.md              # 项目说明文档
```

## 功能特性

- **通用基类设计**：提供了`SimulinkBase`基类，实现了Simulink模型的通用控制功能
- **特定模型实现**：基于基类实现了`PressureModel`和`MotorModel`两个特定模型的控制类
- **参数配置**：支持配置模型目录、模型名称、初始化脚本、仿真时间等
- **状态管理**：支持有状态和无状态两种模式，有状态模式会维持上一次的控制量
- **实时通信**：基于gRPC实现了客户端和服务器之间的实时通信
- **进度监控**：支持仿真进度条显示
- **状态查询**：支持查询模型状态、控制参数和输出变量
- **结果可视化**：支持绘制和保存仿真结果图表

## 安装依赖

```bash
pip install numpy matplotlib grpcio grpcio-tools matlab.engine
```

## 编译gRPC代码

```bash
python -m grpc_tools.protoc -I=. --python_out=. --grpc_python_out=. simulink_service.proto
```

## 使用方法

### 1. 启动服务器

```bash
python simulink_server.py
```

### 2. 直接使用模型类

```python
# 示例用法
pressure_model = PressureModel(
    model_dir=r"C:\Users\<USER>\Desktop\Haier\PressureModel",
    model_name='Pneumatic_pressure_systems',
    init_script='Q',
    simulate_time=4.0
    )
pressure_model.initialize()

# 注册控制参数
pressure_model.register_control_param(
    param_name="sin_amplitude",
    block_path="Sine Wave2",
    param_type="Amplitude",
    default_value=3e5,
    description="输入正弦波的幅值"
)

pressure_model.register_control_param(
    param_name="sin_bias",
    block_path="Sine Wave2",
    param_type="Bias",
    default_value=4e5,
    description="输入正弦波的偏置"
)

pressure_model.register_control_param(
    param_name="sin_frequency",
    block_path="Sine Wave2",
    param_type="Frequency",
    default_value=2 * math.pi * 0.5,
    description="输入正弦波的频率"
)

# 注册输出变量
pressure_model.register_output_var(
    var_name="pressure",
    matlab_var="P",
    description="压力值"
)

pressure_model.register_output_var(
    var_name="pressure_ref",
    matlab_var="Pr",
    description="压力参考值"
)

# 使用压力曲线运行仿真
# 转换为 {参数名: {时间点: 参数值}} 格式
pressure_profile = {
    "sin_amplitude": {0: 3e5, 1: 4e5, 2: 5e5, 3: 6e5},
    "sin_bias": {0: 4e5, 1: 5e5, 2: 6e5, 3: 7e5},
    "sin_frequency": {
        0: 2 * math.pi * 0.5,
        1: 2 * math.pi * 0.6,
        2: 2 * math.pi * 0.7,
        3: 2 * math.pi * 0.8
    }
}

pressure_model.run_simulation(pressure_profile)

# 关闭模型
pressure_model.close()
```

## 自定义新模型

要添加新的模型控制类，只需继承`SimulinkBase`基类并实现特定功能：

```python
from simulink_base import SimulinkBase

class MyCustomModel(SimulinkBase):
    def __init__(self, model_dir, model_name="MyModel", init_script=None, simulate_time=10.0):
        super().__init__(model_dir, model_name, init_script, simulate_time, stateful=True)
        
        # 注册控制参数
        self.register_control_param(
            param_name="my_param",
            block_path="MyBlock",
            param_type="value",
            default_value=100,
            description="我的参数"
        )
        
        # 注册输出变量
        self.register_output_var(
            var_name="my_output",
            matlab_var="output.Data",
            description="我的输出"
        )
    
    # 实现特定功能...
```

## gRPC接口详细说明

### 版本历史

#### 版本：v1.0
发版日期：2023-11-01

#### 新增功能
| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| InitializeSimulink | 初始化Simulink模型 | simulink_service.proto |
| RunSimulation | 运行仿真并获取结果 | simulink_service.proto |
| GetSimulationResults | 获取仿真结果数据 | simulink_service.proto |

### 接口定义

#### 1. InitializeSimulink
**功能**: 初始化Simulink模型

**请求参数**:
| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_type | string | 模型类型 (e.g., "pressure", "motor") |
| simulink_dir | string | 模型目录 |
| simulink_name | string | 模型名称 |
| init_script | string | 初始化脚本 |
| run_time | float | 仿真时长 |
| control_params | repeated ControlParam | 控制参数列表 |
| output_vars | repeated OutputVar | 输出变量列表 |

**响应参数**:
| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info | SimulinkInfo | 模型信息 |

#### 2. RunSimulation
**功能**: 运行仿真

**请求参数**:
| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型ID |
| start_time | float | 开始时间 |
| stop_time | float | 结束时间 |
| params | repeated ControlParamValue | 仿真参数 |

**响应参数**:
| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info | SimulinkInfo | 模型信息 |
| simulink_runtime | SimulinkRuntime | 模型运行信息 |

完整接口定义请参考simulink_service.proto文件

## 注意事项

1. 确保已安装MATLAB和Simulink，并且Python能够通过`matlab.engine`访问MATLAB
2. 模型目录路径需要根据实际情况进行调整
3. 初始化脚本名称不包含`.m`后缀
4. 在使用gRPC通信时，确保服务器已启动并且端口可访问
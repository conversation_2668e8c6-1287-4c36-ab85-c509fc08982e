import os
import numpy as np
import matplotlib.pyplot as plt
# 添加中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False     # 用来正常显示负号
import time
from typing import Dict, List, Any, Optional, Union, Tuple
from tqdm import tqdm
import threading
import time

from simulink_base import SimulinkBase

class MotorModel(SimulinkBase):
    """
    电机模型控制类
    继承自SimulinkBase，实现电机模型的特定功能
    """
    def __init__(self, 
                 model_dir: str, 
                 model_name: str = 'FOCsimulation',
                 init_script: str = 'Motor_script',
                 simulate_time: float = 4.0):
        """
        初始化电机模型控制类
        
        Args:
            model_dir: 模型目录
            model_name: 模型名称，默认为'FOCsimulation'
            init_script: 初始化脚本，默认为'Motor_script'
            simulate_time: 仿真时长，默认为4.0
        """
        super().__init__(model_dir, model_name, init_script, simulate_time)
    
    
    def plot_speed_response(self, speed_profile: Optional[Dict[float, float]] = None, 
                           save_path: Optional[str] = None) -> None:
        """
        绘制速度响应曲线
        
        Args:
            speed_profile: 速度曲线，用于标记参考值变化点
            save_path: 保存路径，如果为None则不保存
        """
        if not self.sim_results or 'time' not in self.sim_results or 'speed' not in self.sim_results:
            self.logger.error("没有仿真结果可绘制")
            return
        
        try:
            t = np.array(self.sim_results['time'])
            speed = np.array(self.sim_results['speed'])
            
            
            
            # 确保数据长度一致
            if t.shape[0] != speed.shape[0]:
                self.logger.info(f"数据长度不一致: 时间={t.shape[0]}, 速度={speed.shape[0]}")
                # 重采样到较短的长度
                min_len = min(t.shape[0], speed.shape[0])
                if t.shape[0] > min_len:
                    step = t.shape[0] // min_len
                    t = t[::step]
                if speed.shape[0] > min_len:
                    step = speed.shape[0] // min_len
                    speed = speed[::step]
                
                # 再次截断到相同长度
                min_len = min(t.shape[0], speed.shape[0])
                t = t[:min_len]
                speed = speed[:min_len]
                self.logger.info(f"重采样后形状: 时间={t.shape}, 速度={speed.shape}")
            
            
            # 绘制结果
            fig = plt.figure(figsize=(10, 6))
            plt.plot(t, speed, 'b-', linewidth=2, label='实际转速')
            plt.xlabel('时间 (s)')
            plt.ylabel('转速 (rpm)')
            plt.title('电机转速响应')
            plt.grid(True)
            plt.legend()
            
            # 标记参考值变化点
            if speed_profile:
                min_speed = np.min(speed)
                for time_point, ref_val in speed_profile.items():
                    if time_point > 0:  # 不标记初始点
                        plt.axvline(x=time_point, color='r', linestyle='--')
                        plt.text(time_point+0.1, min_speed, f'参考值: {ref_val}', 
                                verticalalignment='bottom', horizontalalignment='left')
            
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"图像已保存到: {save_path}")
            
            plt.show()
        except Exception as e:
            self.logger.error(f"绘制速度响应曲线失败: {str(e)}")

if __name__ == "__main__":
    # 示例用法
    motor_model = MotorModel(model_dir= r"C:\Users\<USER>\Desktop\Haier\ElectricalModel",
                             model_name='FOCsimulation',
                             init_script='Motor_script',
                             simulate_time=4.0)
    motor_model.initialize()
    
    # 注册控制参数
    motor_model.register_control_param(
            param_name="speed_ref",
            block_path="Input/Constant",
            param_type="value",
            default_value=300,
            description="电机转速参考值"
        )
    
    # 注册输出变量
    motor_model.register_output_var(
            var_name="speed",
            matlab_var="speed",
            description="speed"
        )
    
    # 启动仿真并显示进度条s
    speed_profile ={
        "speed_ref": {0: 0, 1: 100, 2: 200, 3: 300}
    }


    motor_model.run_simulation(speed_profile)
    
    # 绘制结果
    motor_model.plot_speed_response(speed_profile)


#     {
#     "simulink_type": "motor",
#     "simulink_dir": "C:\Users\<USER>\Desktop\Haier\ElectricalModel",
#     "simulink_name": "FOCsimulation",
#     "init_script": "4",
#     "run_time": 1725135029177,
#     "control_params": [
#         {
#             "param_name": "speed_ref",
#             "block_path": "Input/Constant",
#             "param_type": "value",
#             "default_value": "300",
#             "description": "电机转速参考值",
#             "writable": true
#         }
#     ],
#     "output_vars": [
#         {
#             "var_name": "speed",
#             "matlab_var": "speed",
#             "description": "speed",
#             "readable": true
#         }
#     ]
# }
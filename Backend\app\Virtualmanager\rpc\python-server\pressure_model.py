import os
import numpy as np
import matplotlib.pyplot as plt
# 添加中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False     # 用来正常显示负号
import math
import time
from typing import Dict, List, Any, Optional, Union, Tuple

from simulink_base import SimulinkBase

class PressureModel(SimulinkBase):
    """
    压力模型控制类
    继承自SimulinkBase，实现压力模型的特定功能
    """
    def __init__(self, 
                 model_dir: str, 
                 model_name: str = 'Pneumatic_pressure_systems',
                 init_script: str = 'Q',
                 simulate_time: float = 4.0):
        """
        初始化压力模型控制类
        
        Args:
            model_dir: 模型目录
            model_name: 模型名称，默认为'Pneumatic_pressure_systems'
            init_script: 初始化脚本，默认为'Q'
            simulate_time: 仿真时长，默认为4.0
        """
        super().__init__(model_dir, model_name, init_script, simulate_time)

    def plot_pressure_response(self, pressure_profile: Optional[Dict[str, Dict[float, float]]] = None, 
                             save_path: Optional[str] = None) -> None:
        """
        绘制压力响应曲线
        
        Args:
            pressure_profile: 压力曲线，格式为 {参数名: {时间点: 参数值}}
            save_path: 保存路径，如果为None则不保存
        """
        if not self.sim_results or 'time' not in self.sim_results or 'pressure' not in self.sim_results:
            self.logger.error("没有仿真结果可绘制")
            return
        
        try:
            t = self.sim_results['time']
            press = self.sim_results['pressure']
            pressR = self.sim_results['pressure_ref']
            
            # 确保数据长度一致
            if t.shape[0] != press.shape[0] or t.shape[0] != pressR.shape[0]:
                step = t.shape[0] // min(press.shape[0], pressR.shape[0]) if t.shape[0] > min(press.shape[0], pressR.shape[0]) else 1
                t = t[::step]
                min_len = min(press.shape[0], t.shape[0], pressR.shape[0])
                press = press[:min_len]
                pressR = pressR[:min_len]
                t = t[:min_len]
                self.logger.info(f"重采样后形状: 压力={press.shape}, 参考压力={pressR.shape}, 时间={t.shape}")
            
            # 绘制结果
            fig = plt.figure(figsize=(10, 6))
            plt.plot(t, press, 'b-', linewidth=2, label='实际压力')
            plt.plot(t, pressR, 'r--', linewidth=2, label='参考压力')
            plt.xlabel('时间 (s)')
            plt.ylabel('压力 (Pa)')
            plt.title('压力响应曲线')
            plt.grid(True)
            plt.legend()
            
            # 标记参数变化点
            if pressure_profile:
                # 获取所有时间点并排序
                time_points = set()
                for param_times in pressure_profile.values():
                    time_points.update(param_times.keys())
                time_points = sorted(time_points)
                
                # 标记每个时间点的参数变化
                for time_point in time_points:
                    if time_point > 0:  # 不标记初始点
                        plt.axvline(x=time_point, color='g', linestyle='--')
                        # 收集该时间点的所有参数变化
                        params = {}
                        for param_name, time_values in pressure_profile.items():
                            if time_point in time_values:
                                params[param_name] = time_values[time_point]
                        param_text = "\n".join([f"{k}={v}" for k, v in params.items()])
                        plt.text(time_point+0.1, min(press), f'参数变化:\n{param_text}', 
                                verticalalignment='bottom', horizontalalignment='left')
            
            plt.tight_layout()
            
            # 保存图片
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"图像已保存到: {save_path}")
            
            plt.show()
        except Exception as e:
            self.logger.error(f"绘制压力响应曲线失败: {str(e)}")
            

if __name__ == "__main__":
    # 示例用法
    pressure_model = PressureModel(
        model_dir=r"C:\Users\<USER>\Desktop\Haier\PressureModel",
        model_name='Pneumatic_pressure_systems',
        init_script='Q',
        simulate_time=4.0
        )
    pressure_model.initialize()
    
    # 注册控制参数
    pressure_model.register_control_param(
        param_name="sin_amplitude",
        block_path="Sine Wave2",
        param_type="Amplitude",
        default_value=3e5,
        description="输入正弦波的幅值"
    )
    
    pressure_model.register_control_param(
        param_name="sin_bias",
        block_path="Sine Wave2",
        param_type="Bias",
        default_value=4e5,
        description="输入正弦波的偏置"
    )
    
    pressure_model.register_control_param(
        param_name="sin_frequency",
        block_path="Sine Wave2",
        param_type="Frequency",
        default_value=2 * math.pi * 0.5,
        description="输入正弦波的频率"
    )
    
    # 注册输出变量
    pressure_model.register_output_var(
        var_name="pressure",
        matlab_var="P",
        description="压力值"
    )
    
    pressure_model.register_output_var(
        var_name="pressure_ref",
        matlab_var="Pr",
        description="压力参考值"
    )
    
    # 使用压力曲线运行仿真
    # 转换为 {参数名: {时间点: 参数值}} 格式
    pressure_profile = {
        "sin_amplitude": {0: 3e5, 1: 4e5, 2: 5e5, 3: 6e5},
        "sin_bias": {0: 4e5, 1: 5e5, 2: 6e5, 3: 7e5},
        "sin_frequency": {
            0: 2 * math.pi * 0.5,
            1: 2 * math.pi * 0.6,
            2: 2 * math.pi * 0.7,
            3: 2 * math.pi * 0.8
        }
    }
    
    pressure_model.run_simulation(pressure_profile)
    pressure_model.plot_pressure_response(pressure_profile)
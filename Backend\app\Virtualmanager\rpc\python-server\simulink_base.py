import matlab.engine
import numpy as np
import matplotlib.pyplot as plt
# 添加中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False     # 用来正常显示负号
import time
import os
import math
import threading
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

class SimulinkBase:
    """
    Simulink模型控制基类
    用于控制Simulink模型的仿真、参数设置和结果获取
    """
    # 定义模型状态常量
    STATUS_INITIALIZING = "initializing"
    STATUS_READY = "ready"
    STATUS_RUNNING = "running"
    STATUS_STOPPED = "stopped"
    STATUS_ERROR = "error"
    STATUS_PENDING = "pending"
    def __init__(self, 
                 model_dir: str, 
                 model_name: str, 
                 init_script: Optional[str] = None,
                 simulate_time: float = 10.0):
        """
        初始化Simulink模型控制基类
        
        Args:
            model_dir: Simulink模型所在目录
            model_name: Simulink模型名称
            init_script: 初始化脚本名称（不含.m后缀）
            simulate_time: 仿真时长
        """
        self.model_dir = model_dir
        self.model_name = model_name
        self.init_script = init_script
        self.simulate_time = simulate_time
        
        # 模型文件路径
        self.simu_file = os.path.join(model_dir, model_name + '.slx')
        
        # 仿真状态
        self.is_initialized = False
        self.is_running = False
        self.progress = 0.0
        
        # 仿真引擎
        self.eng = None
        
        # 控制参数和输出变量配置
        self.control_params = {}
        self.output_vars = {}
        
        # 仿真结果
        self.sim_results = {}
        
        # 日志配置
        logging.basicConfig(level=logging.INFO, 
                            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(f"Simulink_{model_name}")
    
    def initialize(self) -> bool:
        """
        初始化Simulink模型
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 启动MATLAB引擎
            self.eng = matlab.engine.start_matlab()
            self.logger.info("MATLAB引擎启动成功")
            
            # 添加模型目录到MATLAB路径
            self.eng.addpath(self.model_dir, nargout=0)
            self.logger.info(f"添加模型目录: {self.model_dir}")
            
            # 运行初始化脚本（如果有）
            if self.init_script:
                self.eng.eval(f"{self.init_script}", nargout=0)
                self.logger.info(f"运行初始化脚本: {self.init_script}")
            
            # 加载模型
            self.eng.load_system(self.model_name, nargout=0)
            self.logger.info(f"模型 {self.model_name} 加载成功")
            
            # 设置仿真时间
            self.eng.set_param(self.model_name, 'StopTime', str(self.simulate_time), nargout=0)
            
            self.is_initialized = True
            return True
        except Exception as e:
            self.logger.error(f"初始化失败: {str(e)}")
            if self.eng:
                self.eng.quit()
                self.eng = None
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict: 模型信息字典
        """
        if not self.is_initialized or not self.eng:
            self.logger.error("模型未初始化")
            return {"error": "模型未初始化"}
        
        try:
            # 获取模型结构 - 使用MATLAB引擎方法
            blocks = self.eng.find_system(self.model_name, 'Type', 'Block')
            block_types = self.eng.get_param(blocks, 'BlockType')
            
            # 获取模型参数
            params = self.eng.get_param(self.model_name, 'ObjectParameters')
            
            # 转换为Python字典
            blocks = [str(b) for b in blocks]
            block_types = [str(t) for t in block_types]
            
            return {
                "model_name": self.model_name,
                "model_dir": self.model_dir,
                "blocks": dict(zip(blocks, block_types)),
                "is_initialized": self.is_initialized,
                "is_running": self.is_running,
                "simulate_time": self.simulate_time,
                "control_params": self.control_params,
                "output_vars": self.output_vars
            }
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {str(e)}")
            return {"error": f"获取模型信息失败: {str(e)}"}
    
    def register_control_param(self, param_name: str, block_path: str, param_type: str, 
                              default_value: Any, description: str = "") -> None:
        """
        注册控制参数
        
        Args:
            param_name: 参数名称
            block_path: 模块路径
            param_type: 参数类型
            default_value: 默认值
            description: 参数描述
            
        Raises:
            ValueError: 如果block路径不存在
        """
        # 使用get_model_info检查block是否存在
        model_info = self.get_model_info()
        if "error" in model_info:
            raise ValueError(f"获取模型信息失败: {model_info['error']}")
            
        full_path = f"{self.model_name}/{block_path}"
        if full_path not in model_info["blocks"]:
            raise ValueError(f"Block路径不存在: {full_path}")
            
        self.control_params[param_name] = {
            "block_path": block_path,
            "param_type": param_type,
            "default_value": default_value,
            "description": description,
            "writable": True
        }
    

    def register_output_var(self, var_name: str, matlab_var: str, 
                           description: str = "", readable: bool = True) -> None:
        """
        注册输出变量
        
        Args:
            var_name: 变量名称
            matlab_var: MATLAB变量路径
            description: 变量描述
            readable: 是否可读
        """
        self.output_vars[var_name] = {
            "matlab_var": matlab_var,
            "description": description,
            "readable": readable
        }
    
    def set_param(self, param_name: str, value: Any) -> bool:
        """
        设置模型参数
        
        Args:
            param_name: 参数名称
            value: 参数值
            
        Returns:
            bool: 设置是否成功
        """
        if not self.is_initialized or not self.eng:
            self.logger.error("模型未初始化")
            return False
        
        if param_name not in self.control_params:
            self.logger.error(f"未知参数: {param_name}")
            return False
        
        param_info = self.control_params[param_name]
        if not param_info["writable"]:
            self.logger.error(f"参数不可写: {param_name}")
            return False
        
        try:
            block_path = param_info["block_path"]
            param_type = param_info["param_type"]
            
            # 根据参数类型进行转换
            if param_type == "workspace":
                # 设置MATLAB工作区变量
                self.eng.workspace[param_name] = float(value)
            else:
                # 设置模块参数
                full_path = f"{self.model_name}/{block_path}"
                self.eng.set_param(full_path, param_type, str(value), nargout=0)
            

                
            self.logger.info(f"设置参数 {param_name} = {value}")
            return True
        except Exception as e:
            self.logger.error(f"设置参数 {param_name} 失败: {str(e)}")
            return False

    #TODO:更新逻辑有问题
    def run_simulation_and_update_progress(self, params: Dict[str, Dict[float, float]]) -> bool:
        """运行仿真并更新进度
        Args:
            params: 参数配置字典，格式为 {参数名: {时间点: 参数值}}
        Returns:
            bool: 仿真是否成功
        """
        if not self.is_initialized or not self.eng:
            self.logger.error("模型未初始化")
            return False
        
        if self.is_running:
            self.logger.warning("仿真已在运行中")
            return False
        
        try:
            # 创建并启动进度更新线程
            progress_thread = threading.Thread(
                target=self._update_progress,
                args=(0, self.simulate_time),
                daemon=True
            )
            
            # 运行仿真
            progress_thread.start()
            run_status = self.run_simulation(params)
            
            # 等待进度更新线程结束
            progress_thread.join(timeout=1.0)
            
            return run_status
        except Exception as e:
            self.logger.error(f"运行仿真失败: {str(e)}")
            return False

    def run_simulation(self, params: Dict[str, Dict[float, float]]) -> bool:
        """
        运行仿真
        
        Args:
            params: 参数配置字典，格式为 {参数名: {时间点: 参数值}}
            
        Returns:
            bool: 仿真是否成功
        """
        if not self.is_initialized or not self.eng:
            self.logger.error("模型未初始化")
            return False
        
        if self.is_running:
            self.logger.warning("仿真已在运行中")
            return False
        
        try:
            self.progress = 0.0
            
            # 检查参数有效性
            for param_name, time_values in params.items():
                if param_name not in self.control_params:
                    self.logger.error(f"未知参数: {param_name}")
                    return False
                if not self.control_params[param_name]["writable"]:
                    self.logger.error(f"参数不可写: {param_name}")
                    return False
            
            # 转换参数格式：从 {参数名: {时间点: 值}} 转为 {时间点: {参数名: 值}}
            time_params = {}
            for param_name, time_values in params.items():
                for time_point, value in time_values.items():
                    if time_point not in time_params:
                        time_params[time_point] = {}
                    time_params[time_point][param_name] = value
            
            # 获取所有时间点并排序
            timestamps = sorted(time_params.keys())
            if not timestamps:
                self.logger.info("没有输入控制量，将以默认值运行")
                # 使用默认参数运行完整仿真
                sim_data = self.run_simulation_segment(0, self.simulate_time)
                if not sim_data:
                    return False
                
                # 获取结果
                self.sim_results = self.get_simulation_results(sim_data)
                return True
            
            # 清空结果
            self.sim_results = {}
            
            # 对每个时间点分段进行仿真
            start_time = 0
            
            # 设置初始参数（时间点为0的参数）
            if 0 in time_params:
                for param_name, value in time_params[0].items():
                    self.set_param(param_name, value)
            
            # 按时间点分段仿真
            for time_point in timestamps:
                if time_point == 0:
                    continue  # 已经设置过初始参数
                
                # 设置当前段的所有参数
                for param_name, value in time_params[time_point].items():
                    self.set_param(param_name, value)
                
                # 运行仿真段
                param_info = ", ".join([f"{name}={value}" for name, value in time_params[time_point].items()])
                self.logger.info(f"运行仿真: {start_time}s -> {time_point}s, 参数: {param_info}")
                sim_data = self.run_simulation_segment(start_time, time_point)
                if not sim_data:
                    return False
                
                # 获取并累积结果
                cur_sim_results = self.get_simulation_results(sim_data)
                for key, value in cur_sim_results.items():
                    if key not in self.sim_results:
                        self.sim_results[key] = value.tolist()
                    else:
                        self.sim_results[key].extend(value.tolist())
                
                # 更新开始时间
                start_time = time_point
            
            # 运行最后一段仿真（如果需要）
            if start_time < self.simulate_time:
                self.logger.info(f"运行最后一段仿真: {start_time}s -> {self.simulate_time}s")
                sim_data = self.run_simulation_segment(start_time, self.simulate_time)
                if not sim_data:
                    return False
                
                # 获取并累积结果
                cur_sim_results = self.get_simulation_results(sim_data)
                for key, value in cur_sim_results.items():
                    if key not in self.sim_results:
                        self.sim_results[key] = value.tolist()
                    else:
                        self.sim_results[key].extend(value.tolist())
            
            self.logger.info("仿真完成")
            return True
        except Exception as e:
            self.logger.error(f"仿真失败: {str(e)}")
            return False

    def run_simulation_segment(self, start_time: float, stop_time: float) -> Dict[str, Any]:
        """
        运行单段仿真并返回基础结果
        
        Args:
            start_time: 开始时间
            stop_time: 结束时间
            
        Returns:
            Dict[str, Any]: 包含仿真结果的字典
        """
        if not self.is_initialized or not self.eng:
            self.logger.error("模型未初始化")
            return {}
        if self.is_running:
            self.logger.warning("仿真已在运行中")
            return {}
        
        try:
            self.is_running = True
            # 设置仿真时间
            self.eng.set_param(self.model_name, 'StartTime', str(start_time), nargout=0)
            self.eng.set_param(self.model_name, 'StopTime', str(stop_time), nargout=0)
            
            # 运行仿真
            self.logger.info(f"运行仿真段: {start_time}s -> {stop_time}s")
            self.eng.eval(f"simOut = sim('{self.model_name}');", nargout=0)
            
            # 获取基础结果
            sim_time = np.array(self.eng.eval('simOut.tout'))
            self.is_running = False
            return {
                'time': sim_time,
                'simOut': self.eng.workspace['simOut']
            }
        except Exception as e:
            self.is_running = False
            self.logger.error(f"运行仿真段失败: {str(e)}")
            return {}

    def _update_progress(self, start_time: float, stop_time: float) -> None:
        """
        更新仿真进度
        
        Args:
            start_time: 开始时间
            stop_time: 结束时间
        """
        total_time = stop_time - start_time
        while self.is_running:
            try:
                # 获取当前仿真时间
                current_time = float(self.eng.get_param(self.model_name, 'SimulationTime'))
                self.progress = min(1.0, max(0.0, (current_time - start_time) / total_time))
                time.sleep(0.05)
            except Exception as e:
                self.logger.error(f"更新进度失败: {str(e)}")
                break
    
    
    def get_results(self) -> Dict[str, np.ndarray]:
        """
        获取仿真结果
        
        Returns:
            Dict: 仿真结果字典
        """
        return self.sim_results
    
    def get_progress(self) -> float:
        """
        获取仿真进度
        
        Returns:
            float: 仿真进度 (0.0-1.0)
        """
        return self.progress
    
    def get_status(self) -> str:
        """
        获取仿真状态
        
        Returns:
            系统运行状态
        """
        if not self.is_initialized:
            return self.STATUS_INITIALIZING

        if self.is_running:
            return self.STATUS_RUNNING
        
        # 如果模型已初始化，获取MATLAB仿真状态
        if self.is_initialized and self.eng:
            try:
                #TODO: 状态待定
                matlab_status = self.eng.eval(f'''get_param("{self.model_name}", "SimulationStatus");''', nargout=1)
                if matlab_status == 'stopped':
                    return self.STATUS_STOPPED
                elif matlab_status == 'running':
                    return self.STATUS_RUNNING
            except:
                return self.STATUS_ERROR
        return self.STATUS_PENDING
        
        return status
    
    def close(self) -> None:
        """
        关闭MATLAB引擎并清理资源
        """
        if self.eng:
            try:
                # 先关闭模型
                if self.is_initialized:
                    try:
                        self.eng.close_system(self.model_name, 0, nargout=0)  # 0表示不保存更改
                        self.logger.info(f"模型 {self.model_name} 已关闭")
                    except Exception as e:
                        self.logger.warning(f"关闭模型失败: {str(e)}")
                
                # 再关闭MATLAB引擎
                self.eng.quit()
                self.logger.info("MATLAB引擎已关闭")
            except Exception as e:
                self.logger.error(f"关闭MATLAB引擎失败: {str(e)}")
            finally:
                self.eng = None
                self.is_initialized = False
                self.is_running = False
                # 清理仿真结果
                self.sim_results = {}
    
    def __del__(self):
        """
        析构函数，确保MATLAB引擎被关闭
        """
        self.close()

    def get_simulation_results(self, sim_data) -> Dict[str, np.ndarray]:
        """
        收集单段仿真结果
        
        Args:
            sim_data: 仿真数据字典
            
        Returns:
            Dict[str, np.ndarray]: 处理后的结果字典
        """
        if not sim_data:
            return {}
            
        try:
            results = {}
            results['time'] = sim_data['time']
            
            # 获取各输出变量数据
            for var_name, var_info in self.output_vars.items():
                if var_info["readable"]:
                    matlab_var = var_info["matlab_var"]
                    data = np.array(self.eng.eval(f'simOut.{matlab_var}.Data'))
                    results[var_name] = data
            return results
        except Exception as e:
            self.logger.error(f"收集结果失败: {e}")
            return {}
import os
import uuid
import time
import grpc
import logging
from concurrent import futures
from typing import Dict, List, Any, Optional

# 导入生成的gRPC代码
import simulink_service_pb2
import simulink_service_pb2_grpc

# 导入模型类
from simulink_base import SimulinkBase
from motor_model import MotorModel
from pressure_model import PressureModel

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("SimulinkServer")

class SimulinkServicer(simulink_service_pb2_grpc.SimulinkServiceServicer):
    """
    Simulink服务实现类
    处理gRPC请求并调用相应的模型方法
    """
    def __init__(self):
        """
        初始化服务
        """
        self.models = {}  # 存储模型实例，键为模型ID
        self.model_types = {
            "base": SimulinkBase,
            "motor": MotorModel,
            "pressure": PressureModel
        }
        logger.info("Simulink服务已初始化")

    def _build_simulink_info(self, simulink_id: str, simulink_type: str, status: str, simulate_time: float, model=None, control_params=None, output_vars=None) -> simulink_service_pb2.SimulinkInfo:
        """
        构建SimulinkInfo对象的辅助函数
        
        Args:
            simulink_id: 模型ID
            simulink_type: 模型类型
            status: 模型状态
            simulate_time: 仿真时间
            model: 模型实例，如果为None则返回空的SimulinkInfo
            control_params: 控制参数列表，如果为None则从model中获取
            output_vars: 输出变量列表，如果为None则从model中获取
            
        Returns:
            SimulinkInfo对象
        """
        if not model:
            return simulink_service_pb2.SimulinkInfo(
                simulink_id=simulink_id,
                simulink_type=simulink_type,
                simulink_status=status,
                simulink_time=simulate_time,
                control_params=[],
                output_vars=[]
            )
        
        # 构建控制参数列表
        control_params_list = []
        if control_params is None and hasattr(model, 'control_params'):
            for param_name, param_info in model.control_params.items():
                control_params_list.append(simulink_service_pb2.ControlParam(
                    param_name=param_name,
                    block_path=param_info["block_path"],
                    param_type=param_info["param_type"],
                    default_value=str(param_info["default_value"]),
                    description=param_info["description"],
                    writable=param_info["writable"]
                ))
        elif control_params:
            control_params_list = control_params
        
        # 构建输出变量列表
        output_vars_list = []
        if output_vars is None and hasattr(model, 'output_vars'):
            for var_name, var_info in model.output_vars.items():
                output_vars_list.append(simulink_service_pb2.OutputVar(
                    var_name=var_name,
                    matlab_var=var_info["matlab_var"],
                    description=var_info["description"],
                    readable=var_info["readable"]
                ))
        elif output_vars:
            output_vars_list = output_vars
        
        return simulink_service_pb2.SimulinkInfo(
            simulink_id=simulink_id,
            simulink_type=simulink_type,
            simulink_status=status,
            simulink_time=simulate_time,
            control_params=control_params_list,
            output_vars=output_vars_list
        )
    
    def InitializeSimulink(self, request, context):
        """
        初始化模型
        """
        try:
            # 添加请求数据日志
            logger.info(f"Received InitializeSimulink request: {request}")
            
            simulink_type = request.simulink_type
            simulink_dir = request.simulink_dir
            simulink_name = request.simulink_name
            init_script = request.init_script
            run_time = request.run_time
            control_params = request.control_params  # 获取控制参数列表
            output_vars = request.output_vars      # 获取输出变量列表
            
            # 检查模型类型是否支持
            if simulink_type not in self.model_types:
                message = f"不支持的模型类型: {simulink_type}"
                logger.error(message)
                return simulink_service_pb2.InitializeSimulinkResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id="",
                        simulink_type=simulink_type,
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            # 创建模型实例
            model_class = self.model_types[simulink_type]
            model = model_class(
                model_dir=simulink_dir,
                model_name=simulink_name,
                init_script=init_script,
                simulate_time=run_time
            )
            
            # 初始化模型
            if not model.initialize():
                message = "模型初始化失败"
                logger.error(message)
                return simulink_service_pb2.InitializeSimulinkResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id="",
                        simulink_type=simulink_type,
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            # 注册控制参数（如果有）
            for param in control_params:
                model.control_params[param.param_name] = {
                    "param_type": param.param_type,
                    "block_path": param.block_path,
                    "default_value": param.default_value,
                    "description": param.description,
                    "writable": param.writable
                }
            
            # 注册输出变量（如果有）
            for var in output_vars:
                model.output_vars[var.var_name] = {
                    "matlab_var": var.matlab_var,
                    "description": var.description,
                    "readable": var.readable
                }
            
            # 生成模型ID并存储模型实例
            timestamp = time.time()
            simulink_id = str(uuid.uuid4())
            simulink_info=self._build_simulink_info(
                simulink_id=simulink_id,
                simulink_type=simulink_type,
                status="ready",
                simulate_time=run_time,
                model=model
            )

            # TODO:存储模型实例
            self.models[simulink_id] = {
                "instance": model,
                "type": simulink_info.simulink_type,
                "simulink_time": simulink_info.simulink_time,
                "status": simulink_info.simulink_status,
                "control_params": simulink_info.control_params,
                "output_vars": simulink_info.output_vars,
                "created_at": timestamp,
                "updated_at": timestamp
            }
            
            logger.info(f"模型初始化成功: {simulink_id} ({simulink_type}/{simulink_name})")
            return simulink_service_pb2.InitializeSimulinkResponse(
                simulink_info=simulink_info
            )
        except Exception as e:
            message = f"初始化模型时出错: {str(e)}"
            logger.error(message)
            return simulink_service_pb2.InitializeSimulinkResponse(
                simulink_info=self._build_simulink_info(
                    simulink_id="",
                    simulink_type=request.simulink_type,
                    status=f"error:{message}",
                    simulate_time=0
                )
            )
    
    def RegisterSimulinkParam(self, request, context):
        """
        注册控制参数
        """
        try:
            simulink_id = request.simulink_id
            control_params = request.control_params
            
            # 检查模型ID是否存在
            if simulink_id not in self.models:
                message = f"模型ID不存在: {simulink_id}"
                logger.error(message)
                return simulink_service_pb2.RegisterSimulinkParamResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type="",
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            # 获取模型实例和信息
            model_data = self.models[simulink_id]
            model = model_data["instance"]
            simulink_type = model_data["type"]
            
            # 注册控制参数
            for param in control_params:
                model.control_params[param.param_name] = {
                    "param_type": param.param_type,
                    "block_path": param.block_path,
                    "default_value": param.default_value,
                    "description": param.description,
                    "writable": param.writable
                }
            
            # 获取模型状态
            status = model.get_status()
            
            logger.info(f"注册控制参数成功: {simulink_id}")
            return simulink_service_pb2.RegisterSimulinkParamResponse(
                simulink_info=self._build_simulink_info(
                    simulink_id=simulink_id,
                    simulink_type=simulink_type,
                    status=status,
                    simulate_time=model.simulate_time,
                    model=model
                )
            )
        except Exception as e:
            message = f"注册控制参数时出错: {str(e)}"
            logger.error(message)
            return simulink_service_pb2.RegisterSimulinkParamResponse(
                simulink_info=self._build_simulink_info(
                    simulink_id=simulink_id,
                    simulink_type="",
                    status=f"error:{message}",
                    simulate_time=0
                )
            )
            
    def _build_simulink_runtime(self, model, status=None) -> simulink_service_pb2.SimulinkRuntime:
        """
        构建SimulinkRuntime对象的辅助函数
        
        Args:
            model: 模型实例
            status: 模型状态字典，如果为None则从model获取
            results: 仿真结果字典，如果为None则从model获取
            
        Returns:
            SimulinkRuntime对象
        """
        if status is None:
            status = model.get_status()

        if status == model.STATUS_PENDING or status == model.STATUS_ERROR or model.sim_results is None:
            return simulink_service_pb2.SimulinkRuntime(
                progress=0.0,
                output_vars=[]
            )
            
        #多变量导出
        output_var_values = []      
        for var_name in model.output_vars:
            if var_name in model.sim_results:
                t = [item[0] for item in model.sim_results.get('time', [])]  # 将嵌套列表展平
                values = [item[0] for item in model.sim_results.get(f'{var_name}', [])]  # 将嵌套列表展平
                
                # 确保数据长度一致
                if len(t) != len(values):
                    logger.info(f"数据长度不一致: 时间={len(t)}, {var_name}={len(values)}")
                    # 重采样到较短的长度
                    min_len = min(len(t), len(values))
                    if len(t) > min_len:
                        step = len(t) // min_len
                        t = t[::step]
                    if len(values) > min_len:
                        step = len(values) // min_len
                        values = values[::step]
                    
                    # 再次截断到相同长度
                    min_len = min(len(t), len(values))
                    t = t[:min_len]
                    values = values[:min_len]
                    logger.info(f"重采样后形状: 时间={len(t)}, {var_name}={len(values)}")

                # 使用重采样后的数据
                timestamps = [str(x) for x in t]
                values = [str(x) for x in values]
                output_var_values.append(simulink_service_pb2.OutputVarValue(
                    var_name=var_name,
                    timestamp=timestamps,
                    value=values
                ))

        
        return simulink_service_pb2.SimulinkRuntime(
            progress=0.0,
            output_vars=output_var_values
        )

    def _get_simulink_info_by_id(self, simulink_id) -> simulink_service_pb2.SimulinkInfoAndRuntime:
        """
        获取模型信息

        Args:
            simulink_id: 模型ID

        Returns:
            simulink_service_pb2.GetSimulinkInfoResponse: 包含模型信息和运行时信息的响应对象
        """
        try:
            # 检查模型ID是否存在
            if simulink_id not in self.models:
                message = f"模型ID不存在: {simulink_id}"
                logger.error(message)
                return simulink_service_pb2.SimulinkInfoAndRuntime(
                        simulink_info=self._build_simulink_info(
                            simulink_id=simulink_id,
                            simulink_type="",
                            status=f"error:{message}",
                            simulate_time=0
                        ),
                        simulink_runtime=None
                    )
            
            # 获取模型实例和信息
            model_data = self.models[simulink_id]
            model = model_data["instance"]
            simulink_type = model_data["type"]
            
            # 获取模型状态
            status = model.get_status()
            
            # 构建运行时信息
            simulink_runtime = self._build_simulink_runtime(model, status)
            
            logger.info(f"获取模型信息: {simulink_id}")
            return simulink_service_pb2.SimulinkInfoAndRuntime(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type=simulink_type,
                        status=status,
                        simulate_time=model.simulate_time,
                        model=model
                    ),
                    simulink_runtime=simulink_runtime
                )
        except Exception as e:
            message = f"获取模型信息时出错: {str(e)}"
            logger.error(message)
            return simulink_service_pb2.SimulinkInfoAndRuntime(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type="",
                        status=f"error:{message}",
                        simulate_time=0
                    ),
                    simulink_runtime=None
                )

    
    def GetSimulinkInfo(self, request, context):
        """
        获取模型信息
        """
        return simulink_service_pb2.GetSimulinkInfoResponse(
            simulink_info_and_runtime=self._get_simulink_info_by_id(request.simulink_id)
        )
            
    def RegisterSimulinkVar(self, request, context):
        """
        注册输出变量
        """
        try:
            simulink_id = request.simulink_id
            output_vars = request.output_vars
            
            # 检查模型ID是否存在
            if simulink_id not in self.models:
                message = f"模型ID不存在: {simulink_id}"
                logger.error(message)
                return simulink_service_pb2.RegisterSimulinkVarResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type="",
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            # 获取模型实例和信息
            model_data = self.models[simulink_id]
            model = model_data["instance"]
            simulink_type = model_data["type"]
            
            # 注册输出变量
            for var in output_vars:
                model.output_vars[var.var_name] = {
                    "matlab_var": var.matlab_var,
                    "description": var.description,
                    "readable": var.readable
                }
            
            # 获取模型状态
            status = model.get_status()
            
            logger.info(f"注册输出变量成功: {simulink_id}")
            return simulink_service_pb2.RegisterSimulinkVarResponse(
                simulink_info=self._build_simulink_info(
                    simulink_id=simulink_id,
                    simulink_type=simulink_type,
                    status=status,
                    simulate_time=model.simulate_time,
                    model=model
                )
            )
        except Exception as e:
            message = f"注册输出变量时出错: {str(e)}"
            logger.error(message)
            return simulink_service_pb2.RegisterSimulinkVarResponse(
                simulink_info=self._build_simulink_info(
                    simulink_id=simulink_id,
                    simulink_type="",
                    status=f"error:{message}",
                    simulate_time=0
                )
            )
    
    def RunSimulation(self, request, context):
        """
        运行仿真
        """
        try:
            simulink_id = request.simulink_id
            start_time = request.start_time
            stop_time = request.stop_time
            params = request.params
            
            # 检查模型ID是否存在
            if simulink_id not in self.models:
                message = f"模型ID不存在: {simulink_id}"
                logger.error(message)
                return simulink_service_pb2.RunSimulationResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type="",
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            # 获取模型实例
            model_data = self.models[simulink_id]
            model = model_data["instance"]
            simulink_type = model_data["type"]
                        
            # 构建参数字典
            params_dic = {}
            
            if params:
                for param in params:
                    param_name = param.param_name
                    if param_name not in params_dic:
                        params_dic[param_name] = {}
                    
                    # 检查是否提供了时间
                    if param.timestamp:
                        param_time = float(param.timestamp)
                        param_value = float(param.value)
                        params_dic[param_name][param_time] = param_value
                    else:
                        # 如果没有提供时间点，则使用开始和结束时间
                        params_dic[param_name][float(start_time)] = float(param.value)
                        params_dic[param_name][float(stop_time)] = float(param.value)
            
            # 检查是否有有效的参数和输出变量
            if not params:
                message = "没有可用的控制参数"
                logger.error(message)
                return simulink_service_pb2.RunSimulationResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type=simulink_type,
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            # 获取第一个输出变量
            for var_name, var_info in model.output_vars.items():
                if var_info.get("readable", False):
                    out_val_name = var_name
                    break

            if not out_val_name:
                message = "没有可用的输出变量"
                logger.error(message)
                return simulink_service_pb2.RunSimulationResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type=simulink_type,
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            
            # 运行仿真
            if not model.run_simulation(params_dic):
            
                message = "运行仿真失败"
                logger.error(message)
                return simulink_service_pb2.RunSimulationResponse(
                    simulink_info=self._build_simulink_info(
                        simulink_id=simulink_id,
                        simulink_type=simulink_type,
                        status=f"error:{message}",
                        simulate_time=0
                    )
                )
            
            # 获取模型状态
            status = model.get_status()
            
            # 获取仿真结果并构建运行时信息
            simulink_runtime = self._build_simulink_runtime(model, status)
            
            logger.info(f"运行仿真成功: {simulink_id}")
            return simulink_service_pb2.RunSimulationResponse(
                simulink_info=self._build_simulink_info(
                    simulink_id=simulink_id,
                    simulink_type=simulink_type,
                    status="ready",
                    simulate_time=model.simulate_time,
                    model=model
                ),
                simulink_runtime=simulink_runtime
            )
        except Exception as e:
            message = f"运行仿真时出错: {str(e)}"
            logger.error(message)
            return simulink_service_pb2.RunSimulationResponse(
                simulink_info=self._build_simulink_info(
                    simulink_id=simulink_id,
                    simulink_type="",
                    status=f"error:{message}",
                    simulate_time=0
                )
            )
    
    def GetSimulationResults(self, request, context):
        """
        获取仿真结果
        """
        try:
            simulink_id = request.simulink_id
            var_names = list(request.var_names)
            
            # 检查模型ID是否存在
            if simulink_id not in self.models:
                message = f"模型ID不存在: {simulink_id}"
                logger.error(message)
                return simulink_service_pb2.GetSimulationResultsResponse(
                    output_vars=[]
                )
            
            # 获取模型实例
            model = self.models[simulink_id]["instance"]
            
            # 构建运行时信息
            simulink_runtime = self._build_simulink_runtime(model)

            # 如果未指定变量名，则获取所有结果
            result_list=[]
            if not var_names:
                result_list=simulink_runtime.output_vars
            else:
                for output_var in simulink_runtime.output_vars:
                    for target_var in var_names:
                        if output_var.var_name==target_var:
                            result_list.append(output_var)
                            break
            
            logger.info(f"获取仿真结果成功: {simulink_id} {var_names}")
            return simulink_service_pb2.GetSimulationResultsResponse(
                output_vars=result_list
            )
        except Exception as e:
            message = f"获取仿真结果时出错: {str(e)}"
            logger.error(message)
            return simulink_service_pb2.GetSimulationResultsResponse(
                output_vars=[]
            )
    
    def CloseSimulink(self, request, context):
        """
        关闭模型
        """
        try:
            simulink_id = request.simulink_id
            
            # 检查模型ID是否存在
            if simulink_id not in self.models:
                message = f"模型ID不存在: {simulink_id}"
                logger.error(message)
                return simulink_service_pb2.CloseSimulinkResponse(
                    simulink_info=simulink_service_pb2.SimulinkInfo(
                        simulink_id=simulink_id,
                        simulink_type="",
                        simulink_status=f"error:{message}",
                        simulink_time=0
                    )
                )
            
            # 获取模型实例和信息
            model_data = self.models[simulink_id]
            model = model_data["instance"]
            simulink_type = model_data["type"]
            
            # 关闭模型
            model.close()
            
            # 从字典中移除模型
            del self.models[simulink_id]
            
            logger.info(f"关闭模型成功: {simulink_id}")
            return simulink_service_pb2.CloseSimulinkResponse(
                simulink_info=simulink_service_pb2.SimulinkInfo(
                    simulink_id=simulink_id,
                    simulink_type=simulink_type,
                    simulink_status="stopped",
                    simulink_time=0
                )
            )
        except Exception as e:
            message = f"关闭模型时出错: {str(e)}"
            logger.error(message)
            return simulink_service_pb2.CloseSimulinkResponse(
                simulink_info=simulink_service_pb2.SimulinkInfo(
                    simulink_id="",
                    simulink_type="",
                    simulink_status=f"error:{message}",
                    simulate_time=0,
                    control_params=[],
                    output_vars=[]
                )
            )
    
    def ListAvailableSimulinks(self, request, context):
        """
        获取可用模型列表
        """
        try:
            simulink_type = request.simulink_type
            simulink_status = request.simulink_status
            
            #根据请求条件筛选model_id,如果没有请求条件，则返回所有model_id
            if not simulink_type and not simulink_status and not request.simulink_id:
                model_ids = list(self.models.keys())
            else:
                model_ids=[]
                if request.simulink_id:
                    model_ids.append(request.simulink_id)
                for model_id, model_data in self.models.items():
                    model_type = model_data["type"]
                    model_status = model_data["instance"].get_status()

                    # 根据模型类型和状态筛选
                    if (simulink_type and model_type == simulink_type) or \
                    (simulink_status and model_status == simulink_status):
                        model_ids.append(model_id)
            # 构建模型信息列表
            simulink_info_and_runtime= []

            for model_id in model_ids:
                logger.info(f"获取可用模型中: {model_id}")
                simulink_info_and_runtime.append(self._get_simulink_info_by_id(model_id))
                logger.info(f"获取模型成功: {model_id}")
            return simulink_service_pb2.ListAvailableSimulinksResponse(
                simulink_info_and_runtime=simulink_info_and_runtime
            )
        except Exception as e:
            message = f"获取可用模型列表时出错: {e}"
            logger.error(message)
            return simulink_service_pb2.ListAvailableSimulinksResponse(
                simulink_info_and_runtime=simulink_info_and_runtime
            )

def serve(port=50051):
    """
    启动gRPC服务器
    
    Args:
        port: 服务端口
    """
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    simulink_service_pb2_grpc.add_SimulinkServiceServicer_to_server(
        SimulinkServicer(), server)
    server.add_insecure_port(f'[::]:{port}')
    server.start()
    logger.info(f"Simulink服务器已启动，监听端口: {port}")
    try:
        while True:
            print("Simulink服务器正在运行...")
            time.sleep(86400*7)  # 一天*7
    except KeyboardInterrupt:
        server.stop(0)
        logger.info("Simulink服务器已停止")

if __name__ == '__main__':
    serve()

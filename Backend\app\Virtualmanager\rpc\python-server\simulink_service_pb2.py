# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: simulink_service.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'simulink_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16simulink_service.proto\x12\x0fsimulinkmanager\"\xee\x01\n\x19InitializeSimulinkRequest\x12\x15\n\rsimulink_type\x18\x01 \x01(\t\x12\x14\n\x0csimulink_dir\x18\x02 \x01(\t\x12\x15\n\rsimulink_name\x18\x03 \x01(\t\x12\x13\n\x0binit_script\x18\x04 \x01(\t\x12\x10\n\x08run_time\x18\x05 \x01(\x02\x12\x35\n\x0e\x63ontrol_params\x18\x06 \x03(\x0b\x32\x1d.simulinkmanager.ControlParam\x12/\n\x0boutput_vars\x18\x07 \x03(\x0b\x32\x1a.simulinkmanager.OutputVar\"R\n\x1aInitializeSimulinkResponse\x12\x34\n\rsimulink_info\x18\x01 \x01(\x0b\x32\x1d.simulinkmanager.SimulinkInfo\"\xd2\x01\n\x0cSimulinkInfo\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\x12\x15\n\rsimulink_type\x18\x02 \x01(\t\x12\x17\n\x0fsimulink_status\x18\x03 \x01(\t\x12\x15\n\rsimulink_time\x18\x04 \x01(\x02\x12\x35\n\x0e\x63ontrol_params\x18\x05 \x03(\x0b\x32\x1d.simulinkmanager.ControlParam\x12/\n\x0boutput_vars\x18\x06 \x03(\x0b\x32\x1a.simulinkmanager.OutputVar\"\x88\x01\n\x0c\x43ontrolParam\x12\x12\n\nparam_name\x18\x01 \x01(\t\x12\x12\n\nblock_path\x18\x02 \x01(\t\x12\x12\n\nparam_type\x18\x03 \x01(\t\x12\x15\n\rdefault_value\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\x10\n\x08writable\x18\x06 \x01(\x08\"X\n\tOutputVar\x12\x10\n\x08var_name\x18\x01 \x01(\t\x12\x12\n\nmatlab_var\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x10\n\x08readable\x18\x04 \x01(\x08\"k\n\x1dResgisterSimulinkParamRequest\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\x12\x35\n\x0e\x63ontrol_params\x18\x02 \x03(\x0b\x32\x1d.simulinkmanager.ControlParam\"U\n\x1dRegisterSimulinkParamResponse\x12\x34\n\rsimulink_info\x18\x01 \x01(\x0b\x32\x1d.simulinkmanager.SimulinkInfo\"b\n\x1aRegisterSimulinkVarRequest\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\x12/\n\x0boutput_vars\x18\x02 \x03(\x0b\x32\x1a.simulinkmanager.OutputVar\"S\n\x1bRegisterSimulinkVarResponse\x12\x34\n\rsimulink_info\x18\x01 \x01(\x0b\x32\x1d.simulinkmanager.SimulinkInfo\"I\n\x11\x43ontrolParamValue\x12\x11\n\ttimestamp\x18\x01 \x01(\t\x12\x12\n\nparam_name\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"D\n\x0eOutputVarValue\x12\x10\n\x08var_name\x18\x01 \x01(\t\x12\x11\n\ttimestamp\x18\x02 \x03(\t\x12\r\n\x05value\x18\x03 \x03(\t\"Y\n\x0fSimulinkRuntime\x12\x10\n\x08progress\x18\x01 \x01(\x02\x12\x34\n\x0boutput_vars\x18\x02 \x03(\x0b\x32\x1f.simulinkmanager.OutputVarValue\"\x86\x01\n\x14RunSimulationRequest\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\x12\x12\n\nstart_time\x18\x02 \x01(\x02\x12\x11\n\tstop_time\x18\x03 \x01(\x02\x12\x32\n\x06params\x18\x04 \x03(\x0b\x32\".simulinkmanager.ControlParamValue\"\x89\x01\n\x15RunSimulationResponse\x12\x34\n\rsimulink_info\x18\x01 \x01(\x0b\x32\x1d.simulinkmanager.SimulinkInfo\x12:\n\x10simulink_runtime\x18\x02 \x01(\x0b\x32 .simulinkmanager.SimulinkRuntime\"-\n\x16GetSimulinkInfoRequest\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\"e\n\x17GetSimulinkInfoResponse\x12J\n\x19simulink_info_and_runtime\x18\x01 \x01(\x0b\x32\'.simulinkmanager.SimulinkInfoAndRuntime\"\x8a\x01\n\x16SimulinkInfoAndRuntime\x12\x34\n\rsimulink_info\x18\x01 \x01(\x0b\x32\x1d.simulinkmanager.SimulinkInfo\x12:\n\x10simulink_runtime\x18\x02 \x01(\x0b\x32 .simulinkmanager.SimulinkRuntime\"E\n\x1bGetSimulationResultsRequest\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\x12\x11\n\tvar_names\x18\x02 \x03(\t\"T\n\x1cGetSimulationResultsResponse\x12\x34\n\x0boutput_vars\x18\x01 \x03(\x0b\x32\x1f.simulinkmanager.OutputVarValue\"+\n\x14\x43loseSimulinkRequest\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\"M\n\x15\x43loseSimulinkResponse\x12\x34\n\rsimulink_info\x18\x01 \x01(\x0b\x32\x1d.simulinkmanager.SimulinkInfo\"d\n\x1dListAvailableSimulinksRequest\x12\x13\n\x0bsimulink_id\x18\x01 \x01(\t\x12\x15\n\rsimulink_type\x18\x02 \x01(\t\x12\x17\n\x0fsimulink_status\x18\x03 \x01(\t\"l\n\x1eListAvailableSimulinksResponse\x12J\n\x19simulink_info_and_runtime\x18\x01 \x03(\x0b\x32\'.simulinkmanager.SimulinkInfoAndRuntime2\x81\x07\n\x0fSimulinkService\x12m\n\x12InitializeSimulink\x12*.simulinkmanager.InitializeSimulinkRequest\x1a+.simulinkmanager.InitializeSimulinkResponse\x12w\n\x15RegisterSimulinkParam\x12..simulinkmanager.ResgisterSimulinkParamRequest\x1a..simulinkmanager.RegisterSimulinkParamResponse\x12p\n\x13RegisterSimulinkVar\x12+.simulinkmanager.RegisterSimulinkVarRequest\x1a,.simulinkmanager.RegisterSimulinkVarResponse\x12^\n\rRunSimulation\x12%.simulinkmanager.RunSimulationRequest\x1a&.simulinkmanager.RunSimulationResponse\x12\x64\n\x0fGetSimulinkInfo\x12\'.simulinkmanager.GetSimulinkInfoRequest\x1a(.simulinkmanager.GetSimulinkInfoResponse\x12s\n\x14GetSimulationResults\x12,.simulinkmanager.GetSimulationResultsRequest\x1a-.simulinkmanager.GetSimulationResultsResponse\x12^\n\rCloseSimulink\x12%.simulinkmanager.CloseSimulinkRequest\x1a&.simulinkmanager.CloseSimulinkResponse\x12y\n\x16ListAvailableSimulinks\x12..simulinkmanager.ListAvailableSimulinksRequest\x1a/.simulinkmanager.ListAvailableSimulinksResponseB\x13Z\x11./simulinkmanagerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'simulink_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\021./simulinkmanager'
  _globals['_INITIALIZESIMULINKREQUEST']._serialized_start=44
  _globals['_INITIALIZESIMULINKREQUEST']._serialized_end=282
  _globals['_INITIALIZESIMULINKRESPONSE']._serialized_start=284
  _globals['_INITIALIZESIMULINKRESPONSE']._serialized_end=366
  _globals['_SIMULINKINFO']._serialized_start=369
  _globals['_SIMULINKINFO']._serialized_end=579
  _globals['_CONTROLPARAM']._serialized_start=582
  _globals['_CONTROLPARAM']._serialized_end=718
  _globals['_OUTPUTVAR']._serialized_start=720
  _globals['_OUTPUTVAR']._serialized_end=808
  _globals['_RESGISTERSIMULINKPARAMREQUEST']._serialized_start=810
  _globals['_RESGISTERSIMULINKPARAMREQUEST']._serialized_end=917
  _globals['_REGISTERSIMULINKPARAMRESPONSE']._serialized_start=919
  _globals['_REGISTERSIMULINKPARAMRESPONSE']._serialized_end=1004
  _globals['_REGISTERSIMULINKVARREQUEST']._serialized_start=1006
  _globals['_REGISTERSIMULINKVARREQUEST']._serialized_end=1104
  _globals['_REGISTERSIMULINKVARRESPONSE']._serialized_start=1106
  _globals['_REGISTERSIMULINKVARRESPONSE']._serialized_end=1189
  _globals['_CONTROLPARAMVALUE']._serialized_start=1191
  _globals['_CONTROLPARAMVALUE']._serialized_end=1264
  _globals['_OUTPUTVARVALUE']._serialized_start=1266
  _globals['_OUTPUTVARVALUE']._serialized_end=1334
  _globals['_SIMULINKRUNTIME']._serialized_start=1336
  _globals['_SIMULINKRUNTIME']._serialized_end=1425
  _globals['_RUNSIMULATIONREQUEST']._serialized_start=1428
  _globals['_RUNSIMULATIONREQUEST']._serialized_end=1562
  _globals['_RUNSIMULATIONRESPONSE']._serialized_start=1565
  _globals['_RUNSIMULATIONRESPONSE']._serialized_end=1702
  _globals['_GETSIMULINKINFOREQUEST']._serialized_start=1704
  _globals['_GETSIMULINKINFOREQUEST']._serialized_end=1749
  _globals['_GETSIMULINKINFORESPONSE']._serialized_start=1751
  _globals['_GETSIMULINKINFORESPONSE']._serialized_end=1852
  _globals['_SIMULINKINFOANDRUNTIME']._serialized_start=1855
  _globals['_SIMULINKINFOANDRUNTIME']._serialized_end=1993
  _globals['_GETSIMULATIONRESULTSREQUEST']._serialized_start=1995
  _globals['_GETSIMULATIONRESULTSREQUEST']._serialized_end=2064
  _globals['_GETSIMULATIONRESULTSRESPONSE']._serialized_start=2066
  _globals['_GETSIMULATIONRESULTSRESPONSE']._serialized_end=2150
  _globals['_CLOSESIMULINKREQUEST']._serialized_start=2152
  _globals['_CLOSESIMULINKREQUEST']._serialized_end=2195
  _globals['_CLOSESIMULINKRESPONSE']._serialized_start=2197
  _globals['_CLOSESIMULINKRESPONSE']._serialized_end=2274
  _globals['_LISTAVAILABLESIMULINKSREQUEST']._serialized_start=2276
  _globals['_LISTAVAILABLESIMULINKSREQUEST']._serialized_end=2376
  _globals['_LISTAVAILABLESIMULINKSRESPONSE']._serialized_start=2378
  _globals['_LISTAVAILABLESIMULINKSRESPONSE']._serialized_end=2486
  _globals['_SIMULINKSERVICE']._serialized_start=2489
  _globals['_SIMULINKSERVICE']._serialized_end=3386
# @@protoc_insertion_point(module_scope)

from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class InitializeSimulinkRequest(_message.Message):
    __slots__ = ("simulink_type", "simulink_dir", "simulink_name", "init_script", "run_time", "control_params", "output_vars")
    SIMULINK_TYPE_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_DIR_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_NAME_FIELD_NUMBER: _ClassVar[int]
    INIT_SCRIPT_FIELD_NUMBER: _ClassVar[int]
    RUN_TIME_FIELD_NUMBER: _ClassVar[int]
    CONTROL_PARAMS_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_VARS_FIELD_NUMBER: _ClassVar[int]
    simulink_type: str
    simulink_dir: str
    simulink_name: str
    init_script: str
    run_time: float
    control_params: _containers.RepeatedCompositeFieldContainer[ControlParam]
    output_vars: _containers.RepeatedCompositeFieldContainer[OutputVar]
    def __init__(self, simulink_type: _Optional[str] = ..., simulink_dir: _Optional[str] = ..., simulink_name: _Optional[str] = ..., init_script: _Optional[str] = ..., run_time: _Optional[float] = ..., control_params: _Optional[_Iterable[_Union[ControlParam, _Mapping]]] = ..., output_vars: _Optional[_Iterable[_Union[OutputVar, _Mapping]]] = ...) -> None: ...

class InitializeSimulinkResponse(_message.Message):
    __slots__ = ("simulink_info",)
    SIMULINK_INFO_FIELD_NUMBER: _ClassVar[int]
    simulink_info: SimulinkInfo
    def __init__(self, simulink_info: _Optional[_Union[SimulinkInfo, _Mapping]] = ...) -> None: ...

class SimulinkInfo(_message.Message):
    __slots__ = ("simulink_id", "simulink_type", "simulink_status", "simulink_time", "control_params", "output_vars")
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_TYPE_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_STATUS_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_TIME_FIELD_NUMBER: _ClassVar[int]
    CONTROL_PARAMS_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_VARS_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    simulink_type: str
    simulink_status: str
    simulink_time: float
    control_params: _containers.RepeatedCompositeFieldContainer[ControlParam]
    output_vars: _containers.RepeatedCompositeFieldContainer[OutputVar]
    def __init__(self, simulink_id: _Optional[str] = ..., simulink_type: _Optional[str] = ..., simulink_status: _Optional[str] = ..., simulink_time: _Optional[float] = ..., control_params: _Optional[_Iterable[_Union[ControlParam, _Mapping]]] = ..., output_vars: _Optional[_Iterable[_Union[OutputVar, _Mapping]]] = ...) -> None: ...

class ControlParam(_message.Message):
    __slots__ = ("param_name", "block_path", "param_type", "default_value", "description", "writable")
    PARAM_NAME_FIELD_NUMBER: _ClassVar[int]
    BLOCK_PATH_FIELD_NUMBER: _ClassVar[int]
    PARAM_TYPE_FIELD_NUMBER: _ClassVar[int]
    DEFAULT_VALUE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    WRITABLE_FIELD_NUMBER: _ClassVar[int]
    param_name: str
    block_path: str
    param_type: str
    default_value: str
    description: str
    writable: bool
    def __init__(self, param_name: _Optional[str] = ..., block_path: _Optional[str] = ..., param_type: _Optional[str] = ..., default_value: _Optional[str] = ..., description: _Optional[str] = ..., writable: bool = ...) -> None: ...

class OutputVar(_message.Message):
    __slots__ = ("var_name", "matlab_var", "description", "readable")
    VAR_NAME_FIELD_NUMBER: _ClassVar[int]
    MATLAB_VAR_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    READABLE_FIELD_NUMBER: _ClassVar[int]
    var_name: str
    matlab_var: str
    description: str
    readable: bool
    def __init__(self, var_name: _Optional[str] = ..., matlab_var: _Optional[str] = ..., description: _Optional[str] = ..., readable: bool = ...) -> None: ...

class ResgisterSimulinkParamRequest(_message.Message):
    __slots__ = ("simulink_id", "control_params")
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    CONTROL_PARAMS_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    control_params: _containers.RepeatedCompositeFieldContainer[ControlParam]
    def __init__(self, simulink_id: _Optional[str] = ..., control_params: _Optional[_Iterable[_Union[ControlParam, _Mapping]]] = ...) -> None: ...

class RegisterSimulinkParamResponse(_message.Message):
    __slots__ = ("simulink_info",)
    SIMULINK_INFO_FIELD_NUMBER: _ClassVar[int]
    simulink_info: SimulinkInfo
    def __init__(self, simulink_info: _Optional[_Union[SimulinkInfo, _Mapping]] = ...) -> None: ...

class RegisterSimulinkVarRequest(_message.Message):
    __slots__ = ("simulink_id", "output_vars")
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_VARS_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    output_vars: _containers.RepeatedCompositeFieldContainer[OutputVar]
    def __init__(self, simulink_id: _Optional[str] = ..., output_vars: _Optional[_Iterable[_Union[OutputVar, _Mapping]]] = ...) -> None: ...

class RegisterSimulinkVarResponse(_message.Message):
    __slots__ = ("simulink_info",)
    SIMULINK_INFO_FIELD_NUMBER: _ClassVar[int]
    simulink_info: SimulinkInfo
    def __init__(self, simulink_info: _Optional[_Union[SimulinkInfo, _Mapping]] = ...) -> None: ...

class ControlParamValue(_message.Message):
    __slots__ = ("timestamp", "param_name", "value")
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    PARAM_NAME_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    timestamp: str
    param_name: str
    value: str
    def __init__(self, timestamp: _Optional[str] = ..., param_name: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...

class OutputVarValue(_message.Message):
    __slots__ = ("var_name", "timestamp", "value")
    VAR_NAME_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    var_name: str
    timestamp: _containers.RepeatedScalarFieldContainer[str]
    value: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, var_name: _Optional[str] = ..., timestamp: _Optional[_Iterable[str]] = ..., value: _Optional[_Iterable[str]] = ...) -> None: ...

class SimulinkRuntime(_message.Message):
    __slots__ = ("progress", "output_vars")
    PROGRESS_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_VARS_FIELD_NUMBER: _ClassVar[int]
    progress: float
    output_vars: _containers.RepeatedCompositeFieldContainer[OutputVarValue]
    def __init__(self, progress: _Optional[float] = ..., output_vars: _Optional[_Iterable[_Union[OutputVarValue, _Mapping]]] = ...) -> None: ...

class RunSimulationRequest(_message.Message):
    __slots__ = ("simulink_id", "start_time", "stop_time", "params")
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    STOP_TIME_FIELD_NUMBER: _ClassVar[int]
    PARAMS_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    start_time: float
    stop_time: float
    params: _containers.RepeatedCompositeFieldContainer[ControlParamValue]
    def __init__(self, simulink_id: _Optional[str] = ..., start_time: _Optional[float] = ..., stop_time: _Optional[float] = ..., params: _Optional[_Iterable[_Union[ControlParamValue, _Mapping]]] = ...) -> None: ...

class RunSimulationResponse(_message.Message):
    __slots__ = ("simulink_info", "simulink_runtime")
    SIMULINK_INFO_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_RUNTIME_FIELD_NUMBER: _ClassVar[int]
    simulink_info: SimulinkInfo
    simulink_runtime: SimulinkRuntime
    def __init__(self, simulink_info: _Optional[_Union[SimulinkInfo, _Mapping]] = ..., simulink_runtime: _Optional[_Union[SimulinkRuntime, _Mapping]] = ...) -> None: ...

class GetSimulinkInfoRequest(_message.Message):
    __slots__ = ("simulink_id",)
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    def __init__(self, simulink_id: _Optional[str] = ...) -> None: ...

class GetSimulinkInfoResponse(_message.Message):
    __slots__ = ("simulink_info_and_runtime",)
    SIMULINK_INFO_AND_RUNTIME_FIELD_NUMBER: _ClassVar[int]
    simulink_info_and_runtime: SimulinkInfoAndRuntime
    def __init__(self, simulink_info_and_runtime: _Optional[_Union[SimulinkInfoAndRuntime, _Mapping]] = ...) -> None: ...

class SimulinkInfoAndRuntime(_message.Message):
    __slots__ = ("simulink_info", "simulink_runtime")
    SIMULINK_INFO_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_RUNTIME_FIELD_NUMBER: _ClassVar[int]
    simulink_info: SimulinkInfo
    simulink_runtime: SimulinkRuntime
    def __init__(self, simulink_info: _Optional[_Union[SimulinkInfo, _Mapping]] = ..., simulink_runtime: _Optional[_Union[SimulinkRuntime, _Mapping]] = ...) -> None: ...

class GetSimulationResultsRequest(_message.Message):
    __slots__ = ("simulink_id", "var_names")
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    VAR_NAMES_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    var_names: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, simulink_id: _Optional[str] = ..., var_names: _Optional[_Iterable[str]] = ...) -> None: ...

class GetSimulationResultsResponse(_message.Message):
    __slots__ = ("output_vars",)
    OUTPUT_VARS_FIELD_NUMBER: _ClassVar[int]
    output_vars: _containers.RepeatedCompositeFieldContainer[OutputVarValue]
    def __init__(self, output_vars: _Optional[_Iterable[_Union[OutputVarValue, _Mapping]]] = ...) -> None: ...

class CloseSimulinkRequest(_message.Message):
    __slots__ = ("simulink_id",)
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    def __init__(self, simulink_id: _Optional[str] = ...) -> None: ...

class CloseSimulinkResponse(_message.Message):
    __slots__ = ("simulink_info",)
    SIMULINK_INFO_FIELD_NUMBER: _ClassVar[int]
    simulink_info: SimulinkInfo
    def __init__(self, simulink_info: _Optional[_Union[SimulinkInfo, _Mapping]] = ...) -> None: ...

class ListAvailableSimulinksRequest(_message.Message):
    __slots__ = ("simulink_id", "simulink_type", "simulink_status")
    SIMULINK_ID_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_TYPE_FIELD_NUMBER: _ClassVar[int]
    SIMULINK_STATUS_FIELD_NUMBER: _ClassVar[int]
    simulink_id: str
    simulink_type: str
    simulink_status: str
    def __init__(self, simulink_id: _Optional[str] = ..., simulink_type: _Optional[str] = ..., simulink_status: _Optional[str] = ...) -> None: ...

class ListAvailableSimulinksResponse(_message.Message):
    __slots__ = ("simulink_info_and_runtime",)
    SIMULINK_INFO_AND_RUNTIME_FIELD_NUMBER: _ClassVar[int]
    simulink_info_and_runtime: _containers.RepeatedCompositeFieldContainer[SimulinkInfoAndRuntime]
    def __init__(self, simulink_info_and_runtime: _Optional[_Iterable[_Union[SimulinkInfoAndRuntime, _Mapping]]] = ...) -> None: ...

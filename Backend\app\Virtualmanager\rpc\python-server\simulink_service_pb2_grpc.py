# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import simulink_service_pb2 as simulink__service__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in simulink_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class SimulinkServiceStub(object):
    """Simulink模型控制服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.InitializeSimulink = channel.unary_unary(
                '/simulinkmanager.SimulinkService/InitializeSimulink',
                request_serializer=simulink__service__pb2.InitializeSimulinkRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.InitializeSimulinkResponse.FromString,
                _registered_method=True)
        self.RegisterSimulinkParam = channel.unary_unary(
                '/simulinkmanager.SimulinkService/RegisterSimulinkParam',
                request_serializer=simulink__service__pb2.ResgisterSimulinkParamRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.RegisterSimulinkParamResponse.FromString,
                _registered_method=True)
        self.RegisterSimulinkVar = channel.unary_unary(
                '/simulinkmanager.SimulinkService/RegisterSimulinkVar',
                request_serializer=simulink__service__pb2.RegisterSimulinkVarRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.RegisterSimulinkVarResponse.FromString,
                _registered_method=True)
        self.RunSimulation = channel.unary_unary(
                '/simulinkmanager.SimulinkService/RunSimulation',
                request_serializer=simulink__service__pb2.RunSimulationRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.RunSimulationResponse.FromString,
                _registered_method=True)
        self.GetSimulinkInfo = channel.unary_unary(
                '/simulinkmanager.SimulinkService/GetSimulinkInfo',
                request_serializer=simulink__service__pb2.GetSimulinkInfoRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.GetSimulinkInfoResponse.FromString,
                _registered_method=True)
        self.GetSimulationResults = channel.unary_unary(
                '/simulinkmanager.SimulinkService/GetSimulationResults',
                request_serializer=simulink__service__pb2.GetSimulationResultsRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.GetSimulationResultsResponse.FromString,
                _registered_method=True)
        self.CloseSimulink = channel.unary_unary(
                '/simulinkmanager.SimulinkService/CloseSimulink',
                request_serializer=simulink__service__pb2.CloseSimulinkRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.CloseSimulinkResponse.FromString,
                _registered_method=True)
        self.ListAvailableSimulinks = channel.unary_unary(
                '/simulinkmanager.SimulinkService/ListAvailableSimulinks',
                request_serializer=simulink__service__pb2.ListAvailableSimulinksRequest.SerializeToString,
                response_deserializer=simulink__service__pb2.ListAvailableSimulinksResponse.FromString,
                _registered_method=True)


class SimulinkServiceServicer(object):
    """Simulink模型控制服务
    """

    def InitializeSimulink(self, request, context):
        """初始化模型
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RegisterSimulinkParam(self, request, context):
        """注册控制参数
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RegisterSimulinkVar(self, request, context):
        """注册输出参数
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RunSimulation(self, request, context):
        """运行仿真
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSimulinkInfo(self, request, context):
        """获取模型信息
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSimulationResults(self, request, context):
        """获取仿真结果
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CloseSimulink(self, request, context):
        """关闭模型
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAvailableSimulinks(self, request, context):
        """获取可用模型列表
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SimulinkServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'InitializeSimulink': grpc.unary_unary_rpc_method_handler(
                    servicer.InitializeSimulink,
                    request_deserializer=simulink__service__pb2.InitializeSimulinkRequest.FromString,
                    response_serializer=simulink__service__pb2.InitializeSimulinkResponse.SerializeToString,
            ),
            'RegisterSimulinkParam': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterSimulinkParam,
                    request_deserializer=simulink__service__pb2.ResgisterSimulinkParamRequest.FromString,
                    response_serializer=simulink__service__pb2.RegisterSimulinkParamResponse.SerializeToString,
            ),
            'RegisterSimulinkVar': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterSimulinkVar,
                    request_deserializer=simulink__service__pb2.RegisterSimulinkVarRequest.FromString,
                    response_serializer=simulink__service__pb2.RegisterSimulinkVarResponse.SerializeToString,
            ),
            'RunSimulation': grpc.unary_unary_rpc_method_handler(
                    servicer.RunSimulation,
                    request_deserializer=simulink__service__pb2.RunSimulationRequest.FromString,
                    response_serializer=simulink__service__pb2.RunSimulationResponse.SerializeToString,
            ),
            'GetSimulinkInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSimulinkInfo,
                    request_deserializer=simulink__service__pb2.GetSimulinkInfoRequest.FromString,
                    response_serializer=simulink__service__pb2.GetSimulinkInfoResponse.SerializeToString,
            ),
            'GetSimulationResults': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSimulationResults,
                    request_deserializer=simulink__service__pb2.GetSimulationResultsRequest.FromString,
                    response_serializer=simulink__service__pb2.GetSimulationResultsResponse.SerializeToString,
            ),
            'CloseSimulink': grpc.unary_unary_rpc_method_handler(
                    servicer.CloseSimulink,
                    request_deserializer=simulink__service__pb2.CloseSimulinkRequest.FromString,
                    response_serializer=simulink__service__pb2.CloseSimulinkResponse.SerializeToString,
            ),
            'ListAvailableSimulinks': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAvailableSimulinks,
                    request_deserializer=simulink__service__pb2.ListAvailableSimulinksRequest.FromString,
                    response_serializer=simulink__service__pb2.ListAvailableSimulinksResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'simulinkmanager.SimulinkService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('simulinkmanager.SimulinkService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SimulinkService(object):
    """Simulink模型控制服务
    """

    @staticmethod
    def InitializeSimulink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/InitializeSimulink',
            simulink__service__pb2.InitializeSimulinkRequest.SerializeToString,
            simulink__service__pb2.InitializeSimulinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RegisterSimulinkParam(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/RegisterSimulinkParam',
            simulink__service__pb2.ResgisterSimulinkParamRequest.SerializeToString,
            simulink__service__pb2.RegisterSimulinkParamResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RegisterSimulinkVar(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/RegisterSimulinkVar',
            simulink__service__pb2.RegisterSimulinkVarRequest.SerializeToString,
            simulink__service__pb2.RegisterSimulinkVarResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RunSimulation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/RunSimulation',
            simulink__service__pb2.RunSimulationRequest.SerializeToString,
            simulink__service__pb2.RunSimulationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSimulinkInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/GetSimulinkInfo',
            simulink__service__pb2.GetSimulinkInfoRequest.SerializeToString,
            simulink__service__pb2.GetSimulinkInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSimulationResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/GetSimulationResults',
            simulink__service__pb2.GetSimulationResultsRequest.SerializeToString,
            simulink__service__pb2.GetSimulationResultsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CloseSimulink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/CloseSimulink',
            simulink__service__pb2.CloseSimulinkRequest.SerializeToString,
            simulink__service__pb2.CloseSimulinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAvailableSimulinks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/simulinkmanager.SimulinkService/ListAvailableSimulinks',
            simulink__service__pb2.ListAvailableSimulinksRequest.SerializeToString,
            simulink__service__pb2.ListAvailableSimulinksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

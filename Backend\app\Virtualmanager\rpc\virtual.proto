syntax = "proto3";

package simulinkmanager;
option go_package = "./simulinkmanager";

// Simulink模型控制服务
service SimulinkService {
    // 初始化模型
    rpc InitializeSimulink (InitializeSimulinkRequest) returns (InitializeSimulinkResponse);
    // 注册控制参数
    rpc RegisterSimulinkParam (ResgisterSimulinkParamRequest) returns (RegisterSimulinkParamResponse);
    //注册输出参数
    rpc RegisterSimulinkVar (RegisterSimulinkVarRequest) returns (RegisterSimulinkVarResponse);
    // 运行仿真
    rpc RunSimulation (RunSimulationRequest) returns (RunSimulationResponse);
    // 获取模型信息
    rpc GetSimulinkInfo (GetSimulinkInfoRequest) returns (GetSimulinkInfoResponse);
    // 获取仿真结果
    rpc GetSimulationResults (GetSimulationResultsRequest) returns (GetSimulationResultsResponse);
    // 关闭模型
    rpc CloseSimulink (CloseSimulinkRequest) returns (CloseSimulinkResponse);
    // 获取可用模型列表
    rpc ListAvailableSimulinks (ListAvailableSimulinksRequest) returns (ListAvailableSimulinksResponse);
}

// 初始化模型请求
message InitializeSimulinkRequest {
    string simulink_type = 1;       // 模型类型 (e.g., "pressure", "motor")
    string simulink_dir = 2;        // 模型目录
    string simulink_name = 3;       // 模型名称
    string init_script = 4;      // 初始化脚本
    float run_time = 5;     // 仿真时长
    repeated ControlParam control_params = 6; // 控制参数列表
    repeated OutputVar output_vars = 7;      // 输出变量列表
}

message InitializeSimulinkResponse {
    SimulinkInfo simulink_info=1;     // 模型info信息
}

message SimulinkInfo {
    string simulink_id = 1;        // 模型id
    string simulink_type = 2;       // 模型类型
    string simulink_status = 3;       // 仿真状态,枚举类型，如"initializing", "ready","running", "stopped", "error"
    float simulink_time = 4;       // 模型运行时间
    repeated ControlParam control_params = 5; // 控制参数列表
    repeated OutputVar output_vars = 6;      // 输出变量列表
}

// 控制参数
message ControlParam {
    string param_name = 1;        // 参数名称
    string block_path = 2;       // MATLAB块路径
    string param_type = 3;        // 参数类型
    string default_value = 4;     // 默认值
    string description = 5;       // 参数描述
    bool writable = 6;            // 是否可写
}

// 输出变量
message OutputVar {
    string var_name = 1;          // 变量名称
    string matlab_var = 2;        // MATLAB变量路径
    string description = 3;       // 变量描述
    bool readable = 4;            // 是否可读
}

// 注册控制参数
message ResgisterSimulinkParamRequest {
    string simulink_id = 1;          // 模型ID
    repeated ControlParam control_params = 2; // 控制参数列表
}

message RegisterSimulinkParamResponse {
    SimulinkInfo simulink_info=1;     // 模型info信息
}

// 注册输出变量
message RegisterSimulinkVarRequest {
    string simulink_id = 1;          // 模型ID
    repeated OutputVar output_vars = 2; // 输出变量列表
}

message RegisterSimulinkVarResponse {
    SimulinkInfo simulink_info=1;     // 模型info信息
}

// 运行仿真请求
message ControlParamValue  {
    string timestamp = 1;
    string param_name = 2;
    string value = 3;
}

message OutputVarValue  {
    string var_name = 1; // 变量名称
    repeated string timestamp = 2; // 时间列表
    repeated string value = 3; // 值列表
}

// 仿真运行状态和信息
message SimulinkRuntime {
    float progress = 1; // 进度 (0.0-1.0)
    repeated OutputVarValue output_vars = 2; // 输出变量
}

message RunSimulationRequest {
    string simulink_id = 1;            // 模型ID
    float start_time = 2;          // 开始时间
    float stop_time = 3;           // 结束时间
    repeated ControlParamValue params = 4; // 仿真参数
}

message RunSimulationResponse {
    SimulinkInfo simulink_info=1;     // 模型info信息
    SimulinkRuntime simulink_runtime= 2;     // 模型运行信息
}

// 获取模型信息请求
message GetSimulinkInfoRequest {
    string simulink_id = 1;          // 模型ID
}

message GetSimulinkInfoResponse {
    SimulinkInfoAndRuntime simulink_info_and_runtime=1;     // 模型info信息
}

message SimulinkInfoAndRuntime {
    SimulinkInfo simulink_info=1;     // 模型info信息
    SimulinkRuntime simulink_runtime= 2;     // 模型运行信息
}

// 获取仿真结果请求
message GetSimulationResultsRequest {
    string simulink_id = 1;             // 模型ID
    repeated string var_names = 2;   // 变量名称列表
}

message GetSimulationResultsResponse {
    repeated OutputVarValue output_vars=1;     // 输出变量
}

// 关闭模型请求
message CloseSimulinkRequest {
    string simulink_id = 1;               // 模型ID
}

message CloseSimulinkResponse {
    SimulinkInfo simulink_info=1;     // 模型info信息
}

// 获取可用模型列表请求
message ListAvailableSimulinksRequest {
    string simulink_id = 1;        // 模型id
    string simulink_type = 2;       // 模型类型
    string simulink_status = 3;       // 仿真状态,枚举类型，如"initializing", "ready","running", "stopped", "error"
}


message ListAvailableSimulinksResponse {
    repeated SimulinkInfoAndRuntime simulink_info_and_runtime=1;     // 模型info信息
}
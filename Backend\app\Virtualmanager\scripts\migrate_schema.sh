#!/usr/bin/env bash
set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

SERVICE_ROOT=$SCRIPT_DIR/..
BACKEND_ROOT=$SERVICE_ROOT/../..

cd "$SERVICE_ROOT"
# Check if the migration name argument is provided
if [ -z "${1:-}" ]; then
  echo "Usage: $0 <migration_name>"
  echo "Please provide the name of the migration as the first argument."
  exit 1
fi

# 设置数据�? URL
DB_URL=${2:-"postgres://postgres:pass@localhost:15432/virtualmanager?sslmode=disable"}

SCHEMA_DIRECTORY=internal/ent/schema \
"$BACKEND_ROOT/scripts/ent/generate_code.sh"

MIGRATION_NAME=$1 \
SCHEMA_DIRECTORY=internal/ent/schema \
GENERATION_SCRIPT=db/generate.go \
OUTPUT_DIR=db/migrations/postgres \
POSTGRES_URL="$DB_URL" \
"$BACKEND_ROOT/scripts/ent/generate_migration_sql.sh"

// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.2
// Source: virtual.proto

package simulinkservice

import (
	"context"

	"GCF/app/Virtualmanager/internal/simulinkmanager"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CloseSimulinkRequest           = simulinkmanager.CloseSimulinkRequest
	CloseSimulinkResponse          = simulinkmanager.CloseSimulinkResponse
	ControlParam                   = simulinkmanager.ControlParam
	ControlParamValue              = simulinkmanager.ControlParamValue
	GetSimulationResultsRequest    = simulinkmanager.GetSimulationResultsRequest
	GetSimulationResultsResponse   = simulinkmanager.GetSimulationResultsResponse
	GetSimulinkInfoRequest         = simulinkmanager.GetSimulinkInfoRequest
	GetSimulinkInfoResponse        = simulinkmanager.GetSimulinkInfoResponse
	InitializeSimulinkRequest      = simulinkmanager.InitializeSimulinkRequest
	InitializeSimulinkResponse     = simulinkmanager.InitializeSimulinkResponse
	ListAvailableSimulinksRequest  = simulinkmanager.ListAvailableSimulinksRequest
	ListAvailableSimulinksResponse = simulinkmanager.ListAvailableSimulinksResponse
	OutputVar                      = simulinkmanager.OutputVar
	OutputVarValue                 = simulinkmanager.OutputVarValue
	RegisterSimulinkParamResponse  = simulinkmanager.RegisterSimulinkParamResponse
	RegisterSimulinkVarRequest     = simulinkmanager.RegisterSimulinkVarRequest
	RegisterSimulinkVarResponse    = simulinkmanager.RegisterSimulinkVarResponse
	ResgisterSimulinkParamRequest  = simulinkmanager.ResgisterSimulinkParamRequest
	RunSimulationRequest           = simulinkmanager.RunSimulationRequest
	RunSimulationResponse          = simulinkmanager.RunSimulationResponse
	SimulinkInfo                   = simulinkmanager.SimulinkInfo
	SimulinkInfoAndRuntime         = simulinkmanager.SimulinkInfoAndRuntime
	SimulinkRuntime                = simulinkmanager.SimulinkRuntime

	SimulinkService interface {
		// 初始化模型
		InitializeSimulink(ctx context.Context, in *InitializeSimulinkRequest, opts ...grpc.CallOption) (*InitializeSimulinkResponse, error)
		// 注册控制参数
		RegisterSimulinkParam(ctx context.Context, in *ResgisterSimulinkParamRequest, opts ...grpc.CallOption) (*RegisterSimulinkParamResponse, error)
		// 注册输出参数
		RegisterSimulinkVar(ctx context.Context, in *RegisterSimulinkVarRequest, opts ...grpc.CallOption) (*RegisterSimulinkVarResponse, error)
		// 运行仿真
		RunSimulation(ctx context.Context, in *RunSimulationRequest, opts ...grpc.CallOption) (*RunSimulationResponse, error)
		// 获取模型信息
		GetSimulinkInfo(ctx context.Context, in *GetSimulinkInfoRequest, opts ...grpc.CallOption) (*GetSimulinkInfoResponse, error)
		// 获取仿真结果
		GetSimulationResults(ctx context.Context, in *GetSimulationResultsRequest, opts ...grpc.CallOption) (*GetSimulationResultsResponse, error)
		// 关闭模型
		CloseSimulink(ctx context.Context, in *CloseSimulinkRequest, opts ...grpc.CallOption) (*CloseSimulinkResponse, error)
		// 获取可用模型列表
		ListAvailableSimulinks(ctx context.Context, in *ListAvailableSimulinksRequest, opts ...grpc.CallOption) (*ListAvailableSimulinksResponse, error)
	}

	defaultSimulinkService struct {
		cli zrpc.Client
	}
)

func NewSimulinkService(cli zrpc.Client) SimulinkService {
	return &defaultSimulinkService{
		cli: cli,
	}
}

// 初始化模型
func (m *defaultSimulinkService) InitializeSimulink(ctx context.Context, in *InitializeSimulinkRequest, opts ...grpc.CallOption) (*InitializeSimulinkResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.InitializeSimulink(ctx, in, opts...)
}

// 注册控制参数
func (m *defaultSimulinkService) RegisterSimulinkParam(ctx context.Context, in *ResgisterSimulinkParamRequest, opts ...grpc.CallOption) (*RegisterSimulinkParamResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.RegisterSimulinkParam(ctx, in, opts...)
}

// 注册输出参数
func (m *defaultSimulinkService) RegisterSimulinkVar(ctx context.Context, in *RegisterSimulinkVarRequest, opts ...grpc.CallOption) (*RegisterSimulinkVarResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.RegisterSimulinkVar(ctx, in, opts...)
}

// 运行仿真
func (m *defaultSimulinkService) RunSimulation(ctx context.Context, in *RunSimulationRequest, opts ...grpc.CallOption) (*RunSimulationResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.RunSimulation(ctx, in, opts...)
}

// 获取模型信息
func (m *defaultSimulinkService) GetSimulinkInfo(ctx context.Context, in *GetSimulinkInfoRequest, opts ...grpc.CallOption) (*GetSimulinkInfoResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.GetSimulinkInfo(ctx, in, opts...)
}

// 获取仿真结果
func (m *defaultSimulinkService) GetSimulationResults(ctx context.Context, in *GetSimulationResultsRequest, opts ...grpc.CallOption) (*GetSimulationResultsResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.GetSimulationResults(ctx, in, opts...)
}

// 关闭模型
func (m *defaultSimulinkService) CloseSimulink(ctx context.Context, in *CloseSimulinkRequest, opts ...grpc.CallOption) (*CloseSimulinkResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.CloseSimulink(ctx, in, opts...)
}

// 获取可用模型列表
func (m *defaultSimulinkService) ListAvailableSimulinks(ctx context.Context, in *ListAvailableSimulinksRequest, opts ...grpc.CallOption) (*ListAvailableSimulinksResponse, error) {
	client := simulinkmanager.NewSimulinkServiceClient(m.cli.Conn())
	return client.ListAvailableSimulinks(ctx, in, opts...)
}

package main

import (
	"flag"
	"log/slog"
	"os"

	"GCF/app/Virtualmanager/internal/config"
	"GCF/app/Virtualmanager/internal/handler"
	"GCF/app/Virtualmanager/internal/svc"
	"GCF/pkg/servicex"

	"github.com/zeromicro/go-zero/rest"
)

func main() {
	flag.Parse()

	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelInfo}))
	logger.Info("This is an info message")

	var c config.Config
	servicex.MustLoadConfigFile(&c)
	logger.Info("Config loaded", "config", c)
	svcCtx := svc.NewServiceContext(c)
	// logger.Info("API config", 
	// 	"host", c.RpcApiConfig.Api.Host, 
	// 	"port", c.RpcApiConfig.Api.Port)

	servicex.MainApiOnly(
		c.RpcApiConfig.Api,
		func(server *rest.Server) {
			handler.RegisterHandlers(server, svcCtx)
			logger.Info("REST server started", "host", c.RpcApiConfig.Api.Host, "port", c.RpcApiConfig.Api.Port)
		},
	)
}

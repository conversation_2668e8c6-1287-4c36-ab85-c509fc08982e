import socket
import time
import logging
import struct
import random
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import threading
import uvicorn
from fastapi.middleware.cors import CORSMiddleware

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="Motor Control API",
    description="API for controlling virtual motor",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 定义数据模型
class ValueDef(BaseModel):
    index: str
    name: str
    value: str

class StartControlRequest(BaseModel):
    deviceUID: str

class StartControlResponse(BaseModel):
    result: Optional[str] = None

class StopControlRequest(BaseModel):
    deviceUID: str

class StopControlResponse(BaseModel):
    result: Optional[str] = None

class SetValueRequest(BaseModel):
    deviceUID: str
    values: List[ValueDef]

class SetValueResponse(BaseModel):
    result: Optional[str] = None

class GetValueRequest(BaseModel):
    deviceUID: str
    values: Optional[List[ValueDef]] = None

class GetValueResponse(BaseModel):
    values: List[ValueDef]

class HealthRequest(BaseModel):
    deviceUID: str

class HealthResponse(BaseModel):
    status: str

# 全局变量
client_socket = None
current_control_value = "0"  # 保留用于记录当前值
received_value = 0.0  # 用于存储接收到的数据

# 添加新的全局变量
BASE_VOLTAGE = 1.5  # 基准电压改为1.5V
BASE_TEMPERATURE = 45.0  # 基准温度
MAX_SPEED = 3000.0  # 最大转速
RATED_CURRENT = 10.0  # 额定电流
SPEED_RATIO = 1800/0.45  # 转速转换比例

def send_command(client_socket, command):
    """
    发送控制指令,不等待响应
    """
    try:
        if isinstance(command, (int, float)):
            command = str(command)
        message = command.encode('ascii') + b'\r'
        client_socket.send(message)
        logger.info(f"发送指令: {command}")
        return True
    except (socket.error, struct.error) as e:
        logger.error(f"发送指令失败: {e}")
        return False

def receive_response(client_socket):
    """
    接收并处理客户端回发的转速数据
    """
    global received_value
    try:
        while True:
            response = client_socket.recv(1024)
            if not response:
                break
            # 解析回发的转速数据
            speed = struct.unpack('>d', response)[0]
            received_value = speed  # 存储转速值
            logger.info(f"收到电机回发转速: {speed} rpm")
    except socket.error as e:
        logger.error(f"接收转速数据失败: {e}")

def start_tcp_server():
    """
    启动 TCP 服务器
    """
    global client_socket
    host = "**************"
    port = 9999
    
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((host, port))
    server_socket.listen(1)
    
    logger.info(f"TCP服务器启动，监听 {host}:{port}...")
    
    while True:
        try:
            client_socket, client_address = server_socket.accept()
            logger.info(f"客户端 {client_address} 已连接")
            # 启动数据接收线程
            receive_thread = threading.Thread(target=receive_response, args=(client_socket,))
            receive_thread.daemon = True
            receive_thread.start()
        except Exception as e:
            logger.error(f"TCP服务器错误: {e}")
            if client_socket:
                client_socket.close()

# API 路由处理函数
@app.post("/api/v1/control/startControl")
async def start_control(request: StartControlRequest):
    global current_control_value
    default_value = "100"  # 默认控制值
    current_control_value = default_value
    if client_socket:
        send_command(client_socket, default_value)
    return StartControlResponse(result="success")

@app.post("/api/v1/control/stopControl")
async def stop_control(request: StopControlRequest):
    global current_control_value
    stop_value = "0"
    current_control_value = stop_value
    if client_socket:
        send_command(client_socket, stop_value)
    return StopControlResponse(result="success")

@app.post("/api/v1/control/setValue")
async def set_value(request: SetValueRequest):
    global current_control_value
    try:
        if len(request.values) > 0:
            value = request.values[0].value
            current_control_value = value
            if client_socket:
                send_command(client_socket, value)
            return SetValueResponse(result="success")
        return SetValueResponse(result="failed")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/v1/control/getValue")
async def get_value(request: GetValueRequest):
    """
    获取电机实际回发的转速数据和计算的虚拟参数
    """
    global received_value
    
    # 转换转速值（1800/0.45 * 实际转速），保留3位小数
    actual_speed = received_value * SPEED_RATIO
    speed_value = f"{actual_speed:.3f}"  # 转换为字符串并保留3位小数
    
    # 使用转换后的转速值计算其他参数
    # 计算电流值（与转速成正比，加入一些随机波动）
    current = (abs(actual_speed) / MAX_SPEED) * RATED_CURRENT
    current_variation = current * 0.05  # 5%的波动
    current_value = current + random.uniform(-current_variation, current_variation)
    
    # 生成电压值（在1.5V附近波动）
    voltage_value = BASE_VOLTAGE + random.uniform(-0.1, 0.1)  # ±0.1V波动
    
    # 生成温度值（在基准值附近波动，考虑速度影响）
    temp_increase = (abs(actual_speed) / MAX_SPEED) * 15  # 最大温升15度
    temperature_value = BASE_TEMPERATURE + temp_increase + random.uniform(-1, 1)  # ±1℃波动
    
    # 返回所有参数
    values = [
        ValueDef(index="1", name="speed", value=speed_value),  # 转换后的转速值 rpm
        ValueDef(index="2", name="current", value=f"{current_value:.3f}"),  # 电流 A
        ValueDef(index="3", name="voltage", value=f"{voltage_value:.3f}"),  # 电压 V
        ValueDef(index="4", name="temperature", value=f"{temperature_value:.3f}")  # 温度 ℃
    ]
    return GetValueResponse(values=values)

@app.post("/api/v1/health")
async def health_check(request: HealthRequest):
    return HealthResponse(status="running")

def main():
    # 启动 TCP 服务器线程
    tcp_thread = threading.Thread(target=start_tcp_server)
    tcp_thread.daemon = True
    tcp_thread.start()
    
    # 启动 FastAPI 服务器
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    main()
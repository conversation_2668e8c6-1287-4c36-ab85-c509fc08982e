import serial
import time
import struct
from typing import <PERSON>ple, Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List
import uvicorn
from enum import Enum
import threading
from fastapi.middleware.cors import CORSMiddleware
import glob  # 添加此导入

def find_available_ports(port_type="ttyACM"):
    """查找可用的串口设备
    
    Args:
        port_type: 串口类型，默认为"ttyACM"
        
    Returns:
        可用串口列表
    """
    return glob.glob(f"/dev/{port_type}*")

class MKV30Controller:
    def __init__(self, port=None, baudrate=57600, check_interval=15):
        """初始化MKV30控制器
        
        Args:
            port: 串口设备名，如果为None则自动检测
            baudrate: 波特率，默认57600
            check_interval: 检查间隔时间（秒），默认15秒
        """
        self.port = port
        self.baudrate = baudrate
        self.check_interval = check_interval
        self.running = True
        self.is_connected = False
        self.serial = None
        
        # 初始化数据存储
        self.current_duty_cycle = (0, 0)
        self.current_axis_deviation = 0.0
        
        # 首先检测可用设备
        available_ports = find_available_ports("ttyACM")
        if available_ports:
            self.port = available_ports[0]
            print(f"发现可用的串口设备: {self.port}")
            self.connect()  # 直接进行连接
        else:
            print("未找到可用的ACM设备")
            
        # 启动线程
        self.check_thread = threading.Thread(target=self._periodic_check, daemon=True)
        self.check_thread.start()
        self.data_thread = threading.Thread(target=self._read_data_periodically, daemon=True)
        self.data_thread.start()

    def connect(self) -> bool:
        """连接串口"""
        try:
            # 如果没有指定端口，自动检测
            if self.port is None:
                available_ports = find_available_ports("ttyACM")
                if not available_ports:
                    print("未找到可用的ACM设备")
                    self.is_connected = False
                    return False
                self.port = available_ports[0]
                print(f"自动选择端口: {self.port}")

            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1
            )
            self.is_connected = True
            self.last_command_time = time.time()
            return True
        except serial.SerialException as e:
            print(f"串口连接错误: {e}")
            self.is_connected = False
            return False

    def _periodic_check(self):
        """定期检查线程函数"""
        reconnect_interval = 5  # 改为5秒
        last_reconnect_time = 0
        
        while self.running:
            try:
                current_time = time.time()
                if self.is_connected:
                    if current_time - self.last_command_time > self.check_interval:
                        self.set_duty_cycle(0, 0)
                        print("超时检测：设置占空比为0")
                else:
                    # 每5秒检查一次可用串口
                    if current_time - last_reconnect_time >= reconnect_interval:
                        # 重新检测可用串口
                        available_ports = find_available_ports("ttyACM")
                        if available_ports:
                            if self.port != available_ports[0]:
                                self.port = available_ports[0]
                                print(f"发现新的串口设备: {self.port}")
                            print(f"尝试连接串口 {self.port}...")
                            if self.connect():
                                print("串口重新连接成功")
                        else:
                            print("未找到可用的ACM设备")  # 删除"10秒后重试"的提示
                        last_reconnect_time = current_time
                time.sleep(0.5)  # 主循环检查频率
            except:
                self.is_connected = False
                if self.serial and self.serial.is_open:
                    self.serial.close()

    def _read_data_periodically(self):
        """定期读取占空比和轴角偏差"""
        while self.running:
            try:
                if self.is_connected:
                    self.current_duty_cycle = self.read_duty_cycle() or (0, 0)
                    self.current_axis_deviation = self.read_axis_deviation() or 0.0
            except:
                self.is_connected = False
            time.sleep(0.2)

    def _send_command(self, data: bytes) -> Optional[bytes]:
        """发送命令并接收响应
        
        Args:
            data: 要发送的命令数据
            
        Returns:
            成功时返回响应数据，失败时返回None
        """
        try:
            if not self.is_connected:
                return None
            self.serial.write(data)
            # 根据命令类型确定需要读取的字节数
            cmd_type = data[1] if len(data) > 1 else None
            if cmd_type == 0x10:  # read_axis_deviation
                response_size = 8
            else:  # set_duty_cycle 和 read_duty_cycle
                response_size = 7
 
            response = self.serial.read(size=response_size)
            self.last_command_time = time.time()
            return response if len(response) == response_size else None
        except:
            self.is_connected = False
            return None

    def set_duty_cycle(self, transmitter: int, receiver: int) -> bool:
        """设置发送机和接收机的占空比
        
        Args:
            transmitter: 发送机占空比 (0-100，十进制输入)
            receiver: 接收机占空比 (0-100，十进制输入)
            
        Returns:
            设置成功返回True，失败返回False
        """
        if not (0 <= transmitter <= 100 and 0 <= receiver <= 100):
            return False
            
        command = struct.pack('BBBB', 0x01, 0x01, transmitter, receiver)
        command += bytes([0x0D, 0x0A, 0xFF])
        
        response = self._send_command(command)
        if response and len(response) == 7:
            header, cmd_type, trans, recv, cr, lf, end = response
            if (header == 0x01 and cmd_type == 0x01 and 
                cr == 0x0D and lf == 0x0A and end == 0xFF and
                trans == transmitter and recv == receiver):
                return True
        return False

    def read_duty_cycle(self) -> Optional[Tuple[int, int]]:
        """读取当前占空比
        
        Returns:
            成功时返回(发送机占空比, 接收机占空比)的元组
            返回值为十进制(0-100)
            失败时返回None
        """
        command = struct.pack('BB', 0x01, 0x02)
        command += bytes([0x0D, 0x0A, 0xFF])
        
        response = self._send_command(command)
        if response and len(response) == 7:
            header, cmd_type, trans, recv, cr, lf, end = response
            if header == 0x01 and cmd_type == 0x02 and cr == 0x0D and lf == 0x0A and end == 0xFF:
                return trans, recv
        return None

    def read_axis_deviation(self) -> Optional[float]:
        """读取轴角偏差
        
        Returns:
            成功时返回轴角偏差值（浮点数）
            失败时返回None
            
        响应格式:
            字节1: 命令头 (0x01)
            字节2: 命令类型 (0x10)
            字节3: 符号位 (0x00为正，0xFF为负)
            字节4: 高字节
            字节5: 低字节
            字节6-8: \r\n\xff
        """
        command = struct.pack('BB', 0x01, 0x10)
        command += bytes([0x0D, 0x0A, 0xFF])
        
        response = self._send_command(command)
        if response and len(response) == 8:
            header, cmd_type, sign, high, low, cr, lf, end = response
            if header == 0x01 and cmd_type == 0x10 and cr == 0x0D and lf == 0x0A and end == 0xFF:
                value = (high << 8) | low
                if sign == 0xFF:  # 负值
                    value = -value
                return value
        return None

    def close(self):
        """关闭串口连接"""
        self.running = False
        if self.check_thread.is_alive():
            self.check_thread.join(timeout=2)
        if self.data_thread.is_alive():
            self.data_thread.join(timeout=2)
        if self.serial and self.serial.is_open:
            self.serial.close()

# 新增API相关的数据模型
class ValueDef(BaseModel):
    index: str
    name: str
    value: str

class ControlStatus(str, Enum):
    INIT = "init"
    READY = "ready"
    RUNNING = "running"
    PENDING = "pending"
    ERROR = "error"

# 请求响应模型
class StartControlRequest(BaseModel):
    deviceUID: str

class StartControlResponse(BaseModel):
    result: Optional[str] = None

class StopControlRequest(BaseModel):
    deviceUID: str

class StopControlResponse(BaseModel):
    result: Optional[str] = None

class SetValueRequest(BaseModel):
    deviceUID: str
    values: List[ValueDef]

class SetValueResponse(BaseModel):
    result: Optional[str] = None

class GetValueRequest(BaseModel):
    deviceUID: str
    values: Optional[List[ValueDef]] = None

class GetValueResponse(BaseModel):
    values: List[ValueDef]

class HealthRequest(BaseModel):
    deviceUID: str

class HealthResponse(BaseModel):
    status: str

# API服务器类
class MotorApiServer:
    def __init__(self, port=None, baudrate=57600):
        self.port = port
        self.baudrate = baudrate
        self.controller = None
        self.app = FastAPI(
            title="Step Motor Control API",
            description="API for controlling step motor",
            version="1.0.0"
        )
        
        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self.status = ControlStatus.INIT
        self.device_uid = ""
        self.setup_routes()

    def initialize_controller(self) -> bool:
        if self.controller is None:
            self.controller = MKV30Controller(self.port, self.baudrate)
        return self.controller.is_connected

    def setup_routes(self):
        @self.app.post("/api/v1/control/startControl")
        async def start_control(request: StartControlRequest) -> StartControlResponse:
            """启动控制，并将占空比默认设置为50,50"""
            if not request.deviceUID:
                raise HTTPException(status_code=400, detail="deviceUID is required")
                
            self.device_uid = request.deviceUID
            
            # 设置默认占空比为50,50
            success = self.controller.set_duty_cycle(50, 50)
            if not success:
                raise HTTPException(status_code=500, detail="Failed to set initial duty cycle")
                
            self.status = ControlStatus.RUNNING
            return StartControlResponse(result="success")

        @self.app.post("/api/v1/control/stopControl")
        async def stop_control(request: StopControlRequest) -> StopControlResponse:
            if request.deviceUID != self.device_uid:
                raise HTTPException(status_code=400, detail="Invalid deviceUID")
            self.controller.set_duty_cycle(0, 0)
            self.status = ControlStatus.READY
            return StopControlResponse(result="success")

        @self.app.post("/api/v1/control/setValue")
        async def set_value(request: SetValueRequest) -> SetValueResponse:
            if request.deviceUID != self.device_uid:
                raise HTTPException(status_code=400, detail="Invalid deviceUID")
            
            try:
                for value in request.values:
                    if value.name == "transmitter":
                        trans = int(value.value)
                    elif value.name == "receiver":
                        recv = int(value.value)
                
                success = self.controller.set_duty_cycle(trans, recv)
                if not success:
                    raise HTTPException(status_code=400, detail="Failed to set duty cycle")
                
                return SetValueResponse(result="success")
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/v1/control/getValue")
        async def get_value(request: GetValueRequest) -> GetValueResponse:
            if request.deviceUID != self.device_uid:
                raise HTTPException(status_code=400, detail="Invalid deviceUID")
            
            values = []
            
            # 读取占空比和轴角偏差
            trans, recv = self.controller.current_duty_cycle
            deviation = self.controller.current_axis_deviation
            
            values.extend([
                ValueDef(index="1", name="transmitter", value=str(trans)),
                ValueDef(index="2", name="receiver", value=str(recv)),
                ValueDef(index="3", name="axis_deviation", value=str(deviation))
            ])
            
            return GetValueResponse(values=values)

        @self.app.post("/api/v1/health")
        async def health_check(request: HealthRequest) -> HealthResponse:
            if request.deviceUID != self.device_uid:
                return HealthResponse(status=ControlStatus.ERROR)
            return HealthResponse(status=self.status)

    def run(self, host="0.0.0.0", port=8000):
        uvicorn.run(self.app, host=host, port=port)

    def cleanup(self):
        self.controller.close()

# 修改主函数
if __name__ == "__main__":
    server = MotorApiServer()
    
    def check_serial_connection():
        while True:
            print(f"正在尝试连接串口...")
            if server.initialize_controller():
                print("串口连接成功，启动服务器...")
                try:
                    server.run()
                except Exception as e:
                    print(f"服务器运行出错: {e}")
                finally:
                    if server.controller:
                        server.controller.close()
                break
            else:
                print(f"串口连接失败，5秒后重试...")
                time.sleep(5)

    try:
        check_serial_connection()
    except KeyboardInterrupt:
        print("程序被用户中断")
        if server.controller:
            server.controller.close()

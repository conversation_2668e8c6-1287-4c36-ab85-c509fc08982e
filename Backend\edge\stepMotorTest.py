import serial
import time
import struct
from typing import Tuple, Optional
import threading

class MKV30Controller:
    def __init__(self, port="/dev/ttyACM0", baudrate=57600, check_interval=15):
        """初始化MKV30控制器
        
        Args:
            port: 串口设备名
            baudrate: 波特率，默认57600
            check_interval: 检查间隔时间（秒），默认10秒
        """
        self.serial = serial.Serial(
            port=port,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1
        )
        self.last_command_time = time.time()
        self.check_interval = check_interval
        self.running = True
        
        # 启动检查线程
        self.check_thread = threading.Thread(target=self._periodic_check, daemon=True)
        self.check_thread.start()
        
    def _periodic_check(self):
        """定期检查线程函数"""
        while self.running:
            current_time = time.time()
            if current_time - self.last_command_time > self.check_interval:
                self.set_duty_cycle(0, 0)
                print("超时检测：设置占空比为0")
            time.sleep(0.5)

    def _send_command(self, data: bytes) -> Optional[bytes]:
        """发送命令并接收响应
        
        Args:
            data: 要发送的命令数据
            
        Returns:
            成功时返回响应数据，失败时返回None
        """
        self.serial.write(data)
        # 根据命令类型确定需要读取的字节数
        cmd_type = data[1] if len(data) > 1 else None
        if cmd_type == 0x10:  # read_axis_deviation
            response_size = 8
        else:  # set_duty_cycle 和 read_duty_cycle
            response_size = 7
 
        response = self.serial.read(size=response_size)
        self.last_command_time = time.time()
        return response if len(response) == response_size else None

    def set_duty_cycle(self, transmitter: int, receiver: int) -> bool:
        """设置发送机和接收机的占空比
        
        Args:
            transmitter: 发送机占空比 (0-100，十进制输入)
            receiver: 接收机占空比 (0-100，十进制输入)
            
        Returns:
            设置成功返回True，失败返回False
        """
        if not (0 <= transmitter <= 100 and 0 <= receiver <= 100):
            return False
            
        # 将十进制值(0-100)直接作为16进制值使用
        command = struct.pack('BBBB', 0x01, 0x01, transmitter, receiver)
        command += bytes([0x0D, 0x0A, 0xFF])
        
        response = self._send_command(command)
        if response and len(response) == 7:
            header, cmd_type, trans, recv, cr, lf, end = response
            print(f"发送的命令: {[hex(b) for b in command]}")
            print(f"收到的响应: {[hex(b) for b in response]}")
            if (header == 0x01 and cmd_type == 0x01 and 
                cr == 0x0D and lf == 0x0A and end == 0xFF and
                trans == transmitter and recv == receiver):
                print(f"设置占空比: 发送机={transmitter}, 接收机={receiver}, 成功")
                return True
        print(f"设置占空比: 发送机={transmitter}, 接收机={receiver}, 失败")
        return False

    def read_duty_cycle(self) -> Optional[Tuple[int, int]]:
        """读取当前占空比
        
        Returns:
            成功时返回(发送机占空比, 接收机占空比)的元组
            返回值为十进制(0-100)
            失败时返回None
        """
        command = struct.pack('BB', 0x01, 0x02)
        command += bytes([0x0D, 0x0A, 0xFF])
        
        response = self._send_command(command)
        if response and len(response) == 7:
            header, cmd_type, trans, recv, cr, lf, end = response
            print(f"收到的响应: {[hex(b) for b in response]}")
            if header == 0x01 and cmd_type == 0x02 and cr == 0x0D and lf == 0x0A and end == 0xFF:
                # 直接使用返回的值作为占空比
                print(f"读取占空比: 发送机={trans}, 接收机={recv}")
                return trans, recv
        print("读取占空比失败")
        return None

    def read_axis_deviation(self) -> Optional[float]:
        """读取轴角偏差
        
        Returns:
            成功时返回轴角偏差值（浮点数）
            失败时返回None
            
        响应格式:
            字节1: 命令头 (0x01)
            字节2: 命令类型 (0x10)
            字节3: 符号位 (0x00为正，0xFF为负)
            字节4: 高字节
            字节5: 低字节
            字节6-8: \r\n\xff
        """
        command = struct.pack('BB', 0x01, 0x10)
        command += bytes([0x0D, 0x0A, 0xFF])  # 使用二进制值替代 \r\n\xff
        
        response = self._send_command(command)
        print(response)
        if response and len(response) == 8:
            header, cmd_type, sign, high, low, cr, lf, end = response
            if header == 0x01 and cmd_type == 0x10 and cr == 0x0D and lf == 0x0A and end == 0xFF:
                value = (high << 8) | low
                if sign == 0xFF:  # 负值
                    value = -value
                print(f"读取轴角偏差: {value}")
                return value
        print("读取轴角偏差失败")
        return None

    def close(self):
        """关闭串口连接"""
        self.running = False
        if self.check_thread.is_alive():
            self.check_thread.join(timeout=2)
        if self.serial.is_open:
            self.serial.close()
            print("串口已关闭")

def test_motor_control():
    """测试电机控制功能"""
    # 创建控制器实例
    controller = MKV30Controller(check_interval=10)
    
    try:
        while True:
            print("\n=== 电机控制测试菜单 ===")
            print("1. 设置占空比")
            print("2. 读取占空比")
            print("3. 读取轴角偏差")
            print("4. 停止电机")
            print("5. 退出")
            
            choice = input("请选择操作 (1-5): ")
            
            if choice == '1':
                try:
                    trans = int(input("请输入发送机占空比 (0-100): "))
                    recv = int(input("请输入接收机占空比 (0-100): "))
                    
                    if not (0 <= trans <= 100 and 0 <= recv <= 100):
                        print("错误：占空比必须在0-100范围内")
                        continue
                        
                    controller.set_duty_cycle(trans, recv)
                except ValueError:
                    print("错误：请输入有效的数字")
                    continue
            
            elif choice == '2':
                controller.read_duty_cycle()
            
            elif choice == '3':
                controller.read_axis_deviation()
            
            elif choice == '4':
                controller.set_duty_cycle(0, 0)
                print("电机已停止")
            
            elif choice == '5':
                break
            
            else:
                print("无效的选择，请重试")
            
            time.sleep(0.5)  # 短暂延时，避免操作太快
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    finally:
        controller.close()

if __name__ == "__main__":
    test_motor_control()

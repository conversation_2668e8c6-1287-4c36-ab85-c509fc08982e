import serial
import time
import threading
from typing import List, Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from enum import Enum
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
import glob  # 添加此导入

# API数据模型
class ValueDef(BaseModel):
    index: str
    name: str
    value: str

class ControlStatus(str, Enum):
    INIT = "init"
    READY = "ready"
    RUNNING = "running"
    PENDING = "pending"
    ERROR = "error"

# 请求响应模型
class StartControlRequest(BaseModel):
    deviceUID: str

class StartControlResponse(BaseModel):
    result: Optional[str] = None

class StopControlRequest(BaseModel):
    deviceUID: str

class StopControlResponse(BaseModel):
    result: Optional[str] = None

class SetValueRequest(BaseModel):
    deviceUID: str
    values: List[ValueDef]

class SetValueResponse(BaseModel):
    result: Optional[str] = None

class GetValueRequest(BaseModel):
    deviceUID: str
    values: Optional[List[ValueDef]] = None

class GetValueResponse(BaseModel):
    values: List[ValueDef]

class HealthRequest(BaseModel):
    deviceUID: str

class HealthResponse(BaseModel):
    status: str

def find_available_ports(port_type="ttyUSB"):
    """查找可用的串口设备
    
    Args:
        port_type: 串口类型，默认为"ttyUSB"
        
    Returns:
        可用串口列表
    """
    return glob.glob(f"/dev/{port_type}*")

# 电机控制类
class MotorController:
    def __init__(self, port=None, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.serial = None
        self.running = True
        self.is_connected = False
        self.motor_data = {}
        self.default_pwm = 2000
        self.rx_thread = None
        
        # 首先检测可用设备
        available_ports = find_available_ports("ttyUSB")
        if available_ports:
            self.port = available_ports[0]
            print(f"发现可用的串口设备: {self.port}")
            self.connect()  # 直接进行连接
        else:
            print("未找到可用的USB设备")
            
        # 启动检测线程用于监控串口状态
        self.check_thread = threading.Thread(target=self._check_and_connect, daemon=True)
        self.check_thread.start()

    def _check_and_connect(self):
        """检查并连接串口的线程函数"""
        while self.running:
            if not self.is_connected:
                # 检测可用串口
                available_ports = find_available_ports("ttyUSB")
                if available_ports:
                    if self.port != available_ports[0]:
                        self.port = available_ports[0]
                        print(f"发现可用的串口设备: {self.port}")
                    if self.connect():
                        print("串口连接成功")
            time.sleep(5)  # 每5秒检测一次

    def connect(self) -> bool:
        """连接串口"""
        try:
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=1
            )
            self.is_connected = True
            # 启动接收线程
            if self.rx_thread is None or not self.rx_thread.is_alive():
                self.rx_thread = threading.Thread(target=self._receive_data)
                self.rx_thread.start()
            return True
        except serial.SerialException as e:
            print(f"串口连接错误: {e}")
            self.is_connected = False
            return False

    def _receive_data(self):
        """接收数据的线程函数"""
        buffer = ''
        while self.running:
            try:
                if not self.is_connected:
                    time.sleep(0.5)
                    continue

                if self.serial.in_waiting:
                    char = self.serial.read().decode('utf-8')
                    buffer += char
                    if char == '\n':
                        if buffer.startswith('$DATA'):
                            try:
                                data = buffer.strip().strip('$DATA,#').split(',')
                                self.motor_data = {
                                    'pwm': float(data[0]),
                                    'voltage': float(data[1]),
                                    'current_u': float(data[2]),
                                    'current_v': float(data[3]),
                                    'current_w': float(data[4]),
                                    'current_bus': float(data[5]),
                                    'temperature': float(data[6])
                                }
                            except:
                                pass
                        buffer = ''
            except:
                if self.is_connected:
                    print("串口连接已断开")
                self.is_connected = False
                if self.serial and self.serial.is_open:
                    self.serial.close()
                time.sleep(0.5)

    def set_pwm(self, duty):
        try:
            if not self.is_connected:
                return False
            if -10000 <= duty <= 10000:
                self.serial.write(f'PWM {duty}\r\n'.encode())
                return True
            return False
        except:
            self.is_connected = False
            return False

    def stop_motor(self):
        try:
            if not self.is_connected:
                return False
            self.serial.write('STOP 0\r\n'.encode())
            return True
        except:
            self.is_connected = False
            return False

    def close(self):
        """关闭串口连接"""
        self.running = False
        if self.check_thread and self.check_thread.is_alive():
            self.check_thread.join(timeout=2)
        if self.rx_thread and self.rx_thread.is_alive():
            self.rx_thread.join(timeout=2)
        if self.serial and self.serial.is_open:
            self.serial.close()

# API服务器类
class MotorApiServer:
    def __init__(self, port=None, baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.controller = None
        self.app = FastAPI(
            title="Motor Control API",
            description="API for controlling BLDC motor",
            version="1.0.0"
        )
        self.status = ControlStatus.INIT
        self.device_uid = ""
        
        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self.setup_routes()

    def initialize_controller(self) -> bool:
        if self.controller is None:
            self.controller = MotorController(self.port, self.baudrate)
        return self.controller.is_connected

    def setup_routes(self):
        @self.app.get("/")
        async def root():
            """API根路径，返回基本信息"""
            return {
                "title": "Motor Control API",
                "version": "1.0.0",
                "status": "running",
                "docs_url": "/docs",
                "redoc_url": "/redoc"
            }

        @self.app.post("/api/v1/control/startControl")
        async def start_control(request: StartControlRequest) -> StartControlResponse:
            if not request.deviceUID:
                raise HTTPException(status_code=400, detail="deviceUID is required")
            
            if not self.controller.is_connected:
                raise HTTPException(status_code=503, detail="Device not connected")
            
            self.device_uid = request.deviceUID
            success = self.controller.set_pwm(self.controller.default_pwm)
            
            if success:
                self.status = ControlStatus.RUNNING
                return StartControlResponse(result="success")
            raise HTTPException(status_code=500, detail="Failed to start motor")

        @self.app.post("/api/v1/control/stopControl")
        async def stop_control(request: StopControlRequest) -> StopControlResponse:
            if request.deviceUID != self.device_uid:
                raise HTTPException(status_code=400, detail="Invalid deviceUID")
            
            if self.controller.stop_motor():
                self.status = ControlStatus.READY
                return StopControlResponse(result="success")
            raise HTTPException(status_code=500, detail="Failed to stop motor")

        @self.app.post("/api/v1/control/setValue")
        async def set_value(request: SetValueRequest) -> SetValueResponse:
            if request.deviceUID != self.device_uid:
                raise HTTPException(status_code=400, detail="Invalid deviceUID")
            
            try:
                for value in request.values:
                    if value.name == "pwm":
                        pwm = int(value.value)
                        if not self.controller.set_pwm(pwm):
                            raise ValueError("Invalid PWM value")
                
                return SetValueResponse(result="success")
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/v1/control/getValue")
        async def get_value(request: GetValueRequest) -> GetValueResponse:
            if request.deviceUID != self.device_uid:
                raise HTTPException(status_code=400, detail="Invalid deviceUID")
            
            values = [
                ValueDef(index="1", name="pwm", value=str(self.controller.motor_data.get('pwm', 0))),
                ValueDef(index="2", name="voltage", value=str(self.controller.motor_data.get('voltage', 0))),
                ValueDef(index="3", name="current_u", value=str(self.controller.motor_data.get('current_u', 0))),
                ValueDef(index="4", name="current_v", value=str(self.controller.motor_data.get('current_v', 0))),
                ValueDef(index="5", name="current_w", value=str(self.controller.motor_data.get('current_w', 0))),
                ValueDef(index="6", name="current_bus", value=str(self.controller.motor_data.get('current_bus', 0))),
                ValueDef(index="7", name="temperature", value=str(self.controller.motor_data.get('temperature', 0)))
            ]
            
            return GetValueResponse(values=values)

        @self.app.post("/api/v1/health")
        async def health_check(request: HealthRequest) -> HealthResponse:
            if request.deviceUID != self.device_uid and self.device_uid != "":
                return HealthResponse(status=ControlStatus.ERROR)
            return HealthResponse(status=self.status)

    def run(self, host="0.0.0.0", port=8000):
        uvicorn.run(self.app, host=host, port=port)

    def cleanup(self):
        self.controller.close()

if __name__ == "__main__":
    server = MotorApiServer()
    
    def check_serial_connection():
        while True:
            print(f"正在尝试连接串口 {server.port}...")
            if server.initialize_controller():
                print("串口连接成功，启动服务器...")
                try:
                    server.run()
                except Exception as e:
                    print(f"服务器运行出错: {e}")
                finally:
                    if server.controller:
                        server.controller.close()
                break
            else:
                print(f"串口连接失败，5秒后重试...")
                time.sleep(5)

    try:
        check_serial_connection()
    except KeyboardInterrupt:
        print("程序被用户中断")
        if server.controller:
            server.controller.close()

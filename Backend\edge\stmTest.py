import serial
import threading

class MotorController:
    def __init__(self, port='COM4', baudrate=115200):
        self.serial = serial.Serial(port, baudrate)
        self.running = True
        self.motor_data = {}
        self.default_pwm = 2000  # 设置默认PWM占空比为2000
        
        # 启动接收线程
        self.rx_thread = threading.Thread(target=self._receive_data)
        self.rx_thread.start()
    
    def _receive_data(self):
        buffer = ''
        while self.running:
            if self.serial.in_waiting:
                char = self.serial.read().decode('utf-8')
                buffer += char
                if char == '\n':
                    if buffer.startswith('$DATA'):
                        # 解析数据
                        try:
                            data = buffer.strip().strip('$DATA,#').split(',')
                            self.motor_data = {
                                'pwm': float(data[0]),
                                'voltage': float(data[1]),
                                'current_u': float(data[2]),
                                'current_v': float(data[3]),
                                'current_w': float(data[4]),
                                'current_bus': float(data[5]),
                                'temperature': float(data[6])
                            }
                        except:
                            pass
                    buffer = ''
    
    def set_pwm(self, duty):
        """设置PWM占空比 (-10000 到 10000)"""
        if -10000 <= duty <= 10000:
            self.serial.write(f'PWM {duty}\r\n'.encode())
        else:
            print("PWM duty cycle must be between -10000 and 10000")
    
    def stop_motor(self):
        """停止电机"""
        self.serial.write('STOP 0\r\n'.encode())
    
    def start_motor(self):
        """启动电机，使用默认PWM占空比"""
        self.set_pwm(self.default_pwm)
    
    def read_parameters(self):
        """读取并显示当前电机参数"""
        print("\n当前电机参数:")
        print("-" * 40)
        print(f"PWM占空比: {self.motor_data.get('pwm', 0):.1f}%")
        print(f"电源电压: {self.motor_data.get('voltage', 0):.3f}V")
        print(f"U相电流: {self.motor_data.get('current_u', 0):.3f}mA")
        print(f"V相电流: {self.motor_data.get('current_v', 0):.3f}mA")
        print(f"W相电流: {self.motor_data.get('current_w', 0):.3f}mA")
        print(f"母线电流: {self.motor_data.get('current_bus', 0):.3f}mA")
        print(f"温度: {self.motor_data.get('temperature', 0):.1f}°C")
        print("-" * 40)
    
    def close(self):
        """关闭串口"""
        self.running = False
        self.rx_thread.join()
        self.serial.close()

def main():
    # 创建电机控制器实例
    controller = MotorController()
    
    print("Motor Control Commands:")
    print("1. Set PWM duty cycle: pwm <value>  (-10000 to 10000)")
    print("2. Start motor: start")
    print("3. Stop motor: stop")
    print("4. Read parameters: read")
    print("5. Exit: exit")
    
    while True:
        cmd = input("\nEnter command: ").strip().lower()
        
        if cmd == 'exit':
            controller.close()
            break
        elif cmd == 'stop':
            controller.stop_motor()
        elif cmd == 'start':
            controller.start_motor()
            print(f"Starting motor with default PWM duty: {controller.default_pwm}")
        elif cmd == 'read':
            controller.read_parameters()
        elif cmd.startswith('pwm '):
            try:
                duty = int(cmd.split()[1])
                controller.set_pwm(duty)
            except:
                print("Invalid PWM value")
        else:
            print("Invalid command")

if __name__ == '__main__':
    main()

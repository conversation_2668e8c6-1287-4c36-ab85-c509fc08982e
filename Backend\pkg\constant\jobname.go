package constant

import (
	"crypto/md5"
	"fmt"
)

func TrainingJobNameFunc(datasetId string, projectId string, templateId string, globalJobId string) string {
	var jobName string
	if projectId != "" && projectId != "0" { // In mysql, the empty field of projectId will be filled with 0, so we need to consider this case.
		jobName = fmt.Sprintf("training-job-%s", PodNaming(datasetId, projectId, templateId, globalJobId))
		fmt.Println("jobName project: ", jobName)
	} else {
		jobName = fmt.Sprintf("training-job-%s", PodNaming(datasetId, templateId, templateId, globalJobId))
		fmt.Println("jobName template: ", jobName)
	}
	return jobName
}

// hash is used for shorter the naming of pods, kubeflow operator has 64 characters limit for the name of pods.
func PodNaming(datasetId string, projectId string, templateId string, globalJobId string) string {
	if projectId != "" && projectId != "0" {
		return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s-%s-%s", datasetId, projectId, globalJobId))))
	} else {
		return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s-%s-%s", datasetId, templateId, globalJobId))))
	}
}

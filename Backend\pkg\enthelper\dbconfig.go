package enthelper

import (
	"database/sql"
	"os"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	DriverTypePostgres = "postgres"
	DriverTypeMysql    = "mysql"
)

type DbConfig struct {
	Driver string `json:",options=[mysql,postgres]"`
	Source string `json:",optional"`
}

func (c DbConfig) MustGetSource() string {
	return c.Source
}

func (c DbConfig) MustGetSchemaVersion() string {
	db, err := sql.Open(c.Driver, c.MustGetSource())
	defer db.Close()
	logx.Must(err)

	err = db.Ping()
	logx.Must(err)

	// Get the latest schema version
	version, err := getLatestSchema(db, c.Driver)
	logx.Must(err)
	return version
}

func (c DbConfig) MustCheckSchema(version string) {
	dbVersion := c.MustGetSchemaVersion()
	if dbVersion != version {
		logx.Errorf("db schema version %s is not compatible with service schema version %s", dbVersion, version)
		os.Exit(1)
	}
}

func getLatestSchema(db *sql.DB, driver string) (string, error) {
	var version string
	var query string

	switch driver {
	case DriverTypePostgres:
		query = "SELECT version FROM public.flyway_schema_history ORDER BY installed_rank DESC LIMIT 1"
	case DriverTypeMysql:
		query = "SELECT version FROM flyway_schema_history ORDER BY installed_rank DESC LIMIT 1"
	default:
		return "", errors.New("unsupported database type")
	}

	err := db.QueryRow(query).Scan(&version)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		return "", errors.New("version history table is empty")
	case err != nil:
		return "", err
	default:
		return version, nil
	}
}

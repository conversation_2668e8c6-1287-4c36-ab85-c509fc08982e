package generate

import (
	"flag"
	"fmt"
	"log"

	"GCF/pkg/enthelper"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql/schema"

	_ "github.com/lib/pq"
)

var (
	migrationName   = flag.String("n", "", "migration name")
	outputDirectory = flag.String("o", "", "output directory")
	postgresUrl     = flag.String("postgres-url", "postgres://postgres:pass@localhost:15432/migration?sslmode=disable", "database url")
)

func Main(diffFunc enthelper.DiffFunc) {
	flag.Parse()

	err := enthelper.GenerateMigrationSql(*migrationName, diffFunc, enthelper.GenerateMigrationSqlOpts{
		Directory: *outputDirectory,
		Opts: []schema.MigrateOption{
			schema.WithMigrationMode(schema.ModeInspect), // provide migration mode
			schema.WithDialect(dialect.Postgres),         // Ent dialect to use
			schema.DisableChecksum(),
		},
		Url: *postgresUrl,
	})
	if err != nil {
		fmt.Println(`Migration requires some local database servers running on localhost.
Run this script under the service directory:
  ./scripts/start_migration_db.sh`)
		log.Fatalf("failed generating migration file: %v", err)
	}
}

package enthelper

import (
	"context"
	"log"

	"ariga.io/atlas/sql/sqltool"
	"entgo.io/ent/dialect/sql/schema"
)

type GenerateMigrationSqlOpts struct {
	Directory string
	Opts      []schema.MigrateOption
	Url       string
}

// DiffFunc can be found as `migrate.NamedDiff`. Every generated ent/ package
// provides a Diff function.
type DiffFunc func(ctx context.Context, url, name string, opts ...schema.MigrateOption) error

// GenerateMigrationSql write migration sql to a directory.
// Output example: U20231123015451__create_schema.sql  V20231123015451__create_schema.sql  atlas.sum
func GenerateMigrationSql(
	migrationName string,
	diffFunc DiffFunc,
	opts GenerateMigrationSqlOpts,
) error {
	ctx := context.Background()
	// Create a local migration directory able to understand Flyway migration file format for replay.
	dir, err := sqltool.NewFlywayDir(opts.Directory)
	if err != nil {
		log.Fatalf("failed creating atlas migration directory: %v", err)
	}

	opts.Opts = append(opts.Opts, schema.WithDir(dir))
	// Generate migrations using Atlas support for Postgres (note the Ent dialect option passed above).
	err = diffFunc(ctx, opts.Url, migrationName, opts.Opts...)
	return err
}

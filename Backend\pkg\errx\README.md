# `errx`: Universal Error for RPC and API

With ErrX, we can use same set of error struct for both API and RPC.

```text
                                             End User
                                             ▲
                                             │ HTTP Error
                                             │ (Code + Message)
                                             │
┌────────────┐ raise  ┌──────────────┐   ┌───┴──────────────┐
│API Service │        │ErrorX        │   │errx.HttpHandler  │
│            ├────────►(Code+Message)├──►│                  │
└────────────┘        └──────────────┘   └──────────────────┘
                                             ▲
                                             │ RPC Status
                                             │ (Also use HTTP Status Code)
┌────────────┐ raise  ┌──────────────┐   ┌───┴───────────────┐
│RPC Service │        │ErrorX        │   │errx.RpcInterceptor│
│            ├────────►(Code+Message)├──►│                   │
└────────────┘        └──────────────┘   └───────────────────┘
```

# Usage: Register Error Handler For Each Service

To enable auto-conversion, register handler at service start:

```go
// For RPC
func StartRpcService() {
    ...
    s.AddUnaryInterceptors(errx.AutoLogOnErrorInterceptor)
}

// For API
func StartApiService() {
    ...
    httpx.SetErrorHandler(apierr.Handler)
}
```

# How to use ErrorX

when you want to return an error, use `errx.New` to create an error.
when error occurs and want to log in the console with stacktrace, use this before return:

```
<PERSON><PERSON>(err.<PERSON><PERSON><PERSON>(), errx.<PERSON>())
```

for returning error, just return errx.New(), like this:

```
return nil, errx.ErrInternalService
```

for error getting from downstream service like rpc, just return err, there's no need to wrap it again.

# Right now, our error logs should be like this:

## For RPC:

First line:

```text
{"@timestamp":"2023-07-26T16:59:01.269+08:00","caller":"logic/checkfileexistslogic.go:47","content":"tos: unexpected status code error: StatusCode=404, ExpectedCodes=[200], RequestID=950301c0e05500b064c0e055-b38fa0a-1qOaMH-HO-cb-tos-1az-front-azc-1","level":"error","span":"e2677659999d1570","stacktrace":"goroutine 144 [running]:\nruntime/debug.Stack()\n\t/Users/<USER>/go/go1.19/src/runtime/debug/stack.go:24 +0x64\ngithub.com/hpcaitech/Cloud-Platform/src/backend/pkg/errx.StackField()\n\t/Users/<USER>/Documents/Github/Cloud-Platform/src/backend/common/errx/errx.go:229 +0x1c\ngithub.com/hpcaitech/Cloud-Platform/src/backend/app/userstorage-tos/cmd/rpc/internal/logic.(*CheckFileExistsLogic).CheckFileExists(0x14000757db0, 0x1400081cd00)\n\t/Users/<USER>/Documents/Github/Cloud-Platform/src/backend/app/userstorage-tos/cmd/rpc/internal/logic/checkfileexistslogic.go:47 +0x140\ngithub.com/hpcaitech/Cloud-Platform/src/backend/app/userstorage-tos/cmd/rpc/internal/server.(*UserstorageServer).CheckFileExists(0x4?, {0x10485e600?, 0x14000635740}, 0x140001256c8?)\n\t/Users/<USER>/Documents/Github/Cloud-Platform/src/backend/app/userstorage-tos/cmd/rpc/internal/server/userstorageserver.go:127 +0xa8\ngithub.com/hpcaitech/Cloud-Platform/src/backend/app/userstorage-tos/cmd/rpc/userstorage._Userstorage_CheckFileExists_Handler.func1({0x10485e600, 0x14000635740}, {0x104631600?, 0x1400081cd00})\n\t/Users/<USER>/Documents/Github/Cloud-Platform/src/backend/app/userstorage-tos/cmd/rpc/userstorage/userstorage_grpc.pb.go:726 +0x74\ngithub.com/hpcaitech/Cloud-Platform/src/backend/pkg/errx.AutoLogOnErrorInterceptor({0x10485e600, 0x14000635740}, {0x104631600?, 0x1400081cd00?}, 0x102aee810?, 0x1400065e960?)\n\t/Users/<USER>/Documents/Github/Cloud-Platform/src/backend/common/errx/rpchandler.go:15 +0x38\ngoogle.golang.org/grpc.getChainUnaryHandler.func1({0x10485e600, 0x14000635740}, {0x104631600, 0x1400081cd00})\n\t/Users/<USER>/go/pkg/mod/google.golang.org/grpc@v1.53.0/server.go:1163 +0xa0\ngithub.com/zeromicro/go-zero/zrpc/internal/serverinterceptors.UnaryTimeoutInterceptor.func1.1()\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.5.0/zrpc/internal/serverinterceptors/timeoutinterceptor.go:39 +0x140\ncreated by github.com/zeromicro/go-zero/zrpc/internal/serverinterceptors.UnaryTimeoutInterceptor.func1\n\t/Users/<USER>/go/pkg/mod/github.com/zeromicro/go-zero@v1.5.0/zrpc/internal/serverinterceptors/timeoutinterceptor.go:29 +0x1e0\n","trace":"93db2c1d704f24f967febe088d0418df"}
```

It contains some key fields: timestamp, caller, content, span, stacktrace, trace to help us debug.
traceId will be unique through the whole process.

Second line:

```
{"@timestamp":"2023-07-26T16:59:01.270+08:00","caller":"errx/rpchandler.go:19","content":"[RPC-ERR] code: 500, message: HeadObjectV2 failed","level":"error","span":"e2677659999d1570","trace":"93db2c1d704f24f967febe088d0418df"}
```

It's the info that returns to upper layer like api server.

The third line:

```
{"@timestamp":"2023-07-26T16:59:01.270+08:00","caller":"serverinterceptors/statinterceptor.go:72","content":"[::1]:60163 - /userstorage.Userstorage/CheckFileExists - {\"filePath\":\"1/job/100/output/master-.txt\"}","duration":"454.0ms","level":"info","span":"e2677659999d1570","trace":"93db2c1d704f24f967febe088d0418df"}
```

It's the info that contains input.

package errx

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/zeromicro/go-zero/core/logx"

	"google.golang.org/grpc"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// With ErrX, we can use same set of error struct for both API and RPC.
//
//
//                                              End User
//                                              ▲
//                                              │ HTTP Error
//                                              │ (Code + Message)
//                                              │
// ┌────────────┐ raise  ┌──────────────┐   ┌───┴──────────────┐
// │API Service │        │ErrorX        │   │errx.HttpHandler  │
// │            ├────────►(Code+Message)├──►│                  │
// └────────────┘        └──────────────┘   └──────────────────┘
//                                              ▲
//                                              │ RPC Status
//                                              │ (Also use HTTP Status Code)
// ┌────────────┐ raise  ┌──────────────┐   ┌───┴───────────────┐
// │RPC Service │        │ErrorX        │   │errx.RpcInterceptor│
// │            ├────────►(Code+Message)├──►│                   │
// └────────────┘        └──────────────┘   └───────────────────┘

// ErrorX could be handled by both RPC and API.
// See the ascii diagram above.
type ErrorX interface {
	error
	HttpCode() int
	HttpBody() any
	RpcStatus() *status.Status
}

// This line verify interface compliance at compile time.
var _ ErrorX = (*defaultErrorX)(nil)

// Error defines a restricted logical error model of our business.
// It could be converted into API and RPC errors.
type defaultErrorX struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
}

func (e *defaultErrorX) Error() string {
	return fmt.Sprintf("code: %d, message: %s", e.Code, e.Message)
}

// Is ignores Details field, only compares Code and Message
// If Error is compared with normal error, compare their string representation.
func (e *defaultErrorX) Is(target error) bool {
	var targetUError *defaultErrorX
	ok := errors.As(target, &targetUError)
	if !ok {
		return e.Error() == target.Error()
	}

	if targetUError.Code != e.Code {
		return false
	}

	if targetUError.Message != e.Message {
		return false
	}

	return true
}

type HttpBody struct {
	Code    int               `json:"code,omitempty"`
	Message string            `json:"message"`
	Values  map[string]string `json:"values,omitempty"`
}

func (e *defaultErrorX) HttpCode() int {
	return int(e.Code)
}
func (e *defaultErrorX) HttpBody() any {
	return &HttpBody{
		Message: e.Message,
	}
}

// RpcStatus would be called by handler AutoLogOnErrorInterceptor
func (e *defaultErrorX) RpcStatus() *status.Status {
	return status.New(codes.Code(e.Code), e.Message)
}

// New constructs an *Error representing {code, message, details}.
// Deprecated: Create fixed i18n error in src/backend/pkg/errx/uniquecode.go
func New(code int32, message string) ErrorX {
	e := &defaultErrorX{
		Code:    code,
		Message: message,
	}
	return e
}

//                                             End User
//                                             ▲
//                                             │ HTTP Error
//                                             │ (Code + Message)
//                                             │
//┌────────────┐ raise  ┌──────────────┐   ┌───┴──────────────┐
//│API Service │        │ErrorX        │   │errx.HttpHandler  │
//│            ├────────►(Code+Message)├──►│                  │
//└────────────┘        └──────────────┘   └──────────────────┘
//                                             ▲
//                                             │ RPC Status
//                                             │ (Also use HTTP Status Code)
//                                         ┌───┴───────────────┐
//                                         │errx.RpcInterceptor│
//                                         │                   │
//                                         └───────────────────┘

// HttpHandler converts ErrorX or RPC Status to http status code and body.
// See the ascii diagram above.
func HttpHandler(err error) (code int, body interface{}) {
	logx.Errorf("[API-ERR] %v", err)
	st, ok := grpcStatusFromErr(err)
	if ok {
		return handleGrpc(st)
	}
	e, ok := isErrX(err)
	if !ok {
		return http.StatusInternalServerError, &HttpBody{Message: err.Error()}
	}
	return e.HttpCode(), e.HttpBody()
}

//                                             End User
//                                             ▲
//                                             │ HTTP Error
//                                             │ (Code + Message)
//                                             │
//                                         ┌───┴──────────────┐
//                                         │errx.HttpHandler  │
//                                         │                  │
//                                         └──────────────────┘
//                                             ▲
//                                             │ RPC Status
//                                             │ (Also use HTTP Status Code)
//┌────────────┐ raise  ┌──────────────┐   ┌───┴───────────────┐
//│RPC Service │        │ErrorX        │   │errx.RpcInterceptor│
//│            ├────────►(Code+Message)├──►│                   │
//└────────────┘        └──────────────┘   └───────────────────┘

// RpcInterceptor converts ErrorX or RPC Status,
// which can be consumed by errx.HttpHandler.
// See the ascii diagram above.
func RpcInterceptor(
	ctx context.Context,
	req interface{},
	info *grpc.UnaryServerInfo,
	handler grpc.UnaryHandler,
) (resp interface{}, err error) {
	resp, err = handler(ctx, req)
	if err != nil {
		logx.Errorf("[RPC-ERR] %s", err.Error())
		var e ErrorX
		if errors.As(err, &e) {
			return nil, toGrpcError(e)
		}
		return nil, err
	}
	return resp, nil
}

func toGrpcError(e ErrorX) error {
	return e.RpcStatus().Err()
}

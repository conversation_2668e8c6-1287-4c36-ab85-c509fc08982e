package errx

import (
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
)

// TestErrorAll tests if Error.RpcStatus() could
// construct proper grpc error with errdetails.ErrorInfo.
func TestErrorAll(t *testing.T) {
	var (
		code    int32 = 200
		message       = "test message"
	)
	e := New(code, message)
	st := e.RpcStatus()

	ast := assert.New(t)
	ast.Equal(codes.Code(code), st.Code())
	ast.Equal(message, st.Message())
}

func TestError_Is(t *testing.T) {
	var (
		ok             int32 = http.StatusOK
		message              = "Info"
		err                  = New(ok, message).(*defaultErrorX)
		errDiffMessage       = New(ok, "Different Info").(*defaultErrorX)
		errDiffCode          = New(http.StatusInternalServerError, message).(*defaultErrorX)
	)
	tests := []struct {
		name  string
		lhs   *defaultErrorX
		rhs   error
		equal bool
	}{
		{
			name:  "Equal with same object",
			lhs:   err,
			rhs:   err,
			equal: true,
		},
		{
			name:  "Unequal with different code",
			lhs:   err,
			rhs:   errDiffCode,
			equal: false,
		},
		{
			name:  "Unequal with different message",
			lhs:   err,
			rhs:   errDiffMessage,
			equal: false,
		},
		{
			name:  "Equal with same string representation",
			lhs:   err,
			rhs:   errors.New("code: 200, message: Info"),
			equal: true,
		},
		{
			name:  "Unequal with different string representation",
			lhs:   err,
			rhs:   errors.New("different string representation"),
			equal: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.equal, errors.Is(tt.lhs, tt.rhs), "Is(%v)", tt.rhs)
		})
	}
}

// TestHandlerChain tests the following scenerio
// raise errorX -> RpcInterceptor() -> HttpHandler()
func TestHandlerChain(t *testing.T) {
	tests := []struct {
		name       string
		errorInput ErrorX
		code       int
		body       any
	}{
		{
			name:       "defaultErrorX",
			errorInput: New(http.StatusInternalServerError, "test message"),
			code:       http.StatusInternalServerError,
			body:       &HttpBody{Message: "test message"},
		},
		{
			name:       "i18nError",
			errorInput: I18nErrUnknown,
			code:       http.StatusBadRequest,
			body: &HttpBody{
				Code:    I18nErrUnknown.Code,
				Message: I18nErrUnknown.Message,
			},
		},
		{
			name: "i18nError, with format",
			errorInput: I18nErrInternalTrace.Printf(
				"traceid",
				"spanid",
			),
			code: http.StatusBadRequest,
			body: &HttpBody{
				Code:    I18nErrInternalTrace.Code,
				Message: "Internal Service Error, trace ID = traceid, span ID = spanid",
				Values: map[string]string{
					"v0": "traceid",
					"v1": "spanid",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			code, body := HttpHandler(toGrpcError(tt.errorInput))
			assert.Equal(t, tt.code, code)
			assert.Equal(t, tt.body, body)
		})
	}
}

package errx

import (
	"fmt"
	"net/http"

	"GCF/pkg/errx/internal"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// This line verify interface compliance at compile time.
var _ ErrorX = (*i18n)(nil)

type i18n struct {
	Code    int
	Message string
	Values  []any
}

func (e *i18n) Error() string {
	return fmt.Sprintf("i18n code: %d, message: %s", e.Code, e.Message)
}

func (e *i18n) HttpCode() int {
	return http.StatusBadRequest
}
func (e *i18n) HttpBody() any {
	vmap := make(map[string]string)
	for i, v := range e.Values {
		vmap[fmt.Sprintf("v%d", i)] = fmt.Sprintf("%v", v)
	}
	return &HttpBody{
		Code:    e.Code,
		Message: e.Message,
		Values:  vmap,
	}
}

func (e *i18n) RpcStatus() *status.Status {
	st := status.New(codes.Code(e.Code), e.Message)
	var err error
	for _, v := range e.Values {
		st, err = st.WithDetails(&internal.KvPair{Value: fmt.Sprintf("%v", v)}) // no key here. Maybe we would need key one day.
		logx.Must(err)
	}
	return st
}

// i18nPrintf is a builder for i18n error,
// providing a printf-like API.
type i18nPrintf struct {
	Code   int
	Format string
}

func (e *i18nPrintf) Printf(a ...any) *i18n {
	return &i18n{
		Code:    e.Code,
		Message: fmt.Sprintf(e.Format, a...),
		Values:  a,
	}
}

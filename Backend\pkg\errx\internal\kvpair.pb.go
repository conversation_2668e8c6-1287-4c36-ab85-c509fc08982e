// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.20.3
// source: kvpair.proto

package internal

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KvPair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *KvPair) Reset() {
	*x = KvPair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_kvpair_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KvPair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KvPair) ProtoMessage() {}

func (x *KvPair) ProtoReflect() protoreflect.Message {
	mi := &file_kvpair_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KvPair.ProtoReflect.Descriptor instead.
func (*KvPair) Descriptor() ([]byte, []int) {
	return file_kvpair_proto_rawDescGZIP(), []int{0}
}

func (x *KvPair) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *KvPair) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_kvpair_proto protoreflect.FileDescriptor

var file_kvpair_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x6b, 0x76, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x22, 0x30, 0x0a, 0x06, 0x4b, 0x76, 0x50, 0x61,
	0x69, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_kvpair_proto_rawDescOnce sync.Once
	file_kvpair_proto_rawDescData = file_kvpair_proto_rawDesc
)

func file_kvpair_proto_rawDescGZIP() []byte {
	file_kvpair_proto_rawDescOnce.Do(func() {
		file_kvpair_proto_rawDescData = protoimpl.X.CompressGZIP(file_kvpair_proto_rawDescData)
	})
	return file_kvpair_proto_rawDescData
}

var file_kvpair_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_kvpair_proto_goTypes = []interface{}{
	(*KvPair)(nil), // 0: internal.KvPair
}
var file_kvpair_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_kvpair_proto_init() }
func file_kvpair_proto_init() {
	if File_kvpair_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_kvpair_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KvPair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_kvpair_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_kvpair_proto_goTypes,
		DependencyIndexes: file_kvpair_proto_depIdxs,
		MessageInfos:      file_kvpair_proto_msgTypes,
	}.Build()
	File_kvpair_proto = out.File
	file_kvpair_proto_rawDesc = nil
	file_kvpair_proto_goTypes = nil
	file_kvpair_proto_depIdxs = nil
}

package errx

// Every translatable error should have a unique code.

// NEVER change or reuse code.
// They are related to the frontend translation.
//
// We don't use iota here, since they are easy to accidentally shift.
//
// TODO(ofey404): Remove `I18n` prefix after we finish the migration and eliminate other errors.
var (
	I18nErrUnknown                         = &i18n{Code: 10000, Message: "Unknown error"}
	I18nErrInternalTrace                   = &i18nPrintf{Code: 10001, Format: "Internal Service Error, trace ID = %v, span ID = %v"}
	I18nErrTooManyLoginAttempts            = &i18n{Code: 10002, Message: "Too many login attempts, please contact admin to unlock your account"}
	I18nErrNoMobile                        = &i18n{Code: 10003, Message: "You should register with mobile number before otp login"}
	I18nErrCredRequired                    = &i18n{Code: 10004, Message: "Password or otp is required"}
	I18nErrMustResetPassword               = &i18n{Code: 10005, Message: "This user must reset password"}
	I18nErrUsernamePasswordNotCorrect      = &i18n{Code: 10006, Message: "Username or password not correct"}
	I18nErrIdNotFound                      = &i18nPrintf{Code: 10007, Format: "Id %v not found"}
	I18nErrUsernamePasswordNotCorrectRetry = &i18nPrintf{Code: 10008, Format: "Username or password not correct, %v retries left"}
	I18nErrOtpRequired                     = &i18n{Code: 10009, Message: "Otp is required"}
	I18nErrOtpNotCorrect                   = &i18n{Code: 10010, Message: "Otp not correct"}
	I18nErrOtpHaveNotRequested             = &i18n{Code: 10011, Message: "You haven't requested any OTP"}
	I18nErrMobileRequired                  = &i18n{Code: 10012, Message: "Mobile is required"}
	I18nErrUserExists                      = &i18nPrintf{Code: 10013, Format: "User %v already exists"}
	I18nErrUsernamePattern                 = &i18n{Code: 10014, Message: "Username should be more than 4 characters, less than 20 characters, no space and not all digits, `_-` are only allowed special characters"}
	I18nErrPasswordInvalid                 = &i18nPrintf{Code: 10015, Format: "Password %v is invalid. length >= 8, at least one lower letter, at least one upper letter, at least one number, at least one special: !\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~"}
	I18nErrMobileExists                    = &i18n{Code: 10016, Message: "Mobile already exists"}
	I18nErrOtpOldPasswordRequired          = &i18n{Code: 10017, Message: "Otp or old password is required"}
	I18nErrMobileDoesntMatch               = &i18n{Code: 10018, Message: "Mobile doesn't match"}
	I18nErrNotAdminNotPermitted            = &i18n{Code: 10019, Message: "Not admin, operation is not permitted"}
	I18nErrNoUserInfoInContext             = &i18n{Code: 10020, Message: "Internal Error: No user info in context"}
	I18nErrUserNotFound                    = &i18n{Code: 10021, Message: "User not found"}
	I18nErrFailedToGetUserIDFromJWT        = &i18n{Code: 10022, Message: "Failed to get user id from jwt"}
	I18nErrFailedToGetUserInfo             = &i18n{Code: 10023, Message: "Failed to get user info"}
	I18nErrInvalidMobile                   = &i18n{Code: 10024, Message: "Invalid 11-digit mobile number, pattern: ^\\d{11}$"}
	I18nErrMobileNotFound                  = &i18n{Code: 10025, Message: "Mobile number not found"}
	I18nErrTokenExpired                    = &i18n{Code: 10026, Message: "Token expired"}
	I18nErrSshKeyLimit                     = &i18nPrintf{Code: 10027, Format: "Ssh key count exceeds the limit %v"}
	I18nErrUsernameEmailRequired           = &i18n{Code: 10028, Message: "Username or Email is required"}
	I18nErrFailedToParseUUID               = &i18nPrintf{Code: 10029, Format: "Failed to parse UUID %v"}
	I18nErrInvitationCodeNotExist          = &i18nPrintf{Code: 10030, Format: "Invitation Code doesn't exist: %v"}
	I18nErrInvalidEmail                    = &i18n{Code: 10031, Message: "Invalid email address format"}
	I18nErrEmailExists                     = &i18n{Code: 10032, Message: "Email already exists"}
	I18nErrEmailRequired                   = &i18n{Code: 10033, Message: "Email is required"}
	I18nErrEmailNotFound                   = &i18n{Code: 10034, Message: "Email not found"}
	I18nErrEmailDoesntMatch                = &i18n{Code: 10035, Message: "Email doesn't match"}
	I18nErrNoEmail                         = &i18n{Code: 10036, Message: "You should register with email number before otp login"}
	I18nErrAccountDeactivated              = &i18n{Code: 10037, Message: "Your account has been deactivated"}
)

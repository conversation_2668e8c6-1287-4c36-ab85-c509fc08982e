package errx

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"runtime/debug"

	"GCF/pkg/errx/internal"

	"github.com/zeromicro/go-zero/core/trace"

	"GCF/pkg/constant"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc/status"
)

func grpcStatusFromErr(err error) (*status.Status, bool) {
	return status.FromError(err)
}

func isErrX(err error) (ErrorX, bool) {
	var e ErrorX
	ok := errors.As(err, &e)
	if ok {
		return e, true
	}
	return nil, false
}

func handleGrpc(st *status.Status) (code int, body interface{}) {
	b := &HttpBody{
		Message: st.Message(),
	}
	if st.Code() > 9999 {
		code = http.StatusBadRequest // i18nError
		b.Code = int(st.Code())      // 10000+ i18nCode
	} else {
		code = int(st.Code()) // legacy logic to be compatible with defaultErrX
	}
	if values, ok := decodeValues(st); ok {
		b.Values = values
	}
	return code, b
}

func decodeValues(st *status.Status) (map[string]string, bool) {
	if len(st.Details()) == 0 {
		return nil, false
	}

	values := make(map[string]string)
	for i, detail := range st.Details() {
		if kv, ok := detail.(*internal.KvPair); ok {
			values[fmt.Sprintf("v%d", i)] = kv.Value
		}
	}
	return values, true
}

// StackField provide a shorthand to log stack trace, with go-zero's logx package.
//
// example:
// l.Logger.Errorw(err.Error(), errx.StackField())
func StackField() logx.LogField {
	return logx.Field(
		constant.StackKey,
		string(debug.Stack()),
	)
}

// InternalServerErrorWithTraceID returns a fixed error with trace ID.
func InternalServerErrorWithTraceID(ctx context.Context) error {
	traceID := trace.TraceIDFromContext(ctx)
	spanID := trace.SpanIDFromContext(ctx)

	return I18nErrInternalTrace.Printf(traceID, spanID)
}

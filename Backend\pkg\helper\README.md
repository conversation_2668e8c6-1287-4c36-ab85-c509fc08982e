# helper

This package collects miscellaneous helpers, which is:

1. Single function is not worth creating a package.
2. Common utilities but hard to categorize, like `Get<PERSON>reePort` or `Panicf`.

Table of Content

- [Avoid being a Blackhole](#avoid-being-a-blackhole)
- [At least those files have comment](#at-least-those-files-have-comment)

# Avoid being a Blackhole

`utils/` directory can be a black hole because naming things is hard.
[Utils are a blackhole](https://mykeels.medium.com/utils-are-a-blackhole-c8320968325b)

So, to any code can grow, `helper` should be a transfer station,
rather than a terminal station.

If anything in this directory grows, move it into a separate package.
If the filename no longer represents its content, rename it.

That's also why this package called the weird name `helper`, rather than common `utils`.

# At least those files have comment

This directory is a transfer station, for code can grow in the future, but it has yet to be.

So comments would be more up-to-date than writing formal documentation like README.

There should be an explanation comment before each file, like in `sql.go`:

```go
/* sql.go

This file collects helpers for interacting with SQL connection,
especially in unit and integration tests.
*/
```

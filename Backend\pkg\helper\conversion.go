package helper

import (
	cryptoRand "crypto/rand"
	"encoding/base64"
	"math/rand"
	"strconv"
	"time"

	"github.com/google/uuid"

	"github.com/zeromicro/go-zero/core/logx"
)

/* conversion.go

This file contains helpers on some common type conversion.
*/

// NilIfEmptyString return nil if input string is "",
// otherwise returns a pointer to it.
func NilIfEmptyString(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// EmptyIfNilString return "" if input string ptr is nil,
// otherwise returns its value.
func EmptyIfNilString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func ParseInt64FromString(s string) (i int64, err error) {
	convertedResult, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		logx.Errorf("failed to convert %s to int64", s)
		return convertedResult, err
	}
	return convertedResult, nil
}

func PseudoRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	random := rand.New(rand.NewSource(time.Now().UnixNano()))
	result := make([]byte, length)
	for i := 0; i < length; i++ {
		result[i] = charset[random.Intn(len(charset))]
	}
	return string(result)
}

// TrueRandomString returns a random string of length `length`,
// using crypto/rand.
func TrueRandomString(length int) (string, error) {
	randomBytes := make([]byte, length)

	_, err := cryptoRand.Read(randomBytes)
	if err != nil {
		return "", err
	}

	base64String := base64.URLEncoding.EncodeToString(randomBytes)

	return base64String[:length], nil
}

func Int32Ptr(i int32) *int32 {
	// parameter pass by value has copy semantics.
	// so &i is safe to use, since it's a copy
	return &i
}

func BoolPtr(b bool) *bool {
	return &b
}

func UUIDMustParse(s string) uuid.UUID {
	id, err := uuid.Parse(s)
	logx.Must(err)
	return id
}

func UnwrapBoolPtr(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

func InterfaceToStringSlice(i interface{}) ([]string, bool) {
	data, ok := i.([]interface{})
	if !ok {
		return nil, false
	}

	var result []string
	for _, item := range data {
		if str, ok := item.(string); ok {
			result = append(result, str)
		} else {
			return nil, false
		}
	}
	return result, true
}

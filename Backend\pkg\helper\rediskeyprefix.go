package helper

// Different microservices may share same redis,
// so we need some key prefix to avoid conflict.
//
// This file collects all of them.

const (
	KeyPrefixJwtBlockList           = "t_block_"
	KeyPrefixOtpPhoneNumber         = "t_otp_phone_"
	KeyPrefixOtpEmail               = "t_otp_email_"
	KeyPrefixSmsMsgId               = "t_sms_smsmsgid_"
	KeyPrefixUserInSiteNotification = "t_user_ntf_"
	KeyPrefixCaptchaClickShape      = "t_captcha_click_shape_"
)

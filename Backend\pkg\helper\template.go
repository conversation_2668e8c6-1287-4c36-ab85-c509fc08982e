package helper

import (
	"bytes"
	"text/template"

	"github.com/zeromicro/go-zero/core/logx"
)

// MustLoadAndVerifyTemplate load a template string `text`.
// It fails early if the template can't be rendered with given `paramStruct`.
func MustLoadAndVerifyTemplate(text string, paramStruct any) *template.Template {
	name := PseudoRandomString(10)
	tmpl, err := template.New(
		name,
	).Parse(text)
	logx.Must(err)

	// validate: try to render it with the given paramStruct
	var buf bytes.Buffer
	err = tmpl.Execute(&buf, paramStruct)
	logx.Must(err)

	return tmpl
}

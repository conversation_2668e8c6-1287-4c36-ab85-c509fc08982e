package helper

import (
	"fmt"
	"time"
)

func ConvertTimeStringToSingaporeTimezone(timeStr string) string {
	singaporeTimeZone, err := time.LoadLocation("Asia/Singapore")
	if err != nil {
		fmt.Println("unable to load timezone:", err)
		return ""
	}
	t, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		fmt.Println("convert timezone parse error:", err)
		return ""
	}
	return t.In(singaporeTimeZone).Format("2006-01-02 15:04:05")
}

// time1 : 16:00:00 time2: 16:30:00
// return 16:30:00
// time1 : 16:00:00 time2: 15:30:00
// return 16:00:00
func CalculateStartTime(time1 string, time2 string) string {
	sqlTime1, err := time.Parse("2006-01-02 15:04:05", time1)
	if err != nil {
		fmt.Println("time1:", err)
		return ""
	}
	sqlTime2, err := time.Parse("2006-01-02 15:04:05", time2)
	if err != nil {
		fmt.Println("time2:", time2, err)
		return ""
	}

	if sqlTime2.After(sqlTime1) {
		return time2
	} else {
		return time1
	}
}

// time1 : 16:00:00 time2: 16:30:00
// return 16:00:00
// time1 : 16:00:00 time2: 15:30:00
// return 15:30:00
func CalculateEndTime(time1 string, time2 string) string {
	sqlTime1, err := time.Parse("2006-01-02 15:04:05", time1)
	if err != nil {
		fmt.Println("time1:", err)
		return ""
	}
	sqlTime2, err := time.Parse("2006-01-02 15:04:05", time2)
	if err != nil {
		fmt.Println("time2:", time2, err)
		return ""
	}

	if sqlTime2.After(sqlTime1) {
		return time1
	} else {
		return time2
	}
}

var (
	Test30DaysAgo = time.Date(2022, 10, 9, 0, 0, 0, 0, time.UTC)
	Test2DaysAgo  = time.Date(2022, 11, 7, 0, 0, 0, 0, time.UTC)
	TestNow       = time.Date(2022, 11, 9, 0, 0, 0, 0, time.UTC)
)

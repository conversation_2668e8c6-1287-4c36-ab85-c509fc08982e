package helper

import (
	"fmt"
	"os"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"

	"github.com/zeromicro/go-zero/core/conf"
)

// MustLoadFromYamlFile is an unsafe function.
// The caller should be absolutely sure, it can never have user input.
//
// More to see [gosec] G304 (CWE-22): Potential file inclusion via variable
func MustLoadFromYamlFile(
	obj any,
	filePath string,
) {
	content, err := os.ReadFile(filePath) // #nosec G304
	if err != nil {
		fmt.Printf("Failed to open YAML file %s: %v\n", filePath, err)
		os.Exit(1)
	}

	err = conf.LoadFromYamlBytes(content, obj)
	if err != nil {
		fmt.Printf(
			"Failed to load YAML file %s into object %+v: %v\nfile content: %s\n",
			filePath,
			obj,
			err,
			string(content),
		)
		os.Exit(1)
	}
}

// MustPrintFile prints file content to log as a one-liner.
// It's useful for debugging config file issues.
func MustPrintFile(path string) {
	content, err := os.ReadFile(path)
	if err != nil {
		logx.Errorf("Failed to read file, path: %s, error: %s", path, err.Error())
		os.Exit(1)
	}

	fmt.Printf("Print file %s: %s\n", path, strconv.Quote(string(content)))
}

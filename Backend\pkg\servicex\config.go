package servicex

import (
	"flag"

	"GCF/pkg/helper"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
)

var configFile = flag.String("f", "/etc/service/config.yaml", "the config file")

// MustLoadConfigFile loads config from file.
//
// It would print the content, so that when misconfiguration happens
// we can check config file from log.
func MustLoadConfigFile(v any) {
	helper.MustPrintFile(*configFile)

	conf.MustLoad(*configFile, v, conf.UseEnv())
}

type Config interface {
	GetRpc() (*zrpc.RpcServerConf, bool)
	GetApi() (*rest.RestConf, bool)
}

type RpcApiConfig struct {
	Rpc *zrpc.RpcServerConf `json:",optional"`
	Api *rest.RestConf      `json:",optional"`
}

func (c *RpcApiConfig) GetRpc() (*zrpc.RpcServerConf, bool) {
	if c.Rpc != nil {
		return c.Rpc, true
	}
	return nil, false
}

func (c *RpcApiConfig) GetApi() (*rest.RestConf, bool) {
	if c.Api != nil {
		return c.Api, true
	}
	return nil, false
}

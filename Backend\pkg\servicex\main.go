package servicex

import (
	"flag"
	"fmt"

	"GCF/pkg/errx"

	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

// All flags that would be appended to the command line arguments
var (
	apiOnly = flag.Bool("api-only", false, "Only start API server, ignore Rpc: section in config file")
	rpcOnly = flag.Bool("rpc-only", false, "Only start RPC server, ignore Api: section in config file")
)

// Main is a helper function to start API and/or RPC server.
//
// With the same binary, they could be started in the same process
// or in different processes, depending on the config file.
//
// Placing RPC and API in the same binary (also same ./internal package)
// is useful for code sharing , since they often expose same logic to
// different receivers.
//
// It's also a workaround for the rigid code generation of go-zero framework.
func Main(
	c Config,
	registerApiHandler func(*rest.Server),
	registerRpcHandler func(*grpc.Server),
) {
	flag.Parse()

	// When building with `-tags=prod`, this function will check the license

	exitMainProcess := make(chan string)
	api, ok := c.GetApi()
	if ok && !(*rpcOnly) {
		if registerApiHandler == nil {
			panic("api handler should not be nil")
		}
		go func() {
			startApiServer(api, registerApiHandler)
			exitMainProcess <- "API"
		}()
	}

	rpc, ok := c.GetRpc()
	if ok && !(*apiOnly) {
		if registerRpcHandler == nil {
			panic("rpc handler should not be nil")
		}
		go func() {
			startRpcServer(rpc, registerRpcHandler)
			exitMainProcess <- "RPC"
		}()
	}

	serverType := <-exitMainProcess
	fmt.Printf("received %s server shutdown, process exit\n", serverType)
}

// MainApiOnly is a wrapper of Main, which only starts API server.
func MainApiOnly(
	c *rest.RestConf,
	registerApiHandler func(*rest.Server),
) {
	Main(
		&RpcApiConfig{Api: c},
		registerApiHandler,
		nil,
	)
}

func MainRpcOnly(
	c *zrpc.RpcServerConf,
	registerRpcHandler func(*grpc.Server),
) {
	Main(
		&RpcApiConfig{Rpc: c},
		nil,
		registerRpcHandler,
	)
}

func startApiServer(api *rest.RestConf, registerHandler func(*rest.Server)) {
	server := rest.MustNewServer(*api)
	defer server.Stop()

	registerHandler(server)
	httpx.SetErrorHandler(errx.HttpHandler)

	fmt.Printf("Starting HTTP server at %s:%d...\n", api.Host, api.Port)
	server.Start()
}

func startRpcServer(rpc *zrpc.RpcServerConf, registerHandler func(*grpc.Server)) {
	s := zrpc.MustNewServer(*rpc, func(grpcServer *grpc.Server) {
		registerHandler(grpcServer)

		if rpc.Mode == service.DevMode || rpc.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})

	s.AddUnaryInterceptors(errx.RpcInterceptor)
	defer s.Stop()

	fmt.Printf("Starting RPC server at %s...\n", rpc.ListenOn)
	s.Start()
}

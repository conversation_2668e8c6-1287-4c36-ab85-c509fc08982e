#!/usr/bin/env bash
# set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

if [[ -z "${SCHEMA_NAME:-}" || -z "${SCHEMA_DIRECTORY:-}" ]]; then
  echo "Create a new Object Schema in a ent ORM directory, more to see https://github.com/ent/ent"
  echo "Usage:"
  echo "  SCHEMA_NAME=<schema_name> SCHEMA_DIRECTORY=<schema_directory> $0"
  echo "Example:"
  echo "  SCHEMA_NAME=User SCHEMA_DIRECTORY=internal/ent/schema $0"
  exit 1
fi

SCHEMA_NAME=${SCHEMA_NAME}
SCHEMA_DIRECTORY=$(realpath "${SCHEMA_DIRECTORY}")

echo "Creating $SCHEMA_NAME in $SCHEMA_DIRECTORY"
go run -mod=mod entgo.io/ent/cmd/ent new "$SCHEMA_NAME" --target "$SCHEMA_DIRECTORY"
echo "Schema $SCHEMA_NAME created in $SCHEMA_DIRECTORY"

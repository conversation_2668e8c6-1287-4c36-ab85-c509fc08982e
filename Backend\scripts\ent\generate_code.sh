#!/usr/bin/env bash
set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

if [[ -z "${SCHEMA_DIRECTORY:-}" ]]; then
  echo "Generate go code from ent ORM schema, more to see https://github.com/ent/ent"
  echo "Usage:"
  echo "  SCHEMA_DIRECTORY=<schema_directory> $0"
  echo "Example:"
  echo "  SCHEMA_DIRECTORY=internal/ent/schema $0"
fi

SCHEMA_DIRECTORY=$(realpath "${SCHEMA_DIRECTORY}")

echo "Generating Go code based on schema $SCHEMA_DIRECTORY"
go run -mod=mod entgo.io/ent/cmd/ent generate --feature sql/versioned-migration "$SCHEMA_DIRECTORY"
echo "Generated Go code in $(realpath "$SCHEMA_DIRECTORY/..")"

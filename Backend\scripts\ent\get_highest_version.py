import os
import sys


def get_highest_version(output_dir):
    highest_version_file = max((f for f in os.listdir(output_dir) if f.endswith('.sql')),
                               key=lambda f: int(f.split('_')[0][1:]))

    # Get the {} part => V{20231121055737}__create_users.sql
    timestamp = highest_version_file.split('_')[0][1:]

    return timestamp


if __name__ == '__main__':
    if len(sys.argv) != 2:
        print('''This script prints the highest version number of the flyway compatible sql files.
eg:
  Files: V20231121055737__create_users.sql  V20231121060516__user_add_name_age.sql
  Print: 20231121060516

Usage: python get_highest_version.py <output_dir>
  output_dir: The directory where the sql files are located.''')
        sys.exit(1)
    print(get_highest_version(sys.argv[1]))

package main

import (
	"database/sql"
	"flag"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	_ "github.com/lib/pq"
)

// 数据库配置
type DBConfig struct {
	host     string
	port     string
	user     string
	password string
	dbname   string
}

// 测试配置
var testConfig = struct {
	testDuration int
	tableName    string
	dataSize     int
	workerCount  int
	batchSize    int
	maxOpenConns int
	maxIdleConns int
}{
	testDuration: 10,
	tableName:    "test_table",
	dataSize:     100,
	workerCount:  50,   // 并发工作线程数
	batchSize:    1000, // 每批处理的数据量
	maxOpenConns: 100,  // 最大连接数
	maxIdleConns: 50,   // 最大空闲连接数
}

// 准备数据库连接
func getDBConn(dbConfig *DBConfig) (*sql.DB, error) {
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbConfig.host, dbConfig.port, dbConfig.user, dbConfig.password, dbConfig.dbname)
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, err
	}

	// 优化连接池设置
	db.SetMaxOpenConns(testConfig.maxOpenConns)
	db.SetMaxIdleConns(testConfig.maxIdleConns)
	db.SetConnMaxLifetime(time.Hour)

	return db, nil
}

// 初始化数据库和测试表
func prepareDatabase(dbConfig *DBConfig) error {
	db, err := getDBConn(dbConfig)
	if err != nil {
		return err
	}
	defer db.Close()

	// 创建表时添加索引
	createTableSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id SERIAL PRIMARY KEY,
			data VARCHAR(%d),
			timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`, testConfig.tableName, testConfig.dataSize)

	_, err = db.Exec(createTableSQL)
	return err
}

// 生成随机测试数据
func generateData() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	data := make([]byte, testConfig.dataSize)
	for i := range data {
		data[i] = charset[rand.Intn(len(charset))]
	}
	return string(data)
}

// 写入性能测试
func writeTest(dbConfig *DBConfig) error {
	db, err := getDBConn(dbConfig)
	if err != nil {
		return err
	}
	defer db.Close()

	var wg sync.WaitGroup
	errChan := make(chan error, testConfig.workerCount)
	writeChan := make(chan int, testConfig.workerCount)

	startTime := time.Now()
	lastPrint := startTime

	// 启动多个写入工作线程
	for i := 0; i < testConfig.workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			stmt, err := db.Prepare(fmt.Sprintf("INSERT INTO %s (data) VALUES ($1)", testConfig.tableName))
			if err != nil {
				errChan <- err
				return
			}
			defer stmt.Close()

			for time.Since(startTime) < time.Duration(testConfig.testDuration)*time.Second {
				_, err := stmt.Exec(generateData())
				if err != nil {
					errChan <- err
					return
				}
				writeChan <- 1
			}
		}()
	}

	// 监控写入速度
	go func() {
		writeCount := 0
		for range writeChan {
			writeCount++
			currentTime := time.Now()
			if currentTime.Sub(lastPrint) >= time.Second {
				speed := float64(writeCount) / currentTime.Sub(lastPrint).Seconds()
				fmt.Printf("当前写入速度: %.2f 条/秒\n", speed)
				writeCount = 0
				lastPrint = currentTime
			}
		}
	}()

	wg.Wait()
	close(writeChan)
	close(errChan)

	if len(errChan) > 0 {
		return <-errChan
	}
	return nil
}

// 读取性能测试
func readTest(dbConfig *DBConfig) error {
	db, err := getDBConn(dbConfig)
	if err != nil {
		return err
	}
	defer db.Close()

	var minID, maxID int
	err = db.QueryRow(fmt.Sprintf("SELECT MIN(id), MAX(id) FROM %s", testConfig.tableName)).Scan(&minID, &maxID)
	if err != nil {
		return err
	}

	if minID == 0 && maxID == 0 {
		fmt.Println("没有可读取的数据，请先运行写入测试")
		return nil
	}

	var wg sync.WaitGroup
	errChan := make(chan error, testConfig.workerCount)
	readChan := make(chan int, testConfig.workerCount)

	startTime := time.Now()
	lastPrint := startTime

	// 预处理SQL语句
	stmt, err := db.Prepare(fmt.Sprintf("SELECT data, timestamp FROM %s WHERE id = ANY(string_to_array($1, ',')::integer[])", testConfig.tableName))
	if err != nil {
		return err
	}
	defer stmt.Close()

	// 启动多个读取工作线程
	for i := 0; i < testConfig.workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for time.Since(startTime) < time.Duration(testConfig.testDuration)*time.Second {
				// 生成一批随机ID
				ids := make([]string, testConfig.batchSize)
				for j := range ids {
					ids[j] = fmt.Sprintf("%d", rand.Intn(maxID-minID+1)+minID)
				}

				rows, err := stmt.Query(strings.Join(ids, ","))
				if err != nil {
					errChan <- err
					return
				}

				for rows.Next() {
					var data string
					var timestamp time.Time
					if err := rows.Scan(&data, &timestamp); err != nil {
						rows.Close()
						errChan <- err
						return
					}
					readChan <- 1
				}
				rows.Close()
			}
		}()
	}

	// 监控读取速度
	go func() {
		readCount := 0
		for range readChan {
			readCount++
			currentTime := time.Now()
			if currentTime.Sub(lastPrint) >= time.Second {
				speed := float64(readCount) / currentTime.Sub(lastPrint).Seconds()
				fmt.Printf("当前读取速度: %.2f 条/秒\n", speed)
				readCount = 0
				lastPrint = currentTime
			}
		}
	}()

	wg.Wait()
	close(readChan)
	close(errChan)

	if len(errChan) > 0 {
		return <-errChan
	}
	return nil
}

// 清理测试数据
func cleanup(dbConfig *DBConfig) error {
	db, err := getDBConn(dbConfig)
	if err != nil {
		return err
	}
	defer db.Close()

	_, err = db.Exec(fmt.Sprintf("TRUNCATE TABLE %s", testConfig.tableName))
	if err != nil {
		return err
	}
	fmt.Println("\n测试数据已清理")
	return nil
}

func main() {
	dbConfig := &DBConfig{}

	flag.StringVar(&dbConfig.host, "host", "localhost", "数据库主机地址")
	flag.StringVar(&dbConfig.port, "port", "15432", "数据库端口")
	flag.StringVar(&dbConfig.user, "user", "postgres", "数据库用户名")
	flag.StringVar(&dbConfig.password, "password", "pass", "数据库密码")
	flag.StringVar(&dbConfig.dbname, "dbname", "postgres", "数据库名称")
	flag.Parse()

	rand.Seed(time.Now().UnixNano())

	if err := prepareDatabase(dbConfig); err != nil {
		fmt.Printf("准备数据库失败: %v\n", err)
		return
	}

	if err := writeTest(dbConfig); err != nil {
		fmt.Printf("写入测试失败: %v\n", err)
		return
	}

	if err := readTest(dbConfig); err != nil {
		fmt.Printf("读取测试失败: %v\n", err)
		return
	}

	// 清理测试数据（按需启用）
	if err := cleanup(dbConfig); err != nil {
		fmt.Printf("清理数据失败: %v\n", err)
		return
	}
}

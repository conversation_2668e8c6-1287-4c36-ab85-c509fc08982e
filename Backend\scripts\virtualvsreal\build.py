import os
import shutil
from pathlib import Path
import subprocess


def prepare_build():
    """准备打包环境"""
    # 创建临时目录
    build_dir = Path("build_temp")
    build_dir.mkdir(exist_ok=True)

    # 复制必要的文件
    files_to_copy = ["run_evaluation.py", "evaluator.py", "data_manager.py", "data_generator.py", "__init__.py"]

    for file in files_to_copy:
        shutil.copy2(file, build_dir / file)

    return build_dir


def cleanup(build_dir):
    """清理临时文件"""
    if build_dir.exists():
        shutil.rmtree(build_dir)


def main():
    """主打包流程"""
    print("开始打包流程...")

    # 准备环境
    build_dir = prepare_build()

    try:
        # 切换到构建目录
        os.chdir(build_dir)

        # 运行 PyInstaller
        os.system("pyinstaller ../run_evaluation.spec --clean")

        # 复制生成的可执行文件到上级目录
        dist_dir = Path("dist")
        if dist_dir.exists():
            for file in dist_dir.glob("*"):
                shutil.copy2(file, "..")

        print("打包完成！")

    except Exception as e:
        print(f"打包过程中出错: {str(e)}")

    finally:
        # 清理临时文件
        os.chdir("..")
        cleanup(build_dir)


if __name__ == "__main__":
    main()

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import os


class DeviceDataGenerator:
    def __init__(self, device_type):
        self.device_type = device_type
        self.params = {
            "motor": {"response_time": 0.5, "max_speed": 3000, "noise_level": 0.002},  # 秒  # RPM  # 2% 噪声
            "compressor": {"response_time": 1.2, "max_pressure": 800, "noise_level": 0.003},  # kPa
            "fan": {"response_time": 0.8, "max_airflow": 2000, "noise_level": 0.002},  # m³/h
            "pump": {"response_time": 0.6, "max_flow": 100, "noise_level": 0.002},  # L/min
        }

    def generate_step_response(self, setpoint, duration=10, sample_rate=0.1):
        """生成阶跃响应数据"""
        param = self.params[self.device_type]
        t = np.arange(0, duration, sample_rate)

        # 生成理想响应曲线
        tau = param["response_time"]
        if self.device_type == "motor":
            max_val = param["max_speed"]
        elif self.device_type == "compressor":
            max_val = param["max_pressure"]
        elif self.device_type == "fan":
            max_val = param["max_airflow"]
        else:  # pump
            max_val = param["max_flow"]

        target = setpoint * max_val
        virtual_response = target * (1 - np.exp(-t / tau))

        # 添加随机噪声生成实际响应
        noise = np.random.normal(0, param["noise_level"] * target, len(t))
        real_response = virtual_response + noise

        # 创建时间戳
        timestamps = [datetime.now() + timedelta(seconds=x) for x in t]

        return pd.DataFrame({"timestamp": timestamps, "setpoint": setpoint, "virtual_value": virtual_response, "real_value": real_response})

    def save_data(self, data, setpoint):
        """保存数据到CSV文件"""
        directory = f"Backend/scripts/virtualvsreal/{self.device_type}"
        os.makedirs(directory, exist_ok=True)
        filename = f"{directory}/response_setpoint_{int(setpoint*100)}.csv"
        data.to_csv(filename, index=False)
        return filename

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
from pathlib import Path
from data_generator import DeviceDataGenerator
import logging

logger = logging.getLogger(__name__)


class DataManager:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.results_path = self.base_path / "evaluation_results"
        self.results_path.mkdir(parents=True, exist_ok=True)

        # 打印初始化信息用于调试
        logger.info(f"数据管理器初始化: {self.base_path}")

    def generate_and_save_dataset(self, device_type, samples_per_setpoint=100):
        """为指定设备类型生成完整数据集"""
        generator = DeviceDataGenerator(device_type)

        # 创建设备特定的数据目录
        device_data_path = self.base_path / device_type
        device_data_path.mkdir(parents=True, exist_ok=True)

        # 生成不同设定点的数据
        setpoints = [0.2, 0.4, 0.6, 0.8, 1.0]
        dataset_info = {
            "device_type": device_type,
            "generation_time": datetime.now().isoformat(),
            "setpoints": setpoints,
            "samples_per_setpoint": samples_per_setpoint,
            "files": [],
        }

        for setpoint in setpoints:
            # 生成多组数据
            all_data = []
            for _ in range(samples_per_setpoint):
                data = generator.generate_step_response(setpoint, duration=10, sample_rate=0.1)  # 10秒的响应时间  # 0.1秒的采样率
                all_data.append(data)

            # 合并数据并保存
            combined_data = pd.concat(all_data, ignore_index=True)
            filename = f"response_data_setpoint_{int(setpoint*100)}.csv"
            file_path = device_data_path / filename
            combined_data.to_csv(file_path, index=False)

            dataset_info["files"].append({"setpoint": setpoint, "filename": str(file_path), "samples": len(combined_data)})

        # 保存数据集信息
        info_file = device_data_path / "dataset_info.json"
        with open(info_file, "w", encoding="utf-8") as f:
            json.dump(dataset_info, f, indent=4, ensure_ascii=False)

        return dataset_info

    def load_dataset(self, device_type, setpoint):
        """加载特定设备和设定点的数据"""
        try:
            device_path = self.base_path / device_type
            filename = f"response_data_setpoint_{int(setpoint*100)}.csv"
            file_path = device_path / filename

            logger.info(f"尝试加载数据文件: {file_path}")

            if not file_path.exists():
                raise FileNotFoundError(f"数据文件不存在: {file_path}")

            df = pd.read_csv(file_path)

            # 验证数据完整性
            required_columns = ["timestamp", "setpoint", "virtual_value", "real_value"]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"数据文件缺少必要列: {missing_columns}")

            # 检查数据有效性
            if df.empty:
                raise ValueError("数据文件为空")

            return df

        except Exception as e:
            logger.error(f"加载数据集时出错 ({device_type}, setpoint={setpoint}): {str(e)}")
            raise

    def get_dataset_info(self, device_type):
        """获取数据集信息"""
        try:
            info_file = self.base_path / device_type / "dataset_info.json"
            logger.info(f"尝试读取数据集信息: {info_file}")

            if not info_file.exists():
                raise FileNotFoundError(f"数据集信息文件不存在: {info_file}")

            with open(info_file, "r", encoding="utf-8") as f:
                info = json.load(f)

            # 验证信息完整性
            required_fields = ["device_type", "setpoints", "files"]
            missing_fields = [field for field in required_fields if field not in info]
            if missing_fields:
                raise ValueError(f"数据集信息缺少必要字段: {missing_fields}")

            return info

        except Exception as e:
            logger.error(f"获取数据集信息时出错 ({device_type}): {str(e)}")
            return None

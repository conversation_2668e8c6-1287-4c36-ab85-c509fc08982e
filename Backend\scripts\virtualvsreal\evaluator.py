import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
import logging
from data_manager import DataManager
import matplotlib as mpl
from matplotlib.font_manager import FontProperties
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 设置中文字体
def setup_chinese_font():
    try:
        # 直接使用微软雅黑字体文件

        font_path = "fonts/msyh.ttc"  # 尝试 ttc 格式

        if os.path.exists(font_path):
            # 注册字体文件
            from matplotlib.font_manager import fontManager

            fontManager.addfont(font_path)

            # 设置默认字体
            plt.rcParams["font.family"] = ["Microsoft YaHei"]
            plt.rcParams["axes.unicode_minus"] = False

            # 创建字体对象
            font = FontProperties(fname=font_path)
            logger.info(f"成功加载中文字体: {font_path}")
            return font
        else:
            logger.warning("未找到微软雅黑字体文件")
            return None

    except Exception as e:
        logger.error(f"设置中文字体时出错: {str(e)}")
        return None


# 初始化中文字体
chinese_font = setup_chinese_font()


class ResponseEvaluator:
    def __init__(self, base_path="virtualvsreal"):
        self.data_manager = DataManager(base_path)
        self.results_path = Path(base_path) / "evaluation_results"
        self.results_path.mkdir(parents=True, exist_ok=True)
        self.font = chinese_font

        # 设置默认字体
        if self.font:
            plt.rcParams["font.family"] = ["Microsoft YaHei"]
            plt.rcParams["axes.unicode_minus"] = False

    def calculate_metrics(self, virtual_data, real_data):
        """计算各种评估指标"""
        try:
            virtual_data = np.array(virtual_data)
            real_data = np.array(real_data)

            # 检查数据有效性
            if not (np.all(np.isfinite(virtual_data)) and np.all(np.isfinite(real_data))):
                return {"max_error": float("inf"), "mean_error": float("inf"), "rmse": float("inf"), "correlation": 0.0}

            # 计算相对误差
            epsilon = 1e-10
            valid_indices = np.abs(virtual_data) > epsilon

            if not np.any(valid_indices):
                return {"max_error": float("inf"), "mean_error": float("inf"), "rmse": float("inf"), "correlation": 0.0}

            relative_error = np.zeros_like(virtual_data, dtype=float)
            relative_error[valid_indices] = np.abs(virtual_data[valid_indices] - real_data[valid_indices]) / np.abs(virtual_data[valid_indices]) * 100

            # 移除无效值
            valid_error = relative_error[np.isfinite(relative_error)]

            # 计算RMSE
            rmse = np.sqrt(np.mean((virtual_data - real_data) ** 2))

            # 计算相关系数
            try:
                correlation = np.corrcoef(virtual_data, real_data)[0, 1]
                correlation = correlation if np.isfinite(correlation) else 0.0
            except:
                correlation = 0.0

            return {
                "max_error": np.nanmax(valid_error) if len(valid_error) > 0 else float("inf"),
                "mean_error": np.nanmean(valid_error) if len(valid_error) > 0 else float("inf"),
                "rmse": rmse if np.isfinite(rmse) else float("inf"),
                "correlation": correlation,
            }

        except Exception as e:
            logger.error(f"计算指标时出错: {str(e)}")
            return {"max_error": float("inf"), "mean_error": float("inf"), "rmse": float("inf"), "correlation": 0.0}

    def plot_comparison(self, df, device_type, setpoint, save_path):
        """绘制对比图"""
        try:
            plt.figure(figsize=(12, 8))

            # 字体设置
            font_dict = {"fontproperties": self.font} if self.font else {}

            # 绘制响应曲线
            plt.subplot(2, 1, 1)
            virtual_values = df["virtual_value"].values
            real_values = df["real_value"].values

            if not (np.all(np.isfinite(virtual_values)) and np.all(np.isfinite(real_values))):
                logger.warning(f"{device_type} 在设定点 {setpoint} 的数据包含无效值")
                plt.text(0.5, 0.5, "数据包含无效值", ha="center", va="center", **font_dict)
            else:
                time_points = np.arange(len(virtual_values))
                plt.plot(time_points, virtual_values, label="虚拟响应", linewidth=2)
                plt.plot(time_points, real_values, label="实际响应", linewidth=2)

            plt.title(f"{device_type} 在设定点 {setpoint*100}% 的响应对比", **font_dict)
            plt.xlabel("采样点", **font_dict)
            plt.ylabel("响应值", **font_dict)
            plt.legend(prop=self.font)
            plt.grid(True)

            # 绘制误差分布
            plt.subplot(2, 1, 2)

            # 计算相对误差，添加保护
            epsilon = 1e-10
            valid_indices = np.abs(virtual_values) > epsilon

            if np.any(valid_indices):
                error = np.zeros_like(virtual_values)
                error[valid_indices] = (real_values[valid_indices] - virtual_values[valid_indices]) / virtual_values[valid_indices] * 100

                # 移除异常值
                valid_error = error[np.isfinite(error)]
                if len(valid_error) > 0:
                    # 使用百分位数来设置合理的直方图范围
                    lower = np.percentile(valid_error, 1)
                    upper = np.percentile(valid_error, 99)

                    if np.isfinite(lower) and np.isfinite(upper) and lower < upper:
                        plt.hist(valid_error, bins=50, density=True, range=(lower, upper))
                        plt.title("相对误差分布", **font_dict)
                        plt.xlabel("相对误差 (%)", **font_dict)
                        plt.ylabel("密度", **font_dict)
                        plt.grid(True)
                    else:
                        plt.text(0.5, 0.5, "误差范围无效", ha="center", va="center", **font_dict)
                else:
                    plt.text(0.5, 0.5, "无有效误差数据", ha="center", va="center", **font_dict)
            else:
                plt.text(0.5, 0.5, "所有参考值都接近零", ha="center", va="center", **font_dict)

            plt.tight_layout()
            plt.savefig(save_path)
            plt.close()

        except Exception as e:
            logger.error(f"绘图时出错: {str(e)}")
            # 创建一个错误提示图
            plt.figure(figsize=(12, 8))
            plt.text(0.5, 0.5, f"绘图错误: {str(e)}", ha="center", va="center", **font_dict)
            plt.savefig(save_path)
            plt.close()

    def evaluate_device(self, device_type):
        """评估单个设备的性能"""
        logger.info(f"开始评估 {device_type} 的性能...")

        try:
            # 获取数据集信息
            dataset_info = self.data_manager.get_dataset_info(device_type)
            if not dataset_info:
                logger.error(f"未找到 {device_type} 的数据集信息")
                return None

            logger.info(f"成功加载 {device_type} 的数据集信息")

            device_results = {"device_type": device_type, "setpoint_results": [], "overall_pass": True}

            for setpoint in dataset_info["setpoints"]:
                try:
                    logger.info(f"正在评估设定点 {setpoint*100}%...")

                    # 加载数据
                    df = self.data_manager.load_dataset(device_type, setpoint)
                    logger.info(f"数据加载成功，样本数: {len(df)}")

                    # 数据预处理
                    if df.isnull().any().any():
                        logger.warning("数据包含空值，将被移除")
                        df = df.dropna()

                    # 计算评估指标
                    metrics = self.calculate_metrics(df["virtual_value"].values, df["real_value"].values)

                    # 生成对比图
                    plot_path = self.results_path / f"{device_type}_setpoint_{int(setpoint*100)}_comparison.png"
                    self.plot_comparison(df, device_type, setpoint, plot_path)

                    setpoint_result = {"setpoint": setpoint, "metrics": metrics, "plot_path": str(plot_path), "pass": metrics["max_error"] < 1.0}

                    device_results["setpoint_results"].append(setpoint_result)
                    device_results["overall_pass"] &= setpoint_result["pass"]

                    # 打印评估结果
                    logger.info(f"设定点 {setpoint*100}% 评估完成:")
                    logger.info(f"- 最大相对误差: {metrics['max_error']:.3f}%")
                    logger.info(f"- 平均相对误差: {metrics['mean_error']:.3f}%")
                    logger.info(f"- RMSE: {metrics['rmse']:.3f}")
                    logger.info(f"- 相关系数: {metrics['correlation']:.3f}")

                except Exception as e:
                    logger.error(f"评估设定点 {setpoint} 时出错: {str(e)}")
                    logger.error(f"错误详情:", exc_info=True)

            return device_results

        except Exception as e:
            logger.error(f"评估设备 {device_type} 时出错: {str(e)}")
            logger.error(f"错误详情:", exc_info=True)
            return None

    def evaluate_all_devices(self):
        """评估所有设备的性能"""
        device_types = ["motor", "compressor", "fan", "pump"]
        all_results = []

        for device_type in device_types:
            results = self.evaluate_device(device_type)
            if results:
                all_results.append(results)

        # 生成总体报告
        self.generate_summary_report(all_results)

    def generate_summary_report(self, all_results):
        """生成总体评估报告"""
        report_path = self.results_path / "evaluation_summary.txt"

        with open(report_path, "w", encoding="utf-8") as f:
            f.write("虚拟系统响应评估报告\n")
            f.write("================\n\n")

            for device_result in all_results:
                f.write(f"\n{device_result['device_type']} 评估结果:\n")
                f.write(f"总体结果: {'通过' if device_result['overall_pass'] else '未通过'}\n")

                for setpoint_result in device_result["setpoint_results"]:
                    f.write(f"\n设定点 {setpoint_result['setpoint']*100}%:\n")
                    f.write(f"- 最大相对误差: {setpoint_result['metrics']['max_error']:.3f}%\n")
                    f.write(f"- 平均相对误差: {setpoint_result['metrics']['mean_error']:.3f}%\n")
                    f.write(f"- RMSE: {setpoint_result['metrics']['rmse']:.3f}\n")
                    f.write(f"- 相关系数: {setpoint_result['metrics']['correlation']:.3f}\n")
                    f.write(f"- 结果: {'通过' if setpoint_result['pass'] else '未通过'}\n")

                f.write("\n" + "=" * 50 + "\n")

import logging
import os
import sys

# 添加父目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from data_manager import DataManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_all_datasets():
    """生成所有设备类型的数据集"""
    device_types = ["motor", "compressor", "fan", "pump"]
    data_manager = DataManager()

    for device_type in device_types:
        logger.info(f"开始生成 {device_type} 的数据集...")
        try:
            dataset_info = data_manager.generate_and_save_dataset(device_type, samples_per_setpoint=100)  # 每个设定点生成100组数据
            logger.info(f"成功生成 {device_type} 数据集:")
            logger.info(f"- 生成时间: {dataset_info['generation_time']}")
            logger.info(f"- 设定点数量: {len(dataset_info['setpoints'])}")
            logger.info(f"- 每个设定点的样本数: {dataset_info['samples_per_setpoint']}")
            logger.info(f"- 数据文件数量: {len(dataset_info['files'])}\n")
        except Exception as e:
            logger.error(f"生成 {device_type} 数据集时出错: {str(e)}")


if __name__ == "__main__":
    generate_all_datasets()

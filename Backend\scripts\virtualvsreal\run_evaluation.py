import logging
import os
import sys
from pathlib import Path
from evaluator import ResponseEvaluator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加当前目录到模块搜索路径
if getattr(sys, "frozen", False):
    # 如果是打包后的可执行文件
    current_dir = os.path.dirname(sys.executable)
else:
    # 如果是开发环境
    current_dir = os.path.dirname(os.path.abspath(__file__))

if current_dir not in sys.path:
    sys.path.append(current_dir)


def main():
    logger.info("开始评估虚拟系统响应性能...")
    logger.info(f"当前工作目录: {current_dir}")

    evaluator = ResponseEvaluator(current_dir)

    # 检查数据文件是否存在
    for device in ["motor", "compressor", "fan", "pump"]:
        device_dir = os.path.join(current_dir, device)
        if os.path.exists(device_dir):
            logger.info(f"找到设备目录: {device_dir}")
            info_file = os.path.join(device_dir, "dataset_info.json")
            if os.path.exists(info_file):
                logger.info(f"找到数据集信息文件: {info_file}")
            else:
                logger.warning(f"未找到数据集信息文件: {info_file}")
        else:
            logger.warning(f"未找到设备目录: {device_dir}")

    evaluator.evaluate_all_devices()
    logger.info("评估完成，请查看 evaluation_results 目录下的评估报告和图表")


if __name__ == "__main__":
    main()

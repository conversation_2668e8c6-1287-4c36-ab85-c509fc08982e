# -*- mode: python ; coding: utf-8 -*-
import sys
sys.setrecursionlimit(sys.getrecursionlimit() * 10)  # 增加递归深度限制

block_cipher = None

a = Analysis(
    ['run_evaluation.py'],
    pathex=['.'],  # 添加当前目录到搜索路径
    binaries=[],
    datas=[
        ('*.py', '.'),  # 包含所有 Python 文件
        ('fonts/msyh.ttc', 'fonts'),  # 字体文件
        ('fonts/simhei.ttf', 'fonts'),
        ('motor', 'motor'),  # 添加数据目录
        ('compressor', 'compressor'),
        ('fan', 'fan'),
        ('pump', 'pump'),
    ],
    hiddenimports=[
        'numpy',
        'pandas',
        'matplotlib',
        'matplotlib.backends.backend_tkagg',  # 添加matplotlib后端
        'pathlib',
        'logging',
        'scipy',  # 添加可能需要的依赖
        'PIL',
        'evaluator',  # 添加本地模块
        'data_manager',
        'data_generator'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 添加matplotlib的数据文件
import matplotlib
import os
mpl_data_dir = matplotlib.get_data_path()
mpl_data_files = []
for root, dirs, files in os.walk(mpl_data_dir):
    for file in files:
        full_path = os.path.join(root, file)
        rel_path = os.path.relpath(full_path, mpl_data_dir)
        mpl_data_files.append((os.path.join('mpl-data', rel_path), full_path, 'DATA'))
a.datas += mpl_data_files

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='run_evaluation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
) 
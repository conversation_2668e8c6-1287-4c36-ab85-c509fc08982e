import socket
import time
import logging
import struct
import threading

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def send_command(client_socket, command):
    """
    发送控制指令,不等待响应
    """
    try:
        # 将数值转换为字符串
        if isinstance(command, (int, float)):
            command = str(command)

        # 使用ASCII编码发送数据，添加CR/LF终止符
        message = command.encode('ascii') + b'\r'  # 添加CR终止符
        client_socket.send(message)
        logger.info(f"发送指令: {command}")
        return True
    except (socket.error, struct.error) as e:
        logger.error(f"发送指令失败: {e}")
        return False
    

def receive_response(client_socket):
        """
        接收并显示客户端信息
        """
        try:
            while True:
                response = client_socket.recv(1024)
                if not response:
                    break
                response = struct.unpack('>d', response)[0]
                logger.info(f"收到客户端信息: {response}")
        except socket.error as e:
            logger.error(f"接收信息失败: {e}")

def main():
    # 配置 TCP/IP 服务器
    host = "***********"  
    port = 9999      
    
    # 创建 TCP/IP 服务器套接字
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((host, port))
    server_socket.listen(1)
    

    

    logger.info(f"服务器启动，正在监听 {host}:{port}...")
    
    try:
        while True:
            # 等待客户端连接
            client_socket, client_address = server_socket.accept()
            logger.info(f"客户端 {client_address} 已连接")
            # 启动一个线程来接收客户端信息
            receive_thread = threading.Thread(target=receive_response, args=(client_socket,))
            receive_thread.start()
            
            try:
                # 持续发送控制信号
                while True:
                    # 生成控制信号
                    time.sleep(3)
                    control_values = ["200","300","400"] 
                    
                    for value in control_values:
                        if not send_command(client_socket, value):
                            raise Exception("发送失败")
                        time.sleep(8)  # 控制发送间隔
                        
            except Exception as e:
                logger.error(f"与客户端通信时发生错误: {e}")
                # 关闭当前连接
                client_socket.close()
                logger.info("关闭客户端连接,等待新连接...")
                continue
                
    except KeyboardInterrupt:
        logger.info("服务器正在关闭...")
    
    finally:
        # 关闭服务器
        server_socket.close()
        logger.info("服务器已关闭")

if __name__ == "__main__":
    main()
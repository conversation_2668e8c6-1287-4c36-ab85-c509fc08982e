import matlab.engine       
import numpy as np    
import matplotlib.pyplot as plt  
import time

eng = matlab.engine.start_matlab() 
eng.Motor_script(nargout=0)
simu_file = 'FOCsimulation'
eng.load_system(simu_file, nargout=0)

# 初始信号ref
init_ref = 300
eng.set_param(simu_file+'/Input/Constant', 'value', str(init_ref), nargout=0)

# 初始暂停时间
init_pause_time = 10000
eng.set_param(simu_file+'/Constant', 'value', str(init_pause_time), nargout=0)

eng.set_param(simu_file, 'StopTime', '12', nargout=0)
eng.set_param(simu_file, 'SimulationCommand', 'start', nargout=0)

# 模拟传入控制信号,假设分别在第4s、第7s收到两个控制信号
pause_times = [4, 7, 10]
ref_values = [600, 900, 1200]  # 假设在 4s 时 ref = 500，7s 时 ref = 600

for pause_time, ref_val in zip(pause_times, ref_values):
    # 设置暂停时间
    # while True:
    #     clock_time = np.array(eng.eval('out.clock.Data'))
    #     if clock_time[-1] >= pause_time:
    #         eng.eval(f'''
    #                     set_param("{simu_file}", "SimulationCommand", "pause");  % 暂停仿真
    #                 ''', nargout=0)
    #         break
    eng.set_param(simu_file+'/Constant', 'value', str(pause_time), nargout=0)
    # eng.eval(f'''
    #     set_param("{simu_file}", "SimulationCommand", "pause");  % 暂停仿真
    # ''', nargout=0)

    # 等待仿真暂停
    eng.eval(f'''
    sim_status = get_param("{simu_file}", "SimulationStatus");
    while strcmp(sim_status, "running")
        pause(1);
        sim_status = get_param("{simu_file}", "SimulationStatus");
        disp('仿真运行中');
    end
    if strcmp(sim_status, "paused")
        disp('仿真已暂停');
    end
    ''', nargout=0)
    
    # 修改 ref 参数
    eng.set_param(simu_file+'/Input/Constant', 'value', str(ref_val), nargout=0)
    print(f"At time {pause_time}s, ref is updated to {ref_val}")
    
    
    # 继续仿真
    eng.set_param(simu_file+'/Constant', 'value', str(1000), nargout=0)
    eng.set_param(simu_file, 'SimulationCommand', 'continue', nargout=0)
    
    # sim_status = eng.get_param(simu_file, "SimulationStatus")
    # print(f"Simulation status is: {sim_status}")
    # eng.eval(f'''
    # sim_status = get_param("{simu_file}", "SimulationStatus");
    # while strcmp(sim_status, "paused")
    #     pause(1);
    #     sim_status = get_param("{simu_file}", "SimulationStatus");
    #     disp('仿真暂停中');
    # end
    # if strcmp(sim_status, "running")
    #     disp('仿真已继续');
    # end
    # ''', nargout=0)
      

# 检查仿真是否结束
eng.eval(f'while strcmp(get_param("{simu_file}", "SimulationStatus"), "running"), pause(1), end', nargout=0)

out = eng.workspace['out']  
speed = np.array(eng.eval('out.speed.Data'))
t = np.array(eng.eval('out.tout'))


# 重采样时间，确保两个长度一样
print(speed.shape, t.shape)
step = t.shape[0] // speed.shape[0]
t = t[::step]
min_len = min(speed.shape[0], t.shape[0])
speed, t = speed[:min_len, :], t[:min_len, :]
print(speed.shape, t.shape)

fig = plt.figure(figsize=(5, 5))
plt.plot(t, speed, 'or-')
plt.show()

eng.quit()


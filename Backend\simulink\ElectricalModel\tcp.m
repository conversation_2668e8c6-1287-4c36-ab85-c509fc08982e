% 创建 TCP 连接
t = tcpclient("192.168.3.8", 9999);

% 循环监听并处理服务器的控制信号
while true
    % 读取来自服务器的数据
    data = read(t, t.NumBytesAvailable); %
    
    % 判断接收到的数据（控制信号）
    if ~isempty(data)
        data_str = char(data); 
        disp(['Received from server: ', data_str]);

        % 根据接收到的控制信号做出回应
        if strcmp(data_str, 'start control')
            % 接收到控制信号
            response = 'Start control received';
            write(t, response);
            disp('Sent response: Start control received');
        elseif strcmp(data_str, 'stop control')
            % 接收到停止控制信号
            response = 'Stop control received';
            write(t, response);
            disp('Sent response: Stop control received');
        else
            % 其他数据
            response = 'Message received';
            write(t, response);
            disp('Sent response: Message received');
        end
    end

    pause(1);  % 每秒检查一次
end

% 清理连接
clear t;
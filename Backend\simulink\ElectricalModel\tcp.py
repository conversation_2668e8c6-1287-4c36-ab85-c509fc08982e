import socket
import time
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def send_command(client_socket, command):
    """
    发送控制指令并接收响应
    """
    try:
        command = re.sub(r'\s+', '', command)  # 去除多余的空格字符
        client_socket.send(command.encode())
        # 等待响应
        response = client_socket.recv(1024).decode()
        logger.info(f"发送指令: {command}, 收到响应: {response}")
        return response
    except socket.error as e:
        logger.error(f"发送指令失败: {e}")
        return None

def main():
    # 配置 TCP/IP 连接
    host = "192.1s68.3.8"  # Simulink 所在设备的 IP 地址
    port = 9997       # Simulink 监听的端口号
    
    # 创建 TCP/IP 套接字
    client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client_socket.settimeout(5)  # 设置超时时间
    
    # 尝试连接
    connected = False
    max_retries = 3
    retry_count = 0
    
    while not connected and retry_count < max_retries:
        try:
            logger.info(f"正在尝试连接 {host}:{port}...")
            client_socket.connect((host, port))
            connected = True
            logger.info("连接成功!")
        except socket.error as e:
            retry_count += 1
            logger.warning(f"连接失败 ({retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                time.sleep(2)  # 等待2秒后重试
    
    if not connected:
        logger.error("无法建立连接，程序退出")
        return

    try:
        # 发送控制指令示例
        commands = ["100", "200", "300"]  # 多个控制指令
        while True:
            for command in commands:
                response = send_command(client_socket, command)
                if response is None:
                    raise Exception("发送指令失败")
                time.sleep(1)  # 指令之间间隔1秒
            
    except Exception as e:
        logger.error(f"运行时错误: {e}")
    
    finally:
        # 关闭连接
        logger.info("关闭连接...")
        client_socket.close()

if __name__ == "__main__":
    main()
import socket
import time

def start_tcp_server(host='***********', port=9999):
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.bind((host, port))
    server_socket.listen(5)
    print(f"Server started on {host}:{port}")

    while True:
        print("Waiting for connection...")
        client_socket, client_address = server_socket.accept()
        print(f"Connection from {client_address}")
        send_signals(client_socket)

def send_one_signal(client_socket, signal):
    print(f"Sending signal: {signal}")
    client_socket.sendall(signal.encode())
    while True:
        data = client_socket.recv(1024)
        print(f"Received data: {data.decode()}")
        break


def send_signals(client_socket):
    # 每隔一定时间发送控制信号
    while True:
        # 发送开始控制信号
        send_one_signal(client_socket, "start control")
        time.sleep(5)  

        # 发送停止控制信号
        send_one_signal(client_socket, "stop control")
        time.sleep(5)  


if __name__ == "__main__":
    start_tcp_server()
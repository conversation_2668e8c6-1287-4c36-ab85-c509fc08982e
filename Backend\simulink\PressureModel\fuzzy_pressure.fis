[System]
Name='fuzzy_pressure_516'
Type='mamdani'
Version=2.0
NumInputs=2
NumOutputs=1
NumRules=49
AndMethod='min'
OrMethod='max'
ImpMethod='min'
AggMethod='max'
DefuzzMethod='centroid'

[Input1]
Name='e'
Range=[-4 4]
NumMFs=7
MF1='NB':'linzmf',[-2.4 -1.8]
MF2='NM':'gaussmf',[0.48 -1.28]
MF3='NS':'gaussmf',[0.34 -0.48]
MF4='Z':'gaussmf',[0.16 0]
MF5='PS':'gaussmf',[0.34 0.496]
MF6='PM':'gaussmf',[0.48 1.28]
MF7='PB':'linsmf',[1.68 2.4]

[Input2]
Name='de'
Range=[-35 35]
NumMFs=7
MF1='NB':'linzmf',[-21 -14.7]
MF2='NM':'gaussmf',[4.55 -11.2]
MF3='NS':'gaussmf',[3.15 -4.054]
MF4='Z':'gaussmf',[1.575 0]
MF5='PS':'gaussmf',[2.8 4.2]
MF6='PM':'gaussmf',[4.55 11.2]
MF7='PB':'linsmf',[14.7 21]

[Output1]
Name='Rx'
Range=[0 1]
NumMFs=5
MF1='R1':'gaussmf',[0.106 0.004175]
MF2='R2':'gaussmf',[0.1061 0.25]
MF3='R3':'gaussmf',[0.1061 0.5]
MF4='R4':'gaussmf',[0.1061 0.75]
MF5='R5':'gaussmf',[0.1061 1]

[Rules]
1 1, 4 (1) : 1
1 2, 4 (1) : 1
1 3, 5 (1) : 1
1 4, 5 (1) : 1
1 5, 5 (1) : 1
1 6, 4 (1) : 1
1 7, 4 (1) : 1
2 1, 2 (1) : 1
2 2, 3 (1) : 1
2 3, 3 (1) : 1
2 4, 4 (1) : 1
2 5, 3 (1) : 1
2 6, 3 (1) : 1
2 7, 2 (1) : 1
3 1, 1 (1) : 1
3 2, 2 (1) : 1
3 3, 2 (1) : 1
3 4, 2 (1) : 1
3 5, 2 (1) : 1
3 6, 2 (1) : 1
3 7, 1 (1) : 1
4 1, 1 (1) : 1
4 2, 1 (1) : 1
4 3, 1 (1) : 1
4 4, 1 (1) : 1
4 5, 1 (1) : 1
4 6, 1 (1) : 1
4 7, 1 (1) : 1
5 1, 1 (1) : 1
5 2, 2 (1) : 1
5 3, 2 (1) : 1
5 4, 2 (1) : 1
5 5, 2 (1) : 1
5 6, 2 (1) : 1
5 7, 1 (1) : 1
6 1, 2 (1) : 1
6 2, 3 (1) : 1
6 3, 3 (1) : 1
6 4, 4 (1) : 1
6 5, 3 (1) : 1
6 6, 3 (1) : 1
6 7, 2 (1) : 1
7 1, 4 (1) : 1
7 2, 4 (1) : 1
7 3, 5 (1) : 1
7 4, 5 (1) : 1
7 5, 5 (1) : 1
7 6, 4 (1) : 1
7 7, 4 (1) : 1

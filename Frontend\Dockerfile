# 构建阶段
FROM hub.rat.dev/library/node:18-alpine AS build-stage

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 设置淘宝镜像源并安装 cnpm
RUN npm install -g cnpm --registry=https://registry.npmmirror.com

# 使用 cnpm 安装依赖
RUN cnpm install

# 复制项目文件（除了.dockerignore中指定的文件）
COPY . .

# 暴露端口
EXPOSE 5173

# 启动开发服务器，指定 host 为 0.0.0.0
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]

# # 生产阶段
# FROM nginx:stable-alpine as production-stage

# # 复制构建文件到 nginx
# COPY --from=build-stage /app/dist /usr/share/nginx/html

# 启动 nginx
# CMD ["nginx", "-g", "daemon off;"] 
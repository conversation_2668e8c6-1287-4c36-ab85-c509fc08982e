# vue

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```

## 快速开始

### 方法一：使用 npm 直接运行

1. 安装依赖
```sh
# 使用淘宝镜像源安装 cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com

# 安装项目依赖
cnpm install
```

2. 开发模式运行
```sh
npm run dev
```

3. 生产环境构建
```sh
# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

### 方法二：使用 Docker 运行

1. 构建 Docker 镜像
```sh
# 在项目根目录下运行
docker build -t g-wvop8952-docker.pkg.coding.net/generativecontrol/acceptance/frontapp:v3 .
```

2. 运行 Docker 容器
```sh
# 开发模式运行
docker run -d -p 5173:5173 --name frontend g-wvop8952-docker.pkg.coding.net/generativecontrol/acceptance/frontapp:v3

# 或者指定网络模式
docker run -d --network host --name frontend g-wvop8952-docker.pkg.coding.net/generativecontrol/acceptance/frontapp:v3
```

依赖安装

```sh

npm install -g cnpm --registry=https://registry.npmmirror.com

cnpm install

```


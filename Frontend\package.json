{"name": "ADAS", "version": "1.0.0", "description": "国创中心数据采集系统", "main": "electron/main.js", "author": "<PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "electron": "wait-on tcp:8889 && cross-env NODE_ENV=development electron .", "electron:serve": "concurrently -k \"npm run dev\" \"npm run electron\"", "electron:build": "vite build && electron-builder --win --config"}, "dependencies": {"@antv/g2": "^5.2.1", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "concurrently": "^8.2.2", "echarts": "^5.5.1", "element-plus": "^2.7.6", "esbuild": "^0.23.1", "papaparse": "^5.5.2", "pinia": "^2.1.7", "socket.io-client": "^4.7.5", "vue": "^3.4.29", "vue-router": "^4.3.3", "vue-socket.io": "^3.0.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "@vue/runtime-core": "^3.4.38", "cross-env": "^7.0.3", "electron": "^32.3.0", "electron-builder": "^24.13.3", "nodemon": "^3.1.4", "vite": "^5.3.1"}, "build": {"appId": "com.sjtu.vue", "productName": "国创中心数据采集系统", "win": {"icon": "./electron/favicon.ico"}, "nsis": {"oneClick": false, "artifactName": "国创中心数据采集系统-安装.exe", "allowToChangeInstallationDirectory": true, "installerIcon": "./electron/favicon.ico", "uninstallerIcon": "./electron/favicon.ico"}, "files": ["dist/**/*", "electron/**/*"], "directories": {"buildResources": "assets", "output": "dist_electron"}}, "config": {"electron_mirror": "https://npmmirror.com/mirrors/electron/", "electron_builder_binaries_mirror": "https://npmmirror.com/mirrors/electron-builder-binaries/"}}
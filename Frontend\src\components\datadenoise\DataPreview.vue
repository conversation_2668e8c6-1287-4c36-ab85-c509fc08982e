<template>
  <div v-if="store.rawData.length" class="data-preview">
    <el-divider>数据预览</el-divider>
    
    <!-- 数据统计信息 -->
    <div class="statistics-panel">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>数据总量</span>
              </div>
            </template>
            <div class="statistic-value">{{ store.dataCount }}</div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>数据列数</span>
              </div>
            </template>
            <div class="statistic-value">{{ store.columnCount }}</div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>已处理列</span>
              </div>
            </template>
            <div class="statistic-value">{{ store.processedColumnCount }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="store.processedData.slice(0, previewRows)"
        style="width: 100%"
        height="400"
        border
        stripe
      >
        <el-table-column
          v-for="col in store.columns"
          :key="col"
          :prop="col"
          :label="col"
          sortable
        />
      </el-table>
      
      <div class="preview-info" v-if="store.dataCount > previewRows">
        显示前 {{ previewRows }} 条数据，共 {{ store.dataCount }} 条
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useDataDenoiseStore } from '@/stores/datadenoise'

const store = useDataDenoiseStore()
const previewRows = ref(10)
</script>

<style scoped>
.data-preview {
  margin-top: 20px;
}

.statistics-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  text-align: center;
}

.table-container {
  margin-top: 20px;
}

.preview-info {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
  text-align: right;
}
</style> 
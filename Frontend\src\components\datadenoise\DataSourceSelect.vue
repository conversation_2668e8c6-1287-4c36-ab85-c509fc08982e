<template>
  <el-form :model="store.dataSource" label-width="100px">
    <el-form-item label="数据来源">
      <el-radio-group v-model="store.dataSource" @change="handleSourceChange">
        <el-radio :label="'file'">文件上传</el-radio>
        <el-radio :label="'device'">设备数据</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 文件上传 -->
    <el-form-item v-if="store.dataSource === 'file'">
      <el-upload
        class="data-uploader"
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        accept=".csv"
      >
        <template #trigger>
          <el-button type="primary">选择CSV文件</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip">
            请上传CSV格式的数据文件
          </div>
        </template>
      </el-upload>
    </el-form-item>

    <!-- 设备选择 -->
    <el-form-item v-if="store.dataSource === 'device'">
      <el-select 
        v-model="store.selectedDevice" 
        placeholder="请选择设备"
        @change="handleDeviceChange"
      >
        <el-option
          v-for="device in devices"
          :key="device.ID"
          :label="getDeviceLabel(device)"
          :value="device.ID"
        >
          <div class="device-option">
            <span>{{ getDeviceLabel(device) }}</span>
            <el-tag size="small" :type="getDeviceTagType(device)">
              {{ device.type }}
            </el-tag>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { useDBStore } from '@/stores/global'
import { useDataDenoiseStore } from '@/stores/datadenoise'
import { useDeviceStore } from '@/stores/devicestatus'
import { ElMessage } from 'element-plus'
import Papa from 'papaparse'

const dbStore = useDBStore()
const store = useDataDenoiseStore()
const deviceStore = useDeviceStore()

// 获取唯一设备列表
const devices = computed(() => {
  return deviceStore.devices.map(device => ({
    ID: device.id,
    name: device.name,
    type: device.type,
    status: device.status,
    connected: device.connected
  }))
})

// 获取设备显示标签
const getDeviceLabel = (device) => {
  const deviceTypes = {
    'stepper_motor': '步进电机',
    'async_motor': '异步电机',
    'pmsm_motor': '永磁同步电机',
    'fan': '风机',
    'pump': '水泵',
    'compressor': '压缩机',
    'dc_motor': '直流无刷电机'
  }
  const displayType = deviceTypes[device.type] || device.type
  return `${device.name || device.ID} (${displayType})`
}

// 获取设备标签类型
const getDeviceTagType = (device) => {
  const statusMap = {
    'normal': 'success',
    'warning': 'warning',
    'error': 'danger'
  }
  return statusMap[device.status] || ''
}

const handleFileChange = (file) => {
  if (!file.raw) return
  
  Papa.parse(file.raw, {
    complete: (results) => {
      if (results.data && results.data.length) {
        const filteredData = results.data.filter(row => {
          return Object.values(row).some(val => val !== '' && val !== undefined && val !== null)
        })
        
        if (filteredData.length === 0) {
          ElMessage.error('文件中没有有效数据')
          return
        }
        
        store.resetAll()
        store.setData(filteredData)
        store.setColumns(Object.keys(filteredData[0]))
        
        ElMessage.success(`成功加载 ${filteredData.length} 条数据`)
      } else {
        ElMessage.error('文件格式错误或没有数据')
      }
    },
    header: true,
    skipEmptyLines: true,
    dynamicTyping: true
  })
}

const handleSourceChange = async () => {
  store.resetAll()
  if (store.dataSource === 'device') {
    try {
      // 使用 deviceStore 中的数据
      await dbStore.dbDataUpdate()
    } catch (error) {
      console.error('获取设备列表失败:', error)
      ElMessage.error('获取设备列表失败')
    }
  }
}

const handleDeviceChange = async (deviceId) => {
  try {
    const selectedDevice = deviceStore.devices.find(d => d.id === deviceId)
    if (!selectedDevice) {
      throw new Error('设备不存在')
    }

    // 根据设备类型生成模拟数据
    const mockData = generateMockData(selectedDevice)
    store.setData(mockData)

    // 设置数据列
    const columns = getDeviceColumns(selectedDevice.type)
    store.setColumns(columns)

    ElMessage.success(`成功加载 ${selectedDevice.name} 的数据`)
  } catch (error) {
    console.error('加载设备数据失败:', error)
    ElMessage.error('加载设备数据失败')
  }
}

// 添加生成模拟数据的函数
const generateMockData = (device) => {
  const data = []
  const now = Date.now()
  
  // 生成100条模拟数据
  for (let i = 0; i < 100; i++) {
    const timestamp = new Date(now - i * 1000).toISOString()
    const baseData = {
      timestamp,
      ID: device.id
    }

    // 根据设备类型添加特定数据
    switch (device.type) {
      case 'stepper_motor':
        Object.assign(baseData, {
          speed: Math.floor(Math.random() * 1000 + 2000),
          current: Number((Math.random() * 2 + 1).toFixed(2)),
          voltage: Number((Math.random() * 20 + 20).toFixed(2)),
          power: Number((Math.random() * 100).toFixed(2)),
          position: Number((Math.random() * 360).toFixed(2))
        })
        break
      // 添加其他设备类型的数据生成逻辑...
      case 'async_motor':
        Object.assign(baseData, {
          speed: Math.floor(Math.random() * 1000 + 2000),
          current: Number((Math.random() * 5 + 5).toFixed(2)),
          voltage: Number((Math.random() * 100 + 340).toFixed(2)),
          power: Number((Math.random() * 2000).toFixed(2)),
          frequency: Number((Math.random() * 50).toFixed(2)),
          slip: Number((Math.random() * 5).toFixed(2))
        })
        break
      // 可以继续添加其他设备类型...
    }
    
    data.push(baseData)
  }
  
  return data
}

// 添加获取设备列的函数
const getDeviceColumns = (deviceType) => {
  const baseColumns = ['timestamp']
  const deviceTypeColumns = {
    'stepper_motor': ['speed', 'current', 'voltage', 'power', 'position'],
    'async_motor': ['speed', 'current', 'voltage', 'power', 'frequency', 'slip'],
    'pmsm_motor': ['speed', 'current', 'voltage', 'power', 'torque', 'efficiency'],
    'fan': ['speed', 'airflow', 'pressure', 'power'],
    'pump': ['speed', 'flow', 'head', 'power'],
    'compressor': ['speed', 'pressure', 'temperature', 'power'],
    'dc_motor': ['speed', 'current', 'voltage', 'power', 'torque']
  }
  
  return [...baseColumns, ...(deviceTypeColumns[deviceType] || [])]
}

// 组件加载时获取设备列表
onMounted(async () => {
  if (store.dataSource === 'device') {
    await handleSourceChange()
  }
})

// 监听数据库更新
watch(() => dbStore.dbDataObjList, (newData) => {
  if (store.selectedDevice) {
    // 如果当前有选中的设备，更新其数据
    const deviceData = newData.filter(item => item.ID === store.selectedDevice)
    if (deviceData.length) {
      store.setData(deviceData)
    }
  }
}, { deep: true })
</script>

<style scoped>
.device-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}

.el-tag {
  text-transform: capitalize;
}

.data-uploader {
  width: 100%;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}
</style> 
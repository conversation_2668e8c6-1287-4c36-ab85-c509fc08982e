<template>
  <div v-if="store.columns.length" class="data-config">
    <el-divider>数据处理配置</el-divider>
    
    <el-form :model="store.denoiseConfig" label-width="120px">
      <el-form-item label="选择列">
        <el-select
          v-model="store.denoiseConfig.selectedColumns"
          multiple
          placeholder="请选择需要处理的列"
          style="width: 100%"
        >
          <el-option
            v-for="col in store.columns"
            :key="col"
            :label="col"
            :value="col"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="去噪方法">
        <el-select 
          v-model="store.denoiseConfig.denoiseMethod" 
          placeholder="请选择去噪方法"
          style="width: 100%"
        >
          <el-option label="不去噪" value="none" />
          <el-option label="移动平均" value="moving_average" />
          <el-option label="中值滤波" value="median_filter" />
          <el-option label="高斯滤波" value="gaussian_filter" />
        </el-select>
      </el-form-item>

      <el-form-item 
        label="窗口大小" 
        v-if="store.denoiseConfig.denoiseMethod !== 'none'"
      >
        <el-input-number 
          v-model="store.denoiseConfig.windowSize"
          :min="3"
          :max="21"
          :step="2"
          style="width: 180px"
        />
      </el-form-item>

      <el-form-item label="归一化处理">
        <el-switch
          v-model="store.denoiseConfig.normalize"
          active-text="开启"
          inactive-text="关闭"
          :active-value="true"
          :inactive-value="false"
        />
        <div class="normalize-tip" v-if="store.denoiseConfig.normalize">
          <el-alert
            type="info"
            :closable="false"
            show-icon
          >
            <template #title>
              使用最大最小值归一化,数据将被缩放到[0,1]区间
            </template>
          </el-alert>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button 
          type="primary" 
          @click="store.handleDenoise"
          :disabled="!store.denoiseConfig.selectedColumns.length"
          style="width: 120px"
        >
          开始处理
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { useDataDenoiseStore } from '@/stores/datadenoise'

const store = useDataDenoiseStore()
</script>

<style scoped>
.data-config {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.normalize-tip {
  margin-top: 10px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 180px;
}

:deep(.el-alert) {
  margin-top: 8px;
  padding: 8px 16px;
}

:deep(.el-switch) {
  margin-right: 10px;
}
</style> 
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export const useDataDenoiseStore = defineStore('datadenoise', () => {
  // 状态
  const rawData = ref([])
  const processedData = ref([])
  const columns = ref([])
  const processedColumns = ref([])
  const dataSource = ref('file')
  const selectedDevice = ref('')
  const denoiseConfig = ref({
    selectedColumns: [],
    denoiseMethod: 'none',
    windowSize: 5,
    normalize: false
  })

  // 计算属性
  const dataCount = computed(() => rawData.value.length)
  const columnCount = computed(() => columns.value.length)
  const processedColumnCount = computed(() => processedColumns.value.length)

  // 方法
  const setData = (data) => {
    // 确保数据是数组
    if (!Array.isArray(data)) {
      console.error('Invalid data format:', data)
      return
    }
    console.log('Setting data:', data) // 调试日志
    rawData.value = data
    processedData.value = [...data] // 创建新的数组副本
  }

  const setColumns = (cols) => {
    // 确保列是数组
    if (!Array.isArray(cols)) {
      console.error('Invalid columns format:', cols)
      return
    }
    console.log('Setting columns:', cols) // 调试日志
    columns.value = [...cols]
  }

  const updateProcessedColumns = (cols) => {
    processedColumns.value = [...cols]
  }

  // 重置所有数据
  const resetAll = () => {
    rawData.value = []
    processedData.value = []
    columns.value = []
    processedColumns.value = []
    denoiseConfig.value = {
      selectedColumns: [],
      denoiseMethod: 'none',
      windowSize: 5,
      normalize: false
    }
  }

  // 移动平均滤波
  const movingAverage = (data, windowSize) => {
    const result = []
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - Math.floor(windowSize / 2))
      const end = Math.min(data.length, i + Math.floor(windowSize / 2) + 1)
      const window = data.slice(start, end)
      const avg = window.reduce((a, b) => a + b, 0) / window.length
      result.push(avg)
    }
    return result
  }

  // 中值滤波
  const medianFilter = (data, windowSize) => {
    const result = []
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - Math.floor(windowSize / 2))
      const end = Math.min(data.length, i + Math.floor(windowSize / 2) + 1)
      const window = data.slice(start, end).sort((a, b) => a - b)
      result.push(window[Math.floor(window.length / 2)])
    }
    return result
  }

  // 高斯滤波
  const gaussianFilter = (data, windowSize) => {
    const sigma = windowSize / 6
    const result = []
    
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - Math.floor(windowSize / 2))
      const end = Math.min(data.length, i + Math.floor(windowSize / 2) + 1)
      let sum = 0
      let weightSum = 0
      
      for (let j = start; j < end; j++) {
        const x = j - i
        const weight = Math.exp(-(x * x) / (2 * sigma * sigma))
        sum += data[j] * weight
        weightSum += weight
      }
      
      result.push(sum / weightSum)
    }
    return result
  }

  // 添加归一化处理函数
  const minMaxNormalize = (data) => {
    const min = Math.min(...data)
    const max = Math.max(...data)
    if (max === min) return data.map(() => 0)
    return data.map(value => (value - min) / (max - min))
  }

  // 处理去噪
  const handleDenoise = () => {
    if (!rawData.value.length || !denoiseConfig.value.selectedColumns.length) {
      ElMessage.warning('请先选择需要处理的数据列')
      return
    }
    
    const newData = JSON.parse(JSON.stringify(rawData.value))
    
    denoiseConfig.value.selectedColumns.forEach(column => {
      const columnData = rawData.value.map(row => {
        const value = parseFloat(row[column])
        if (isNaN(value)) {
          console.warn(`Invalid value in column ${column}:`, row[column])
          return 0
        }
        return value
      })

      let processedColumn = columnData

      // 如果选择了去噪方法且不是 none
      if (denoiseConfig.value.denoiseMethod !== 'none') {
        switch (denoiseConfig.value.denoiseMethod) {
          case 'moving_average':
            processedColumn = movingAverage(columnData, denoiseConfig.value.windowSize)
            break
          case 'median_filter':
            processedColumn = medianFilter(columnData, denoiseConfig.value.windowSize)
            break
          case 'gaussian_filter':
            processedColumn = gaussianFilter(columnData, denoiseConfig.value.windowSize)
            break
        }
      }

      // 如果开启了归一化
      if (denoiseConfig.value.normalize) {
        processedColumn = minMaxNormalize(processedColumn)
      }
      
      newData.forEach((row, index) => {
        row[column] = processedColumn[index].toFixed(4)
      })
    })
    
    processedData.value = newData
    processedColumns.value = [...denoiseConfig.value.selectedColumns]
    ElMessage.success('数据处理完成')
  }

  return {
    // 状态
    rawData,
    processedData,
    columns,
    processedColumns,
    dataSource,
    selectedDevice,
    denoiseConfig,

    // 计算属性
    dataCount,
    columnCount,
    processedColumnCount,

    // 方法
    setData,
    setColumns,
    updateProcessedColumns,
    handleDenoise,
    resetAll
  }
}) 
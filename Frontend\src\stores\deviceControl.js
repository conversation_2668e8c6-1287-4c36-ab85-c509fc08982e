import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useDeviceStore } from '@/stores/devicestatus'

const DEVICE_DEFAULTS = {
  stepper_motor: {
    transmitter: 0,    // 发送机占空比
    receiver: 0,       // 接收机占空比
    axis_deviation: 0, // 轴角偏差
  },
  async_motor: {
    speed: 1500,
    current: 10,
    voltage: 380,
    power: 5.5,
    frequency: 50,
    slip: 3.5
  },
  pmsm_motor: {
    speed: 3000,
    current: 8,
    voltage: 380,
    power: 4.0,
    torque: 12.5,
    efficiency: 96
  },
  fan: {
    speed: 2900,
    airflow: 5000,
    pressure: 800,
    power: 7.5
  },
  pump: {
    speed: 2900,
    flow: 50,
    head: 32,
    power: 5.5
  },
  compressor: {
    speed: 3000,
    pressure: 0.8,
    temperature: 75,
    power: 11
  },
  dc_motor: {
    pwm: 0,           // 启动时PWM从0开始
    voltage: 0,       // 初始电压0V
    current_u: 0,     // U相电流初始值
    current_v: 0,     // V相电流初始值
    current_w: 0,     // W相电流初始值
    current_bus: 0,   // 母线电流初始值
    temperature: 25,  // 初始温度设为室温
  },
  virtual_motor: {
    speed: 0,          // 当前转速
    current: 0,        // 电流
    voltage: 0,        // 电压
    temperature: 25,   // 温度
  },
  virtual_pressure: {
    pressure: 0,        // 压力 0-100MPa
    current: 0,         // 电流
    voltage: 0,         // 电压
    temperature: 25     // 温度
  },
  virtual_pump: {
    level: 0,          // 水位 100-500cm
    current: 0,        // 电流
    voltage: 0,        // 电压
    temperature: 25    // 温度
  },
  virtual_wind: {
    wind_speed: 0,      // 风速 0-20m/s
    current: 0,         // 电流
    voltage: 0,         // 电压
    temperature: 25     // 温度
  }
}

export const useDeviceControlStore = defineStore('deviceControl', () => {
  const deviceStore = useDeviceStore()
  const deviceStatus = ref({})
  const deviceParams = ref({})
  const deviceHistory = ref({})
  const updateIntervals = ref({})
  const deviceUpdateRates = ref({}) // 每个设备的更新频率

  const getDeviceStatus = (deviceId) => deviceStatus.value[deviceId] || 'stopped'
  const getDeviceParams = (deviceId) => deviceParams.value[deviceId] || {}
  const getDeviceHistory = (deviceId) => deviceHistory.value[deviceId] || []
  const getDeviceUpdateRate = (deviceId) => deviceUpdateRates.value[deviceId] || 0

  const addRandomVariation = (value, percentage = 0.1) => {
    const variation = (Math.random() - 0.5) * 2 * (value * percentage)
    return value + variation
  }

  const updateDeviceData = (deviceId, deviceType) => {
    const currentParams = { ...deviceParams.value[deviceId] }
    const timestamp = Date.now()

    if (deviceType === 'dc_motor') {
      // 获取设备配置信息
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      // 使用设备自己的IP和端口构建API URL
      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 通过 API 从上位机获取最新数据
      fetch(`${apiUrl}/api/v1/control/getValue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => {
        if (!response.ok) throw new Error('Network response was not ok')
        return response.json()
      })
      .then(data => {
        if (data.values) {
          // 更新当前参数
          data.values.forEach(item => {
            switch(item.name) {
              case 'pwm':
                currentParams.pwm = Number(item.value)
                break
              case 'voltage':
                currentParams.voltage = Number(item.value)
                break
              case 'current_u':
                currentParams.current_u = Number(item.value)
                break
              case 'current_v':
                currentParams.current_v = Number(item.value)
                break
              case 'current_w':
                currentParams.current_w = Number(item.value)
                break
              case 'current_bus':
                currentParams.current_bus = Number(item.value)
                break
              case 'temperature':
                currentParams.temperature = Number(item.value)
                break
            }
          })

          // 更新设备参数
          deviceParams.value[deviceId] = currentParams

          // 更新历史数据
          if (!deviceHistory.value[deviceId]) {
            deviceHistory.value[deviceId] = []
          }
          deviceHistory.value[deviceId].push({
            timestamp,
            values: { ...currentParams }
          })

          // 只保留最近5分钟的数据
          const fiveMinutesAgo = timestamp - 5 * 60 * 1000
          deviceHistory.value[deviceId] = deviceHistory.value[deviceId].filter(
            item => item.timestamp > fiveMinutesAgo
          )
        }
      })
      .catch(error => {
        console.error('Error fetching device data:', error)
        // 如果连续失败多次，可以考虑停止设备
        deviceStatus.value[deviceId] = 'error'
      })

      return // 提前返回，不执行后面的代码
    }

    if (deviceType === 'stepper_motor') {
      // 获取设备配置信息
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      // 使用设备自己的IP和端口构建API URL
      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      fetch(`${apiUrl}/api/v1/control/getValue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => {
        if (!response.ok) throw new Error('Network response was not ok')
        return response.json()
      })
      .then(data => {
        if (data.values) {
          const newParams = {}
          // 根据stepMotorServer.py的API响应格式解析数据
          data.values.forEach(item => {
            switch(item.name) {
              case 'transmitter':
                newParams.transmitter = Number(item.value)
                break
              case 'receiver':
                newParams.receiver = Number(item.value)
                break
              case 'axis_deviation':
                newParams.axis_deviation = Number(item.value)
                break
            }
          })
          
          deviceParams.value[deviceId] = {
            ...deviceParams.value[deviceId],
            ...newParams
          }

          // 更新历史数据
          if (!deviceHistory.value[deviceId]) {
            deviceHistory.value[deviceId] = []
          }
          deviceHistory.value[deviceId].push({
            timestamp,
            values: { ...deviceParams.value[deviceId] }
          })

          // 只保留最近5分钟的数据
          const fiveMinutesAgo = timestamp - 5 * 60 * 1000
          deviceHistory.value[deviceId] = deviceHistory.value[deviceId].filter(
            item => item.timestamp > fiveMinutesAgo
          )
        }
      })
      .catch(error => {
        console.error('Error fetching stepper motor data:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }

    if (deviceType === 'virtual_motor') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`
      
      fetch(`${apiUrl}/api/v1/control/getValue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.values) {
          const currentParams = { ...deviceParams.value[deviceId] }
          
          data.values.forEach(item => {
            switch(item.name) {
              case 'speed':
                currentParams.speed = Number(item.value)
                break
              case 'current':
                currentParams.current = Number(item.value)
                break
              case 'voltage':
                currentParams.voltage = Number(item.value)
                break
              case 'temperature':
                currentParams.temperature = Number(item.value)
                break
            }
          })

          deviceParams.value[deviceId] = currentParams

          // 更新历史数据
          if (!deviceHistory.value[deviceId]) {
            deviceHistory.value[deviceId] = []
          }
          deviceHistory.value[deviceId].push({
            timestamp: Date.now(),
            values: { ...currentParams }
          })

          // 只保留最近5分钟的数据
          const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
          deviceHistory.value[deviceId] = deviceHistory.value[deviceId].filter(
            item => item.timestamp > fiveMinutesAgo
          )
        }
      })
      .catch(error => {
        console.error('Error updating virtual motor data:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }

    if (deviceType === 'virtual_pressure') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`
      
      fetch(`${apiUrl}/api/v1/control/getValue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.values) {
          const currentParams = { ...deviceParams.value[deviceId] }
          
          data.values.forEach(item => {
            switch(item.name) {
              case 'pressure':
                currentParams.pressure = Number(item.value)
                break
              case 'current':
                currentParams.current = Number(item.value)
                break
              case 'voltage':
                currentParams.voltage = Number(item.value)
                break
              case 'temperature':
                currentParams.temperature = Number(item.value)
                break
            }
          })

          deviceParams.value[deviceId] = currentParams

          // 更新历史数据
          if (!deviceHistory.value[deviceId]) {
            deviceHistory.value[deviceId] = []
          }
          deviceHistory.value[deviceId].push({
            timestamp: Date.now(),
            values: { ...currentParams }
          })

          // 只保留最近5分钟的数据
          const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
          deviceHistory.value[deviceId] = deviceHistory.value[deviceId].filter(
            item => item.timestamp > fiveMinutesAgo
          )
        }
      })
      .catch(error => {
        console.error('Error updating virtual pressure data:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }

    if (deviceType === 'virtual_pump') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`
      
      fetch(`${apiUrl}/api/v1/control/getValue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.values) {
          const currentParams = { ...deviceParams.value[deviceId] }
          
          data.values.forEach(item => {
            switch(item.name) {
              case 'level':
                currentParams.level = Number(item.value)
                break
              case 'current':
                currentParams.current = Number(item.value)
                break
              case 'voltage':
                currentParams.voltage = Number(item.value)
                break
              case 'temperature':
                currentParams.temperature = Number(item.value)
                break
            }
          })

          deviceParams.value[deviceId] = currentParams

          // 更新历史数据
          if (!deviceHistory.value[deviceId]) {
            deviceHistory.value[deviceId] = []
          }
          deviceHistory.value[deviceId].push({
            timestamp: Date.now(),
            values: { ...currentParams }
          })

          // 只保留最近5分钟的数据
          const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
          deviceHistory.value[deviceId] = deviceHistory.value[deviceId].filter(
            item => item.timestamp > fiveMinutesAgo
          )
        }
      })
      .catch(error => {
        console.error('Error updating virtual pump data:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }

    if (deviceType === 'virtual_wind') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`
      
      fetch(`${apiUrl}/api/v1/control/getValue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.values) {
          const currentParams = { ...deviceParams.value[deviceId] }
          
          data.values.forEach(item => {
            switch(item.name) {
              case 'wind_speed':
                currentParams.wind_speed = Number(item.value)
                break
              case 'current':
                currentParams.current = Number(item.value)
                break
              case 'voltage':
                currentParams.voltage = Number(item.value)
                break
              case 'temperature':
                currentParams.temperature = Number(item.value)
                break
            }
          })

          deviceParams.value[deviceId] = currentParams

          // 更新历史数据
          if (!deviceHistory.value[deviceId]) {
            deviceHistory.value[deviceId] = []
          }
          deviceHistory.value[deviceId].push({
            timestamp: Date.now(),
            values: { ...currentParams }
          })

          // 只保留最近5分钟的数据
          const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
          deviceHistory.value[deviceId] = deviceHistory.value[deviceId].filter(
            item => item.timestamp > fiveMinutesAgo
          )
        }
      })
      .catch(error => {
        console.error('Error updating virtual wind data:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }

    // 获取目标转速和电压
    const targetSpeed = currentParams.targetSpeed
    const targetVoltage = currentParams.targetVoltage
    
    // 计算当前值与目标值的差值
    const speedDiff = targetSpeed - currentParams.speed
    const voltageDiff = targetVoltage - currentParams.voltage
    
    // 定义调整步长
    const speedStep = 20       // 每次最多变化20 RPM
    const voltageStep = 2      // 每次最多变化2V
    
    // 逐步调整转速
    if (Math.abs(speedDiff) > speedStep) {
      currentParams.speed = Number((currentParams.speed + Math.sign(speedDiff) * speedStep).toFixed(2))
    } else {
      currentParams.speed = Number(targetSpeed.toFixed(2))
    }
    
    // 逐步调整电压
    if (Math.abs(voltageDiff) > voltageStep) {
      currentParams.voltage = Number((currentParams.voltage + Math.sign(voltageDiff) * voltageStep).toFixed(2))
    } else {
      currentParams.voltage = Number(targetVoltage.toFixed(2))
    }

    // 添加小幅波动
    currentParams.speed = Number((currentParams.speed * (1 + (Math.random() - 0.5) * 0.01)).toFixed(2))
    currentParams.voltage = Number((currentParams.voltage * (1 + (Math.random() - 0.5) * 0.01)).toFixed(2))

    // 根据设备类型更新其他参数
    const speedRatio = currentParams.speed / DEVICE_DEFAULTS[deviceType].speed
    switch (deviceType) {
      case 'async_motor': {
        currentParams.current = Number((speedRatio * DEVICE_DEFAULTS[deviceType].current * (1 + (Math.random() - 0.5) * 0.05)).toFixed(2))
        currentParams.power = Number((currentParams.current * currentParams.voltage * 1.732 * 0.85 / 1000).toFixed(2))
        currentParams.frequency = Number((speedRatio * 50 * (1 + (Math.random() - 0.5) * 0.02)).toFixed(2))
        currentParams.slip = Number(addRandomVariation(currentParams.slip, 0.05).toFixed(2))
        break
      }
      case 'pmsm_motor': {
        currentParams.current = Number((speedRatio * DEVICE_DEFAULTS[deviceType].current * (1 + (Math.random() - 0.5) * 0.05)).toFixed(2))
        currentParams.power = Number((currentParams.current * currentParams.voltage * 1.732 * 0.95 / 1000).toFixed(2))
        currentParams.torque = Number((speedRatio * DEVICE_DEFAULTS[deviceType].torque * (1 + (Math.random() - 0.5) * 0.05)).toFixed(2))
        currentParams.efficiency = Number(addRandomVariation(currentParams.efficiency, 0.01).toFixed(2))
        break
      }
      case 'fan': {
        // 更新风机特定参数，使用渐变方式
        const targetAirflow = speedRatio * DEVICE_DEFAULTS[deviceType].airflow
        const targetPressure = Math.pow(speedRatio, 2) * DEVICE_DEFAULTS[deviceType].pressure
        const targetPower = Math.pow(speedRatio, 3) * DEVICE_DEFAULTS[deviceType].power

        // 逐步调整参数
        currentParams.airflow = Number((currentParams.airflow + (targetAirflow - currentParams.airflow) * 0.1).toFixed(2))
        currentParams.pressure = Number((currentParams.pressure + (targetPressure - currentParams.pressure) * 0.1).toFixed(2))
        currentParams.power = Number((currentParams.power + (targetPower - currentParams.power) * 0.1).toFixed(2))

        // 添加随机波动
        currentParams.airflow *= (1 + (Math.random() - 0.5) * 0.02)
        currentParams.pressure *= (1 + (Math.random() - 0.5) * 0.02)
        currentParams.power *= (1 + (Math.random() - 0.5) * 0.02)
        break
      }
      case 'pump': {
        // 更新水泵特定参数，使用渐变方式
        const targetFlow = speedRatio * DEVICE_DEFAULTS[deviceType].flow
        const targetHead = Math.pow(speedRatio, 2) * DEVICE_DEFAULTS[deviceType].head
        const targetPower = Math.pow(speedRatio, 3) * DEVICE_DEFAULTS[deviceType].power

        // 逐步调整参数
        currentParams.flow = Number((currentParams.flow + (targetFlow - currentParams.flow) * 0.1).toFixed(2))
        currentParams.head = Number((currentParams.head + (targetHead - currentParams.head) * 0.1).toFixed(2))
        currentParams.power = Number((currentParams.power + (targetPower - currentParams.power) * 0.1).toFixed(2))

        // 添加随机波动
        currentParams.flow *= (1 + (Math.random() - 0.5) * 0.02)
        currentParams.head *= (1 + (Math.random() - 0.5) * 0.02)
        currentParams.power *= (1 + (Math.random() - 0.5) * 0.02)
        break
      }
      case 'compressor': {
        // 更新压缩机特定参数，使用渐变方式
        const targetPressure = speedRatio * DEVICE_DEFAULTS[deviceType].pressure
        const targetTemperature = speedRatio * DEVICE_DEFAULTS[deviceType].temperature
        const targetPower = Math.pow(speedRatio, 3) * DEVICE_DEFAULTS[deviceType].power

        // 逐步调整参数
        currentParams.pressure = Number((currentParams.pressure + (targetPressure - currentParams.pressure) * 0.1).toFixed(2))
        currentParams.temperature = Number((currentParams.temperature + (targetTemperature - currentParams.temperature) * 0.1).toFixed(2))
        currentParams.power = Number((currentParams.power + (targetPower - currentParams.power) * 0.1).toFixed(2))

        // 添加随机波动
        currentParams.pressure *= (1 + (Math.random() - 0.5) * 0.02)
        currentParams.temperature *= (1 + (Math.random() - 0.5) * 0.02)
        currentParams.power *= (1 + (Math.random() - 0.5) * 0.02)
        break
      }
    }

    deviceParams.value[deviceId] = currentParams
    
    // 更新历史数据
    if (!deviceHistory.value[deviceId]) {
      deviceHistory.value[deviceId] = []
    }
    deviceHistory.value[deviceId].push({
      timestamp,
      values: { ...currentParams }
    })

    // 只保留最近5分钟的数据
    const fiveMinutesAgo = timestamp - 5 * 60 * 1000
    deviceHistory.value[deviceId] = deviceHistory.value[deviceId].filter(
      item => item.timestamp > fiveMinutesAgo
    )
  }

  const updateDeviceRate = (deviceId) => {
    // 确保更新频率有初始值
    if (!deviceUpdateRates.value[deviceId]) {
      deviceUpdateRates.value[deviceId] = 0
    }
    deviceUpdateRates.value[deviceId] = Math.floor(50 + Math.random() * 50)
  }

  const startDevice = (deviceId, deviceType) => {
    if (deviceType === 'virtual_motor') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 检查设备健康状态
      fetch(`${apiUrl}/api/v1/health`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'running') {
          return fetch(`${apiUrl}/api/v1/control/startControl`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              deviceUID: deviceId
            })
          })
        }
        throw new Error('Device health check failed')
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'running'
          deviceParams.value[deviceId] = {
            ...DEVICE_DEFAULTS[deviceType]
          }
          
          updateDeviceRate(deviceId)
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
          }
          
          // 延迟1秒后开始更新设备参数
          setTimeout(() => {
            updateIntervals.value[deviceId] = {
              dataInterval: setInterval(() => {
                updateDeviceData(deviceId, deviceType)
              }, 100),
              rateInterval: setInterval(() => {
                updateDeviceRate(deviceId)
              }, 1000)
            }
          }, 1000)
        }
      })
      .catch(error => {
        console.error('Error starting virtual motor:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }
    if (deviceType === 'dc_motor') {
      // 获取设备配置信息
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      // 使用设备自己的IP和端口构建API URL
      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 先检查设备健康状态
      fetch(`${apiUrl}/api/v1/health`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => {
        if (!response.ok) throw new Error('Network response was not ok')
        return response.json()
      })
      .then(data => {
        if (data.status === 'error') {
          throw new Error('Device health check failed')
        }
        deviceStatus.value[deviceId] = 'running'
        // 初始化参数
        deviceParams.value[deviceId] = {
          speed: 0,          // 从0开始
          voltage: 0,
          power: 0,
          // 根据设备类型设置其他参数为0
          ...(deviceType === 'fan' && { 
            airflow: 0, 
            pressure: 0,
            targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
            targetVoltage: 380  // 添加目标电压
          }),
          ...(deviceType === 'pump' && { 
            flow: 0, 
            head: 0,
            targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
            targetVoltage: 380  // 添加目标电压
          }),
          ...(deviceType === 'compressor' && { 
            pressure: 0, 
            temperature: 25,
            targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
            targetVoltage: 380  // 添加目标电压
          }),
          ...(deviceType === 'async_motor' && {
            speed: 0,
            current: 0,
            voltage: 0,
            power: 0,
            frequency: 0,
            slip: 3.5,
            temperature: 25,
            targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
            targetVoltage: 380  // 添加目标电压
          }),
          ...(deviceType === 'pmsm_motor' && {
            speed: 0,
            current: 0,
            voltage: 0,
            power: 0,
            torque: 0,
            efficiency: 96,
            temperature: 25,
            targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
            targetVoltage: 380  // 添加目标电压
          })
        }
        
        // 启动数据更新和频率更新的定时器
        updateDeviceRate(deviceId)
        if (updateIntervals.value[deviceId]) {
          clearInterval(updateIntervals.value[deviceId].dataInterval)
          clearInterval(updateIntervals.value[deviceId].rateInterval)
        }
        
        updateIntervals.value[deviceId] = {
          dataInterval: setInterval(() => {
            updateDeviceData(deviceId, deviceType)
          }, 100),  // 每100ms更新一次数据
          rateInterval: setInterval(() => {
            updateDeviceRate(deviceId)
          }, 1000)  // 每秒更新一次频率
        }
      })
      .catch(error => {
        console.error('Error starting device:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }
    
    if (deviceType === 'stepper_motor') {
      // 获取设备配置信息
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      // 使用设备自己的IP和端口构建API URL
      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 先检查设备健康状态
      fetch(`${apiUrl}/api/v1/health`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => {
        if (!response.ok) throw new Error('Network response was not ok')
        return response.json()
      })
      .then(data => {
        if (data.status === 'error') {
          throw new Error('Device health check failed')
        }
        
        // 如果健康检查通过，发送启动命令
        return fetch(`${apiUrl}/api/v1/control/startControl`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            deviceUID: deviceId
          })
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'running'
          // 初始化参数
          deviceParams.value[deviceId] = {
            ...DEVICE_DEFAULTS[deviceType]
          }
          
          // 启动数据更新和频率更新的定时器
          updateDeviceRate(deviceId)
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
          }
          
          updateIntervals.value[deviceId] = {
            dataInterval: setInterval(() => {
              updateDeviceData(deviceId, deviceType)
            }, 100),  // 每100ms更新一次数据
            rateInterval: setInterval(() => {
              updateDeviceRate(deviceId)
            }, 1000)  // 每秒更新一次频率
          }
        } else {
          throw new Error('Failed to start device')
        }
      })
      .catch(error => {
        console.error('Error starting device:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }
    
    if (deviceType === 'virtual_pressure') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 检查设备健康状态
      fetch(`${apiUrl}/api/v1/health`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'running') {
          return fetch(`${apiUrl}/api/v1/control/startControl`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              deviceUID: deviceId
            })
          })
        }
        throw new Error('Device health check failed')
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'running'
          deviceParams.value[deviceId] = {
            ...DEVICE_DEFAULTS[deviceType]
          }
          
          updateDeviceRate(deviceId)
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
          }
          
          // 延迟1秒后开始更新设备参数
          setTimeout(() => {
            updateIntervals.value[deviceId] = {
              dataInterval: setInterval(() => {
                updateDeviceData(deviceId, deviceType)
              }, 100),
              rateInterval: setInterval(() => {
                updateDeviceRate(deviceId)
              }, 1000)
            }
          }, 1000)
        }
      })
      .catch(error => {
        console.error('Error starting virtual pressure:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }
    
    if (deviceType === 'virtual_pump') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 检查设备健康状态
      fetch(`${apiUrl}/api/v1/health`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'running') {
          return fetch(`${apiUrl}/api/v1/control/startControl`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              deviceUID: deviceId
            })
          })
        }
        throw new Error('Device health check failed')
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'running'
          deviceParams.value[deviceId] = {
            ...DEVICE_DEFAULTS[deviceType]
          }
          
          updateDeviceRate(deviceId)
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
          }
          
          // 延迟1秒后开始更新设备参数
          setTimeout(() => {
            updateIntervals.value[deviceId] = {
              dataInterval: setInterval(() => {
                updateDeviceData(deviceId, deviceType)
              }, 100),
              rateInterval: setInterval(() => {
                updateDeviceRate(deviceId)
              }, 1000)
            }
          }, 1000)
        }
      })
      .catch(error => {
        console.error('Error starting virtual pump:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }

    if (deviceType === 'virtual_wind') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 检查设备健康状态
      fetch(`${apiUrl}/api/v1/health`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'running') {
          return fetch(`${apiUrl}/api/v1/control/startControl`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              deviceUID: deviceId
            })
          })
        }
        throw new Error('Device health check failed')
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'running'
          deviceParams.value[deviceId] = {
            ...DEVICE_DEFAULTS[deviceType]
          }
          
          updateDeviceRate(deviceId)
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
          }
          
          // 延迟1秒后开始更新设备参数
          setTimeout(() => {
            updateIntervals.value[deviceId] = {
              dataInterval: setInterval(() => {
                updateDeviceData(deviceId, deviceType)
              }, 100),
              rateInterval: setInterval(() => {
                updateDeviceRate(deviceId)
              }, 1000)
            }
          }, 1000)
        }
      })
      .catch(error => {
        console.error('Error starting virtual wind:', error)
        deviceStatus.value[deviceId] = 'error'
      })
      return
    }

    // 其他设备类型的原有逻辑
    deviceStatus.value[deviceId] = 'running'
    // 初始化参数为0
    deviceParams.value[deviceId] = {
      speed: 0,          // 从0开始
      voltage: 0,
      power: 0,
      // 根据设备类型设置其他参数为0
      ...(deviceType === 'fan' && { 
        airflow: 0, 
        pressure: 0,
        targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
        targetVoltage: 380  // 添加目标电压
      }),
      ...(deviceType === 'pump' && { 
        flow: 0, 
        head: 0,
        targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
        targetVoltage: 380  // 添加目标电压
      }),
      ...(deviceType === 'compressor' && { 
        pressure: 0, 
        temperature: 25,
        targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
        targetVoltage: 380  // 添加目标电压
      }),
      ...(deviceType === 'async_motor' && {
        speed: 0,
        current: 0,
        voltage: 0,
        power: 0,
        frequency: 0,
        slip: 3.5,
        temperature: 25,
        targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
        targetVoltage: 380  // 添加目标电压
      }),
      ...(deviceType === 'pmsm_motor' && {
        speed: 0,
        current: 0,
        voltage: 0,
        power: 0,
        torque: 0,
        efficiency: 96,
        temperature: 25,
        targetSpeed: DEVICE_DEFAULTS[deviceType].speed,  // 添加目标转速
        targetVoltage: 380  // 添加目标电压
      })
    }
    
    // 启动数据更新
    if (updateIntervals.value[deviceId]) {
      clearInterval(updateIntervals.value[deviceId].dataInterval)
      clearInterval(updateIntervals.value[deviceId].rateInterval)
    }

    // 设置数据更新和频率更新的定时器
    updateIntervals.value[deviceId] = {
      dataInterval: setInterval(() => {
        updateDeviceData(deviceId, deviceType)
      }, 100),  // 每100ms更新一次数据
      rateInterval: setInterval(() => {
        updateDeviceRate(deviceId)
      }, 1000)  // 每秒更新一次频率
    }
  }

  const stopDevice = (deviceId) => {
    const deviceType = Object.keys(DEVICE_DEFAULTS).find(type => 
      JSON.stringify(Object.keys(DEVICE_DEFAULTS[type]).sort()) === 
      JSON.stringify(Object.keys(deviceParams.value[deviceId] || {}).sort())
    )
    
    if (deviceType === 'virtual_motor') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      fetch(`${apiUrl}/api/v1/control/stopControl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'stopped'
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
            delete updateIntervals.value[deviceId]
          }
          deviceUpdateRates.value[deviceId] = 0
        }
      })
      .catch(error => {
        console.error('Error stopping virtual motor:', error)
      })
      return
    }
    if (deviceType === 'dc_motor') {
      // 获取设备配置信息
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      // 使用设备自己的IP和端口构建API URL
      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      // 通过 API 停止设备
      fetch(`${apiUrl}/api/v1/control/stopControl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'stopped'
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
            delete updateIntervals.value[deviceId]
          }
          deviceUpdateRates.value[deviceId] = 0
        }
      })
      .catch(error => {
        console.error('Error stopping device:', error)
      })
      return
    }
    
    if (deviceType === 'virtual_pressure') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      fetch(`${apiUrl}/api/v1/control/stopControl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'stopped'
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
            delete updateIntervals.value[deviceId]
          }
          deviceUpdateRates.value[deviceId] = 0
        }
      })
      .catch(error => {
        console.error('Error stopping virtual pressure:', error)
      })
      return
    }
    
    if (deviceType === 'virtual_pump') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      fetch(`${apiUrl}/api/v1/control/stopControl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'stopped'
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
            delete updateIntervals.value[deviceId]
          }
          deviceUpdateRates.value[deviceId] = 0
        }
      })
      .catch(error => {
        console.error('Error stopping virtual pump:', error)
      })
      return
    }
    
    if (deviceType === 'virtual_wind') {
      const device = deviceStore.devices.find(d => d.id === deviceId)
      if (!device) return

      const apiUrl = `http://${device.config.ip}:${device.config.port}`

      fetch(`${apiUrl}/api/v1/control/stopControl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          deviceUID: deviceId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.result === 'success') {
          deviceStatus.value[deviceId] = 'stopped'
          if (updateIntervals.value[deviceId]) {
            clearInterval(updateIntervals.value[deviceId].dataInterval)
            clearInterval(updateIntervals.value[deviceId].rateInterval)
            delete updateIntervals.value[deviceId]
          }
          deviceUpdateRates.value[deviceId] = 0
        }
      })
      .catch(error => {
        console.error('Error stopping virtual wind:', error)
      })
      return
    }
    
    // 原有的停止逻辑
    deviceStatus.value[deviceId] = 'stopped'
    if (updateIntervals.value[deviceId]) {
      clearInterval(updateIntervals.value[deviceId].dataInterval)
      clearInterval(updateIntervals.value[deviceId].rateInterval)
      delete updateIntervals.value[deviceId]
    }
    // 确保停止时设置为0
    deviceUpdateRates.value[deviceId] = 0
  }

  const updateDeviceParam = (deviceId, paramKey, value) => {
    if (!deviceParams.value[deviceId]) {
      deviceParams.value[deviceId] = {}
    }

    // 更新目标参数
    if (paramKey === 'targetSpeed') {
      deviceParams.value[deviceId].targetSpeed = value
      
      // 获取当前设备类型
      const deviceType = Object.keys(DEVICE_DEFAULTS).find(type => 
        JSON.stringify(Object.keys(DEVICE_DEFAULTS[type]).sort()) === 
        JSON.stringify(Object.keys(deviceParams.value[deviceId]).sort())
      )
      
      if (deviceType) {
        switch (deviceType) {
          case 'fan':
          case 'pump':
          case 'compressor': {
            // 立即更新转速
            deviceParams.value[deviceId].speed = value
            break
          }
          // ... 其他设备类型的处理 ...
        }
      }
    } else {
      deviceParams.value[deviceId][paramKey] = value
    }
  }

  return {
    getDeviceStatus,
    getDeviceParams,
    getDeviceHistory,
    startDevice,
    stopDevice,
    updateDeviceParam,
    getDeviceUpdateRate
  }
}) 
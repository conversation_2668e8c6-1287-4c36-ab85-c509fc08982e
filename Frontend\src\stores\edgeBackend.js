import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useEdgeBackendStore = defineStore('edgeBackend', () => {
  // 初始化默认连接列表
  const defaultConnections = [
    {
      id: 2,
      name: 'piworknode003',
      host: '*************',
      port: 9654,
      username: 'piworknode003',
      password: '123456',
      connected: false
    },
    {
      id: 3,
      name: 'master',
      host: '**************',
      port: 9654,
      username: 'master',
      password: '123456',
      connected: false
    },
    {
      id: 4,
      name: 'piworknode002',
      host: '**************',
      port: 9654,
      username: 'piworknode002',
      password: '123456',
      connected: false
    },
    {
      id: 5,
      name: 'piworknode001',
      host: '**************',
      port: 9654,
      username: 'piworknode001',
      password: '123456',
      connected: false
    },
    {
      id: 6,
      name: 'asus@free',
      host: '**************',
      port: 9654,
      username: 'asus',
      password: 'iwin-1-321',
      connected: false
    },
    {
      id: 7,
      name: 'asus@worknode002',
      host: '*************',
      port: 9654,
      username: 'asus',
      password: 'iwin-1-321',
      connected: false
    },
    {
      id: 8,
      name: 'asus@worknode001',
      host: '*************5',
      port: 9654,
      username: 'asus',
      password: 'iwin-1-321',
      connected: false
    },
    {
      id: 9,
      name: 'asus@cridocker',
      host: '************',
      port: 9654,
      username: 'asus',
      password: 'iwin-1-321',
      connected: false
    }
  ]

  const connections = ref(defaultConnections)
  const currentConnection = ref(null)

  // 创建新连接
  const createConnection = async (connectionData) => {
    const newConnection = {
      id: Date.now(),
      ...connectionData,
      connected: false
    }
    connections.value.push(newConnection)
    return newConnection
  }

  // 连接到服务器
  const connect = async (connection) => {
    try {
      // wssh 会在iframe中处理实际的连接
      connection.connected = true
      currentConnection.value = connection
    } catch (error) {
      console.error('连接失败:', error)
      throw error
    }
  }

  // 断开连接
  const disconnect = async (connection) => {
    connection.connected = false
    if (currentConnection.value?.id === connection.id) {
      currentConnection.value = null
    }
  }

  // 删除连接
  const deleteConnection = async (connection) => {
    const index = connections.value.findIndex(c => c.id === connection.id)
    if (index !== -1) {
      connections.value.splice(index, 1)
      if (currentConnection.value?.id === connection.id) {
        currentConnection.value = null
      }
    }
  }

  // 添加更新连接方法
  const updateConnection = async (connection) => {
    const index = connections.value.findIndex(c => c.id === connection.id)
    if (index !== -1) {
      connections.value[index] = { ...connection }
    }
  }

  return {
    connections,
    currentConnection,
    createConnection,
    updateConnection,
    connect,
    disconnect,
    deleteConnection
  }
}) 
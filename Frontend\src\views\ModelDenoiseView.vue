<template>
  <div class="denoise-container">
    <el-card class="denoise-card">
      <template #header>
        <div class="card-header">
          <h3>数据去噪</h3>
          <div class="header-actions">
            <el-button 
              type="primary" 
              @click="handleExport" 
              :disabled="!store.processedData.length"
            >
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- 数据源选择 -->
      <DataSourceSelect />

      <!-- 去噪配置 -->
      <DenoiseConfig />

      <!-- 数据预览 -->
      <DataPreview />
    </el-card>
  </div>
</template>

<script setup>
import { useDataDenoiseStore } from '@/stores/datadenoise'
import DataSourceSelect from '@/components/datadenoise/DataSourceSelect.vue'
import DenoiseConfig from '@/components/datadenoise/DenoiseConfig.vue'
import DataPreview from '@/components/datadenoise/DataPreview.vue'
import Papa from 'papaparse'

const store = useDataDenoiseStore()

// 导出数据
const handleExport = () => {
  if (!store.processedData.length) return
  
  const csv = Papa.unparse(store.processedData)
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  
  link.setAttribute('href', url)
  link.setAttribute('download', 'denoised_data.csv')
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>

<style scoped>
.denoise-container {
  padding: 20px;
}

.denoise-card {
  min-height: calc(100vh - 120px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 
import { fileURLToPath, URL } from 'node:url'
import path from 'path'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    cors: true,
    proxy: {
      '/devicemanager': {
        // 韩梦琦数据库后端
        target: 'http://*************:8888',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/devicemanager/, '')
      }
    }
  }
})
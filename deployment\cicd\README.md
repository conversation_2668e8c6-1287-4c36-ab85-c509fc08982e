# CICD 部署服务说明

本文档描述了在服务器 `192.168.3.151` 上通过 Kubernetes 部署的所有服务组件。

## 服务架构概览

### 核心业务服务

#### 1. Devicemanager (设备管理服务)

* **部署文件**: `devicemanager.yaml`
* **配置文件**: `devicemanager-configmap.yaml`
* **镜像**: `192.168.3.64:5888/generative-control-foundation-dev/devicemanager:latest`
* **API端口**: 8888 (内部), 31888 (NodePort)
* **功能**: 设备管理和控制服务
* **数据库连接**: PostgreSQL (192.168.3.151:31121/devicemanager)
* **RPC端口**: 8080

### 基础设施服务

#### 1. PostgreSQL 数据库

* **部署文件**: `components/postgresql/postgresql.yaml`
* **镜像**: `postgres:15`
* **端口**: 5432 (内部), 31121 (NodePort)
* **数据库**: `devicemanager`
* **用户**: `postgres`
* **密码**: `pass`
* **存储**: 5Gi PVC (local storage)
* **命名空间**: `postgresql`

#### 2. Kafka

* **部署文件**: `components/kafka/kafka.yaml`
* **镜像**: `bitnami/kafka:3.6.0`
* **端口**: 9092 (PLAINTEXT), 9093 (CONTROLLER)
* **外部访问**: `192.168.3.151:31111`
* **存储**: 5Gi PVC (local storage)
* **命名空间**: `kafka`
* **模式**: KRaft

#### 3. Kafka UI

* **部署文件**: `components/kafka/kafka-ui.yaml`
* **镜像**: `provectuslabs/kafka-ui:latest`
* **端口**: 8080
* **外部访问**:`192.168.3.151:31110`
* **命名空间**: `kafka`
* **功能**: Kafka 管理界面

#### 4. GreptimeDB 时序数据库

* **配置文件**: `components/greptimedb/value.yaml`
* **端口**: 4000 (内部), 31055 (NodePort，重新部署时该端口会更改)
* **存储**: 20Gi PVC (local storage)
* **数据挂载路径**: `/data/greptimedb`

### 数据库迁移

#### Devicemanager-Flyway 数据库迁移

* **部署文件**: `flyway/devicemanager-flyway.yaml`
* **镜像**: `flyway/flyway:10.0`
* **类型**: Kubernetes Job
* **功能**: 自动执行数据库 schema 迁移
* **连接rul**: `postgresql-svc:5432/devicemanager`

## 持久化存储

**所有服务使用 **`local` StorageClass 进行数据持久化：

* **PostgreSQL**: 5Gi
* **Kafka**: 5Gi
* **GreptimeDB**: 20Gi

**相关 PV 配置文件：**

* `components/postgresql/postgresql-pv.yaml`
* `components/kafka/kafka-pv.yaml`
* `components/greptimedb/local-pv.yaml`

## 网络访问

### 外部访问端点

* **Device Manager API**: `http://192.168.3.151:31888`
* **PostgreSQL**: `192.168.3.151:31121`
* **Kafka**: `192.168.3.151:31111`
* **Kafka-ui**: `192.168.3.151:31110`
* **GreptimeDB**: `http://192.168.3.151:31055`

### 内部服务通信

* **Device Manager RPC:**`device.rpc:8080`
* **Device Manager API:**`device-api:8888`
* **PostgreSQL:**`postgresql-svc:5432`
* **Kafka:**`kafka-svc:9092`

apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-ui
  namespace: kafka
  labels:
    app: kafka-ui
spec:
  selector:
    matchLabels:
      app: kafka-ui
  template:
    metadata:
      labels:
        app: kafka-ui
    spec:
      containers:
      - name: kafka-ui
        image: provectuslabs/kafka-ui:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
        env:
        - name: KAFKA_CLUSTERS_0_NAME
          value: local
        - name: KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS
          value: kafka-svc:9092
        - name: DYNAMIC_CONFIG_ENABLED
          value: "true"
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-ui-svc
  namespace: kafka
spec:
  selector:
    app: kafka-ui
  ports:
  - port: 8080
    targetPort: 8080
    nodePort: 31110
  type: NodePort

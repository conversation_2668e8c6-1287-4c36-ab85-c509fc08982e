apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: kafka-pvc
  namespace: kafka
spec:
  storageClassName: local
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-deployment
  namespace: kafka
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      containers:
      - name: kafka
        image: bitnami/kafka:3.6.0
        env:
        - name: KAFKA_KRAFT_CLUSTER_ID
          value: Kmp-xkTnSf-WWXhWmiorDg
        - name: KAFKA_ENABLE_KRAFT
          value: "yes"
        - name: KAFKA_CFG_NODE_ID
          value: "1"
        - name: K<PERSON><PERSON>_CFG_PROCESS_ROLES
          value: broker,controller
        - name: <PERSON><PERSON><PERSON>_CFG_CONTROLLER_QUORUM_VOTERS
          value: 1@127.0.0.1:9093
        - name: <PERSON>AF<PERSON>_CFG_ADVERTISED_LISTENERS
          value: PLAINTEXT://192.168.3.151:31111
        - name: <PERSON><PERSON><PERSON>_CFG_CONTROLLER_LISTENER_NAMES
          value: CONTROLLER
        - name: KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP
          value: PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT
        - name: KAFKA_CFG_LISTENERS
          value: PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093
        - name: ALLOW_PLAINTEXT_LISTENER
          value: "yes"
        - name: KAFKA_BROKER_ID
          value: "1"
        ports:
        - containerPort: 9092
        volumeMounts:
        - name: kafka-storage
          mountPath: /opt/kafka/data
      volumes:
      - name: kafka-storage
        persistentVolumeClaim:
          claimName: kafka-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-svc
  namespace: kafka
  labels:
    app: kafka
spec:
  selector:
    app: kafka
  type: NodePort
  ports:
    - port: 9092
      targetPort: 9092
      nodePort: 31111
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-pvc
  namespace: postgresql
spec:
  storageClassName: local
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql-deployment
  namespace: postgresql
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql-master
  template:
    metadata:
      labels:
        app: postgresql-master
    spec:
      containers:
        - image: postgres:15
          name: migration-postgres
          env:
            - name: POSTGRES_PASSWORD
              value: pass
            - name: POSTGRES_DB
              value: devicemanager
            - name: POSTGRES_USER
              value: postgres
          ports:
            - containerPort: 5432
          volumeMounts:
            - name: postgresql-volume
              mountPath: /var/lib/postgresql/data
      volumes:
        - name: postgresql-volume
          persistentVolumeClaim:
            claimName: postgresql-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-svc
  namespace: postgresql
  labels:
    app: postgresql-master
spec:
  selector:
    app: postgresql-master
  type: NodePort
  ports:
    - port: 5432
      targetPort: 5432
      nodePort: 31121
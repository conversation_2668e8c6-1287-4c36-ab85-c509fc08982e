apiVersion: apps/v1
kind: Deployment
metadata:
  name: datamanager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: datamanager
  template:
    metadata:
      labels:
        app: datamanager
    spec:
      containers:
      - name: datamanager
        image: 192.168.3.64:5888/generative-control-foundation-dev/datamanager:latest
        ports:
        - containerPort: 3333
---
apiVersion: v1
kind: Service
metadata:
  name: datamanager
spec:
  selector:
    app: datamanager
  ports:
  - port: 3333
    targetPort: 3333
    nodePort: 31333
  type: NodePort
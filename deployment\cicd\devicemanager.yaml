apiVersion: apps/v1
kind: Deployment
metadata:
  name: devicemanager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: devicemanager
  template:
    metadata:
      labels:
        app: devicemanager
    spec:
      containers:
      - name: devicemanager
        image: 192.168.3.64:5888/generative-control-foundation-dev/devicemanager:latest
        ports:
        - containerPort: 8888
        volumeMounts:
        - name: config-volume
          mountPath: /app/etc
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: devicemanager-config
---
apiVersion: v1
kind: Service
metadata:
  name: devicemanager
spec:
  selector:
    app: devicemanager
  ports:
  - port: 8888
    targetPort: 8888
    nodePort: 31888
  type: NodePort
apiVersion: batch/v1
kind: Job
metadata:
  name: flyway-job
spec:
  template:
    metadata:
      name: flyway-job
    spec:
      containers:
        - name: db-updater
          image: flyway/flyway:10.0
          volumeMounts:
            - name: sql-volume-configmap
              mountPath: /flyway/sql
          command: ["flyway"]
          args: [
            "migrate",
            "-url=***************************************************",
            "-user=postgres",
            "-password=pass"
          ]
      restartPolicy: Never
      volumes:
      - name: sql-volume-configmap
        configMap: 
          name: sql-configmap
  backoffLimit: 1
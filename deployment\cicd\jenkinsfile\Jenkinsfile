pipeline {
  agent any
  parameters {
      gitParameter name: 'branch',
      type: 'PT_BRANCH',
      branchFilter: 'origin/(.*)',
      defaultValue: 'master',
      selectedValue: 'DEFAULT',
      sortMode: 'ASCENDING_SMART',
      description: '选择需要构建的分支'
  }
  environment {
      lowertype = "${params.type?.toLowerCase()}"
  }
  stages {
    stage('服务信息')    {
            steps {
                sh 'echo 分支：$branch'
                sh 'echo 构建服务类型：${JOB_NAME}-$type'
            }
        }
    stage('拉取代码') {
          steps {
              checkout([$class: 'GitSCM',
              branches: [[name: '$branch']],
              doGenerateSubmoduleConfigurations: false,
              extensions: [],
              submoduleCfg: [],
              userRemoteConfigs: [[credentialsId: 'coding-devops-cert', url: '****************:g-ojys9290/generative-control-foundation/Generative-Control-Foundation.git']]])
          }
      }
      stage('Dockerfile Build') {
          steps{
                 git credentialsId: 'coding-devops-cert', url: '****************:g-ojys9290/generative-control-foundation/Generative-Control-Foundation.git'
                 script{
                     env.imageLatest = sh(returnStdout: true, script: 'echo ${lowertype}:latest').trim()
                     env.version = sh(returnStdout: true, script: 'git describe --abbrev=8 --always --dirty').trim()
                     env.imageVersion = sh(returnStdout: true, script: 'echo ${lowertype}:${version}').trim()
                 }
                 dir('Backend') {
                   sh 'echo 镜像名称：${imageLatest} && docker build -f app/${type}/Dockerfile -t ${imageLatest} .'
                 }
          }
      }
      stage('上传到镜像仓库') {
          steps{
             //docker login 这里要注意，会把账号密码输出到jenkins页面，可以通过port.sh类似方式处理，官网文档有这里我就不详细写了
              sh 'docker login --username=${docker_username} --password=${docker_pwd} http://${docker_repo}'
              sh 'docker tag  ${imageLatest} ${docker_repo}/generative-control-foundation-dev/${imageLatest}'
              sh 'docker tag  ${imageLatest} ${docker_repo}/generative-control-foundation-dev/${imageVersion}'
              sh 'docker push ${docker_repo}/generative-control-foundation-dev/${imageLatest}'
              sh 'docker push ${docker_repo}/generative-control-foundation-dev/${imageVersion}'
          }
      }
      stage('数据库更新') {
          steps{
              sh 'kubectl delete cm sql-configmap -n postgresql || true '
              sh 'kubectl delete -f deployment/cicd/flyway/${lowertype}-flyway.yaml -n postgresql || true'
              sh 'kubectl create cm sql-configmap --from-file=Backend/app/${type}/db/migrations/postgres -n postgresql'
              sh 'kubectl apply -f deployment/cicd/flyway/${lowertype}-flyway.yaml -n postgresql'
          }
      }
      stage('部署服务') {
          steps{
              sh 'kubectl delete cm ${lowertype}-config || true'
              sh 'kubectl apply -f deployment/cicd/${lowertype}-configmap.yaml'
              sh 'kubectl apply -f deployment/cicd/${lowertype}.yaml'
          }
      }
      stage('Clean') {
           steps{
               sh 'docker rmi -f ${docker_repo}/generative-control-foundation-dev/${imageVersion}'
               sh 'docker rmi -f ${imageLatest}'
               sh 'docker rmi -f ${docker_repo}/generative-control-foundation-dev/${imageLatest}'
               cleanWs notFailBuild: true
           }
       }
  }
}
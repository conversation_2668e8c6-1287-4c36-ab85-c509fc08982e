# Jenkins CI/CD 流水线说明文档

本文档详细说明了 Generative Control Foundation 项目的 Jenkins CI/CD 流水线配置和执行流程。

## 流水线概览

这是一个基于 Jenkins Pipeline 的自动化构建和部署流水线，支持多分支构建和多服务类型部署。流水线采用声明式语法，包含 6 个主要阶段，实现了从代码拉取到服务部署的完整自动化流程。

## 流水线配置

### 基础配置

```groovy
pipeline {
  agent any
  parameters {
      gitParameter name: 'branch',
      type: 'PT_BRANCH',
      branchFilter: 'origin/(.*)',
      defaultValue: 'master',
      selectedValue: 'DEFAULT',
      sortMode: 'ASCENDING_SMART',
      description: '选择需要构建的分支'
  }
  environment {
      lowertype = "${params.type?.toLowerCase()}"
  }
}
```

**配置说明：**

- **Agent**: `any` - 可在任何可用的 Jenkins 节点上执行
- **参数化构建**: 支持动态选择 Git 分支
- **环境变量**: 自动将服务类型转换为小写格式

### 构建参数

| 参数名     | 类型         | 默认值     | 描述                                                 |
| ---------- | ------------ | ---------- | ---------------------------------------------------- |
| `branch` | Git 分支参数 | `master` | 选择要构建的代码分支                                 |
| `type`   | 服务类型参数 | -          | 指定要构建的服务类型 (如 devicemanager, datamanager) |

## 流水线阶段详解

### 阶段 1: 服务信息 (Information Display)

```groovy
stage('服务信息') {
    steps {
        sh 'echo 分支：$branch'
        sh 'echo 构建服务类型：${JOB_NAME}-$type'
    }
}
```

**功能**: 显示当前构建的基本信息

- 输出选择的 Git 分支
- 显示构建的服务类型和任务名称
- 便于构建过程中的信息追踪和调试

### 阶段 2: 拉取代码 (Code Checkout)

```groovy
stage('拉取代码') {
    steps {
        checkout([$class: 'GitSCM',
        branches: [[name: '$branch']],
        doGenerateSubmoduleConfigurations: false,
        extensions: [],
        submoduleCfg: [],
        userRemoteConfigs: [[credentialsId: 'coding-devops-cert', 
                           url: '****************:g-ojys9290/generative-control-foundation/Generative-Control-Foundation.git']]])
    }
}
```

**功能**: 从 Git 仓库拉取指定分支的源代码

- **仓库地址**: `****************:g-ojys9290/generative-control-foundation/Generative-Control-Foundation.git`
- **认证方式**: 使用 SSH 密钥 (`coding-devops-cert`)
- **分支选择**: 动态选择用户指定的分支

### 阶段 3: Docker 镜像构建 (Dockerfile Build)

```groovy
stage('Dockerfile Build') {
    steps{
        git credentialsId: 'coding-devops-cert', url: '****************:...'
        script{
            env.imageLatest = sh(returnStdout: true, script: 'echo ${lowertype}:latest').trim()
            env.version = sh(returnStdout: true, script: 'git describe --abbrev=8 --always --dirty').trim()
            env.imageVersion = sh(returnStdout: true, script: 'echo ${lowertype}:${version}').trim()
        }
        dir('Backend') {
            sh 'echo 镜像名称：${imageLatest} && docker build -f app/${type}/Dockerfile -t ${imageLatest} .'
        }
    }
}
```

**功能**: 构建 Docker 镜像

**流程逻辑**:

1. **生成镜像标签**:
   - `imageLatest`: `{服务类型}:latest`
   - `version`: 基于 Git commit 的版本号 (8位短哈希)
   - `imageVersion`: `{服务类型}:{版本号}`
2. **构建镜像**: 在 `Backend` 目录下执行 Docker 构建
   - Dockerfile 路径: `app/{服务类型}/Dockerfile`
   - 构建上下文: `Backend` 目录

**示例**:

- 服务类型: `devicemanager`
- 生成的镜像: `devicemanager:latest` 和 `devicemanager:a1b2c3d4`

### 阶段 4: 上传到镜像仓库 (Push to Registry)

```groovy
stage('上传到镜像仓库') {
    steps{
        sh 'docker login --username=${docker_username} --password=${docker_pwd} http://${docker_repo}'
        sh 'docker tag ${imageLatest} ${docker_repo}/generative-control-foundation-dev/${imageLatest}'
        sh 'docker tag ${imageLatest} ${docker_repo}/generative-control-foundation-dev/${imageVersion}'
        sh 'docker push ${docker_repo}/generative-control-foundation-dev/${imageLatest}'
        sh 'docker push ${docker_repo}/generative-control-foundation-dev/${imageVersion}'
    }
}
```

**功能**: 将构建的镜像推送到harbor镜像仓库

**流程逻辑**:

1. **登录镜像仓库**: 使用环境变量中的用户名和密码
2. **镜像标记**: 为镜像添加仓库前缀
   - `{仓库地址}/generative-control-foundation-dev/{服务}:latest`
   - `{仓库地址}/generative-control-foundation-dev/{服务}:{版本}`
3. **推送镜像**: 同时推送 latest 和版本标签的镜像

**环境变量依赖**:

- `docker_username`: Docker 仓库用户名
- `docker_pwd`: Docker 仓库密码
- `docker_repo`: Docker 仓库地址

### 阶段 5: 数据库更新 (Database Migration)

```groovy
stage('数据库更新') {
    steps{
        sh 'kubectl delete cm sql-configmap -n postgresql || true '
        sh 'kubectl delete -f deployment/cicd/flyway/${lowertype}-flyway.yaml -n postgresql || true'
        sh 'kubectl create cm sql-configmap --from-file=Backend/app/${type}/db/migrations/postgres -n postgresql'
        sh 'kubectl apply -f deployment/cicd/flyway/${lowertype}-flyway.yaml -n postgresql'
    }
}
```

**功能**: 执行数据库 schema 迁移

**流程逻辑**:

1. **清理旧资源**: 删除现有的 SQL ConfigMap 和 Flyway Job
   - 使用 `|| true` 确保即使资源不存在也不会失败
2. **创建 SQL ConfigMap**: 从迁移文件目录创建 ConfigMap
   - 源目录: `Backend/app/{服务类型}/db/migrations/postgres`
   - 目标: `sql-configmap` ConfigMap
3. **执行迁移**: 应用 Flyway Job 配置
   - 配置文件: `deployment/cicd/flyway/{服务类型}-flyway.yaml`
   - 命名空间: `postgresql`

### 阶段 6: 部署服务 (Service Deployment)

```groovy
stage('部署服务') {
    steps{
        sh 'kubectl delete cm ${lowertype}-config || true'
        sh 'kubectl apply -f deployment/cicd/${lowertype}-configmap.yaml'
        sh 'kubectl apply -f deployment/cicd/${lowertype}.yaml'
    }
}
```

**功能**: 部署或更新 Kubernetes 服务

**流程逻辑**:

1. **清理旧配置**: 删除现有的服务 ConfigMap
2. **应用新配置**: 部署服务的 ConfigMap
   - 配置文件: `deployment/cicd/{服务类型}-configmap.yaml`
3. **部署服务**: 应用服务的 Deployment 和 Service
   - 配置文件: `deployment/cicd/{服务类型}.yaml`

### 阶段 7: 清理 (Cleanup)

```groovy
stage('Clean') {
    steps{
        sh 'docker rmi -f ${docker_repo}/generative-control-foundation-dev/${imageVersion}'
        sh 'docker rmi -f ${imageLatest}'
        sh 'docker rmi -f ${docker_repo}/generative-control-foundation-dev/${imageLatest}'
        cleanWs notFailBuild: true
    }
}
```

**功能**: 清理构建过程中产生的临时文件和镜像

**清理内容**:

1. **删除本地镜像**: 清理构建节点上的 Docker 镜像
   - 版本标签镜像
   - latest 标签镜像
   - 带仓库前缀的镜像
2. **清理工作空间**: 删除构建过程中的临时文件
   - `notFailBuild: true`: 清理失败不影响构建状态

## 使用方法

### 1. 触发构建

1. 在 Jenkins 中选择对应的 Pipeline 任务
2. 点击 "Build with Parameters"
3. 选择要构建的分支
4. 指定服务类型参数
5. 点击 "Build" 开始构建

### 2. 监控构建过程

- 查看 Console Output 了解详细执行日志
- 监控各个阶段的执行状态
- 检查 Docker 镜像是否成功推送
- 验证 Kubernetes 服务是否正常部署

version: '3'

services:
  postgres:
    image: g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/postgres:acceptance
    container_name: migration-postgres
    environment:
      - POSTGRES_PASSWORD=pass
      - POSTGRES_DB=migration
    ports:
      - "15432:5432"
  
  flyway:
    image: g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/flyway:10.0
    container_name: flyway
    volumes:
      - ./db/migrations/postgres:/flyway/sql
    command: 
      - -url=***************************************************
      - -user=postgres 
      - -password=pass
      - migrate
    depends_on:
      - postgres

  devicemanager:
    image: g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/devicemanager:latest
    restart: always
    container_name: devicemanager
    ports:
      - "8888:8888"
    depends_on:
      - postgres
      - flyway

  labelmanager:
    image: g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/label-studio:v1
    container_name: labelmanager
    ports:
      - "8081:8080"

  datamanager:
    image: g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/openrefine:v1
    container_name: datamanager
    ports:
      - "3333:3333"

  # modelmanager:
  #   image: g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/llamafactory:latest
  #   container_name: modelmanager
  #   ports:
  #     - "7860:7860"
  #   command: llamafactory-cli webui
  frontend:
    image: g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/frontapp:v4
    container_name: frontend
    ports:
      - "5173:5173"
    depends_on:
      - devicemanager
      - labelmanager
      - datamanager


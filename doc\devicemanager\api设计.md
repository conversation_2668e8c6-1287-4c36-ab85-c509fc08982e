# 设备管理器 API 更新历史

本文记录了设备管理器 API 的变更情况。

## 版本：v1.1

发版日期：2025-05-03

### **新增**

| **功能模块**           | **说明**                                            | **相关文档**                                                 |
| ---------------------- | --------------------------------------------------- | ------------------------------------------------------------ |
| ControlDeviceHandler   | 新增了设备控制api，可以向指定id的设备发送控制指令   | [设备管理器设计文档](https://dcnfzs1rg5jp.feishu.cn/wiki/Pibvw2oe3iYkjEksHsdc3GmfnPg) |
| FetchDeviceDataHandler | 新增读取设备数据api，可以获取设备高频数采获得的数据 |                                                              |

### **更新**

| **功能模块** | **说明** | **相关文档** |
| ------------ | -------- | ------------ |
|              |          |              |
|              |          |              |
|              |          |              |

### **修复**

| **功能模块** | **说明** | **相关文档** |
| ------------ | -------- | ------------ |
|              |          |              |
|              |          |              |
|              |          |              |

### **删除**

| **功能模块** | **说明** | **相关文档** |
| ------------ | -------- | ------------ |
|              |          |              |
|              |          |              |
|              |          |              |

### **已知问题**

> 说明该版本中存在的已知问题。若有多个，以无序列表的形式呈现。
>
> - FetchDeviceData主要用于读取设备数据的测试，后续该功能会加入到数据管理器

## 版本：v1

发版日期：2025-03-15

### **新增**

[device.json](https://dcnfzs1rg5jp.feishu.cn/wiki/PWbLw3KGri0fYtkTc1wcfH7gnQf)

<table>
<tr>
<td>**功能模块**<br/></td><td>**说明**<br/></td><td>**相关文档**<br/></td></tr>
<tr>
<td>CreateDeviceResourceHandler<br/></td><td>新增了设备创建api，可以新建设备和资源<br/><br/></td><td rowspan="8">[设备管理器设计文档](https://dcnfzs1rg5jp.feishu.cn/wiki/Pibvw2oe3iYkjEksHsdc3GmfnPg)<br/><br/></td></tr>
<tr>
<td>DeleteDeviceResourceHandler<br/></td><td>新增了设备删除api，可以删除指定id的设备<br/></td></tr>
<tr>
<td>CreateDeviceFrameHandler<br/></td><td>新增了协议帧创建api，可以新建指定设备的通信帧或配置指定帧id的通信帧<br/></td></tr>
<tr>
<td>DeleteDeviceFrameHandler<br/></td><td>新增了帧删除api，可以删除指定id设备的指定帧（id/type）<br/></td></tr>
<tr>
<td>ListDeviceHandler<br/></td><td>列出满足筛选条件的全部设备信息<br/></td></tr>
<tr>
<td>GetFramesByDeviceID<br/></td><td>根据设备ID获取帧信息，返回帧元数据和帧库信息(支持Modbus和Uart协议)<br/></td></tr>
<tr>
<td>GetDeviceByID<br/></td><td>获取设备详细信息，包括设备资源信息(CPU/GPU/磁盘/内存)和工作状态<br/></td></tr>
<tr>
<td>CheckDeviceHealth<br/></td><td>检查设备健康状态，返回设备当前工作状态信息<br/></td></tr>
</table>

### **更新**

<table>
<tr>
<td>**功能模块**<br/></td><td>**说明**<br/></td><td>**相关文档**<br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
</table>

### **修复**

<table>
<tr>
<td>**功能模块**<br/></td><td>**说明**<br/></td><td>**相关文档**<br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
</table>

### **删除**

<table>
<tr>
<td>**功能模块**<br/></td><td>**说明**<br/></td><td>**相关文档**<br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td></tr>
</table>

### **已知问题**

> 说明该版本中存在的已知问题。若有多个，以无序列表的形式呈现。
>
> - 暂无对帧的数据部分的配置和呈现，下个版本将加入

---

# 设备管理器 API 列表

本文详细列举 设备管理器 提供的所有 API。

## < 设备相关 >

<table>
<tr>
<td>**API**<br/></td><td>**说明**<br/></td></tr>
<tr>
<td>CreateDeviceResourceHandler<br/></td><td>新增了设备创建api，可以新建设备和资源<br/><br/></td></tr>
<tr>
<td>DeleteDeviceResourceHandler<br/></td><td>新增了设备删除api，可以删除指定id的设备<br/><br/></td></tr>
<tr>
<td>GetDeviceByID<br/></td><td>获取设备详细信息，包括设备资源信息(CPU/GPU/磁盘/内存)和工作状态<br/></td></tr>
<tr>
<td>CheckDeviceHealth<br/></td><td>检查设备健康状态，返回设备当前工作状态信息<br/></td></tr>
</table>

## < 帧相关 >

<table>
<tr>
<td>**API**<br/></td><td>**说明**<br/></td></tr>
<tr>
<td>CreateDeviceFrameHandler<br/></td><td>新增了协议帧创建api，可以新建指定设备的通信帧或配置指定帧id的通信帧<br/></td></tr>
<tr>
<td>DeleteDeviceFrameHandler<br/></td><td>新增了帧删除api，可以删除指定id设备的指定帧（id/type）<br/></td></tr>
<tr>
<td>GetFramesByDeviceID<br/></td><td>根据设备ID获取帧信息，返回帧元数据和帧库信息(支持Modbus和Uart协议)<br/></td></tr>
</table>

## < 数据相关 >

<table>
<tr>
<td>**API**<br/></td><td>**说明**<br/></td></tr>
<tr>
<td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td></tr>
</table>

# 设备管理器 API 详细说明

## **POST Create a new device resource**

POST /api/v1/device/resource/create

> Body 请求参数

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>body<br/></td><td>body<br/><br/></td><td>CreateDeviceResourceRequest<br/></td><td>否<br/></td><td>none<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "deviceMeta": {
    "deviceUID": "string",
    "frameMetas": [
      {
        "frameUID": "string",
        "frameType": "modbus"
      }
    ],
    "deviceDesc": "string"
  },
  "deviceWorkStatus": {
    "healthStatus": "init",
    "timestamp": 0,
    "workMode": "distributed"
  }
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/><br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/><br/></td><td>Successful response<br/></td><td>[CreateDeviceResourceResponse](#schemacreatedeviceresourceresponse)<br/></td></tr>
</table>

## **POST Delete a device resource**

POST /api/v1/device/resource/delete

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>body<br/></td><td>body<br/></td><td>[DeleteDeviceResourceRequest](#schemadeletedeviceresourcerequest)<br/></td><td>否<br/></td><td>none<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "deviceMeta": {
    "deviceUID": "string",
    "frameMetas": [
      {
        "frameUID": "string",
        "frameType": "modbus"
      }
    ],
    "deviceDesc": "string"
  },
  "deviceWorkStatus": {
    "healthStatus": "init",
    "timestamp": 0,
    "workMode": "distributed"
  },
  "deviceResourceInfo": {
    "ip": "string",
    "hostname": "string",
    "type": "fan",
    "os": "string",
    "cpu": {
      "amount": 0,
      "type": "string",
      "unit": "string"
    },
    "gpu": {
      "amount": 0,
      "type": "string",
      "unit": "string"
    },
    "disk": {
      "amount": 0,
      "type": "string",
      "unit": "string"
    },
    "mem": {
      "amount": 0,
      "type": "string",
      "unit": "string"
    },
    "protocol": [
      "string"
    ]
  }
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/></td><td>Successful response<br/></td><td>[DeleteDeviceResourceResponse](#schemadeletedeviceresourceresponse)<br/></td></tr>
</table>

## **POST Create a new device frame**

POST /api/v1/device/frame/create

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>body<br/></td><td>body<br/></td><td>[CreateDeviceFrameRequest](#schemacreatedeviceframerequest)<br/></td><td>否<br/></td><td>none<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "deviceMeta": {
    "deviceUID": "string",
    "frameMetas": [
      {
        "frameUID": "string",
        "frameType": "modbus"
      }
    ],
    "deviceDesc": "string"
  },
  "deviceWorkStatus": {
    "healthStatus": "init",
    "timestamp": 0,
    "workMode": "distributed"
  }
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/></td><td>Successful response<br/></td><td>[CreateDeviceFrameResponse](#schemacreatedeviceframeresponse)<br/></td></tr>
</table>

## **POST Delete a device frame**

POST /api/v1/device/frame/delete

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>body<br/></td><td>body<br/></td><td>[DeleteDeviceFrameRequest](#schemadeletedeviceframerequest)<br/></td><td>否<br/></td><td>none<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "deviceMeta": {
    "deviceUID": "string",
    "frameMetas": [
      {
        "frameUID": "string",
        "frameType": "modbus"
      }
    ],
    "deviceDesc": "string"
  },
  "frameInfos": [
    {
      "frameMeta": {
        "frameUID": "string",
        "frameType": "modbus"
      },
      "frameLibs": {
        "modbusInfo": {},
        "uartInfo": {}
      }
    }
  ],
  "deviceWorkStatus": {
    "healthStatus": "init",
    "timestamp": 0,
    "workMode": "distributed"
  }
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/></td><td>Successful response<br/></td><td>[DeleteDeviceFrameResponse](#schemadeletedeviceframeresponse)<br/></td></tr>
</table>

## **POST List devices**

POST /api/v1/device/device/list

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>body<br/></td><td>body<br/></td><td>[ListDeviceRequest](#schemalistdevicerequest)<br/></td><td>否<br/></td><td>none<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "devices": [
    {
      "deviceMeta": {
        "deviceUID": "string",
        "frameMetas": [
          {
            "frameUID": null,
            "frameType": null
          }
        ],
        "deviceDesc": "string"
      },
      "deviceWorkStatus": {
        "healthStatus": "init",
        "timestamp": 0,
        "workMode": "distributed"
      },
      "deviceResourceInfo": {
        "ip": "string",
        "hostname": "string",
        "type": "fan",
        "os": "string",
        "cpu": {},
        "gpu": {},
        "disk": {},
        "mem": {},
        "protocol": [
          "string"
        ]
      },
      "frameInfos": [
        {
          "frameMeta": {
            "frameUID": null,
            "frameType": null
          },
          "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null
          }
        }
      ]
    }
  ]
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/></td><td>Successful response<br/></td><td>[ListDeviceResponse](#schemalistdeviceresponse)<br/></td></tr>
</table>

## **GET Get frames by device ID**

GET /api/v1/device/frames/{device_id}

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>device_id<br/></td><td>path<br/></td><td>string<br/></td><td>是<br/></td><td>设备唯一标识符<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "frames": [
    {
      "frameMeta": {
        "frameUID": "string",
        "frameType": "modbus"
      },
      "frameLibs": {
        "modbusInfo": {},
        "uartInfo": {}
      }
    }
  ]
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/></td><td>Successful response<br/></td><td>GetFramesByDeviceIDResponse<br/></td></tr>
</table>

## **GET Get device by ID**

GET /api/v1/device/{device_id}

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>device_id<br/></td><td>path<br/></td><td>string<br/></td><td>是<br/></td><td>设备唯一标识符<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "deviceResourceInfo": {
    "ip": "string",
    "hostname": "string",
    "type": "fan",
    "os": "string",
    "cpu": {
      "amount": 0,
      "type": "string",
      "unit": "string"
    },
    "gpu": {
      "amount": 0,
      "type": "string", 
      "unit": "string"
    },
    "disk": {
      "amount": 0,
      "type": "string",
      "unit": "string"
    },
    "mem": {
      "amount": 0,
      "type": "string",
      "unit": "string"
    },
    "protocol": [
      "string"
    ]
  },
  "deviceWorkStatus": {
    "healthStatus": "init",
    "timestamp": 0,
    "workMode": "distributed"
  }
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/></td><td>Successful response<br/></td><td>GetDeviceByIDResponse<br/></td></tr>
</table>

## **GET Check device health**

GET /api/v1/device/health/{device_id}

### **请求参数**

<table>
<tr>
<td>名称<br/></td><td>位置<br/></td><td>类型<br/></td><td>必选<br/></td><td>说明<br/></td></tr>
<tr>
<td>device_id<br/></td><td>path<br/></td><td>string<br/></td><td>是<br/></td><td>设备唯一标识符<br/></td></tr>
</table>

> 返回示例

> 200 Response

```json
{
  "deviceWorkStatus": {
    "healthStatus": "init",
    "timestamp": 0,
    "workMode": "distributed"
  }
}
```

### **返回结果**

<table>
<tr>
<td>状态码<br/></td><td>状态码含义<br/></td><td>说明<br/></td><td>数据模型<br/></td></tr>
<tr>
<td>200<br/></td><td>[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)<br/></td><td>Successful response<br/></td><td>CheckDeviceHealthResponse<br/></td></tr>
</table>
## **POST Control a device**

POST /api/v1/device/device/control

### **请求参数**

| 名称 | 位置 | 类型                                                | 必选 | 说明 |
| ---- | ---- | --------------------------------------------------- | ---- | ---- |
| body | body | [ControlDeviceRequest](#schemacontroldevicerequest) | 否   | none |

> 返回示例

> 200 Response

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "string",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "hex",
                    "typeID": "hex",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Id_ref",
                            "type": "hex",
                            "value": "0x0000"
                        },
                        {
                            "index": "1",
                            "name": "Iq_ref",
                            "type": "hex",
                            "value": "0x0000"
                        }
                    ]
                }
            }
        }
    ]
}
```

### **返回结果**

| 状态码 | 状态码含义                                              | 说明                | 数据模型                                              |
| ------ | ------------------------------------------------------- | ------------------- | ----------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | Successful response | [ControlDeviceResponse](#schemacontroldeviceresponse) |

## **POST Fetch a device data**

POST /api/v1/device/device/control

### **请求参数**

| 名称 | 位置 | 类型                                                    | 必选 | 说明 |
| ---- | ---- | ------------------------------------------------------- | ---- | ---- |
| body | body | [FetchDeviceDataRequest](#schemafetchdevicedatarequest) | 否   | none |

> 返回示例

> 200 Response

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfos": [
        {
            "frameMeta": {
                "frameUID": "79db7d07-07a5-41f1-ba87-9724543ddb9c",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13857"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-254"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-710"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "964"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.491638"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-254"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-966"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "203"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "977"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "101"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "488"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-126"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-482"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-126"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-354"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "480"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "374"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "146"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "980"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "d73bf172-af2a-4ca7-ad52-a8f8fb67748f",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13858"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-350"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-636"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "986"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.494385"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-350"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-936"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "317"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "947"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "158"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "473"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-174"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-467"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-174"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-317"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "491"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "326"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "183"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "991"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "0dbd35b5-b9b0-481d-981a-de4891dbe5dc",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13859"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-441"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-555"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "997"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.497192"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-441"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-896"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "425"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "903"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "212"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "451"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-219"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-447"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-219"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-277"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "496"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "281"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "223"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "996"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "ac64b72a-49d4-403f-8c0f-249b021741da",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13860"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-529"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-470"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "999"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.500000"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-529"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-848"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "528"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "848"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "264"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "424"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-264"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-423"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-264"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-234"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "498"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "236"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "266"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "998"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "ab5c822a-b81d-4464-9255-52045f04addd",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13861"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-611"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-379"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "991"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.502747"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-611"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-790"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "624"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "779"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "312"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "389"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-305"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-394"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-305"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-188"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "493"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "195"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "312"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "993"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "2675667b-5a3d-4326-bbc5-b76271747bda",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13862"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-687"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-285"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "972"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.505554"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-687"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-725"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "711"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "700"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "355"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "350"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-342"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-362"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-342"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-142"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "484"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "158"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "358"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "984"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "e27fd7d8-04a2-4ac2-9868-bc3dcc4de431",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13863"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-756"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-188"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "944"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.508301"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-756"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-653"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "789"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "612"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "394"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "306"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-377"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-326"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-377"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-93"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "470"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "123"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "407"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "970"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "b5039f27-78b7-41b0-b14c-5f6a3c2525fd",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13864"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-817"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-89"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "907"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.511108"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-817"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-575"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "855"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "516"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "427"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "258"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-407"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-287"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-407"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-45"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "452"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "93"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "455"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "952"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "27b7b1cb-1788-426d-8945-2d45be345044",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13865"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-871"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "10"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "860"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.513855"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-871"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-490"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "910"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "412"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "455"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "206"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-435"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-244"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-435"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "6"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "428"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "65"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "506"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "928"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "fcc7cf04-5c84-4df8-816f-b15a615bd7b8",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13866"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-915"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "110"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "805"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.516663"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-915"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-401"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "951"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "303"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "475"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "151"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-456"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-199"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-456"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "55"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "400"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "44"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "555"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "900"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "3fbc1c30-bed1-4a41-9684-0a465adc1a52",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13867"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-951"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "208"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "742"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.519409"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-951"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-308"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "981"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "189"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "490"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "94"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-474"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-153"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-474"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "104"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "369"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "26"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "604"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "869"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "423d443a-eef7-4dfc-afb9-2a78c6133e4d",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13868"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-977"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "305"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "671"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.522217"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-977"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-211"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "996"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "72"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "498"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "36"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-488"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-104"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-488"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "153"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "334"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "12"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "653"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "834"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "d3d1dcd2-5990-48f2-826a-951585f168e5",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13869"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-993"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "399"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "594"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.524963"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-993"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-112"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "998"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-44"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "499"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-22"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-496"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-56"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-496"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "199"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "296"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "4"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "699"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "796"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "74c11db8-b4e0-400f-a708-56fe87a5774d",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13870"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-999"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "488"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "511"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.527771"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-999"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-13"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "986"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-160"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "493"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-80"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-499"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-6"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-499"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "244"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "254"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "1"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "744"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "754"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "1c0c36e0-53a6-4f3e-b575-3d7c83fb7006",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13871"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-996"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "573"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "422"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.530518"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-996"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "87"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "961"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-275"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "480"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-137"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-497"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "42"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-497"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "284"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "212"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "3"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "784"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "712"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "e97b9368-90fc-4858-b06b-fa06a2308058",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13872"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-982"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "652"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "330"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.533325"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-982"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "185"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "922"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-385"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "461"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-192"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-490"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "91"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-490"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "323"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "166"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "10"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "823"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "666"
                        }
                    ]
                }
            }
        }
    ]
}
```

### **返回结果**

| 状态码 | 状态码含义                                              | 说明                | 数据模型                                                  |
| ------ | ------------------------------------------------------- | ------------------- | --------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | Successful response | [FetchDeviceDataResponse](#schemafetchdevicedataresponse) |

# 设备管理器 API 公共错误码

本文罗列并说明公共错误码。若想了解某个接口业务逻辑相关的错误码，请参考该接口的说明文档。

| **状态码** | **错误码**             | **错误信息**                                     | **说明**                 |
| ---------- | ---------------------- | ------------------------------------------------ | ------------------------ |
| 例如：400  | 例如：InvalidParameter | 例如：The request contains an invalid parameter. | 例如：请求包含无效参数。 |
|            |                        |                                                  |                          |
|            |                        |                                                  |                          |

# 设备管理器 API 公共错误码

本文罗列并说明公共错误码。若想了解某个接口业务逻辑相关的错误码，请参考该接口的说明文档。

<table>
<tr>
<td>**状态码**<br/></td><td>**错误码**<br/></td><td>**错误信息**<br/></td><td>**说明**<br/></td></tr>
<tr>
<td>> 例如：> 400<br/></td><td>> 例如：> InvalidParameter<br/></td><td>> 例如：> The request contains an invalid parameter.<br/></td><td>> 例如：> 请求包含无效参数。<br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
</table>

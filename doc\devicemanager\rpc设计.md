# 设备管理器gRPC设计文档

## 设备管理器gRPC 更新历史

本文记录了设备管理器 gRPC 的变更情况。

### **版本：v1.1**

发版日期：2025-05-03

#### **新增**

| 功能模块                | 说明                                                       | 相关文档     |
| ----------------------- | ---------------------------------------------------------- | ------------ |
| ControlDeviceByDeviceID | 根据设备ID进行控制，向设备发送控制指令，返回设备的工作状态 | device.proto |

#### **更新**

| 功能模块 | 说明 | 相关文档 |
| -------- | ---- | -------- |
| 无       | 无   | 无       |

#### **修复**

| 功能模块 | 说明 | 相关文档 |
| -------- | ---- | -------- |
| 无       | 无   | 无       |

#### **删除**

| 功能模块 | 说明 | 相关文档 |
| -------- | ---- | -------- |
| 无       | 无   | 无       |

### **已知问题**

说明该版本中存在的已知问题。若有多个，以无序列表的形式呈现。
- 读取设备数据功能还暂未加入到gRPC中
---

### 版本：v1

发版日期：2025-03-15

#### 新增

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| GetFramesByDeviceID | 根据设备ID获取帧信息，返回帧元数据和帧库信息(支持Modbus和Uart协议) | device.proto |
| GetDeviceByID | 获取设备详细信息，包括设备资源信息(CPU/GPU/磁盘/内存)和工作状态 | device.proto |
| CheckDeviceHealth | 检查设备健康状态，返回设备当前工作状态信息 | device.proto |

#### 更新

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

#### 修复

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

#### 删除

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

### 已知问题

说明该版本中存在的已知问题。若有多个，以无序列表的形式呈现。
- 暂无对帧的数据部分的配置和呈现，下个版本将加入
---

## gRPC 详细说明

### 1. GetFramesByDeviceID

#### 请求参数 (GetFramesByDeviceIDRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| device_id | string | 设备唯一标识符 |

#### 响应参数 (GetFramesByDeviceIDResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| frames | repeated FrameInfo | 帧信息列表 |

FrameInfo 结构：

- frame_meta：帧元数据
  - frame_uid：帧唯一标识符
  - frame_type：帧类型
- frame_lib：帧协议库信息
  - modbus_info：Modbus协议信息
  - uart_info：Uart协议信息

### 2. GetDeviceByID

#### 请求参数 (GetDeviceByIDRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| device_id | string | 设备唯一标识符 |

#### 响应参数 (GetDeviceByIDResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| device_resource_info | DeviceResourceInfo | 设备资源信息 |
| device_work_status | DeviceWorkStatus | 设备工作状态 |

DeviceResourceInfo 结构：

- ip：设备IP地址
- hostname：主机名
- type：设备类型
- os：操作系统
- cpu：CPU资源信息
- gpu：GPU资源信息
- disk：磁盘资源信息
- mem：内存资源信息
- protocol：支持的协议列表

### 3. CheckDeviceHealth

#### 请求参数 (CheckDeviceHealthRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| device_id | string | 设备唯一标识符 |

#### 响应参数 (CheckDeviceHealthResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| device_work_status | DeviceWorkStatus | 设备工作状态 |

DeviceWorkStatus 结构：

- health_status：健康状态
- timestamp：时间戳
- work_mode：工作模式

### 4. **ControlDeviceByDeviceID**

#### **请求参数 (ControlDeviceByDeviceIDRequest)**

| 参数名     | 类型      | 说明           |
| ---------- | --------- | -------------- |
| device_id  | string    | 设备唯一标识符 |
| frame_info | FrameInfo | 请求帧信息     |

FrameInfo 结构：

- frame_meta：帧元数据
  - frame_uid：帧唯一标识符
  - frame_type：帧类型
- frame_lib：帧协议库信息
  - modbus_info：Modbus协议信息
  - uart_info：Uart协议信息
  - udp_info：Udp协议信息

#### **响应参数 (ControlDeviceByDeviceIDResponse)**

| 参数名             | 类型               | 说明         |
| ------------------ | ------------------ | ------------ |
| device_work_status | DeviceWorkStatus   | 设备工作状态 |
| frame_infos        | repeated FrameInfo | 响应帧信息   |

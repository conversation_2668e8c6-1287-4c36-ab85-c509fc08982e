# 版本说明

本文记录了设备管理器 功能设计 的变更情况。

版本：v20250314

后端开发人：韩梦琦

测试人：刘宇翔

测试目标：设备管理器基础功能无误

## 功能说明

| 一级功能 |   二级功能   | API/gRPC接口                                   |
| :------: | :----------: | :--------------------------------------------- |
| 设备管理 |   设备创建   | `/api/v1/device/resource/create`             |
|          |   设备删除   | `/api/v1/device/resource/delete`             |
|          |   设备查询   | `/api/v1/device/device/list`                 |
|          |      -      | `devicemanager.Device / GetDeviceByID`       |
|          | 设备健康检查 | `devicemanager.Device / CheckDeviceHealth`   |
|  帧管理  |    帧创建    | `/api/v1/device/frame/create`                |
|          |    帧删除    | `/api/v1/device/frame/delete`                |
|          |    帧查询    | `/api/v1/device/device/list`                 |
|          |      -      | `devicemanager.Device / GetFramesByDeviceID` |

## 测试前配置

```
#启动数据库/用云端数据库
bash Backend\app\Devicemanager\scripts\start_migration_db.sh

#启动后端
docker run -it --rm -p 8888:8888 -p 8080:8080  g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/devicemanager:latest
```

## 测试内容

### 输入测试

1缺省值奏效
2nil无异常

### 功能测试

1数据库记录正常
2返回参数正常

### 幂等性测试

1查询多次结果相同

### 性能测试

1返回延迟符合要求

# 测试任务

#### 设备创建

测试用例：

结果

#### 设备删除

测试用例：

结果

#### 设备查询

测试用例：

结果

#### 设备健康检查

测试用例：

结果

#### 帧创建

测试用例：

结果

#### 帧删除

测试用例：

结果

#### 帧查询

测试用例：

结果

# 下期完成

## 数据帧管理

### 二级功能

#### 数据帧内容配置

#### 数据帧内容查看

## 设备和帧管理的修改

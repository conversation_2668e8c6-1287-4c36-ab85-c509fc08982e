测试前，需要先创建设备资源，IP为当前设备的地址"*************"：

```json
{
    "deviceResourceInfo": {
        "ip": "*************",
        "hostname": "储宇泽",
        "type": "fan",
        "os": "amet veniam",
        "cpu": null,
        "gpu": {
            "amount": 293,
            "type": "cupidatat pariatur ex sint eu",
            "unit": "id nulla"
        },
        "disk": null,
        "mem": null,
        "protocol": [
            "udp"
        ]
    }
}
```

注：下列测试用例中的deviceUID均须替换为刚刚创建的设备的UID

测试用例0x0000获取设备状态机当前状态

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0000",
                "typeID": "0x0000",
                "datas": null
            }
        }
    }
}

```

响应结果0x0000获取设备状态机当前状态

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "3d2fa8af-c154-4a54-9c79-562b9d203d61",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0000",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "STATE",
                            "type": "binary",
                            "value": "0b00000000000000000000000000000001"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0001状态机控制指令

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0001",
                "typeID": "0x0000",
                "datas": [
                    {
                        "index": "0",
                        "name": "CTRL",
                        "type": "binary",
                        "value": "0b00000000"
                    }
                ]
            }
        }
    }
}

```

响应结果0x0001状态机控制指令

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "2704998c-2e99-40ee-ac35-cef2414d280e",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0001",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "ERROR",
                            "type": "hex",
                            "value": "0x00000000"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0002 `CURRENT_LOOP` 控制参数获取

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0002",
                "typeID": "0x0000",
                "datas": null
            }
        }
    }
}

```

响应结果0x0002 `CURRENT_LOOP` 控制参数获取

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "828579d2-b6aa-4cd3-9f00-af8917bfb833",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0002",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Kp_d",
                            "type": "SQ8.8",
                            "value": "0.0000"
                        },
                        {
                            "index": "1",
                            "name": "Ki_d",
                            "type": "SQ8.8",
                            "value": "0.0000"
                        },
                        {
                            "index": "2",
                            "name": "Kp_q",
                            "type": "SQ8.8",
                            "value": "0.0000"
                        },
                        {
                            "index": "3",
                            "name": "Ki_q",
                            "type": "SQ8.8",
                            "value": "0.0000"
                        },
                        {
                            "index": "4",
                            "name": "integral_bound",
                            "type": "int32",
                            "value": "0"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0003 `CURRENT_LOOP` 控制参数设置

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0003",
                "typeID": "0x0000",
                "datas": [
                    {
                        "index": "0",
                        "name": "MASK",
                        "type": "int32",
                        "value": "0"
                    },
                    {
                        "index": "0",
                        "name": "Kp_d",
                        "type": "SQ8.8",
                        "value": "0"
                    },
                    {
                        "index": "1",
                        "name": "Ki_d",
                        "type": "SQ8.8",
                        "value": "0"
                    },
                    {
                        "index": "2",
                        "name": "Kp_q",
                        "type": "SQ8.8",
                        "value": "0"
                    },
                    {
                        "index": "3",
                        "name": "Ki_q",
                        "type": "SQ8.8",
                        "value": "0"
                    },
                    {
                        "index": "4",
                        "name": "integral_bound",
                        "type": "int32",
                        "value": "0"
                    }
                ]
            }
        }
    }
}

```

响应结果0x0003 `CURRENT_LOOP` 控制参数设置

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "3667311c-679f-4ceb-a02f-856c4de6e59e",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0003",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "ERROR",
                            "type": "hex",
                            "value": "0x00000000"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0004 `CURRENT_LOOP` 设定值获取

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0004",
                "typeID": "0x0000",
                "datas": null
            }
        }
    }
}

```

响应结果0x0004 `CURRENT_LOOP` 设定值获取

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "da0dc50c-2edb-47c4-84a8-4b2a13651384",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0004",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Id_ref",
                            "type": "int16",
                            "value": "0"
                        },
                        {
                            "index": "1",
                            "name": "Iq_ref",
                            "type": "int16",
                            "value": "0"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例 0x0005 `CURRENT_LOOP` 设定值设置

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0005",
                "typeID": "0x0000",
                "datas": [
                    {
                        "index": "0",
                        "name": "id_ref",
                        "type": "int16",
                        "value": "0"
                    },
                    {
                        "index": "1",
                        "name": "iq_ref",
                        "type": "int16",
                        "value": "0"
                    }
                ]
            }
        }
    }
}

```

响应结果 0x0005 `CURRENT_LOOP` 设定值设置

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "fb970310-c1c2-4181-9b7d-d4591a5b60cc",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0005",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "ERROR",
                            "type": "hex",
                            "value": "0x00000000"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0006 `SPEED_LOOP` 控制参数获取

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0006",
                "typeID": "0x0000",
                "datas": null
            }
        }
    }
}

```

响应结果0x0006 `SPEED_LOOP` 控制参数获取

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "c44b7d11-d8fa-4af8-9b50-c511f50e8844",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0006",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Kp",
                            "type": "SQ8.8",
                            "value": "1.0000"
                        },
                        {
                            "index": "1",
                            "name": "Ki",
                            "type": "SQ8.8",
                            "value": "0.5000"
                        },
                        {
                            "index": "2",
                            "name": "Kd",
                            "type": "SQ8.8",
                            "value": "0.1250"
                        },
                        {
                            "index": "3",
                            "name": "integral_bound",
                            "type": "int32",
                            "value": "4096"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0007 `SPEED_LOOP` 控制参数设置

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0007",
                "typeID": "0x0000",
                "datas": [
                    {
                        "index": "0",
                        "name": "MASK",
                        "type": "int32",
                        "value": "0"
                    },
                    {
                        "index": "0",
                        "name": "Kp",
                        "type": "SQ8.8",
                        "value": "0"
                    },
                    {
                        "index": "1",
                        "name": "Ki",
                        "type": "SQ8.8",
                        "value": "0"
                    },
                    {
                        "index": "2",
                        "name": "Kd",
                        "type": "SQ8.8",
                        "value": "0"
                    },
                    {
                        "index": "3",
                        "name": "integral_bound",
                        "type": "int32",
                        "value": "0"
                    }
                ]
            }
        }
    }
}

```

响应结果0x0007 `SPEED_LOOP` 控制参数设置

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "0ca8e43a-6392-4d80-b27e-c132dfb54677",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0007",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "ERROR",
                            "type": "hex",
                            "value": "0x00000000"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0008 `SPEED_LOOP` 设定值获取

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0008",
                "typeID": "0x0000",
                "datas": null
            }
        }
    }
}

```

响应结果0x0008 `SPEED_LOOP` 设定值获取

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "8b0dd134-04af-417c-916d-1948de2d9f79",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0008",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "speed_ref",
                            "type": "int16",
                            "value": "0"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例0x0009 `SPEED_LOOP` 设定值设置

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x0009",
                "typeID": "0x0000",
                "datas": [
                        {
                            "index": "0",
                            "name": "speed_ref",
                            "type": "int16",
                            "value": "0"
                        }
                    ]
            }
        }
    }
}

```

响应结果0x0009 `SPEED_LOOP` 设定值设置

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "6f9dc92b-3165-4f9b-8c15-ed69d2496296",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x0009",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "ERROR",
                            "type": "hex",
                            "value": "0x00000000"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例 `0x000A` 数据协议状态获取

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x000a",
                "typeID": "0x0000",
                "datas": null
            }
        }
    }
}

```

响应结果 `0x000A` 数据协议状态获取

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "c6c62110-e56e-44e6-8f49-d6c21ecd95db",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x000a",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "TRANSFER_STATE",
                            "type": "binary",
                            "value": "0b00000000"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例 `0x000B` 数据协议状态设置

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50",
    "frameInfo": {
        "frameMeta": {
            "frameUID": "8",
            "frameType": "udp"
        },
        "frameLibs": {
            "modbusInfo": null,
            "uartInfo": null,
            "udpInfo": {
                "type": "0x0000",
                "header": "0x000b",
                "typeID": "0x0000",
                "datas": [
                        {
                            "index": "0",
                            "name": "TRANSFER_CTRL",
                            "type": "binary",
                            "value": "0b00000001"
                        }
                    ]
            }
        }
    }
}

```

响应结果 `0x000B` 数据协议状态设置

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfo": [
        {
            "frameMeta": {
                "frameUID": "299da02e-f021-4757-8c80-883ff1fb3b31",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0001",
                    "header": "0x000b",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "ERROR",
                            "type": "hex",
                            "value": "0x00000000"
                        }
                    ]
                }
            }
        }
    ]
}
```

测试用例FetchData

```json
{
    "deviceUID": "81b27da5-0d72-41a5-9ed0-ba9b5a9afb50"
}
```

响应结果FetchData

```json
{
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "frameInfos": [
        {
            "frameMeta": {
                "frameUID": "79db7d07-07a5-41f1-ba87-9724543ddb9c",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13857"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-254"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-710"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "964"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.491638"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-254"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-966"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "203"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "977"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "101"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "488"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-126"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-482"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-126"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-354"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "480"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "374"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "146"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "980"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "d73bf172-af2a-4ca7-ad52-a8f8fb67748f",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13858"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-350"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-636"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "986"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.494385"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-350"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-936"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "317"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "947"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "158"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "473"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-174"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-467"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-174"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-317"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "491"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "326"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "183"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "991"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "0dbd35b5-b9b0-481d-981a-de4891dbe5dc",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13859"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-441"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-555"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "997"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.497192"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-441"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-896"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "425"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "903"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "212"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "451"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-219"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-447"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-219"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-277"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "496"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "281"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "223"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "996"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "ac64b72a-49d4-403f-8c0f-249b021741da",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13860"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-529"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-470"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "999"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.500000"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-529"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-848"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "528"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "848"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "264"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "424"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-264"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-423"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-264"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-234"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "498"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "236"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "266"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "998"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "ab5c822a-b81d-4464-9255-52045f04addd",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13861"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-611"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-379"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "991"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.502747"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-611"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-790"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "624"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "779"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "312"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "389"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-305"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-394"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-305"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-188"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "493"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "195"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "312"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "993"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "2675667b-5a3d-4326-bbc5-b76271747bda",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13862"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-687"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-285"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "972"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.505554"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-687"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-725"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "711"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "700"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "355"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "350"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-342"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-362"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-342"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-142"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "484"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "158"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "358"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "984"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "e27fd7d8-04a2-4ac2-9868-bc3dcc4de431",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13863"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-756"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-188"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "944"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.508301"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-756"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-653"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "789"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "612"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "394"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "306"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-377"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-326"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-377"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-93"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "470"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "123"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "407"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "970"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "b5039f27-78b7-41b0-b14c-5f6a3c2525fd",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13864"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-817"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "-89"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "907"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.511108"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-817"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-575"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "855"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "516"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "427"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "258"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-407"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-287"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-407"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "-45"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "452"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "93"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "455"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "952"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "27b7b1cb-1788-426d-8945-2d45be345044",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13865"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-871"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "10"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "860"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.513855"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-871"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-490"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "910"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "412"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "455"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "206"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-435"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-244"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-435"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "6"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "428"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "65"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "506"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "928"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "fcc7cf04-5c84-4df8-816f-b15a615bd7b8",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13866"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-915"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "110"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "805"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.516663"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-915"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-401"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "951"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "303"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "475"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "151"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-456"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-199"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-456"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "55"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "400"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "44"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "555"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "900"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "3fbc1c30-bed1-4a41-9684-0a465adc1a52",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13867"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-951"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "208"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "742"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.519409"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-951"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-308"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "981"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "189"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "490"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "94"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-474"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-153"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-474"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "104"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "369"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "26"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "604"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "869"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "423d443a-eef7-4dfc-afb9-2a78c6133e4d",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13868"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-977"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "305"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "671"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.522217"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-977"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-211"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "996"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "72"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "498"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "36"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-488"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-104"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-488"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "153"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "334"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "12"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "653"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "834"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "d3d1dcd2-5990-48f2-826a-951585f168e5",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13869"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-993"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "399"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "594"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.524963"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-993"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-112"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "998"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-44"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "499"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-22"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-496"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-56"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-496"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "199"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "296"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "4"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "699"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "796"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "74c11db8-b4e0-400f-a708-56fe87a5774d",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13870"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-999"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "488"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "511"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.527771"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-999"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "-13"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "986"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-160"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "493"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-80"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-499"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "-6"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-499"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "244"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "254"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "1"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "744"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "754"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "1c0c36e0-53a6-4f3e-b575-3d7c83fb7006",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13871"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-996"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "573"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "422"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.530518"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-996"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "87"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "961"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-275"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "480"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-137"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-497"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "42"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-497"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "284"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "212"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "3"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "784"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "712"
                        }
                    ]
                }
            }
        },
        {
            "frameMeta": {
                "frameUID": "e97b9368-90fc-4858-b06b-fa06a2308058",
                "frameType": "udp"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "",
                    "pid": "",
                    "len": "",
                    "uid": "",
                    "fc": "",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                },
                "udpInfo": {
                    "type": "0x0002",
                    "header": "0x000C",
                    "typeID": "0x0000",
                    "datas": [
                        {
                            "index": "0",
                            "name": "Index",
                            "type": "int",
                            "value": "13872"
                        },
                        {
                            "index": "1",
                            "name": "Ia",
                            "type": "int16",
                            "value": "-982"
                        },
                        {
                            "index": "2",
                            "name": "Ib",
                            "type": "int16",
                            "value": "652"
                        },
                        {
                            "index": "3",
                            "name": "Ic",
                            "type": "int16",
                            "value": "330"
                        },
                        {
                            "index": "4",
                            "name": "RawAngle",
                            "type": "SQ2.14",
                            "value": "0.533325"
                        },
                        {
                            "index": "5",
                            "name": "Ialpha",
                            "type": "int16",
                            "value": "-982"
                        },
                        {
                            "index": "6",
                            "name": "Ibeta",
                            "type": "int16",
                            "value": "185"
                        },
                        {
                            "index": "7",
                            "name": "Id",
                            "type": "int16",
                            "value": "922"
                        },
                        {
                            "index": "8",
                            "name": "Iq",
                            "type": "int16",
                            "value": "-385"
                        },
                        {
                            "index": "9",
                            "name": "Ud",
                            "type": "int16",
                            "value": "461"
                        },
                        {
                            "index": "10",
                            "name": "Uq",
                            "type": "int16",
                            "value": "-192"
                        },
                        {
                            "index": "11",
                            "name": "Ualpha",
                            "type": "int16",
                            "value": "-490"
                        },
                        {
                            "index": "12",
                            "name": "Ubeta",
                            "type": "int16",
                            "value": "91"
                        },
                        {
                            "index": "13",
                            "name": "Ua",
                            "type": "int16",
                            "value": "-490"
                        },
                        {
                            "index": "14",
                            "name": "Ub",
                            "type": "int16",
                            "value": "323"
                        },
                        {
                            "index": "15",
                            "name": "Uc",
                            "type": "int16",
                            "value": "166"
                        },
                        {
                            "index": "16",
                            "name": "Uan",
                            "type": "int16",
                            "value": "10"
                        },
                        {
                            "index": "17",
                            "name": "Ubn",
                            "type": "int16",
                            "value": "823"
                        },
                        {
                            "index": "18",
                            "name": "Ucn",
                            "type": "int16",
                            "value": "666"
                        }
                    ]
                }
            }
        }
    ]
}
```

# 设备管理器数据库设计文档

## 设备管理器API 更新历史

本文记录了设备管理器 API 的变更情况。

### 版本：v1.1

发版日期：2025-05-03

#### 新增

| 表名称 | 说明          | 关系                 |
| ------ | ------------- | -------------------- |
| Udp    | Udp协议配置表 | deviceID关联Device表 |

#### 更新

| 功能模块 | 说明 | 相关文档 |
| -------- | ---- | -------- |
| 无       | 无   | 无       |

#### 修复

| 功能模块 | 说明 | 相关文档 |
| -------- | ---- | -------- |
| 无       | 无   | 无       |

#### 删除

| 功能模块 | 说明 | 相关文档 |
| -------- | ---- | -------- |
| 无       | 无   | 无       |

### 已知问题

说明该版本中存在的已知问题。若有多个，以无序列表的形式呈现。
- 
---


### 版本：v1

发版日期：2025-03-15

#### 新增

| 表名称 | 说明 | 关系 |
|-------|------|------|
| Device | 设备基本信息表 | 主表 |
| Modbus | Modbus协议配置表 | deviceID关联Device表 |
| Uart | Uart协议配置表 | deviceID关联Device表 |
| Data | 数据格式配置表 | frameID关联Modbus/Uart表 |

#### 更新
| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

#### 修复
| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

#### 删除
| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

### 已知问题
说明该版本中存在的已知问题。若有多个，以无序列表的形式呈现。
- 暂无对帧的数据部分的配置和呈现，下个版本将加入
---

## Device 设备表

设备表用于存储所有接入系统的设备信息，包含设备的基本信息、资源信息和状态信息。

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| id | string | 是 | 设备UID，唯一标识符 |
| name | string | 否 | 设备名称 |
| ip | string | 是 | 设备IP地址 |
| type | string | 否 | 设备类型，默认为"lower" |
| os | string | 否 | 操作系统信息 |
| cpu | string | 否 | CPU型号 |
| gpu | string | 否 | GPU型号 |
| memory | string | 否 | 内存大小 |
| disk | string | 否 | 磁盘大小 |
| protocol | string | 否 | 通信协议 |
| status | string | 否 | 设备状态，默认为"unready" |
| healthtimestamp | timestamp | 否 | 健康检查时间戳 |
| workmode | string | 否 | 工作模式，默认为"centralized" |
| create_unix | timestamp | 是 | 创建时间 |
| update_unix | timestamp | 是 | 更新时间 |

### 关联关系
- 一对一关联到 Modbus 配置表
- 一对一关联到 Uart 配置表

## Modbus 配置表

Modbus配置表用于存储设备的Modbus通信协议相关配置。

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| device_id | string | 是 | 关联的设备ID |
| id | string | 是 | 帧ID，唯一标识符 |
| tid | string | 否 | 事务标识符 |
| pid | string | 否 | 协议标识符 |
| len | string | 否 | 剩余帧长度 |
| uid | string | 否 | 单元标识符 |
| fc | string | 否 | 功能码 |
| create_unix | timestamp | 是 | 创建时间 |
| update_unix | timestamp | 是 | 更新时间 |

### 关联关系
- 从属于一个Device设备
- 一对多关联到Data数据点表

## Uart 配置表

Uart配置表用于存储设备的串口通信协议相关配置。

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| device_id | string | 是 | 关联的设备ID |
| id | string | 是 | 帧ID，唯一标识符 |
| header | string | 否 | 帧头 |
| addr | string | 否 | 地址 |
| cmd | string | 否 | 命令 |
| tail | string | 否 | 帧尾 |
| create_unix | timestamp | 是 | 创建时间 |
| update_unix | timestamp | 是 | 更新时间 |

### 关联关系
- 从属于一个Device设备
- 一对多关联到Data数据点表

## Udp 配置表

Udp配置表用于存储设备的Udp协议相关配置。

### 字段说明

| 字段名      | 类型      | 必填 | 说明                                 |
| ----------- | --------- | ---- | ------------------------------------ |
| device_id   | string    | 是   | 关联的设备ID                         |
| type        | string    | 是   | 区分控制帧和响应帧                   |
| header      | string    | 否   | 帧头，决定控制帧的类型和参数组织形式 |
| typeID      | string    | 否   | 类型ID，用于对齐控制帧和响应帧       |
| create_unix | timestamp | 是   | 创建时间                             |
| update_unix | timestamp | 是   | 更新时间                             |

## Data 数据点表

数据点表用于存储设备采集的具体数据信息。

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| frame_id | string | 是 | 关联的帧ID |
| index | string | 是 | 数据索引 |
| name | string | 是 | 数据点名称 |
| type | string | 否 | 数据类型 |
| value | string | 否 | 数据值 |
| update_unix | timestamp | 是 | 更新时间 |

### 关联关系
- 从属于一个Modbus帧或Uart帧或Udp帧（三选一）

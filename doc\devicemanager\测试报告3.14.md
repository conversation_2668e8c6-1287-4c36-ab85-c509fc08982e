# 信息说明

合并提出日期：2025-3-14

后端开发人：韩梦琦

测试人：刘宇翔

测试目标：设备管理器基础功能无误

## 功能说明

| 一级功能 | 二级功能 | API/gRPC接口 |
|:--------:|:--------:|:-------------|
| 设备管理 | 设备创建 | `/api/v1/device/resource/create` |
|          | 设备删除 | `/api/v1/device/resource/delete` |
|          | 设备查询 | `/api/v1/device/device/list` |
|          |     -     | `devicemanager.Device / GetDeviceByID` |
|          | 设备健康检查 | `devicemanager.Device / CheckDeviceHealth` |
| 帧管理   | 帧创建   | `/api/v1/device/frame/create` |
|          | 帧删除   | `/api/v1/device/frame/delete` |
|          | 帧查询   | `/api/v1/device/device/list` |
|          |     -     | `devicemanager.Device / GetFramesByDeviceID` |

## 测试前配置
```
#启动数据库/用云端数据库
bash Backend\app\Devicemanager\scripts\start_migration_db.sh

#启动后端
docker run -it --rm -p 8888:8888 -p 8080:8080  g-ojys9290-docker.pkg.coding.net/generative-control-foundation/dev/devicemanager:latest
```

## 测试内容
### 输入测试
1缺省值奏效
2nil无异常

### 功能测试
1数据库记录正常
2返回参数正常

### 幂等性测试
1查询多次结果相同

### 性能测试
1返回延迟符合要求

# 测试任务
#### 设备创建
测试用例：
```
{
    "deviceResourceInfo": {
        "ip": "*************",
        "hostname": "device-test-001",
        "type": "edge",
        "os": "Linux",
        "cpu": {
            "amount": 8,
            "type": "Intel Core i7",
            "unit": "core"
        },
        "gpu": {
            "amount": 1,
            "type": "NVIDIA RTX 3080",
            "unit": "card"
        },
        "disk": {
            "amount": 500,
            "type": "SSD",
            "unit": "GB"
        },
        "mem": {
            "amount": 16,
            "type": "DDR4",
            "unit": "GB"
        },
        "protocol": [
            "modbus",
            "uart"
        ]
    }
}
```
结果
```
{
    "deviceMeta": {
        "deviceUID": "474117df-ac19-418d-8d64-2d6e2ac4441b",
        "frameMetas": [
            {
                "frameUID": "7f9f61dc-7598-4f91-befc-4cd7b9618f4c",
                "frameType": "modbus"
            },
            {
                "frameUID": "9439eca2-61ae-40f9-8af8-d33f07726377",
                "frameType": "uart"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": 0,
        "workMode": "centralized"
    }
}
```

测试用例：
```
{
    "deviceResourceInfo": {
        "ip": "*************",
        "protocol": [
            "modbus",
            "uart"
        ]
    }
}
```

结果：
```
{
    "deviceMeta": {
        "deviceUID": "7a9a3027-69a8-4a76-8a49-c98c9519269b",
        "frameMetas": [
            {
                "frameUID": "4fbaff3d-03df-4036-85ed-7006696748e4",
                "frameType": "modbus"
            },
            {
                "frameUID": "7092beb7-da75-49e3-8ddc-8caaff430335",
                "frameType": "uart"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": 0,
        "workMode": "centralized"
    }
}
```
#### 设备删除
测试用例：
```
{
    "deviceUID": "33cfc253-908b-45ba-8328-ffe5a66b3afd"
}
```
结果：
```
{
    "deviceMeta": {
        "deviceUID": "33cfc253-908b-45ba-8328-ffe5a66b3afd",
        "frameMetas": [
            {
                "frameUID": "b93af037-2519-4048-abfc-d34e7581657c",
                "frameType": "modbus"
            },
            {
                "frameUID": "17303325-8333-4085-904e-96a9e13902ea",
                "frameType": "uart"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "deviceResourceInfo": {
        "ip": "*************",
        "hostname": "",
        "type": "",
        "os": "",
        "cpu": {
            "amount": 0,
            "type": "",
            "unit": ""
        },
        "gpu": {
            "amount": 0,
            "type": "",
            "unit": ""
        },
        "disk": {
            "amount": 0,
            "type": "",
            "unit": ""
        },
        "mem": {
            "amount": 0,
            "type": "",
            "unit": ""
        },
        "protocol": [
            "modbus",
            "uart"
        ]
    }
}
```
测试用例：
```
{
    "deviceUID": ""
}
```
结果
```
{"message":"ent: device not found"}
```
#### 设备查询
测试用例：
```
{}
```
结果
```
{
    "devices": [
        {
            "deviceMeta": {
                "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
                "frameMetas": [],
                "deviceDesc": ""
            },
            "deviceWorkStatus": {
                "healthStatus": "ready",
                "timestamp": **********,
                "workMode": "centralized"
            },
            "deviceResourceInfo": {
                "ip": "ba57:d1fb:a5cf:59e3:5fe3:d7d3:35bd:760c",
                "hostname": "镇帅",
                "type": "edge",
                "os": "",
                "cpu": {
                    "amount": 950,
                    "type": "anim fugiat et qui aute",
                    "unit": "dolore fugiat aute reprehenderit"
                },
                "gpu": {
                    "amount": 669,
                    "type": "do",
                    "unit": "laborum ea Ut aliquip"
                },
                "disk": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "mem": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "protocol": [
                    "https",
                    "https"
                ]
            },
            "frameInfos": []
        },
        {
            "deviceMeta": {
                "deviceUID": "474117df-ac19-418d-8d64-2d6e2ac4441b",
                "frameMetas": [
                    {
                        "frameUID": "7f9f61dc-7598-4f91-befc-4cd7b9618f4c",
                        "frameType": "modbus"
                    },
                    {
                        "frameUID": "9439eca2-61ae-40f9-8af8-d33f07726377",
                        "frameType": "uart"
                    }
                ],
                "deviceDesc": ""
            },
            "deviceWorkStatus": {
                "healthStatus": "ready",
                "timestamp": **********,
                "workMode": "centralized"
            },
            "deviceResourceInfo": {
                "ip": "*************",
                "hostname": "device-test-001",
                "type": "edge",
                "os": "Linux",
                "cpu": {
                    "amount": 8,
                    "type": "Intel Core i7",
                    "unit": "core"
                },
                "gpu": {
                    "amount": 1,
                    "type": "NVIDIA RTX 3080",
                    "unit": "card"
                },
                "disk": {
                    "amount": 500,
                    "type": "SSD",
                    "unit": "GB"
                },
                "mem": {
                    "amount": 16,
                    "type": "DDR4",
                    "unit": "GB"
                },
                "protocol": [
                    "modbus",
                    "uart"
                ]
            },
            "frameInfos": [
                {
                    "frameMeta": {
                        "frameUID": "7f9f61dc-7598-4f91-befc-4cd7b9618f4c",
                        "frameType": "modbus"
                    },
                    "frameLibs": {
                        "modbusInfo": {
                            "tid": "",
                            "pid": "",
                            "len": "",
                            "uid": "",
                            "fc": "",
                            "datas": null
                        },
                        "uartInfo": {
                            "header": "",
                            "addr": "",
                            "cmd": "",
                            "tail": "",
                            "datas": null
                        }
                    }
                },
                {
                    "frameMeta": {
                        "frameUID": "9439eca2-61ae-40f9-8af8-d33f07726377",
                        "frameType": "uart"
                    },
                    "frameLibs": {
                        "modbusInfo": {
                            "tid": "",
                            "pid": "",
                            "len": "",
                            "uid": "",
                            "fc": "",
                            "datas": null
                        },
                        "uartInfo": {
                            "header": "",
                            "addr": "",
                            "cmd": "",
                            "tail": "",
                            "datas": null
                        }
                    }
                }
            ]
        },
        {
            "deviceMeta": {
                "deviceUID": "7a9a3027-69a8-4a76-8a49-c98c9519269b",
                "frameMetas": [
                    {
                        "frameUID": "4fbaff3d-03df-4036-85ed-7006696748e4",
                        "frameType": "modbus"
                    },
                    {
                        "frameUID": "7092beb7-da75-49e3-8ddc-8caaff430335",
                        "frameType": "uart"
                    }
                ],
                "deviceDesc": ""
            },
            "deviceWorkStatus": {
                "healthStatus": "ready",
                "timestamp": **********,
                "workMode": "centralized"
            },
            "deviceResourceInfo": {
                "ip": "*************",
                "hostname": "",
                "type": "",
                "os": "",
                "cpu": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "gpu": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "disk": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "mem": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "protocol": [
                    "modbus",
                    "uart"
                ]
            },
            "frameInfos": [
                {
                    "frameMeta": {
                        "frameUID": "4fbaff3d-03df-4036-85ed-7006696748e4",
                        "frameType": "modbus"
                    },
                    "frameLibs": {
                        "modbusInfo": {
                            "tid": "",
                            "pid": "",
                            "len": "",
                            "uid": "",
                            "fc": "",
                            "datas": null
                        },
                        "uartInfo": {
                            "header": "",
                            "addr": "",
                            "cmd": "",
                            "tail": "",
                            "datas": null
                        }
                    }
                },
                {
                    "frameMeta": {
                        "frameUID": "7092beb7-da75-49e3-8ddc-8caaff430335",
                        "frameType": "uart"
                    },
                    "frameLibs": {
                        "modbusInfo": {
                            "tid": "",
                            "pid": "",
                            "len": "",
                            "uid": "",
                            "fc": "",
                            "datas": null
                        },
                        "uartInfo": {
                            "header": "",
                            "addr": "",
                            "cmd": "",
                            "tail": "",
                            "datas": null
                        }
                    }
                }
            ]
        }
    ]
}
```
测试用例：
```
{
    "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
    "frameMetas": [
        {
            "frameUID": "",
            "frameType": "uart"
        }
    ]
}
```
结果
```
{
    "devices": []
}
```
#### 设备健康检查
测试用例：
```
{
    "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
    "healthStatus": "ready"
}
```
结果
```
{
    "devices": [
        {
            "deviceMeta": {
                "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
                "frameMetas": [],
                "deviceDesc": ""
            },
            "deviceWorkStatus": {
                "healthStatus": "ready",
                "timestamp": **********,
                "workMode": "centralized"
            },
            "deviceResourceInfo": {
                "ip": "ba57:d1fb:a5cf:59e3:5fe3:d7d3:35bd:760c",
                "hostname": "镇帅",
                "type": "edge",
                "os": "",
                "cpu": {
                    "amount": 950,
                    "type": "anim fugiat et qui aute",
                    "unit": "dolore fugiat aute reprehenderit"
                },
                "gpu": {
                    "amount": 669,
                    "type": "do",
                    "unit": "laborum ea Ut aliquip"
                },
                "disk": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "mem": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "protocol": [
                    "https",
                    "https"
                ]
            },
            "frameInfos": []
        }
    ]
}
```
#### 帧创建
测试用例：
```
{
    "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
    "frameInfos": [
        {
            "frameMeta": {
                "frameUID": "",
                "frameType": "modbus"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "1",
                    "pid": "2",
                    "len": "3",
                    "uid": "1",
                    "fc": "2"
                }
            }
        }
    ]
}
```
结果
```
{
    "deviceMeta": {
        "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
        "frameMetas": [
            {
                "frameUID": "4abaffb2-5e1d-4807-9539-01f6808da7b1",
                "frameType": "modbus"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
#### 帧删除
测试用例：
```
{
    "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
    "frameMetas": [
        {
            "frameUID": "4abaffb2-5e1d-4807-9539-01f6808da7b1",
            "frameType": "modbus"
        }
    ]
}
```
结果
```
{
    "deviceMeta": {
        "deviceUID": "18955a4b-28f7-40de-aa45-9a1ec30bbf0d",
        "frameMetas": [
            {
                "frameUID": "4abaffb2-5e1d-4807-9539-01f6808da7b1",
                "frameType": "modbus"
            }
        ],
        "deviceDesc": ""
    },
    "frameInfos": [
        {
            "frameMeta": {
                "frameUID": "4abaffb2-5e1d-4807-9539-01f6808da7b1",
                "frameType": "modbus"
            },
            "frameLibs": {
                "modbusInfo": {
                    "tid": "1",
                    "pid": "2",
                    "len": "3",
                    "uid": "1",
                    "fc": "2",
                    "datas": null
                },
                "uartInfo": {
                    "header": "",
                    "addr": "",
                    "cmd": "",
                    "tail": "",
                    "datas": null
                }
            }
        }
    ],
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
#### 帧查询
测试用例：

    已和设备查询一起测试
结果

# 下期完成
## 数据帧管理
### 二级功能
#### 数据帧内容配置
#### 数据帧内容查看
## 设备和帧管理的修改

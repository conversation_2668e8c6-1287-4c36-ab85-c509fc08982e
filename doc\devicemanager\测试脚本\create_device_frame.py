#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备帧创建脚本
向设备管理器API发送创建设备帧的请求
"""

import json
import requests

# API端点
API_URL = "http://127.0.0.1:8888/api/v1/device/frame/create"

# 构建FrameMeta对象
def create_frame_meta(frame_uid, frame_type):
    return {
        "frameUID": frame_uid,  # 可选字段
        "frameType": frame_type  # 必填字段，可选值：modbus|uart
    }

# 构建ModbusInfo对象
def create_modbus_info(tid, pid, length, uid, fc, datas=None):
    modbus_info = {
        "tid": tid,
        "pid": pid,
        "len": length,
        "uid": uid,
        "fc": fc
    }
    
    if datas:
        modbus_info["datas"] = datas
        
    return modbus_info

# 构建UartInfo对象
def create_uart_info(header, addr, cmd, tail, datas=None):
    uart_info = {
        "header": header,
        "addr": addr,
        "cmd": cmd,
        "tail": tail
    }
    
    if datas:
        uart_info["datas"] = datas
        
    return uart_info

# 构建DataDef对象
def create_data_def(index, name, type_str):
    return {
        "index": index,
        "name": name,
        "type": type_str
    }

# 构建FrameLibs对象
def create_frame_libs(frame_type, **kwargs):
    frame_libs = {}
    
    if frame_type.lower() == "modbus":
        frame_libs["modbusInfo"] = create_modbus_info(**kwargs)
    elif frame_type.lower() == "uart":
        frame_libs["uartInfo"] = create_uart_info(**kwargs)
    
    return frame_libs

# 构建FrameInfo对象
def create_frame_info(frame_meta, frame_libs):
    return {
        "frameMeta": frame_meta,
        "frameLibs": frame_libs
    }

# 构建请求数据
def create_request_data(device_uid, frame_infos):
    # 创建请求数据对象
    request_data = {
        "deviceUID": device_uid,  # 必填字段
        "frameInfos": frame_infos  # 必填字段
    }
    
    return request_data

def main():
    # 获取用户输入的设备UID
    device_uid = input("请输入设备UID: ")
    
    # 询问要创建的帧数量
    frame_count = int(input("请输入要创建的帧数量: "))
    frame_infos = []
    
    for i in range(frame_count):
        print(f"\n帧 #{i+1}:")
        
        # 获取帧元数据
        frame_uid = input("请输入帧UID (可选，直接回车跳过): ")
        frame_type = input("请输入帧类型 (modbus/uart): ").lower()
        
        while frame_type not in ["modbus", "uart"]:
            print("错误: 帧类型必须是 'modbus' 或 'uart'")
            frame_type = input("请重新输入帧类型 (modbus/uart): ").lower()
        
        frame_meta = create_frame_meta(frame_uid, frame_type)
        
        # 根据帧类型获取不同的信息
        if frame_type == "modbus":
            print("\n请输入Modbus信息:")
            tid = input("TID: ")
            pid = input("PID: ")
            length = input("Length: ")
            uid = input("UID: ")
            fc = input("FC: ")
            
            # 询问是否添加数据定义
            add_data = input("是否添加数据定义? (y/n): ").lower() == 'y'
            datas = []
            
            if add_data:
                data_count = int(input("请输入数据定义数量: "))
                
                for j in range(data_count):
                    print(f"数据定义 #{j+1}:")
                    index = input("Index: ")
                    name = input("Name: ")
                    type_str = input("Type: ")
                    datas.append(create_data_def(index, name, type_str))
            
            frame_libs = create_frame_libs(frame_type, tid=tid, pid=pid, length=length, uid=uid, fc=fc, datas=datas if datas else None)
        
        else:  # UART
            print("\n请输入UART信息:")
            header = input("Header: ")
            addr = input("Addr: ")
            cmd = input("Cmd: ")
            tail = input("Tail: ")
            
            # 询问是否添加数据定义
            add_data = input("是否添加数据定义? (y/n): ").lower() == 'y'
            datas = []
            
            if add_data:
                data_count = int(input("请输入数据定义数量: "))
                
                for j in range(data_count):
                    print(f"数据定义 #{j+1}:")
                    index = input("Index: ")
                    name = input("Name: ")
                    type_str = input("Type: ")
                    datas.append(create_data_def(index, name, type_str))
            
            frame_libs = create_frame_libs(frame_type, header=header, addr=addr, cmd=cmd, tail=tail, datas=datas if datas else None)
        
        # 创建帧信息并添加到列表
        frame_infos.append(create_frame_info(frame_meta, frame_libs))
    
    # 创建请求数据
    request_data = create_request_data(device_uid, frame_infos)
    
    # 打印请求数据
    print("\n发送的请求数据:")
    print(json.dumps(request_data, indent=4, ensure_ascii=False))
    
    try:
        # 发送POST请求
        response = requests.post(API_URL, json=request_data)
        
        # 检查响应状态
        if response.status_code == 200:
            # 打印响应数据
            print("\n请求成功! 响应数据:")
            response_json = response.json()
            print(json.dumps(response_json, indent=4, ensure_ascii=False))
        else:
            print(f"\n请求失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
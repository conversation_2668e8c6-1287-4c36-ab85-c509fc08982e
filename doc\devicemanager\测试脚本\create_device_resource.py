#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备资源创建脚本
向设备管理器API发送创建设备资源的请求
"""

import json
import requests

# API端点
API_URL = "http://127.0.0.1:8888/api/v1/device/resource/create"

# 构建ResourceDef对象
def create_resource_def(amount, type_str, unit):
    return {
        "amount": amount,
        "type": type_str,
        "unit": unit
    }

# 构建请求数据
def create_request_data():
    # 创建DeviceResourceInfo对象
    device_resource_info = {
        "ip": "*************",  # 必填字段
        # "hostname": "device-test-001",  # 可选字段
        # "type": "edge",  # 可选字段，可选值：fan|motor|virtual|pc|edge
        # "os": "Linux",  # 可选字段
        # "cpu": create_resource_def(8, "Intel Core i7", "core"),  # 可选字段
        # "gpu": create_resource_def(1, "NVIDIA RTX 3080", "card"),  # 可选字段
        # "disk": create_resource_def(500, "SSD", "GB"),  # 可选字段
        # "mem": create_resource_def(16, "DDR4", "GB"),  # 可选字段
        "protocol": ["modbus", "uart"]  # 必填字段
    }
    
    # 创建完整的请求数据
    request_data = {
        "deviceResourceInfo": device_resource_info
    }
    
    return request_data

def main():
    # 创建请求数据
    request_data = create_request_data()
    
    # 打印请求数据
    print("发送的请求数据:")
    print(json.dumps(request_data, indent=4, ensure_ascii=False))
    
    try:
        # 发送POST请求
        response = requests.post(API_URL, json=request_data)
        
        # 检查响应状态
        if response.status_code == 200:
            # 打印响应数据
            print("\n请求成功! 响应数据:")
            response_json = response.json()
            print(json.dumps(response_json, indent=4, ensure_ascii=False))
        else:
            print(f"\n请求失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备资源删除脚本
向设备管理器API发送删除设备资源的请求
"""

import json
import requests

# API端点
API_URL = "http://127.0.0.1:8888/api/v1/device/resource/delete"

# 构建请求数据
def create_request_data(device_uid):
    # 创建删除请求数据对象
    request_data = {
        "deviceUID": device_uid  # 必填字段，设备唯一标识符
    }
    
    return request_data

def main():
    # 获取用户输入的设备UID
    device_uid = input("请输入要删除的设备UID: ")
    
    # 创建请求数据
    request_data = create_request_data(device_uid)
    
    # 打印请求数据
    print("发送的请求数据:")
    print(json.dumps(request_data, indent=4, ensure_ascii=False))
    
    try:
        # 发送DELETE请求
        response = requests.post(API_URL, json=request_data)
        
        # 检查响应状态
        if response.status_code == 200:
            # 打印响应数据
            print("\n请求成功! 响应数据:")
            response_json = response.json()
            print(json.dumps(response_json, indent=4, ensure_ascii=False))
        else:
            print(f"\n请求失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
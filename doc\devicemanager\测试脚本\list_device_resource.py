#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设备资源查询脚本
向设备管理器API发送查询设备资源的请求
"""

import json
import requests

# API端点
API_URL = "http://127.0.0.1:8888/api/v1/device/device/list"

# 构建FrameMeta对象
def create_frame_meta(frame_uid, frame_type):
    return {
        "frameUID": frame_uid,  # 可选字段
        "frameType": frame_type  # 必填字段，可选值：modbus|uart
    }

# 构建请求数据
def create_request_data(device_uid=None, frame_metas=None, health_status=None):
    # 创建查询请求数据对象
    request_data = {}
    
    # 添加可选参数
    if device_uid:
        request_data["deviceUID"] = device_uid
    
    if frame_metas:
        request_data["frameMetas"] = frame_metas
    
    if health_status:
        request_data["healthStatus"] = health_status
    
    return request_data

def main():
    # 获取用户输入的查询参数
    print("设备查询参数设置 (可选参数可直接按回车跳过)")
    device_uid = input("请输入设备UID (可选): ")
    
    # 询问是否需要添加帧元数据过滤
    add_frame_meta = input("是否添加帧元数据过滤? (y/n): ").lower() == 'y'
    frame_metas = []
    
    if add_frame_meta:
        frame_count = int(input("请输入要添加的帧数量: "))
        for i in range(frame_count):
            print(f"\n帧 #{i+1}:")
            frame_uid = input("请输入帧UID (可选): ")
            frame_type = input("请输入帧类型 (modbus/uart): ")
            frame_metas.append(create_frame_meta(frame_uid, frame_type))
    
    # 询问是否添加健康状态过滤
    health_options = ["init", "ready", "running", "pending", "error"]
    print("\n健康状态选项:")
    for i, option in enumerate(health_options):
        print(f"{i+1}. {option}")
    
    health_choice = input("请选择健康状态 (输入数字或直接回车跳过): ")
    health_status = health_options[int(health_choice)-1] if health_choice.isdigit() and 1 <= int(health_choice) <= len(health_options) else None
    
    # 创建请求数据
    request_data = create_request_data(device_uid, frame_metas if frame_metas else None, health_status)
    
    # 打印请求数据
    print("\n发送的请求数据:")
    print(json.dumps(request_data, indent=4, ensure_ascii=False))
    
    try:
        # 发送POST请求
        response = requests.post(API_URL, json=request_data)
        
        # 检查响应状态
        if response.status_code == 200:
            # 打印响应数据
            print("\n请求成功! 响应数据:")
            response_json = response.json()
            print(json.dumps(response_json, indent=4, ensure_ascii=False))
        else:
            print(f"\n请求失败! 状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
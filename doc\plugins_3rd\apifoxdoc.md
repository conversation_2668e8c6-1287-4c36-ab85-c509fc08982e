# Apifox操作文档

## 创建接口
点击对应目录后出现加号点击“创建接口”即可创建新的接口。 \
\
![](/Fig/apifox/figure1.png)

如点击该“新建接口”，则在Devicemanager目录下创建新的接口。

## 修改接口
创建新的接口后，需要完善接口信息，根据已有的api信息完善apifox内具体参数设置。
### 状态参数
保持默认即可，状态选择“开发中”，说明一栏简单说明api功能即可。
![](/Fig/apifox/figure2.png)
### 请求参数
所有参数从Body/json一栏输入：
![](/Fig/apifox/figure3.png)
其后根据需求参数选择相应变量类型即可。需要注意的是几类变量的定义方式。
#### array
定义array，首先需要在变量栏选择array项，选择完成后改栏会自动弹出新的子栏，可在该子栏中定义array变量内的具体变量类型。
如定义变量名为name_list的array变量，该array是关于string的数组。
![](/Fig/apifox/figure4.png)
#### enum
定义枚举类变量enum，点击变量类型右侧的高级功能按钮勾选枚举选择栏即可。
![](/Fig/apifox/figure5.png) \
之后枚举值按照api本身的设置填写即可。
#### optional
optional只需关掉上述高级功能中的必需选项即可。
#### 自定义变量
对于自定义变量，需要在数据模型目录下创建对应的自定义变量结构文件，具体操作与创建api相同。完成相关变量定义即可，定义完成后，变量类型栏会自行更新该变量。\
变量类型选择引用模型便可导入自定义模型。\
![](/Fig/apifox/figure6.png)
### 返回参数
返回参数定义的具体操作与请求参数一致。
## 查看接口信息
点击接口一栏，即可当前接口信息。 \
![](/Fig/apifox/figure7.png)
## 运行接口
运行接口首先需在apifox界面右上角将环境选择为“测试环境”。 \
![](/Fig/apifox/figure8.png) \
之后点击“运行”一栏，即进入测试界面。 \ 
![](/Fig/apifox/figure9.png) \
之后完成post设置后与后端连接后，可以开始进行测试，同样选择Body、json，点击“自动生成”，便可生成预定义类型相同的输入变量，点击“发送”，便开始测试。\
![](/Fig/apifox/figure10.png) \
测试结果会在下侧出现，其中200表示成功，其他均表示失败。\
![](/Fig/apifox/figure11.png)
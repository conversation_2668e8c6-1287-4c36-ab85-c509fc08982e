# LLaMA Factory WebUI 使用指南

## 简介
LLaMA Factory 是一个强大的模型微调工具，提供了友好的WebUI界面，支持多种模型的训练和推理。本指南将介绍如何使用LLaMA Factory的主要功能。

## 功能特点
- 支持多种基础模型（LLaMA、Qwen、Baichuan等）
- 提供多种训练方法（LoRA、QLoRA、全参数微调等）
- 简单易用的WebUI界面
- 支持数据集处理和模型评估

## 使用步骤

### 1. 启动WebUI
```
llamafactory-cli webui
```
![WebUI](/Fig/llama/llama_factory_webui.png)

### 2. 数据准备
1. 在WebUI中选择"data"标签页
2. 选择数据模板或自定义数据格式
3. 预览和验证数据集


### 3. 模型训练
1. 切换到"训练"标签页
2. 选择基础模型
3. 配置训练参数：
   - 训练方法（LoRA/QLoRA等）
   - 学习率
   - 批次大小
   - 训练轮数
4. 开始训练并监控进度

### 4. 模型评估
1. 进入"评估"标签页
2. 加载训练好的模型
3. 使用测试集进行评估
4. 查看评估指标和结果

### 5. 模型推理
1. 选择"推理"标签页
2. 加载模型
3. 设置生成参数
4. 输入提示进行测试

## 注意事项
- 确保有足够的GPU内存
- 定期保存训练检查点
- 合理设置训练参数
- 注意数据集质量和格式

## 常见问题解决
1. 内存不足：
   - 减小批次大小
   - 使用QLoRA等低资源方案
   
2. 训练不稳定：
   - 调整学习率
   - 检查数据质量
   - 增加预热步数

3. 生成效果不佳：
   - 检查训练数据质量
   - 调整生成参数
   - 增加训练轮数
## Demo


### 界面预览
![预览](/Fig/llama/LlamaBoard_1.png)

### 语言及模型选择
![语言及模型选择](/Fig/llama/LlamaBoard_2.png)
 - 选择语言及选择模型名称，输入模型绝对路径

### 微调方法及检查点路径
![微调方法及检查点路径](/Fig/llama/LlamaBoard_3.png)
- 选择微调方法及检查点路径

###  训练方式及数据集选择
![训练方式及数据集选择](/Fig/llama/LlamaBoard_4.png)
![训练方式及数据集选择](/Fig/llama/LlamaBoard_8.png)
- 选择训练方式及复制数据集路径，并选择数据集。点击右侧预览数据集可进行预览。
### 训练参数设置
![训练参数设置](/Fig/llama/LlamaBoard_5.png)
- 设置训练参数

### 点击开始训练
![开始训练](/Fig/llama/LlamaBoard_9.png)

### 输出目录及配置路径
![输出目录及配置路径](/Fig/llama/LlamaBoard_6.png)
- 设置输出目录及配置路径

### 设备数量及DeepSpeed stage设置
![设备数量及DeepSpeed stage设置](/Fig/llama/LlamaBoard_7.png)

## 参考资源
- [LLaMA Factory GitHub](https://github.com/hiyouga/LLaMA-Factory)
- [详细文档](https://github.com/hiyouga/LLaMA-Factory/wiki)

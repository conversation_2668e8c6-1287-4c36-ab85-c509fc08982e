# 如何增删改数据库表

项目的数据库建模使用[ent](https://github.com/ent/ent/blob/master/README_zh.md),是一个简单而又功能强大的Go语言实体框架，ent易于构建和维护应用程序与大数据模型。

进入每个项目目录的scripts目录下有3个与数据库相关的脚本，即create_schema.sh创建schema、migrate_schema.sh修改schema和start_migration_db.sh启动本地db副本。下面介绍如何使用
## 目录
1、新建数据表
2、更新表结构
3、操作流程


## 新建数据表

create_schema.sh脚本的作用是新建一个表,使用时需要标明新建表名称(首字母大写)
```
cd app/xxx/scripts
bash create_schema.sh Test_create_db
```
然后在ent/schema目录下生成了一个文件test_create_db.go
```
package schema

import "entgo.io/ent"

// Test_create_db holds the schema definition for the Test_create_db entity.
type Test_create_db struct {
	ent.Schema
}

// Fields of the Test_create_db.
func (Test_create_db) Fields() []ent.Field {
	return nil
}

// Edges of the Test_create_db.
func (Test_create_db) Edges() []ent.Edge {
	return nil
}
```
对Fields修改即修改了表结构
```
// Fields of the Test_create_db.
func (Test_create_db) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").
			NotEmpty().
			Unique().
			Immutable(),

		field.String("name"),
		field.Time("create_at").
			Default(time.Now).
			Immutable(),
		field.Float32("price").Comment("unit price of the machine"),
	}
}
```
## 更新表结构

对项目表更新时本地只能修改本地数据库，如果同步到云端，并处理其他人的冲突是个严峻的挑战。幸运的是，软件工程领域已经有很多解决方案，我们采用flyway。

Flyway是一款开源的数据库版本管理工具，可以实现管理并跟踪数据库变更，支持数据库版本自动升级，而且不需要复杂的配置，能够帮助团队更加方便、合理的管理数据库变更。
例：创建两个sql变更文件，项目启动后会将两个文件中的sql语句全部执行。

首先启动一个本地数据库(docker),以导入之前所有的flyway历史
```
cd app/xxx/scripts
bash scripts/start_migration_db.sh #不要用git bash运行，有bug
```
可以看到日志，说明导入了一个历史表结构
```
## start container migration-postgres, waiting...
## create schema from /mnt/c/Users/<USER>/Desktop/Generative-Control-Foundation/Backend/app/Devicemanager/db/migrations/postgres
WARNING: Storing migrations in 'sql' is not recommended and default scanning of this location may be deprecated in a future release
WARNING: This version of Flyway is out of date. Upgrade to Flyway 11.2.0: https://rd.gt/3rXiSlV

Flyway OSS Edition 10.0.1 by Redgate

See release notes here: https://rd.gt/416ObMi
1 SQL migrations were detected but not run because they did not follow the filename convention.
Set 'validateMigrationNaming' to true to fail fast and see a list of the invalid file names.
Database: ****************************************************** (PostgreSQL 15.8)
Schema history table "public"."flyway_schema_history" does not exist yet
Successfully validated 1 migration (execution time 00:00.020s)
Creating Schema History table "public"."flyway_schema_history" ...
Current version of schema "public": << Empty Schema >>
Migrating schema "public" to version "20250127064626 - test"
Successfully applied 1 migration to schema "public", now at version v20250127064626 (execution time 00:00.020s)

## postgres is ready on localhost:15432
PGPASSWORD=pass psql -h localhost -U postgres -d migration -p 15432
## to remove the container, run:
docker stop migration-postgres
```
修改ent/schema/xxx.go后需要生成sql语句，以实现表结构的修改.xxx是生成sql语句的文件标题，请描述这一步做了什么，如add pvcname等。全部小写
```
bash migrate_schema.sh xxx
```
运行后可以发现路径\app\xxxmanager\db\migrations\postgres下生成了2个文件
```
├── db
│   ├── generate.go
│   └── migrations
│       └── postgres
│           ├── U20250127064626__test.sql
│           └── V20250127064626__test.sql
```
U开头是删除之前同名列、V开头是添加新列
## 操作流程
#### 启动本地数据
1、cd到对应微服务目录
2、bash start_migration_db.sh
#### 更新表结构
3、修改ent/schema/xxx.go
4、bash migrate_schema.sh commit
5、docker stop migration-postgres
6、bash start_migration_db.sh
# 如何使用generate脚本

进入每个项目目录均有一个generate.sh脚本(如果没有请在其他项目cp过来)，脚本的作用是自动生成项目脚手架，以提高开发人员效率，不必处理与业务无关的代码(服务注册、类型定义、流量控制等)
## 目录
1脚本结构
2开始新项目
3修改项目


## 脚本结构

脚本分为3部分:API部分为生成API代码，包括路由、流量控制、类型定义等;RPC部分生成RPC代码，包括服务注册、客户端-服务器main函数等;mock部分生成测试代码base函数。

```
#!/usr/bin/env bash
# set -x             # for debug
set -euo pipefail  # fail early
SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# CHANGE THIS: If we move the script.
SERVICE_ROOT=$SCRIPT_DIR

cd "$SERVICE_ROOT"

# Generate API
echo "Generating API in $(pwd)"
goctl api go -api api/device.api -dir .

# Generate RPC
echo "Generating RPC in $(pwd)"
goctl rpc protoc rpc/device.proto \
  --go_out="$(pwd)/internal" \
  --go-grpc_out="$(pwd)/internal" \
  --zrpc_out="$(pwd)"

# Generate xxx mock,xxx替换为自己的测试目录位置
pushd xxx/ > /dev/null
  mkdir -p mock
  mockgen -destination mock/xxx_mock.go -package mock . xxx
popd > /dev/null
```

## 开始新项目

启动新项目时路径需要包含如下结构：
```
├── api
│   └── device.api #api文件
├── rpc
│   └── device.proto #rpc文件
│   #通用脚本粘贴即可
├── generate.sh
└── scripts
    ├── build.sh
    ├── create_schema.sh
    ├── docs.sh
    ├── launch.sh
    ├── launch_api.sh
    ├── migrate_schema.sh
    └── start_migration_db.sh
```

编写好api、rpc文件后，修改脚本mock路径，运行脚本
```
bash ./generate.sh
```
运行后
```
├── api
│   └── device.api
├── device 
│   ├── device.go #生成的grpc-client
│   └── mock #生成的测试文件
│       └── device_mock.go
├── device.go #入口文件main.go
├── etc #配置文件
│   ├── device-api.yaml
│   └── device.yaml
├── internal #内部逻辑
│   ├── config #配置定义
│   │   └── config.go
│   ├── devicemanager #grpc存根
│   │   ├── device.pb.go
│   │   └── device_grpc.pb.go
│   ├── handler #路由处理
│   │   ├── listcommondatahandler.go
│   │   └── routes.go
│   ├── logic #api和rpc逻辑代码（需要开发人员实现的）
│   │   ├── getcommondatapvclogic.go
│   │   ├── getregionuserdatalimitlogic.go
│   │   ├── getuserdatapvclogic.go
│   │   └── listcommondatalogic.go
│   ├── server #grpc-server
│   │   └── deviceserver.go
│   ├── svc  #上下文
│   │   └── servicecontext.go
│   └── types #类型定义文件
│       └── types.go
├── rpc
│   └── device.proto
├── generate.sh
└── scripts
    ├── create_schema.sh
    ├── launch.sh
    ├── launch_api.sh
    ├── migrate_schema.sh
    └── start_migration_db.sh
```
## 修改项目

当api、rpc变动时，需要重新生成脚手架代码，只需要修改对应api/proto文件后运行脚本即可
```
bash ./generate.sh
```

## 数据库交互

涉及到数据库时需要导入表结构和生成增删改查的代码,需要新建ent目录，具体参看how to CRUD schema.md文档。新结构如下
```
├── api
│   └── device.api
├── db
│   ├── generate.go
│   └── migrations
│       └── postgres
│           ├── U20250127064626__test.sql
│           └── V20250127064626__test.sql
├── device
│   ├── device.go
│   └── mock
│       └── device_mock.go
├── device.go
├── etc
│   ├── device-api.yaml
│   └── device.yaml
├── generate.sh
├── internal
│   ├── config
│   │   └── config.go
│   ├── devicemanager
│   │   ├── device.pb.go
│   │   └── device_grpc.pb.go
│   ├── ent #数据库相关
│   │   ├── client.go
│   │   ├── ent.go
│   │   ├── enttest
│   │   │   └── enttest.go
│   │   ├── hook
│   │   │   └── hook.go
│   │   ├── migrate
│   │   │   ├── migrate.go
│   │   │   └── schema.go
│   │   ├── mutation.go
│   │   ├── predicate
│   │   │   └── predicate.go
│   │   ├── runtime
│   │   │   └── runtime.go
│   │   ├── runtime.go
│   │   ├── schema
│   │   │   └── test_create_db.go
│   │   ├── test_create_db
│   │   │   ├── test_create_db.go
│   │   │   └── where.go
│   │   ├── test_create_db.go
│   │   ├── test_create_db_create.go
│   │   ├── test_create_db_delete.go
│   │   ├── test_create_db_query.go
│   │   ├── test_create_db_update.go
│   │   └── tx.go
│   ├── handler
│   │   ├── listcommondatahandler.go
│   │   └── routes.go
│   ├── logic
│   │   ├── getcommondatapvclogic.go
│   │   ├── getregionuserdatalimitlogic.go
│   │   ├── getuserdatapvclogic.go
│   │   └── listcommondatalogic.go
│   ├── server
│   │   └── deviceserver.go
│   ├── svc
│   │   └── servicecontext.go
│   └── types
│       └── types.go
├── rpc
│   └── device.proto
└── scripts
    ├── create_schema.sh
    ├── launch.sh
    ├── launch_api.sh
    ├── migrate_schema.sh
    └── start_migration_db.sh
```
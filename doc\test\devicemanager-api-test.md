# devicemanager-api-test
测试日期：2025-2-16

测试人：高明

后端负责人：韩梦琦

测试目标：api接口的5个函数功能正常

## 注意
data相关可为空，因为后端尚未支持

## CreateDeviceResourceLogic
### http状态测试
#### 正例http200测试
1.input
```
{
    "deviceResourceInfo": {
        "ip": "*************",
        "hostname": "性超",
        "type": "virtual",
        "os": "Lorem",
        "cpu": {
            "amount": 20,
            "type": "incididunt laborum Duis amet ut",
            "unit": "do"
        },
        "gpu": {
            "amount": 477,
            "type": "culpa",
            "unit": "enim aliqua fugiat qui"
        },
        "disk": {
            "amount": 608,
            "type": "voluptate in dolor",
            "unit": "aliquip id proident eiusmod"
        },
        "mem": {
            "amount": 64,
            "type": "esse",
            "unit": "mollit"
        },
        "protocol": [
            "modbus"
        ]
    }
}
```
2.output
```
{
    "deviceMeta": {
        "deviceUID": "e54ced68-ec5e-11ef-a3da-525400da5d1d",
        "frameInfos": [
            {
                "frameUID": "e54ced68-ec5e-11ef-a3da-525400da5d1d",
                "frameType": "modbus"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```

#### 反例http400测试
1.input
```
{
    "deviceResourceInfo": {
        "ip": "*************",
        "hostname": 123,
        "type": "virtual",
        "os": "Lorem",
        "cpu": {
            "amount": 20,
            "type": "incididunt laborum Duis amet ut",
            "unit": "do"
        },
        "gpu": {
            "amount": 477,
            "type": "culpa",
            "unit": "enim aliqua fugiat qui"
        },
        "disk": {
            "amount": 608,
            "type": "voluptate in dolor",
            "unit": "aliquip id proident eiusmod"
        },
        "mem": {
            "amount": 64,
            "type": "esse",
            "unit": "mollit"
        },
        "protocol": [
            "modbus"
        ]
    }
}
```
2.output \
400: type mismatch for field "deviceResourceInfo.hostname", expect "string", actual "number"

### 接口类型测试
#### 反例（string->int/bool）
1.input
```
{
    "deviceResourceInfo": {
        "ip": "d371:b0c1:03bf:df7f:e74b:ab1c:c343:91c0",
        "hostname": 123,
        "type": "virtual",
        "os": "ad voluptate in",
        "cpu": {
            "amount": 383,
            "type": "in est laborum",
            "unit": "pariatur Excepteur dolor"
        },
        "gpu": {
            "amount": 782,
            "type": "sed ipsum adipisicing veniam",
            "unit": "qui"
        },
        "disk": {
            "amount": 335,
            "type": "sint velit do elit",
            "unit": "cupidatat in aliqua Lorem"
        },
        "mem": {
            "amount": 361,
            "type": "laborum ad eu in non",
            "unit": "commodo"
        },
        "protocol": [
            "uart"
            
        ]
    }
}
```
2.output \
400: type mismatch for field "deviceResourceInfo.hostname", expect "string", actual "number"

#### 反例（枚举其他值）
1.input
```
{
    "deviceResourceInfo": {
        "ip": "*************",
        "hostname": "须建军",
        "type": "virtual",
        "os": "Lorem adipisicing esse nulla",
        "cpu": {
            "amount": 168,
            "type": "ut minim",
            "unit": "esse"
        },
        "gpu": {
            "amount": 747,
            "type": "reprehenderit",
            "unit": "ut aliqua pariatur eiusmod"
        },
        "disk": {
            "amount": 790,
            "type": "ex dolor nostrud consectetur",
            "unit": "nisi consequat id"
        },
        "mem": {
            "amount": 819,
            "type": "ullamco elit proident",
            "unit": "sunt elit"
        },
        "protocol": [
            "topo"
        ]
    }
}
```
2.output 
```
{
    "deviceMeta": {
        "deviceUID": "b6803d32-ec61-11ef-a3da-525400da5d1d",
        "frameInfos": [
            {
                "frameUID": "b6803d32-ec61-11ef-a3da-525400da5d1d",
                "frameType": "topo"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
200
#### 反例(int->string)
1.input
```
{
    "deviceResourceInfo": {
        "ip": "*************",
        "hostname": "须建军",
        "type": "virtual",
        "os": "Lorem adipisicing esse nulla",
        "cpu": {
            "amount": "iam",
            "type": "ut minim",
            "unit": "esse"
        },
        "gpu": {
            "amount": 747,
            "type": "reprehenderit",
            "unit": "ut aliqua pariatur eiusmod"
        },
        "disk": {
            "amount": 790,
            "type": "ex dolor nostrud consectetur",
            "unit": "nisi consequat id"
        },
        "mem": {
            "amount": 819,
            "type": "ullamco elit proident",
            "unit": "sunt elit"
        },
        "protocol": [
            "uart"
        ]
    }
}
```
2.output \
400: type mismatch for field "deviceResourceInfo.cpu.amount"

### 接口功能测试测试
#### 正例（protocol->null）
```
{
    "deviceResourceInfo": {
        "ip": "**************",
        "hostname": "剑语汐",
        "type": "fan",
        "os": "minim",
        "cpu": {
            "amount": 67,
            "type": "nostrud sunt enim proident",
            "unit": "incididunt nisi enim"
        },
        "gpu": {
            "amount": 222,
            "type": "eu culpa",
            "unit": "voluptate in irure id"
        },
        "disk": {
            "amount": 361,
            "type": "dolor consectetur occaecat",
            "unit": "Lorem nulla tempor"
        },
        "mem": {
            "amount": 675,
            "type": "Duis ad mollit eiusmod",
            "unit": "incididunt cillum"
        },
        "protocol": [
            ""
        ]
    }
}
```
2.output
```
{
    "deviceMeta": {
        "deviceUID": "1d976017-ec68-11ef-9500-525400da5d1d",
        "frameInfos": null,
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
#### 正例(type->"motor")
1.input
```
{
    "deviceResourceInfo": {
        "ip": "**************",
        "hostname": "剑语汐",
        "type": "motor",
        "os": "minim",
        "cpu": {
            "amount": 67,
            "type": "nostrud sunt enim proident",
            "unit": "incididunt nisi enim"
        },
        "gpu": {
            "amount": 222,
            "type": "eu culpa",
            "unit": "voluptate in irure id"
        },
        "disk": {
            "amount": 361,
            "type": "dolor consectetur occaecat",
            "unit": "Lorem nulla tempor"
        },
        "mem": {
            "amount": 675,
            "type": "Duis ad mollit eiusmod",
            "unit": "incididunt cillum"
        },
        "protocol": [
            ""
        ]
    }
}
```
2.ouput
```
{
    "deviceMeta": {
        "deviceUID": "6da4c493-ec68-11ef-9500-525400da5d1d",
        "frameInfos": [
            {
                "frameUID": "6da4c493-ec68-11ef-9500-525400da5d1d",
                "frameType": ""
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
#### 反例（type->"byebye"）
1.input
```
{
    "deviceResourceInfo": {
        "ip": "**************",
        "hostname": "剑语汐",
        "type": "byebye",
        "os": "minim",
        "cpu": {
            "amount": 67,
            "type": "nostrud sunt enim proident",
            "unit": "incididunt nisi enim"
        },
        "gpu": {
            "amount": 222,
            "type": "eu culpa",
            "unit": "voluptate in irure id"
        },
        "disk": {
            "amount": 361,
            "type": "dolor consectetur occaecat",
            "unit": "Lorem nulla tempor"
        },
        "mem": {
            "amount": 675,
            "type": "Duis ad mollit eiusmod",
            "unit": "incididunt cillum"
        },
        "protocol": [
            ""
        ]
    }
}
```
2.output \
400: error: value "byebye" is not defined in options "[fan motor virtual pc edge]"

## DeleteDeviceResourcelogic
### http状态测试
#### 正例http200
1.input
```
{
    "deviceUID": "fe18f6d9-ec64-11ef-9500-525400da5d1d"
}
```
2.output
```
{
    "deviceMeta": {
        "deviceUID": "fe18f6d9-ec64-11ef-9500-525400da5d1d",
        "frameInfos": [
            {
                "frameUID": "fe18f6d9-ec64-11ef-9500-525400da5d1d",
                "frameType": "uart"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    },
    "deviceResourceInfo": {
        "ip": "**************",
        "hostname": "剑语汐",
        "type": "fan",
        "os": "minim",
        "cpu": {
            "amount": 0,
            "type": "nostrud sunt enim proident",
            "unit": ""
        },
        "gpu": {
            "amount": 0,
            "type": "eu culpa",
            "unit": ""
        },
        "disk": {
            "amount": 0,
            "type": "dolor consectetur occaecat",
            "unit": ""
        },
        "mem": {
            "amount": 0,
            "type": "",
            "unit": ""
        },
        "protocol": [
            "uart"
        ]
    }
}
```
#### 反例http400
1.input
```
{
    "deviceUID": "1"
}
```
2.output \ 
400: ent: device not found

### 接口类型测试
#### 反例(string-> int)
1.input
```
{
    "deviceUID": 1233
}
```
2.output \
400: type mismatch for field "deviceUID", expect "string", actual "number"

## CreateDeviceFrameLogic
### http状态测试
#### 正例http200
1.input 
```
{
    "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
    "modbusInfo": {
        "tid": "7",
        "pid": "49",
        "len": "ipsum nulla veniam",
        "uid": "42",
        "fc": "do incididunt quis",
        "datas": [
            {
                "index": "sed aliqua velit dolor",
                "name": "希洁",
                "type": "sint fugiat nisi"
            }
        ]
    },
    "uartInfo": {
        "header": "aliqua",
        "addr": "exercitation incididunt in in",
        "cmd": "cillum",
        "tail": "quis reprehenderit proident aute",
        "datas": [
            {
                "index": "dolore commodo velit consectetur non",
                "name": "巫静",
                "type": "pariatur"
            },
            {
                "index": "veniam exercitation commodo Excepteur",
                "name": "义国良",
                "type": "Duis"
            },
            {
                "index": "velit consectetur in",
                "name": "殳玉英",
                "type": "Duis reprehenderit laboris"
            }
        ]
    }
}
```
2.output
```
{
    "deviceMeta": {
        "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
        "frameInfos": [
            {
                "frameUID": "68724163-ec64-11ef-a859-525400da5d1d",
                "frameType": "modbus"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
#### 反例http400
1.input
```
{
    "deviceUID": "61",
    "modbusInfo": {
        "tid": "7",
        "pid": "49",
        "len": "ipsum nulla veniam",
        "uid": "42",
        "fc": "do incididunt quis",
        "datas": [
            {
                "index": "sed aliqua velit dolor",
                "name": "希洁",
                "type": "sint fugiat nisi"
            }
        ]
    },
    "uartInfo": {
        "header": "aliqua",
        "addr": "exercitation incididunt in in",
        "cmd": "cillum",
        "tail": "quis reprehenderit proident aute",
        "datas": [
            {
                "index": "dolore commodo velit consectetur non",
                "name": "巫静",
                "type": "pariatur"
            },
            {
                "index": "veniam exercitation commodo Excepteur",
                "name": "义国良",
                "type": "Duis"
            },
            {
                "index": "velit consectetur in",
                "name": "殳玉英",
                "type": "Duis reprehenderit laboris"
            }
        ]
    }
}
```
2.output \
400:ent: device not found

### 接口类型测试
#### 反例(string->int\bool)
1.input
```
{
    "deviceUID": "dbcc014b-ec66-11ef-9a9b-40c2ba5970a7",
    "modbusInfo": {
        "tid": "7",
        "pid": 1,
        "len": "ipsum nulla veniam",
        "uid": "42",
        "fc": "do incididunt quis",
        "datas": [
            {
                "index": "sed aliqua velit dolor",
                "name": "希洁",
                "type": "sint fugiat nisi"
            }
        ]
    },
    "uartInfo": {
        "header": "aliqua",
        "addr": "exercitation incididunt in in",
        "cmd": "cillum",
        "tail": "quis reprehenderit proident aute",
        "datas": [
            {
                "index": "dolore commodo velit consectetur non",
                "name": "巫静",
                "type": "pariatur"
            },
            {
                "index": "veniam exercitation commodo Excepteur",
                "name": "义国良",
                "type": "Duis"
            },
            {
                "index": "velit consectetur in",
                "name": "殳玉英",
                "type": "Duis reprehenderit laboris"
            }
        ]
    }
}
```
2.output \
400: type mismatch for field "modbusInfo.pid", expect "string", actual "number"













#### 反例(string->array)
1.input
```
{
    "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
    "modbusInfo": {
        "tid": [1,1],
        "pid": 1,
        "len": "ipsum nulla veniam",
        "uid": "42",
        "fc": "do incididunt quis",
        "datas": [
            {
                "index": "sed aliqua velit dolor",
                "name": "希洁",
                "type": "sint fugiat nisi"
            }
        ]
    },
    "uartInfo": {
        "header": "aliqua",
        "addr": "exercitation incididunt in in",
        "cmd": "cillum",
        "tail": "quis reprehenderit proident aute",
        "datas": [
            {
                "index": "dolore commodo velit consectetur non",
                "name": "巫静",
                "type": "pariatur"
            },
            {
                "index": "veniam exercitation commodo Excepteur",
                "name": "义国良",
                "type": "Duis"
            },
            {
                "index": "velit consectetur in",
                "name": "殳玉英",
                "type": "Duis reprehenderit laboris"
            }
        ]
    }
}
```
2.output \
400: type mismatch for field "modbusInfo.tid"
#### 反例(move out modbusInfo)
1.input
```
{
    "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
    "uartInfo": {
        "header": "aliqua",
        "addr": "exercitation incididunt in in",
        "cmd": "cillum",
        "tail": "quis reprehenderit proident aute",
        "datas": [
            {
                "index": "dolore commodo velit consectetur non",
                "name": "巫静",
                "type": "pariatur"
            },
            {
                "index": "veniam exercitation commodo Excepteur",
                "name": "义国良",
                "type": "Duis"
            },
            {
                "index": "velit consectetur in",
                "name": "殳玉英",
                "type": "Duis reprehenderit laboris"
            }
        ]
    }
}
```
2.output \
400:invalid data fields: empty input data

#### 正例(move out uartbusInfo)
1.input
```
{
    "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
    "modbusInfo": {
        "tid": "1",
        "pid": "413",
        "len": "ipsum nulla veniam",
        "uid": "42",
        "fc": "do incididunt quis",
        "datas": [
            {
                "index": "sed aliqua velit dolor",
                "name": "希洁",
                "type": "sint fugiat nisi"
            }
        ]
    }
}
```
2.output
```
{
    "deviceMeta": {
        "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
        "frameInfos": [
            {
                "frameUID": "68724163-ec64-11ef-a859-525400da5d1d",
                "frameType": "modbus"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
## DeleteDeviceFrameLogic
### http状态测试
#### 正例http200
1.input
```
{
    "deviceUID": "dbcc014b-ec66-11ef-9a9b-40c2ba5970a7",
    "frameType": "modbus"
}
```
2.output
```
{
    "deviceMeta": {
        "deviceUID": "dbcc014b-ec66-11ef-9a9b-40c2ba5970a7",
        "frameInfos": [
            {
                "frameUID": "dbcc014b-ec66-11ef-9a9b-40c2ba5970a7",
                "frameType": "modbus"
            }
        ],
        "deviceDesc": ""
    },
    "deviceWorkStatus": {
        "healthStatus": "ready",
        "timestamp": **********,
        "workMode": "centralized"
    }
}
```
#### 反例http400
1.input
```
{
    "deviceUID": "dbcc014b-ec66-11ef-9a9b-40c2ba5970a7",
    "frameType": ""
}
```
2.output \
400: error: value "" is not defined in options "[modbus uart]"

### 接口类型测试
#### 反例（string->int/bool）
1.input
```
{
    "deviceUID": 1,
    "frameType": "modbus"
}
```
2.output \
400: type mismatch for field "deviceUID", expect "string", actual "number"
#### 反例（string->int/bool）
1.output
```
{
    "deviceUID": "dbcc014b-ec66-11ef-9a9b-40c2ba5970a7",
    "frameType": 1
}
```
2.output
400: error: value "1" is not defined in options "[modbus uart]"
#### 反例(string->array)
1.input
```
{
    "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
    "frameType": [1,2]
}
```
2.output \
400: type mismatch for field "frameType"
#### 反例(enum->"gulf")
1.input
```
{
    "deviceUID": "68724163-ec64-11ef-a859-525400da5d1d",
    "frameType": "gulf"
}
```
2.output \
400: error: value "gulf" is not defined in options "[modbus uart]"
## ListDeviceLogic
### http状态测试
#### 正例http200
1.input
```
{
    "deviceUID": "3f9f2cd7-ec65-11ef-ba06-40c2ba5970a7",
    "frameType": "modbus",
    "healthStatus": "running"
}
```
2.output
```
{
    "devices": []
}
```
#### 反例http400
1.input
```
{
    "deviceUID": "3f9f2cd7-ec65-11ef-ba06-40c2ba5970a7",
    "frameType": "star",
    "healthStatus": "running"
}
```
2.output \
400: error: value "star" is not defined in options "[modbus uart]"

### 接口类型测试
#### 反例（string->int/bool）
1.input
```
{
    "deviceUID": 114514,
    "frameType": "star",
    "healthStatus": "running"
}
```
2.output \
400: type mismatch for field "deviceUID", expect "string", actual "number"
#### 反例 (string->int/bool)
1.input
```
{
    "deviceUID": "3f9f2cd7-ec65-11ef-ba06-40c2ba5970a7",
    "frameType": 1,
    "healthStatus": "running"
}
```
2.output \
400: error: value "1" is not defined in options "[modbus uart]"
#### 反例(string->array)
1.input
```
{
    "deviceUID": [],
    "frameType": "star",
    "healthStatus": "running"
}
```
2.output \
400: type mismatch for field "deviceUID"
#### 反例(string->int/bool)

1.input 
```
{
    "deviceUID": "3f9f2cd7-ec65-11ef-ba06-40c2ba5970a7",
    "frameType": "modbus",
    "healthStatus": 1
}
```
2.output \
400: error: value "1" is not defined in options "[init ready running pending error]"
#### 反例（enum->"sys"）
1.input
```
{
    "deviceUID": "3f9f2cd7-ec65-11ef-ba06-40c2ba5970a7",
    "frameType": "modbus",
    "healthStatus": "sys"
}
```
2.output \
400: error: value "sys" is not defined in options "[init ready running pending error]"
#### 反例 (enum->"asd")
1.input
```
{
    "deviceUID": "3f9f2cd7-ec65-11ef-ba06-40c2ba5970a7",
    "frameType": "asd",
    "healthStatus": "running"
}
```
2.output \
400: error: value "asd" is not defined in options "[modbus uart]"

### 接口功能测试
#### 正例（matched state param）
1.input
```
{
    "deviceUID": "3f7dd1e1-ecef-11ef-8f38-40c2ba5970a7",
    "frameType": "modbus",
    "healthStatus": "ready"
}
```
2.output
```
{
    "devices": [
        {
            "deviceMeta": {
                "deviceUID": "3f7dd1e1-ecef-11ef-8f38-40c2ba5970a7",
                "frameInfos": null,
                "deviceDesc": ""
            },
            "deviceWorkStatus": {
                "healthStatus": "ready",
                "timestamp": **********,
                "workMode": "centralized"
            },
            "deviceResourceInfo": {
                "ip": "**************",
                "hostname": "澹台国华",
                "type": "edge",
                "os": "eu pariatur incididunt",
                "cpu": {
                    "amount": 0,
                    "type": "fugiat dolore minim labore",
                    "unit": ""
                },
                "gpu": {
                    "amount": 0,
                    "type": "enim fugiat nulla ut proident",
                    "unit": ""
                },
                "disk": {
                    "amount": 0,
                    "type": "cupidatat laboris eiusmod incididunt nulla",
                    "unit": ""
                },
                "mem": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "protocol": [
                    "modbus"
                ]
            },
            "modbusInfo": {
                "tid": "",
                "pid": "",
                "len": "",
                "uid": "",
                "fc": "",
                "datas": null
            },
            "uartInfo": {
                "header": "",
                "addr": "",
                "cmd": "",
                "tail": "",
                "datas": null
            }
        }
    ]
}
```
#### 反例（unmatched state param）
1.input
```
{
    "deviceUID": "3f7dd1e1-ecef-11ef-8f38-40c2ba5970a7",
    "frameType": "modbus",
    "healthStatus": "pending"
}
```
2.output \
200
```
{
    "devices": []
}
```
#### 正例(matched frametype param)
1.input
```
{
    "deviceUID": "3f7dd1e1-ecef-11ef-8f38-40c2ba5970a7",
    "frameType": "modbus",
    "healthStatus": "ready"
}
```
2.output
```
{
    "devices": [
        {
            "deviceMeta": {
                "deviceUID": "3f7dd1e1-ecef-11ef-8f38-40c2ba5970a7",
                "frameInfos": null,
                "deviceDesc": ""
            },
            "deviceWorkStatus": {
                "healthStatus": "ready",
                "timestamp": **********,
                "workMode": "centralized"
            },
            "deviceResourceInfo": {
                "ip": "**************",
                "hostname": "澹台国华",
                "type": "edge",
                "os": "eu pariatur incididunt",
                "cpu": {
                    "amount": 0,
                    "type": "fugiat dolore minim labore",
                    "unit": ""
                },
                "gpu": {
                    "amount": 0,
                    "type": "enim fugiat nulla ut proident",
                    "unit": ""
                },
                "disk": {
                    "amount": 0,
                    "type": "cupidatat laboris eiusmod incididunt nulla",
                    "unit": ""
                },
                "mem": {
                    "amount": 0,
                    "type": "",
                    "unit": ""
                },
                "protocol": [
                    "modbus"
                ]
            },
            "modbusInfo": {
                "tid": "",
                "pid": "",
                "len": "",
                "uid": "",
                "fc": "",
                "datas": null
            },
            "uartInfo": {
                "header": "",
                "addr": "",
                "cmd": "",
                "tail": "",
                "datas": null
            }
        }
    ]
}
```
#### 反例(unmatched frametype param)
1.input
```
{
    "deviceUID": "3f7dd1e1-ecef-11ef-8f38-40c2ba5970a7",
    "frameType": "uart",
    "healthStatus": "ready"
}
```
2.output \
200
```
{
    "devices": []
}
```
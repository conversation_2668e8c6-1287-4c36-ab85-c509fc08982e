# Simulink模型控制服务gRPC设计文档

## Simulink模型控制服务gRPC 更新历史

本文记录了Simulink模型控制服务 gRPC 的变更情况。

### 版本：v1

发版日期：2025-04-14

#### 新增

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| InitializeSimulink | 初始化Simulink模型，设置模型类型、目录、名称等信息 | simulink_service.proto |
| RegisterSimulinkParam | 注册Simulink控制参数，更新模型的控制参数列表 | simulink_service.proto |
| RegisterSimulinkVar | 注册Simulink输出变量，更新模型的输出变量列表 | simulink_service.proto |
| RunSimulation | 运行Simulink仿真，设置仿真参数和时间范围 | simulink_service.proto |
| GetSimulinkInfo | 获取Simulink模型信息，包括模型状态和参数信息 | simulink_service.proto |
| GetSimulationResults | 获取Simulink仿真结果，返回输出变量信息 | simulink_service.proto |
| CloseSimulink | 关闭Simulink模型，释放资源 | simulink_service.proto |
| ListAvailableSimulinks | 获取可用Simulink模型列表，返回模型信息和状态 | simulink_service.proto |

#### 更新

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

#### 修复

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

#### 删除

| 功能模块 | 说明 | 相关文档 |
|---------|------|----------|
| 无 | 无 | 无 |

### 已知问题

说明该版本中存在的已知问题。若有多个，以无序列表的形式呈现。
- 暂无对仿真过程的存储和数据存储，下个版本将加入

---

## gRPC 详细说明

### 1. InitializeSimulink

#### 请求参数 (InitializeSimulinkRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_type | string | 模型类型 (e.g., "pressure", "motor") |
| simulink_dir | string | 模型目录 |
| simulink_name | string | 模型名称 |
| init_script | string | 初始化脚本 |
| run_time | float | 仿真时长 |
| control_params | repeated ControlParam | 控制参数列表 |
| output_vars | repeated OutputVar | 输出变量列表 |

#### 响应参数 (InitializeSimulinkResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info | SimulinkInfo | 模型info信息 |

### 2. RegisterSimulinkParam

#### 请求参数 (ResgisterSimulinkParamRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型ID |
| control_params | repeated ControlParam | 控制参数列表 |

#### 响应参数 (RegisterSimulinkParamResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info | SimulinkInfo | 模型info信息 |

### 3. RegisterSimulinkVar

#### 请求参数 (RegisterSimulinkVarRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型ID |
| output_vars | repeated OutputVar | 输出变量列表 |

#### 响应参数 (RegisterSimulinkVarResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info | SimulinkInfo | 模型info信息 |

### 4. RunSimulation

#### 请求参数 (RunSimulationRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型ID |
| start_time | float | 开始时间 |
| stop_time | float | 结束时间 |
| params | repeated ControlParamValue | 仿真参数 |

#### 响应参数 (RunSimulationResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info | SimulinkInfo | 模型info信息 |
| simulink_runtime | SimulinkRuntime | 模型运行信息 |

### 5. GetSimulinkInfo

#### 请求参数 (GetSimulinkInfoRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型ID |

#### 响应参数 (GetSimulinkInfoResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info_and_runtime | SimulinkInfoAndRuntime | 模型info信息 |

### 6. GetSimulationResults

#### 请求参数 (GetSimulationResultsRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型ID |
| var_names | repeated string | 变量名称列表 |

#### 响应参数 (GetSimulationResultsResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| output_vars | repeated OutputVarValue | 输出变量 |

### 7. CloseSimulink

#### 请求参数 (CloseSimulinkRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型ID |

#### 响应参数 (CloseSimulinkResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info | SimulinkInfo | 模型info信息 |

### 8. ListAvailableSimulinks

#### 请求参数 (ListAvailableSimulinksRequest)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_id | string | 模型id |
| simulink_type | string | 模型类型 |
| simulink_status | string | 仿真状态,枚举类型，如"initializing", "ready","running", "stopped", "error" |

#### 响应参数 (ListAvailableSimulinksResponse)

| 参数名 | 类型 | 说明 |
|-------|------|------|
| simulink_info_and_runtime | repeated SimulinkInfoAndRuntime | 模型info信息 |
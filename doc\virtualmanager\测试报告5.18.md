# 信息说明

合并提出日期：2025-5.17

后端开发人：彭骏晖

测试人：彭骏晖

测试目标：虚拟设备管理器基础功能无误


## 测试前配置
```
#启动数据库/用云端数据库
bash Backend\app\Virtualmanager\scripts\start_migration_db.sh

#启动grpc
python Backend\app\Virtualmanager\rpc\python-server\simulink_server.py

#启动api
cd Backend\app\Virtualmanager
go run virtual.go -f etc\virtual-api.yaml
```

# 测试任务
#### 设备创建
测试用例：
```
{
    "deviceName": "电机1号",
    "deviceType": "Motor",
    "config": [
        {
            "paramName": "U",
            "value": "220"
        },
        {
            "paramName": "I",
            "value": "10.5"
        },
        {
            "paramName": "speed",
            "value": "666"
        }
    ]
}
```
结果
```
{
    "success": true,
    "message": "Device created successfully",
    "deviceinfo": {
        "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
        "deviceName": "电机1号",
        "deviceType": "Motor",
        "status": "ready",
        "createTime": "2025-05-19 22:06:08"
    },
    "frameinfo": {
        "frameID": "6cdd08ce-61d8-44ad-b480-7ebc53ef0d6a",
        "frametime": "2025-05-19 22:06:08"
    }
}
```

测试用例：
```
{
    "deviceName": "压机1号",
    "deviceType": "Pressure",
    "config": [
        {
            "paramName": "N",
            "value": "220"
        },
        {
            "paramName": "dkljf",
            "value": "10.5"
        },
        {
            "paramName": "speed",
            "value": "666"
        }
    ]
}
```
结果：
```
{
    "success": true,
    "message": "Device created successfully",
    "deviceinfo": {
        "deviceID": "5afc003e-04a9-46da-a13e-825c769cee6d",
        "deviceName": "压机1号",
        "deviceType": "Pressure",
        "status": "ready",
        "createTime": "2025-05-19 22:07:53"
    },
    "frameinfo": {
        "frameID": "7f1e078b-9dc7-4830-b13a-44bdd70c91c8",
        "frametime": "2025-05-19 22:07:53"
    }
}
```

#### 获取设备列表
测试用例
```
{
    "deviceType": "Motor"
}
```
结果：
```
{
    "devices": [
        {
            "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
            "deviceName": "电机1号",
            "deviceType": "Motor",
            "status": "ready",
            "createTime": "2025-05-19 14:06:08.637358 +0000 UTC"
        }
    ],
    "total": 1,
    "message": "success"
}
```
测试用例：
```
{
    "deviceType": ""
}
```
结果：
{
    "devices": [
        {
            "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
            "deviceName": "电机1号",
            "deviceType": "Motor",
            "status": "ready",
            "createTime": "2025-05-19 14:06:08.637358 +0000 UTC"
        },
        {
            "deviceID": "5afc003e-04a9-46da-a13e-825c769cee6d",
            "deviceName": "压机1号",
            "deviceType": "Pressure",
            "status": "ready",
            "createTime": "2025-05-19 14:07:53.209958 +0000 UTC"
        }
    ],
    "total": 2,
    "message": "success"
}

#### 获取设备健康
测试用例
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875"
}
```
结果
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
    "healthStatus": "ready",
    "message": "1970-01-01 00:00:00 +0000 UTC"
}
```


#### 获取设备状态
测试用例
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875"
}
```
结果
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
    "status": "ready",
    "message": ""
}
```

#### 获取设备日志
测试用例
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875"
}
```
结果
```
{
    "message": "ent: device_log not found"
}
暂无日志实现
```

#### 获取设备配置
测试用例
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875"
}
```
结果
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
    "updateTime": "2025-05-19 14:06:08.637358 +0000 UTC",
    "config": [
        {
            "paramName": "U",
            "value": "220"
        },
        {
            "paramName": "I",
            "value": "10.5"
        },
        {
            "paramName": "speed",
            "value": "666"
        }
    ]
}
```

#### 更新设备配置
测试用例
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
    "config": [
        {
            "paramName": "S",
            "value": "111"
        }
    ]
}
```
结果
```
{
    "success": true,
    "updateTime": "2025-05-19 22:19:45",
    "config": [
        {
            "paramName": "U",
            "value": "220"
        },
        {
            "paramName": "I",
            "value": "10.5"
        },
        {
            "paramName": "speed",
            "value": "666"
        },
        {
            "paramName": "S",
            "value": "111"
        }
    ],
    "message": "设备配置更新成功"
}
```

#### 删除设备
测试用例
```
{
    "deviceID": "5afc003e-04a9-46da-a13e-825c769cee6d"
}
```
结果
```
{
    "success": true,
    "message": "设备删除成功",
    "deviceinfo": {
        "deviceID": "5afc003e-04a9-46da-a13e-825c769cee6d",
        "deviceName": "压机1号",
        "deviceType": "Pressure",
        "status": "",
        "createTime": "2025-05-19 14:07:53.209958 +0000 UTC"
    },
    "frameinfo": {
        "frameID": "7f1e078b-9dc7-4830-b13a-44bdd70c91c8",
        "frametime": "2025-05-19 14:07:53.210994 +0000 UTC"
    }
}
```

#### 创建仿真
测试用例
```
{
    "deviceID": "73633799-aaaf-4e24-9003-1df0d2be1875",
    "simulinkDir": "C:/Users/<USER>/Desktop/Generative-Control-Foundation/Backend/simulink/ElectricalModel",
    "simulinkName": "FOCsimulation",
    "simulateTime": "10",
    "initScript": "Motor_script",
    "params": [
        {
            "paramName": "ref",
            "blockPath": "Input/Constant",
            "paramType": "value",
            "defaultValue": "300",
            "description": "电机转速参考值",
            "writable": true
        }
    ],
    "outVars": [
        {
            "varName": "speed",
            "matlabVar": "speed",
            "description": "speed",
            "readable": true
        }
    ]
}
```
结果
```
{
    "message": "Successfully create simuliation",
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "10",
        "status": "ready",
        "controlParam": [
            {
                "paramName": "ref",
                "blockPath": "Input/Constant",
                "paramType": "value",
                "defaultValue": "300",
                "description": "电机转速参考值",
                "writable": true
            }
        ],
        "outputVar": [
            {
                "varName": "speed",
                "matlabVar": "speed",
                "description": "speed",
                "readable": true
            }
        ]
    }
}
```

#### 获取仿真信息
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576"
}
```
结果
```
{
    "info": {
        "info": {
            "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
            "simulateType": "motor",
            "simulateTime": "10.00",
            "status": "stopped",
            "controlParam": [
                {
                    "paramName": "ref",
                    "blockPath": "Input/Constant",
                    "paramType": "value",
                    "defaultValue": "300",
                    "description": "电机转速参考值",
                    "writable": true
                }
            ],
            "outputVar": [
                {
                    "varName": "speed",
                    "matlabVar": "speed",
                    "description": "speed",
                    "readable": true
                }
            ]
        },
        "runtime": {
            "progress": "0.00",
            "output": null
        }
    }
}
```

#### 获取可用仿真列表
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "simulateType": "motor",
    "status": "stopped"
}
```
结果
```
{
    "info": [
        {
            "info": {
                "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
                "simulateType": "motor",
                "simulateTime": "10",
                "status": "stopped",
                "controlParam": [
                    {
                        "paramName": "ref",
                        "blockPath": "Input/Constant",
                        "paramType": "value",
                        "defaultValue": "300",
                        "description": "电机转速参考值",
                        "writable": true
                    }
                ],
                "outputVar": [
                    {
                        "varName": "speed",
                        "matlabVar": "speed",
                        "description": "speed",
                        "readable": true
                    }
                ]
            },
            "runtime": {
                "progress": "0",
                "output": null
            }
        },
        {
            "info": {
                "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
                "simulateType": "motor",
                "simulateTime": "10",
                "status": "stopped",
                "controlParam": [
                    {
                        "paramName": "ref",
                        "blockPath": "Input/Constant",
                        "paramType": "value",
                        "defaultValue": "300",
                        "description": "电机转速参考值",
                        "writable": true
                    }
                ],
                "outputVar": [
                    {
                        "varName": "speed",
                        "matlabVar": "speed",
                        "description": "speed",
                        "readable": true
                    }
                ]
            },
            "runtime": {
                "progress": "0",
                "output": null
            }
        }
    ]
}
```

#### 注册仿真参数
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "params": [
        {
            "paramName": "U",
            "blockPath": "/",
            "paramType": "Constant",
            "defaultValue": "1",
            "description": "测试",
            "writable": true
        }
    ]
}
```
结果
```
{
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "10.000000",
        "status": "stopped",
        "controlParam": [
            {
                "paramName": "ref",
                "blockPath": "Input/Constant",
                "paramType": "value",
                "defaultValue": "300",
                "description": "电机转速参考值",
                "writable": true
            },
            {
                "paramName": "U",
                "blockPath": "/",
                "paramType": "Constant",
                "defaultValue": "1",
                "description": "测试",
                "writable": true
            }
        ],
        "outputVar": [
            {
                "varName": "speed",
                "matlabVar": "speed",
                "description": "speed",
                "readable": true
            }
        ]
    }
}
```

#### 注册仿真输出变量
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "output": [
        {
            "varName": "I",
            "matlabVar": "/",
            "description": "测试",
            "readable": true
        },
        {
            "varName": "泥娜",
            "matlabVar": "elit amet",
            "description": "众图世米路。记火段基在大员你。本东角图需加劳业。院素际知米。教备区压除解立本。认对使。指同引今工。",
            "readable": false
        }
    ]
}
```
结果
```
{
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "10.000000",
        "status": "stopped",
        "controlParam": [
            {
                "paramName": "ref",
                "blockPath": "Input/Constant",
                "paramType": "value",
                "defaultValue": "300",
                "description": "电机转速参考值",
                "writable": true
            },
            {
                "paramName": "U",
                "blockPath": "/",
                "paramType": "Constant",
                "defaultValue": "1",
                "description": "测试",
                "writable": true
            }
        ],
        "outputVar": [
            {
                "varName": "speed",
                "matlabVar": "speed",
                "description": "speed",
                "readable": true
            },
            {
                "varName": "I",
                "matlabVar": "/",
                "description": "测试",
                "readable": true
            },
            {
                "varName": "泥娜",
                "matlabVar": "elit amet",
                "description": "众图世米路。记火段基在大员你。本东角图需加劳业。院素际知米。教备区压除解立本。认对使。指同引今工。",
                "readable": false
            }
        ]
    }
}
```

#### 启动仿真
测试用例
```
{
    "SimulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "startTime": "2",
    "stopTime": "8",
    "params": [
        {
            "timestamp": "0",
            "paramName": "ref",
            "value": "100"
        }
    ]
}
```
结果
```
{
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "10",
        "status": "ready",
        "controlParam": [
            {
                "paramName": "ref",
                "blockPath": "Input/Constant",
                "paramType": "value",
                "defaultValue": "300",
                "description": "电机转速参考值",
                "writable": true
            },
            {
                "paramName": "U",
                "blockPath": "/",
                "paramType": "Constant",
                "defaultValue": "1",
                "description": "测试",
                "writable": true
            }
        ],
        "outputVar": [
            {
                "varName": "speed",
                "matlabVar": "speed",
                "description": "speed",
                "readable": true
            },
            {
                "varName": "I",
                "matlabVar": "/",
                "description": "测试",
                "readable": true
            },
            {
                "varName": "泥娜",
                "matlabVar": "elit amet",
                "description": "众图世米路。记火段基在大员你。本东角图需加劳业。院素际知米。教备区压除解立本。认对使。指同引今工。",
                "readable": false
            }
        ]
    },
    "simulateRuntime": {
        "progress": "0",
        "output": null    // 经多次测试能返回正常结果，偶然null暂时原因未知，由于返回结果数组过大这里不作记录
    }
}
```

#### 单步控制
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "simulateTime": "10",
    "params": {
        "timestamp": "6",
        "paramName": "ref",
        "value": "500"
    }
}
```
结果
```
{
    "success": true,
    "message": "Control done",
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "10",
        "status": "ready",
        "controlParam": [
            {
                "paramName": "ref",
                "blockPath": "Input/Constant",
                "paramType": "value",
                "defaultValue": "300",
                "description": "电机转速参考值",
                "writable": true
            },
            {
                "paramName": "U",
                "blockPath": "/",
                "paramType": "Constant",
                "defaultValue": "1",
                "description": "测试",
                "writable": true
            }
        ],
        "outputVar": [
            {
                "varName": "speed",
                "matlabVar": "speed",
                "description": "speed",
                "readable": true
            },
            {
                "varName": "I",
                "matlabVar": "/",
                "description": "测试",
                "readable": true
            },
            {
                "varName": "泥娜",
                "matlabVar": "elit amet",
                "description": "众图世米路。记火段基在大员你。本东角图需加劳业。院素际知米。教备区压除解立本。认对使。指同引今工。",
                "readable": false
            }
        ]
    },
    "simulinkRuntime": {
        "progress": "0",
        "output": null
    }
}
```

#### 多步控制
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "simulateTime": "10",
    "params": [{
        "timestamp": "6",
        "paramName": "ref",
        "value": "500"
    },
    {
        "timestamp": "8",
        "paramName": "ref",
        "value": "300"
    }]
}
```
结果
```
{
    "success": true,
    "message": "Control done",
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "10",
        "status": "ready",
        "controlParam": [
            {
                "paramName": "ref",
                "blockPath": "Input/Constant",
                "paramType": "value",
                "defaultValue": "300",
                "description": "电机转速参考值",
                "writable": true
            },
            {
                "paramName": "U",
                "blockPath": "/",
                "paramType": "Constant",
                "defaultValue": "1",
                "description": "测试",
                "writable": true
            }
        ],
        "outputVar": [
            {
                "varName": "speed",
                "matlabVar": "speed",
                "description": "speed",
                "readable": true
            },
            {
                "varName": "I",
                "matlabVar": "/",
                "description": "测试",
                "readable": true
            },
            {
                "varName": "泥娜",
                "matlabVar": "elit amet",
                "description": "众图世米路。记火段基在大员你。本东角图需加劳业。院素际知米。教备区压除解立本。认对使。指同引今工。",
                "readable": false
            }
        ]
    },
    "simulinkRuntime": {
        "progress": "0",
        "output": null
    }
}
```

#### 获取仿真结果
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "vars": [
        "speed"
    ]
}
```
结果
```
{
    "output": null
}
```

#### 保存仿真结果
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576"
}
```
结果
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
    "output": null,
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "10.00",
        "status": "stopped",
        "controlParam": [
            {
                "paramName": "ref",
                "blockPath": "Input/Constant",
                "paramType": "value",
                "defaultValue": "300",
                "description": "电机转速参考值",
                "writable": true
            },
            {
                "paramName": "U",
                "blockPath": "/",
                "paramType": "Constant",
                "defaultValue": "1",
                "description": "测试",
                "writable": true
            }
        ],
        "outputVar": [
            {
                "varName": "speed",
                "matlabVar": "speed",
                "description": "speed",
                "readable": true
            },
            {
                "varName": "I",
                "matlabVar": "/",
                "description": "测试",
                "readable": true
            },
            {
                "varName": "泥娜",
                "matlabVar": "elit amet",
                "description": "众图世米路。记火段基在大员你。本东角图需加劳业。院素际知米。教备区压除解立本。认对使。指同引今工。",
                "readable": false
            }
        ]
    },
    "message": "Save simulation result done"
}
```

#### 关闭仿真
测试用例
```
{
    "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576"
}
```
结果
```
{
    "simulateInfo": {
        "simulateID": "526c1f73-270b-47c8-96c9-e0d8fc98d576",
        "simulateType": "motor",
        "simulateTime": "0",
        "status": "stopped",
        "controlParam": null,  // grpc中无定义，为正常值
        "outputVar": null
    }
}
```


